{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{addNewCommentCase,deleteCommentCase,detailCase,duplicateCase,getCaseHistory,getListCommentCase,updateAssignedCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import CaseHistory from\"../../components/CaseHistory\";import{baseURLFile,COUNTRIES,CURRENCYITEMS}from\"../../constants\";import{useDropzone}from\"react-dropzone\";import{toast}from\"react-toastify\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{CASE_DUPLICATE_REQUEST}from\"../../redux/constants/caseConstants\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function DetailCaseScreen(){var _caseInfo$assurance_n,_caseInfo$created_use,_caseInfo$created_use2,_caseInfo$assurance_n2,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$pat,_caseInfo$patient2,_caseInfo$patient3,_caseInfo$case_status,_caseInfo$patient$ful2,_caseInfo$patient4,_caseInfo$patient$bir,_caseInfo$patient5,_caseInfo$patient$pat2,_caseInfo$patient6,_caseInfo$patient$pat3,_caseInfo$patient7,_caseInfo$patient$pat4,_caseInfo$patient8,_caseInfo$patient$pat5,_caseInfo$patient9,_caseInfo$case_type,_caseInfo$case_type_i,_caseInfo$currency_pr,_caseInfo$coordinator5,_caseInfo$coordinator6,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$assistance_,_caseInfo$medical_rep,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$assurance_s,_caseInfo$assurance$a2,_caseInfo$assurance2,_caseInfo$assurance_n3,_caseInfo$policy_numb,_caseInfo$upload_auth;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams,setSearchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const tabParam=searchParams.get(\"tab\")||\"General Information\";const historyPageParam=searchParams.get(\"historyPage\")||\"1\";const[isLoading,setIsLoading]=useState(false);const[openDiag,setOpenDiag]=useState(false);const[selectCoordinator,setSelectCoordinator]=useState(\"\");const[selectCoordinatorError,setSelectCoordinatorError]=useState(\"\");const[selectPage,setSelectPage]=useState(tabParam);const[commentInput,setCommentInput]=useState(\"\");const[commentInputError,setCommentInputError]=useState(\"\");const[isDuplicate,setIsDuplicate]=useState(false);const[isDeleteComment,setIsDeleteComment]=useState(false);const[selectComment,setSelectComment]=useState(\"\");const[eventType,setEventType]=useState(\"\");// files comment\n// initialMedicalReports\nconst[filesComments,setFilesComments]=useState([]);const{getRootProps:getRootComments,getInputProps:getInputComments}=useDropzone({accept:{\"image/*\":[]},onDrop:acceptedFiles=>{setFilesComments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesComments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCommentCase=useSelector(state=>state.commentCaseList);const{comments,loadingCommentCase,errorCommentCase,pages}=listCommentCase;const commentCaseDelete=useSelector(state=>state.deleteCommentCase);const{loadingCommentCaseDelete,successCommentCaseDelete,errorCommentCaseDelete}=commentCaseDelete;const createCommentCase=useSelector(state=>state.createNewCommentCase);const{loadingCommentCaseAdd,successCommentCaseAdd,errorCommentCaseAdd}=createCommentCase;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseAssignedUpdate=useSelector(state=>state.updateCaseAssigned);const{loadingCaseAssignedUpdate,errorCaseAssignedUpdate,successCaseAssignedUpdate}=caseAssignedUpdate;const caseDuplicat=useSelector(state=>state.duplicateCase);const{loadingCaseDuplicate,errorCaseDuplicate,successCaseDuplicate,caseDuplicate}=caseDuplicat;const caseHistoryState=useSelector(state=>state.caseHistory);const{loadingHistory,errorHistory,history,page:historyCurrentPage,pages:historyTotalPages}=caseHistoryState;// We don't need historyPage state anymore as we're using URL parameters directly\n//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{console.log(userInfo);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[navigate,userInfo,dispatch,id,page]);useEffect(()=>{if(successCommentCaseAdd){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseAdd]);useEffect(()=>{if(successCommentCaseDelete){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseDelete]);useEffect(()=>{if(successCaseDuplicate&&caseDuplicate){navigate(\"/cases/edit/\"+caseDuplicate);dispatch({type:\"RESET_DUPLICATE_CASE\"});}},[successCaseDuplicate,caseDuplicate]);// Reset flag on navigation back\nuseEffect(()=>{return()=>setIsDuplicate(false);},[]);useEffect(()=>{if(successCaseAssignedUpdate){setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[successCaseAssignedUpdate]);// Fetch history data when the History tab is selected or history page changes\nuseEffect(()=>{if(selectPage===\"History\"&&id){// Get the historyPage from URL parameters\nconst historyPageFromUrl=searchParams.get('page')||'1';dispatch(getCaseHistory(id,historyPageFromUrl));}},[selectPage,id,dispatch,searchParams]);// We don't need the handleHistoryPageChange function anymore\n// since Paginate component handles navigation directly through links\n// Handle tab selection\nconst handleTabChange=tabName=>{setSelectPage(tabName);// Update URL with the new tab\nconst newParams=new URLSearchParams(searchParams);newParams.set('tab',tabName);setSearchParams(newParams);};const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";case\"coordination-fee\":return\"Coordination Fee\";case\"coordinated-missing-payment\":return\"Coordinated, Missing Payment\";case\"failed\":return\"Failed\";default:return casestatus;}};const caseStatusColor=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"text-danger\";case\"coordinated-missing-m-r\":return\"text-[#FFA500]\";case\"coordinated-missing-invoice\":return\"text-[#FFA500]\";case\"waiting-for-insurance-authorization\":return\"text-primary\";case\"coordinated-patient-not-seen-yet\":return\"text-primary\";case\"fully-coordinated\":return\"text-[#008000]\";case\"failed\":return\"text-[#d34053]\";default:return\"\";}};const getIconCountry=country=>{const foundCountry=COUNTRIES.find(option=>option.title===country);if(foundCountry){return foundCountry.icon;}else{return\"\";}};//\nconst getCurrencyCode=code=>{const patientCurrency=code!==null&&code!==void 0?code:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){var _foundCurrency$symbol;return(_foundCurrency$symbol=foundCurrency.symbol)!==null&&_foundCurrency$symbol!==void 0?_foundCurrency$symbol:code;}else{return code;}};const getSectionIndex=selectItem=>{if(selectItem===\"General Information\"){return 0;}else if(selectItem===\"Coordination Details\"){return 1;}else if(selectItem===\"Medical Reports\"){return 2;}else if(selectItem===\"Invoices\"){return 3;}else if(selectItem===\"Insurance Authorization\"){return 4;}else if(selectItem===\"History\"){return 0;}else{return 0;}};//\nreturn/*#__PURE__*/_jsxs(DefaultLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col justify-between my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\",children:[\"CIA REF: \",(_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"---\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Created By:\"}),\" \",(_caseInfo$created_use=(_caseInfo$created_use2=caseInfo.created_user)===null||_caseInfo$created_use2===void 0?void 0:_caseInfo$created_use2.full_name)!==null&&_caseInfo$created_use!==void 0?_caseInfo$created_use:\"---\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:flex hidden  flex-row justify-end\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator,_caseInfo$coordinator2;setSelectCoordinator((_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex flex-row items-center bg-[#FF9100] hover:bg-[#FF9100] text-white px-3 py-2 rounded-lg transition-colors duration-300 shadow-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-sm\",children:\"Assign Coordinator\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\",children:[\"CIA REF: \",(_caseInfo$assurance_n2=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n2!==void 0?_caseInfo$assurance_n2:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:caseInfo.is_pay?/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold bg-primary px-2 py-1 text-white\",children:\"Paid\"}):/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold bg-danger  px-2 py-1 text-white\",children:\"Unpaid\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"CIA:\"}),\" \",(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Full Name:\"}),\" \",(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1  text-sm items-center  \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-[#303030] opacity-80\",children:\"Country:\"}),\" \",getIconCountry((_caseInfo$patient$pat=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.patient_country)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\"),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"text-[#303030] opacity-80\",children:caseStatus((_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.patient_country)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Status:\"}),\" \",(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map((stat,index)=>/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:caseStatusColor(stat.status_coordination),children:caseStatus(stat.status_coordination)}),\"- \"]}))]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row justify-end md:hidden\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator3,_caseInfo$coordinator4;setSelectCoordinator((_caseInfo$coordinator3=(_caseInfo$coordinator4=caseInfo.coordinator_user)===null||_caseInfo$coordinator4===void 0?void 0:_caseInfo$coordinator4.id)!==null&&_caseInfo$coordinator3!==void 0?_caseInfo$coordinator3:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex flex-row items-center bg-[#FF9100] text-white px-2 py-1 rounded \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-sm\",children:\" Assigned Coordinator \"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center\",children:/*#__PURE__*/_jsxs(\"a\",{className:\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",href:\"/cases/edit/\"+caseInfo.id+\"?section=\"+getSectionIndex(selectPage),children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Edit Case\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\",\"History\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleTabChange(select),className:`px-4 py-1 md:my-0 my-1  text-sm ${selectPage===select?\"rounded-full bg-[#0388A6] text-white font-medium \":\"font-normal text-[#838383]\"}`,children:select}))}),selectPage===\"General Information\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Patient Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$ful2=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date of Birth:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$bir=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat2=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_phone)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat3=(_caseInfo$patient7=caseInfo.patient)===null||_caseInfo$patient7===void 0?void 0:_caseInfo$patient7.patient_email)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Country:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat4=(_caseInfo$patient8=caseInfo.patient)===null||_caseInfo$patient8===void 0?void 0:_caseInfo$patient8.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"City:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat5=(_caseInfo$patient9=caseInfo.patient)===null||_caseInfo$patient9===void 0?void 0:_caseInfo$patient9.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Case Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Type :\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[(_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"---\",caseInfo.case_type===\"Medical\"&&caseInfo.case_type_item&&/*#__PURE__*/_jsxs(_Fragment,{children:[\" | \",(_caseInfo$case_type_i=caseInfo.case_type_item)!==null&&_caseInfo$case_type_i!==void 0?_caseInfo$case_type_i:\"\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Price of service :\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:parseFloat(caseInfo.price_tatal).toFixed(2)+\" \"+getCurrencyCode((_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Price of service (EUR) :\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:parseFloat(caseInfo.eur_price).toFixed(2)+\" \"+getCurrencyCode(\"EUR\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Case Creation Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Assigned Coordinator:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$coordinator5=(_caseInfo$coordinator6=caseInfo.coordinator_user)===null||_caseInfo$coordinator6===void 0?void 0:_caseInfo$coordinator6.full_name)!==null&&_caseInfo$coordinator5!==void 0?_caseInfo$coordinator5:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Description:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Coordination Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Current Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Last Updated Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.updated_at)})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-sm py-5 px-5 rounded-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-5\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-2 rounded-md mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-[#303030] font-medium text-base\",children:\"Assistances Informations\"})]}),((_caseInfo$assistance_=caseInfo.assistance_services)===null||_caseInfo$assistance_===void 0?void 0:_caseInfo$assistance_.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:caseInfo.assistance_services.map((itemAssistance,index)=>{var _itemAssistance$provi;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl shadow-sm overflow-hidden\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsxs(\"h4\",{className:\"font-medium text-[#303030]\",children:[\"Appointment #\",index+1]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center space-x-2\",children:itemAssistance.appointment_date&&/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\",children:formatDate(itemAssistance.appointment_date)})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"px-5 py-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mb-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-4\",children:[itemAssistance.start_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-gray-50 rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Hospital Starting Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-800\",children:formatDate(itemAssistance.start_date)})]})]}),itemAssistance.end_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-gray-50 rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Hospital Ending Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-800\",children:formatDate(itemAssistance.end_date)})]})]}),itemAssistance.service_location&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center bg-gray-50 rounded-lg p-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white rounded-full p-2 shadow-sm mr-3\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-800\",children:itemAssistance.service_location})]})]})]})}),((_itemAssistance$provi=itemAssistance.provider_services)===null||_itemAssistance$provi===void 0?void 0:_itemAssistance$provi.length)>0&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})})}),/*#__PURE__*/_jsx(\"h5\",{className:\"text-sm font-medium text-[#303030]\",children:\"Providers\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:itemAssistance.provider_services.map((providerService,idx)=>{var _providerService$prov,_providerService$prov2;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-800\",children:((_providerService$prov=providerService.provider)===null||_providerService$prov===void 0?void 0:_providerService$prov.full_name)||\"---\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\",children:providerService.service_type||\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\",children:[providerService.service_specialist&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-gray-400 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#303030]\",children:providerService.service_specialist})]})]}),providerService.provider_date&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-gray-400 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Visit Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#303030]\",children:formatDate(providerService.provider_date)})]})]}),((_providerService$prov2=providerService.provider)===null||_providerService$prov2===void 0?void 0:_providerService$prov2.phone)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-gray-400 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 block\",children:\"Contact\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-[#303030]\",children:providerService.provider.phone})]})]})]})]},idx);})})]})]})]},index);})}):/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-xl p-8 text-center shadow-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-8 h-8 text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-[#303030] font-medium mb-2\",children:\"No Assistances Informations\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-500 text-sm\",children:\"No assistances details have been added to this case yet.\"})]})]})})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Invoice Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date Issued:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.date_issued)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Amount:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[\"$ \",parseFloat(caseInfo.invoice_amount).toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Due Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Insurance Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Insurance Company Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance$a2=(_caseInfo$assurance2=caseInfo.assurance)===null||_caseInfo$assurance2===void 0?void 0:_caseInfo$assurance2.assurance_name)!==null&&_caseInfo$assurance$a2!==void 0?_caseInfo$assurance$a2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"CIA Reference:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_n3=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n3!==void 0?_caseInfo$assurance_n3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Policy Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"History\"?/*#__PURE__*/_jsx(CaseHistory,{historyData:{history:history,page:historyCurrentPage,pages:historyTotalPages,count:(history===null||history===void 0?void 0:history.length)||0},loading:loadingHistory,error:errorHistory}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 b py-3  px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1  py-1 px-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Comment\"}),/*#__PURE__*/_jsx(\"textarea\",{value:commentInput,onChange:v=>setCommentInput(v.target.value),className:`  ${commentInputError?\"border-danger\":\"border-[#F1F3FF]\"} min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:commentInputError?commentInputError:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 bg-white py-1 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Images\"}),/*#__PURE__*/_jsxs(\"div\",{...getRootComments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputComments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-7 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-sm\",children:\"Drag & Drop Images or BROWSE\"})]})]})})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesComments===null||filesComments===void 0?void 0:filesComments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-[#81838E] text-center  shadow-1 \",children:/*#__PURE__*/_jsx(\"img\",{src:file.preview,className:\"size-8\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesComments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{disabled:loadingCommentCaseAdd,onClick:async()=>{var check=true;setCommentInputError(\"\");if(commentInput===\"\"&&filesComments.length===0){setCommentInputError(\"This field is required.\");check=false;}if(check){await dispatch(addNewCommentCase({content:commentInput,// files\nfiles_commet:filesComments},id));}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",children:loadingCommentCaseAdd?\"Loading ..\":\"Save\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5\",children:loadingCommentCase?/*#__PURE__*/_jsx(Loader,{}):errorCommentCase?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCommentCase}):comments?/*#__PURE__*/_jsx(_Fragment,{children:comments===null||comments===void 0?void 0:comments.map((comment,index)=>{var _comment$coordinator,_comment$coordinator2,_comment$coordinator3,_comment$coordinator4,_comment$coordinator5,_comment$coordinator6,_comment$coordinator$,_comment$coordinator7,_comment$content,_comment$files;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-start\",children:[/*#__PURE__*/_jsx(\"div\",{children:comment.coordinator?(_comment$coordinator=comment.coordinator)!==null&&_comment$coordinator!==void 0&&_comment$coordinator.photo?/*#__PURE__*/_jsx(\"img\",{className:\" size-12 rounded-full\",src:baseURLFile+((_comment$coordinator2=comment.coordinator)===null||_comment$coordinator2===void 0?void 0:_comment$coordinator2.photo),onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"div\",{className:\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" uppercase\",children:[(_comment$coordinator3=comment.coordinator)!==null&&_comment$coordinator3!==void 0&&_comment$coordinator3.first_name?(_comment$coordinator4=comment.coordinator)===null||_comment$coordinator4===void 0?void 0:_comment$coordinator4.first_name[0]:\"\",(_comment$coordinator5=comment.coordinator)!==null&&_comment$coordinator5!==void 0&&_comment$coordinator5.last_name?(_comment$coordinator6=comment.coordinator)===null||_comment$coordinator6===void 0?void 0:_comment$coordinator6.last_name[0]:\"\"]})}):null}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row mb-1 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 text-xs flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"p\",{children:formatDate(comment.created_at)}),comment.can_delete?/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setSelectComment(comment.id);setEventType(\"delete\");setIsDeleteComment(true);},className:\"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-3\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})}),/*#__PURE__*/_jsx(\"p\",{className:\"px-1\",children:\" Delete\"})]}):null]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1 font-semibold\",children:(_comment$coordinator$=(_comment$coordinator7=comment.coordinator)===null||_comment$coordinator7===void 0?void 0:_comment$coordinator7.full_name)!==null&&_comment$coordinator$!==void 0?_comment$coordinator$:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1  whitespace-pre-line\",children:(_comment$content=comment.content)!==null&&_comment$content!==void 0?_comment$content:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap items-center  my-1\",children:comment===null||comment===void 0?void 0:(_comment$files=comment.files)===null||_comment$files===void 0?void 0:_comment$files.map((file,index)=>/*#__PURE__*/_jsx(\"a\",{target:\"_blank\",rel:\"noopener noreferrer\",href:baseURLFile+file.file,children:/*#__PURE__*/_jsx(\"img\",{src:baseURLFile+file.file,className:\"size-30 shadow-1 rounded m-1\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}))}),/*#__PURE__*/_jsx(\"hr\",{className:\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"})]})]});})}):null})]})})]}):null]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDeleteComment,message:eventType===\"delete\"?\"Are you sure you want to delete this Comment?\":\"Are you sure ?\",title:eventType===\"delete\"?\"Delete Comment\":\"Confirmation\",icon:\"delete\",confirmText:\"Delete\",cancelText:\"Cancel\",onConfirm:async()=>{if(eventType===\"delete\"&&selectComment!==\"\"){dispatch(deleteCommentCase(selectComment));setIsDeleteComment(false);setEventType(\"\");}else{setIsDeleteComment(false);setEventType(\"\");setSelectComment(\"\");}},onCancel:()=>{setIsDeleteComment(false);setEventType(\"\");setSelectComment(\"\");},loadEvent:loadingCommentCaseDelete}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:openDiag,title:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center text-[#0388A6]\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Assign Case Coordinator\"})]}),message:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full my-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 text-[#0388A6] mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"})}),/*#__PURE__*/_jsxs(\"label\",{className:\"text-[#0388A6] font-medium text-sm\",children:[\"Assigned Coordinator \",/*#__PURE__*/_jsx(\"span\",{className:\"text-red-500\",children:\"*\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-gray-400\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"select\",{className:`bg-white border ${selectCoordinatorError?\"border-red-500 focus:ring-red-500 focus:border-red-500\":\"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"} text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`,value:selectCoordinator,onChange:v=>setSelectCoordinator(v.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select a coordinator...\"}),coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name},item.id))]}),/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 text-gray-400\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m19.5 8.25-7.5 7.5-7.5-7.5\"})})})]}),selectCoordinatorError&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mt-2 text-red-500\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 mr-1\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs\",children:selectCoordinatorError})]})]}),icon:\"info\",confirmText:\"Assign Coordinator\",cancelText:\"Cancel\",confirmButtonClass:\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\",cancelButtonClass:\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\",onConfirm:async()=>{setSelectCoordinatorError(\"\");if(selectCoordinator===\"\"){setSelectCoordinatorError(\"This field is required.\");}else{setIsLoading(true);await dispatch(updateAssignedCase(id,{coordinator:selectCoordinator}));setIsLoading(false);}},onCancel:()=>{setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);setIsLoading(false);},loadEvent:isLoading,loadingText:\"Assigning coordinator...\",loadingIcon:/*#__PURE__*/_jsxs(\"svg\",{className:\"animate-spin h-5 w-5 text-white\",xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",children:[/*#__PURE__*/_jsx(\"circle\",{className:\"opacity-25\",cx:\"12\",cy:\"12\",r:\"10\",stroke:\"currentColor\",strokeWidth:\"4\"}),/*#__PURE__*/_jsx(\"path\",{className:\"opacity-75\",fill:\"currentColor\",d:\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"})]})})]});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getCaseHistory", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "CaseHistory", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance_n2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$case_type", "_caseInfo$case_type_i", "_caseInfo$currency_pr", "_caseInfo$coordinator5", "_caseInfo$coordinator6", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$assistance_", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n3", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "setSearchParams", "page", "get", "tabParam", "historyPageParam", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "caseHistoryState", "caseHistory", "loadingHistory", "errorHistory", "history", "historyCurrentPage", "historyTotalPages", "redirect", "console", "log", "type", "historyPageFromUrl", "handleTabChange", "tabName", "newParams", "URLSearchParams", "set", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "message", "assurance_number", "class", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "strokeWidth", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "case_type", "case_type_item", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "case_description", "updated_at", "assistance_services", "length", "itemAssistance", "_itemAssistance$provi", "appointment_date", "start_date", "end_date", "service_location", "provider_services", "providerService", "idx", "_providerService$prov", "_providerService$prov2", "provider", "service_type", "service_specialist", "provider_date", "phone", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "historyData", "count", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "can_delete", "files", "isOpen", "confirmText", "cancelText", "onConfirm", "onCancel", "loadEvent", "confirmButtonClass", "cancelButtonClass", "loadingText", "loadingIcon", "cx", "cy", "r"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getCaseHistory,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n\n  const caseHistoryState = useSelector((state) => state.caseHistory);\n  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = (tabName) => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:hidden\">\n                  CIA REF: {caseInfo.assurance_number ?? \"---\"}\n                </div>\n                <div className=\"w-3\"></div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:flex hidden  flex-row justify-end\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-[#FF9100] hover:bg-[#FF9100] text-white px-3 py-2 rounded-lg transition-colors duration-300 shadow-sm\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-5 h-5 mr-2\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg>\n                  <span className=\"font-medium text-sm\">Assign Coordinator</span>\n                </button>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\" text-[#32475C] text-sm  font-bold opacity-85 ml-1 md:my-0 my-1 md:block hidden\">\n                  CIA REF: {caseInfo.assurance_number ?? \"---\"}\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    {caseInfo.is_pay ? (\n                      <span className=\"font-semibold bg-primary px-2 py-1 text-white\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"font-semibold bg-danger  px-2 py-1 text-white\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseInfo.case_status?.map((stat, index) => (\n                      <>\n                        <span\n                          className={caseStatusColor(stat.status_coordination)}\n                        >\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                        {\"- \"}\n                      </>\n                    ))}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n              <div className=\"flex flex-row justify-end md:hidden\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-[#FF9100] text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                  \"History\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => handleTabChange(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Type :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_type ?? \"---\"}\n                        {\n                          caseInfo.case_type===\"Medical\" && caseInfo.case_type_item && <> | {caseInfo.case_type_item??\"\"}</>\n                        }\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Price of service :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.price_tatal).toFixed(2) +\n                          \" \" +\n                          getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">\n                        Price of service (EUR) :\n                      </div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.eur_price).toFixed(2) +\n                          \" \" +\n                          getCurrencyCode(\"EUR\")}\n                      </div>\n                    </div>\n\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                \n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-sm py-5 px-5 rounded-lg\">\n                    <div className=\"w-full\">\n                      <div className=\"flex items-center mb-5\">\n                        <div className=\"bg-[#E6F4F7] p-2 rounded-md mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                          </svg>\n                        </div>\n                        <h3 className=\"text-[#303030] font-medium text-base\">Assistances Informations</h3>\n                      </div>\n\n                      {caseInfo.assistance_services?.length > 0 ? (\n                        <div className=\"space-y-6\">\n                          {caseInfo.assistance_services.map((itemAssistance, index) => (\n                            <div key={index} className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                              {/* Assistance Header */}\n                              <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\">\n                                <div className=\"flex justify-between items-center\">\n                                  <div className=\"flex items-center\">\n                                    <div className=\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\">\n                                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                      </svg>\n                                    </div>\n                                    <h4 className=\"font-medium text-[#303030]\">Appointment #{index + 1}</h4>\n                                  </div>\n                                  <div className=\"flex items-center space-x-2\">\n                                    {itemAssistance.appointment_date && (\n                                      <div className=\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\">\n                                        {formatDate(itemAssistance.appointment_date)}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Assistance Content */}\n                              <div className=\"px-5 py-4\">\n                                {/* Assistance Details */}\n                                <div className=\"mb-6\">\n                                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                                    {itemAssistance.start_date && (\n                                      <div className=\"flex items-center bg-gray-50 rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-gray-500 block\">Hospital Starting Date</span>\n                                          <span className=\"text-sm font-medium text-gray-800\">{formatDate(itemAssistance.start_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.end_date && (\n                                      <div className=\"flex items-center bg-gray-50 rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-gray-500 block\">Hospital Ending Date</span>\n                                          <span className=\"text-sm font-medium text-gray-800\">{formatDate(itemAssistance.end_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.service_location && (\n                                      <div className=\"flex items-center bg-gray-50 rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-gray-500 block\">Service Location</span>\n                                          <span className=\"text-sm font-medium text-gray-800\">{itemAssistance.service_location}</span>\n                                        </div>\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Provider Services */}\n                                {itemAssistance.provider_services?.length > 0 && (\n                                  <div>\n                                    <div className=\"flex items-center mb-4\">\n                                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n                                        </svg>\n                                      </div>\n                                      <h5 className=\"text-sm font-medium text-[#303030]\">Providers</h5>\n                                    </div>\n\n                                    <div className=\"space-y-4\">\n                                      {itemAssistance.provider_services.map((providerService, idx) => (\n                                        <div key={idx} className=\"bg-gray-50 p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\">\n                                          <div className=\"flex items-center mb-3\">\n                                            <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\">\n                                              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                              </svg>\n                                            </div>\n                                            <div>\n                                              <span className=\"text-sm font-medium text-gray-800\">{providerService.provider?.full_name || \"---\"}</span>\n                                              <span className=\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\">{providerService.service_type || \"---\"}</span>\n                                            </div>\n                                          </div>\n\n                                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\">\n                                            {providerService.service_specialist && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-gray-400 mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-gray-500 block\">Speciality</span>\n                                                  <span className=\"text-sm text-[#303030]\">{providerService.service_specialist}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider_date && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-gray-400 mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-gray-500 block\">Visit Date</span>\n                                                  <span className=\"text-sm text-[#303030]\">{formatDate(providerService.provider_date)}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider?.phone && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-gray-400 mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-gray-500 block\">Contact</span>\n                                                  <span className=\"text-sm text-[#303030]\">{providerService.provider.phone}</span>\n                                                </div>\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"bg-white rounded-xl p-8 text-center shadow-sm\">\n                          <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-[#303030] font-medium mb-2\">No Assistances Informations</h4>\n                          <p className=\"text-gray-500 text-sm\">No assistances details have been added to this case yet.</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          $ {parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">CIA Reference:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/* \"History\" */}\n              {selectPage === \"History\" ? (\n                <CaseHistory\n                  historyData={{\n                    history: history,\n                    page: historyCurrentPage,\n                    pages: historyTotalPages,\n                    count: history?.length || 0\n                  }}\n                  loading={loadingHistory}\n                  error={errorHistory}\n                />\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs flex flex-row items-center\">\n                                <p>{formatDate(comment.created_at)}</p>\n                                {comment.can_delete ? (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"text-danger px-1 mx-1 font-bold text-md hover:border-b flex flex-row items-center \"\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-3\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                                      />\n                                    </svg>\n                                    <p className=\"px-1\"> Delete</p>\n                                  </button>\n                                ) : null}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1  whitespace-pre-line\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        title={eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\"}\n        icon=\"delete\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      <ConfirmationModal\n        isOpen={openDiag}\n        title={\n          <div className=\"flex items-center text-[#0388A6]\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n            </svg>\n            <span>Assign Case Coordinator</span>\n          </div>\n        }\n        message={\n          <div className=\"w-full my-4\">\n            <div className=\"flex items-center mb-2\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6] mr-2\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n              </svg>\n              <label className=\"text-[#0388A6] font-medium text-sm\">\n                Assigned Coordinator <span className=\"text-red-500\">*</span>\n              </label>\n            </div>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                </svg>\n              </div>\n              <select\n                className={`bg-white border ${\n                  selectCoordinatorError\n                    ? \"border-red-500 focus:ring-red-500 focus:border-red-500\"\n                    : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"\n                } text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}\n                value={selectCoordinator}\n                onChange={(v) => setSelectCoordinator(v.target.value)}\n              >\n                <option value={\"\"}>Select a coordinator...</option>\n                {coordinators?.map((item) => (\n                  <option key={item.id} value={item.id}>{item.full_name}</option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m19.5 8.25-7.5 7.5-7.5-7.5\" />\n                </svg>\n              </div>\n            </div>\n            {selectCoordinatorError && (\n              <div className=\"flex items-center mt-2 text-red-500\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                </svg>\n                <span className=\"text-xs\">{selectCoordinatorError}</span>\n              </div>\n            )}\n          </div>\n        }\n        icon=\"info\"\n        confirmText=\"Assign Coordinator\"\n        cancelText=\"Cancel\"\n        confirmButtonClass=\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\"\n        cancelButtonClass=\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\"\n        onConfirm={async () => {\n          setSelectCoordinatorError(\"\");\n\n          if (selectCoordinator === \"\") {\n            setSelectCoordinatorError(\"This field is required.\");\n          } else {\n            setIsLoading(true);\n            await dispatch(\n              updateAssignedCase(id, { coordinator: selectCoordinator })\n            );\n            setIsLoading(false);\n          }\n        }}\n        onCancel={() => {\n          setSelectCoordinator(\"\");\n          setSelectCoordinatorError(\"\");\n          setOpenDiag(false);\n          setIsLoading(false);\n        }}\n        loadEvent={isLoading}\n        loadingText=\"Assigning coordinator...\"\n        loadingIcon={\n          <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        }\n      />\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,iBAAiB,CACjBC,iBAAiB,CACjBC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,kBAAkB,CAClBC,kBAAkB,KACb,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,WAAW,KAAM,8BAA8B,CACtD,OAASC,WAAW,CAAEC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAEvE,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,sBAAsB,KAAQ,qCAAqC,CAC5E,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnE,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAG3E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4E,QAAQ,CAAG7E,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8E,QAAQ,CAAGhF,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEiF,EAAG,CAAC,CAAG7E,SAAS,CAAC,CAAC,CACxB,KAAM,CAAC8E,YAAY,CAAEC,eAAe,CAAC,CAAG9E,eAAe,CAAC,CAAC,CACzD,KAAM,CAAA+E,IAAI,CAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGJ,YAAY,CAACG,GAAG,CAAC,KAAK,CAAC,EAAI,qBAAqB,CACjE,KAAM,CAAAE,gBAAgB,CAAGL,YAAY,CAACG,GAAG,CAAC,aAAa,CAAC,EAAI,GAAG,CAE/D,KAAM,CAACG,SAAS,CAAEC,YAAY,CAAC,CAAG1F,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC2F,QAAQ,CAAEC,WAAW,CAAC,CAAG5F,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC6F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG9F,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC+F,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGhG,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACiG,UAAU,CAAEC,aAAa,CAAC,CAAGlG,QAAQ,CAACuF,QAAQ,CAAC,CACtD,KAAM,CAACY,YAAY,CAAEC,eAAe,CAAC,CAAGpG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACqG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGtG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACuG,WAAW,CAAEC,cAAc,CAAC,CAAGxG,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAACyG,eAAe,CAAEC,kBAAkB,CAAC,CAAG1G,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC2G,aAAa,CAAEC,gBAAgB,CAAC,CAAG5G,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC6G,SAAS,CAAEC,YAAY,CAAC,CAAG9G,QAAQ,CAAC,EAAE,CAAC,CAE9C;AACA;AACA,KAAM,CAAC+G,aAAa,CAAEC,gBAAgB,CAAC,CAAGhH,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAEiH,YAAY,CAAEC,eAAe,CAAEC,aAAa,CAAEC,gBAAiB,CAAC,CACtE/F,WAAW,CAAC,CACVgG,MAAM,CAAE,CACN,SAAS,CAAE,EACb,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,gBAAgB,CAAEQ,SAAS,EAAK,CAC9B,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEJ3H,SAAS,CAAC,IAAM,CACd,MAAO,IACLgH,aAAa,CAACiB,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAK,SAAS,CAAGhI,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAGrI,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAAC1H,UAAU,CAAC,CAC3D,KAAM,CAAE+H,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,eAAe,CAAG1I,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACU,eAAe,CAAC,CACrE,KAAM,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,KAAM,CAAC,CAC7DL,eAAe,CAEjB,KAAM,CAAAM,iBAAiB,CAAGhJ,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAAC3H,iBAAiB,CAAC,CACzE,KAAM,CACJ2I,wBAAwB,CACxBC,wBAAwB,CACxBC,sBACF,CAAC,CAAGH,iBAAiB,CAErB,KAAM,CAAAI,iBAAiB,CAAGpJ,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACoB,oBAAoB,CAAC,CAC5E,KAAM,CAAEC,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CACzEJ,iBAAiB,CAEnB,KAAM,CAAAK,gBAAgB,CAAGzJ,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACyB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,kBAAkB,CAAG9J,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAAC8B,kBAAkB,CAAC,CAC3E,KAAM,CACJC,yBAAyB,CACzBC,uBAAuB,CACvBC,yBACF,CAAC,CAAGJ,kBAAkB,CAEtB,KAAM,CAAAK,YAAY,CAAGnK,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACzH,aAAa,CAAC,CAChE,KAAM,CACJ4J,oBAAoB,CACpBC,kBAAkB,CAClBC,oBAAoB,CACpBC,aACF,CAAC,CAAGJ,YAAY,CAEhB,KAAM,CAAAK,gBAAgB,CAAGxK,WAAW,CAAEiI,KAAK,EAAKA,KAAK,CAACwC,WAAW,CAAC,CAClE,KAAM,CAAEC,cAAc,CAAEC,YAAY,CAAEC,OAAO,CAAEzF,IAAI,CAAE0F,kBAAkB,CAAE9B,KAAK,CAAE+B,iBAAkB,CAAC,CAAGN,gBAAgB,CAEtH;AACA;AACA,KAAM,CAAAO,QAAQ,CAAG,GAAG,CACpBlL,SAAS,CAAC,IAAM,CACd,GAAI,CAACqI,QAAQ,CAAE,CACbrD,QAAQ,CAACkG,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLC,OAAO,CAACC,GAAG,CAAC/C,QAAQ,CAAC,CAErBnD,QAAQ,CAACxE,UAAU,CAACyE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,CAAEsE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACwD,QAAQ,CAAEqD,QAAQ,CAAEnD,QAAQ,CAAEC,EAAE,CAAEG,IAAI,CAAC,CAAC,CAE5CtF,SAAS,CAAC,IAAM,CACd,GAAI0J,qBAAqB,CAAE,CACzBrD,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBU,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,CAAEsE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACuE,qBAAqB,CAAC,CAAC,CAE3B1J,SAAS,CAAC,IAAM,CACd,GAAIqJ,wBAAwB,CAAE,CAC5BhD,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBU,gBAAgB,CAAC,EAAE,CAAC,CACpB/B,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,CAAEsE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACkE,wBAAwB,CAAC,CAAC,CAE9BrJ,SAAS,CAAC,IAAM,CACd,GAAIyK,oBAAoB,EAAIC,aAAa,CAAE,CACzC1F,QAAQ,CAAC,cAAc,CAAG0F,aAAa,CAAC,CACxCxF,QAAQ,CAAC,CAAEmG,IAAI,CAAE,sBAAuB,CAAC,CAAC,CAC5C,CACF,CAAC,CAAE,CAACZ,oBAAoB,CAAEC,aAAa,CAAC,CAAC,CAEzC;AACA1K,SAAS,CAAC,IAAM,CACd,MAAO,IAAMyG,cAAc,CAAC,KAAK,CAAC,CACpC,CAAC,CAAE,EAAE,CAAC,CAENzG,SAAS,CAAC,IAAM,CACd,GAAIqK,yBAAyB,CAAE,CAC7BtE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBX,QAAQ,CAACxE,UAAU,CAACyE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACrE,kBAAkB,CAAC,GAAG,CAAEsE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAAC6I,yBAAyB,CAAC,CAAC,CAE/B;AACArK,SAAS,CAAC,IAAM,CACd,GAAIkG,UAAU,GAAK,SAAS,EAAIf,EAAE,CAAE,CAClC;AACA,KAAM,CAAAmG,kBAAkB,CAAGlG,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC1DL,QAAQ,CAACtE,cAAc,CAACuE,EAAE,CAAEmG,kBAAkB,CAAC,CAAC,CAClD,CACF,CAAC,CAAE,CAACpF,UAAU,CAAEf,EAAE,CAAED,QAAQ,CAAEE,YAAY,CAAC,CAAC,CAE5C;AACA;AAEA;AACA,KAAM,CAAAmG,eAAe,CAAIC,OAAO,EAAK,CACnCrF,aAAa,CAACqF,OAAO,CAAC,CAEtB;AACA,KAAM,CAAAC,SAAS,CAAG,GAAI,CAAAC,eAAe,CAACtG,YAAY,CAAC,CACnDqG,SAAS,CAACE,GAAG,CAAC,KAAK,CAAEH,OAAO,CAAC,CAC7BnG,eAAe,CAACoG,SAAS,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAG,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,IAAK,kBAAkB,CACrB,MAAO,kBAAkB,CAC3B,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,QAAQ,CACX,MAAO,QAAQ,CACjB,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAID,UAAU,EAAK,CACtC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,aAAa,CACtB,IAAK,yBAAyB,CAC5B,MAAO,gBAAgB,CACzB,IAAK,6BAA6B,CAChC,MAAO,gBAAgB,CACzB,IAAK,qCAAqC,CACxC,MAAO,cAAc,CACvB,IAAK,kCAAkC,CACrC,MAAO,cAAc,CACvB,IAAK,mBAAmB,CACtB,MAAO,gBAAgB,CACzB,IAAK,QAAQ,CACX,MAAO,gBAAgB,CACzB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIC,OAAO,EAAK,CAClC,KAAM,CAAAC,YAAY,CAAGrL,SAAS,CAACsL,IAAI,CAAEC,MAAM,EAAKA,MAAM,CAACC,KAAK,GAAKJ,OAAO,CAAC,CAEzE,GAAIC,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CAACI,IAAI,CAC1B,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIC,IAAI,EAAK,CAChC,KAAM,CAAAC,eAAe,CAAGD,IAAI,SAAJA,IAAI,UAAJA,IAAI,CAAI,EAAE,CAElC,KAAM,CAAAE,aAAa,CAAG5L,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqL,IAAI,CACtCC,MAAM,EAAKA,MAAM,CAACI,IAAI,GAAKC,eAC9B,CAAC,CAED,GAAIC,aAAa,CAAE,KAAAC,qBAAA,CACjB,OAAAA,qBAAA,CAAOD,aAAa,CAACE,MAAM,UAAAD,qBAAA,UAAAA,qBAAA,CAAIH,IAAI,CACrC,CAAC,IAAM,CACL,MAAO,CAAAA,IAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAK,eAAe,CAAIC,UAAU,EAAK,CACtC,GAAIA,UAAU,GAAK,qBAAqB,CAAE,CACxC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,sBAAsB,CAAE,CAChD,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,iBAAiB,CAAE,CAC3C,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,UAAU,CAAE,CACpC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,yBAAyB,CAAE,CACnD,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,SAAS,CAAE,CACnC,MAAO,EAAC,CACV,CAAC,IAAM,CACL,MAAO,EAAC,CACV,CACF,CAAC,CAED;AACA,mBACEvL,KAAA,CAACf,aAAa,EAAAuM,QAAA,eACZxL,KAAA,QAAKyL,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfxL,KAAA,QAAKyL,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD1L,IAAA,MAAG4L,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBxL,KAAA,QAAKyL,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1L,IAAA,SACEiM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNnM,IAAA,SAAM2L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ1L,IAAA,SAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1L,IAAA,SACEiM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPnM,IAAA,MAAG4L,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB1L,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ1L,IAAA,SAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB1L,IAAA,SACEiM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPnM,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGL7E,eAAe,cACd7G,IAAA,CAACZ,MAAM,GAAE,CAAC,CACR0H,aAAa,cACf9G,IAAA,CAACX,KAAK,EAACoK,IAAI,CAAE,OAAQ,CAAC2C,OAAO,CAAEtF,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACV9G,KAAA,QAAAwL,QAAA,eAEExL,KAAA,QAAKyL,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvDxL,KAAA,QAAKyL,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DxL,KAAA,QAAKyL,SAAS,CAAC,2EAA2E,CAAAD,QAAA,EAAC,WAChF,EAAA/K,qBAAA,CAACqG,QAAQ,CAACqF,gBAAgB,UAAA1L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACzC,CAAC,cACNX,IAAA,QAAK2L,SAAS,CAAC,KAAK,CAAM,CAAC,cAE3BzL,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,2JAA2J,CAC9J,CAAC,CACC,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD1L,IAAA,SAAM2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,CAAC,GAAG,EAAA9K,qBAAA,EAAAC,sBAAA,CACrDmG,QAAQ,CAACuF,YAAY,UAAA1L,sBAAA,iBAArBA,sBAAA,CAAuB2L,SAAS,UAAA5L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,EACH,CAAC,EACH,CAAC,cACNZ,IAAA,QAAK2L,SAAS,CAAC,sCAAsC,CAAAD,QAAA,cACnDxL,KAAA,WACEuM,OAAO,CAAEA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CACbxI,oBAAoB,EAAAuI,qBAAA,EAAAC,sBAAA,CAAC3F,QAAQ,CAAC4F,gBAAgB,UAAAD,sBAAA,iBAAzBA,sBAAA,CAA2BpJ,EAAE,UAAAmJ,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzDrI,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACF4H,SAAS,CAAC,qIAAqI,CAAAD,QAAA,eAE/I1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBc,WAAW,CAAC,KAAK,CACjBb,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExB1L,IAAA,SACEiM,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2XAA2X,CAC9X,CAAC,CACC,CAAC,cACNnM,IAAA,SAAM2L,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAC,oBAAkB,CAAM,CAAC,EACzD,CAAC,CACN,CAAC,cAENxL,KAAA,QAAKyL,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DxL,KAAA,QAAKyL,SAAS,CAAC,iFAAiF,CAAAD,QAAA,EAAC,WACtF,EAAA5K,sBAAA,CAACkG,QAAQ,CAACqF,gBAAgB,UAAAvL,sBAAA,UAAAA,sBAAA,CAAI,KAAK,EACzC,CAAC,cACNZ,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,8fAA8f,CACjgB,CAAC,CACC,CAAC,CACH,CAAC,cACNnM,IAAA,QAAK2L,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CACrD1E,QAAQ,CAAC8F,MAAM,cACd9M,IAAA,SAAM2L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,MAEhE,CAAM,CAAC,cAEP1L,IAAA,SAAM2L,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,QAEhE,CAAM,CACP,CACE,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACNzL,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,oIAAoI,CACvI,CAAC,CACC,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD1L,IAAA,SAAM2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAC,GAAG,EAAA3K,qBAAA,EAAAC,mBAAA,CAC9CgG,QAAQ,CAAC+F,SAAS,UAAA/L,mBAAA,iBAAlBA,mBAAA,CAAoBgM,cAAc,UAAAjM,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACzC,CAAC,cACNf,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACNzL,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD1L,IAAA,SAAM2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,CAAC,GAAG,EAAAzK,qBAAA,EAAAC,iBAAA,CACpD8F,QAAQ,CAACiG,OAAO,UAAA/L,iBAAA,iBAAhBA,iBAAA,CAAkBsL,SAAS,UAAAvL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAClC,CAAC,EACH,CAAC,cAENf,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACExL,KAAA,QACE2L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,eAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACFnM,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C1L,IAAA,SAAM2L,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,UAE1D,CAAM,CAAC,CAAC,GAAG,CACVf,cAAc,EAAAxJ,qBAAA,EAAAC,kBAAA,CAAC4F,QAAQ,CAACiG,OAAO,UAAA7L,kBAAA,iBAAhBA,kBAAA,CAAkB8L,eAAe,UAAA/L,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAAE,GAAG,cAC7DnB,IAAA,SAAM2L,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACxClB,UAAU,EAAAnJ,kBAAA,CAAC2F,QAAQ,CAACiG,OAAO,UAAA5L,kBAAA,iBAAhBA,kBAAA,CAAkB6L,eAAe,CAAC,CAC1C,CAAC,EACJ,CAAC,cACNlN,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,EACH,CAAC,cAEN3L,IAAA,QAAK2L,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC7DxL,KAAA,QAAKyL,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D1L,IAAA,QAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD1L,IAAA,SAAM2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAC,GAAG,EAAApK,qBAAA,CACjD0F,QAAQ,CAACmG,WAAW,UAAA7L,qBAAA,iBAApBA,qBAAA,CAAsBwE,GAAG,CAAC,CAACsH,IAAI,CAAEC,KAAK,gBACrCnN,KAAA,CAAAE,SAAA,EAAAsL,QAAA,eACE1L,IAAA,SACE2L,SAAS,CAAEjB,eAAe,CAAC0C,IAAI,CAACE,mBAAmB,CAAE,CAAA5B,QAAA,CAEpDlB,UAAU,CAAC4C,IAAI,CAACE,mBAAmB,CAAC,CACjC,CAAC,CACN,IAAI,EACL,CACH,CAAC,EACC,CAAC,cACNtN,IAAA,QAAK2L,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,CACH,CAAC,cACN3L,IAAA,QAAK2L,SAAS,CAAC,qCAAqC,CAAAD,QAAA,cAClDxL,KAAA,WACEuM,OAAO,CAAEA,CAAA,GAAM,KAAAc,sBAAA,CAAAC,sBAAA,CACbrJ,oBAAoB,EAAAoJ,sBAAA,EAAAC,sBAAA,CAACxG,QAAQ,CAAC4F,gBAAgB,UAAAY,sBAAA,iBAAzBA,sBAAA,CAA2BjK,EAAE,UAAAgK,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CACzDlJ,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACF4H,SAAS,CAAC,uEAAuE,CAAAD,QAAA,eAEjF1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,aAAa,CAAAZ,QAAA,cAEnB1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,cACNnM,IAAA,QAAK2L,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,wBAAsB,CAAK,CAAC,EACpD,CAAC,CACN,CAAC,EACH,CAAC,cAENxL,KAAA,QAAKyL,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD1L,IAAA,QAAK2L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCxL,KAAA,MACEyL,SAAS,CAAC,mGAAmG,CAC7GC,IAAI,CACF,cAAc,CACd5E,QAAQ,CAACzD,EAAE,CACX,WAAW,CACXiI,eAAe,CAAClH,UAAU,CAC3B,CAAAoH,QAAA,eAED1L,IAAA,SAAA0L,QAAA,cACE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACF,CAAC,cACPnM,IAAA,SAAM2L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACtC,CAAC,CAiDD,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,iGAAiG,CAAAD,QAAA,CAC7G,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CACzB,SAAS,CACV,CAAC5F,GAAG,CAAC,CAAC2H,MAAM,CAAEJ,KAAK,gBAClBrN,IAAA,WACEyM,OAAO,CAAEA,CAAA,GAAM9C,eAAe,CAAC8D,MAAM,CAAE,CACvC9B,SAAS,CAAG,mCACVrH,UAAU,GAAKmJ,MAAM,CACjB,mDAAmD,CACnD,4BACL,EAAE,CAAA/B,QAAA,CAEF+B,MAAM,CACD,CACT,CAAC,CACC,CAAC,CAELnJ,UAAU,GAAK,qBAAqB,cACnCpE,KAAA,QAAKyL,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFxL,KAAA,QAAKyL,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAnK,sBAAA,EAAAC,kBAAA,CACzBwF,QAAQ,CAACiG,OAAO,UAAAzL,kBAAA,iBAAhBA,kBAAA,CAAkBgL,SAAS,UAAAjL,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNrB,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjK,qBAAA,EAAAC,kBAAA,CACzBsF,QAAQ,CAACiG,OAAO,UAAAvL,kBAAA,iBAAhBA,kBAAA,CAAkBgM,SAAS,UAAAjM,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNvB,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/J,sBAAA,EAAAC,kBAAA,CACzBoF,QAAQ,CAACiG,OAAO,UAAArL,kBAAA,iBAAhBA,kBAAA,CAAkB+L,aAAa,UAAAhM,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNzB,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA7J,sBAAA,EAAAC,kBAAA,CACzBkF,QAAQ,CAACiG,OAAO,UAAAnL,kBAAA,iBAAhBA,kBAAA,CAAkB8L,aAAa,UAAA/L,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cAON3B,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3J,sBAAA,EAAAC,kBAAA,CACzBgF,QAAQ,CAACiG,OAAO,UAAAjL,kBAAA,iBAAhBA,kBAAA,CAAkBkL,eAAe,UAAAnL,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACxC,CAAC,EACH,CAAC,cACN7B,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzJ,sBAAA,EAAAC,kBAAA,CACzB8E,QAAQ,CAACiG,OAAO,UAAA/K,kBAAA,iBAAhBA,kBAAA,CAAkB2L,YAAY,UAAA5L,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACrC,CAAC,EACH,CAAC,EACH,CAAC,cACN/B,KAAA,QAAKyL,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,cAExD,CAAK,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3CxL,KAAA,QAAKyL,SAAS,CAAC,aAAa,CAAAD,QAAA,GAAAvJ,mBAAA,CACzB6E,QAAQ,CAAC8G,SAAS,UAAA3L,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAE1B6E,QAAQ,CAAC8G,SAAS,GAAG,SAAS,EAAI9G,QAAQ,CAAC+G,cAAc,eAAI7N,KAAA,CAAAE,SAAA,EAAAsL,QAAA,EAAE,KAAG,EAAAtJ,qBAAA,CAAC4E,QAAQ,CAAC+G,cAAc,UAAA3L,qBAAA,UAAAA,qBAAA,CAAE,EAAE,EAAG,CAAC,EAEjG,CAAC,EACH,CAAC,cACNlC,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBsC,UAAU,CAAChH,QAAQ,CAACiH,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAC1C,GAAG,CACHhD,eAAe,EAAA7I,qBAAA,CAAC2E,QAAQ,CAACmH,cAAc,UAAA9L,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C,CAAC,EACH,CAAC,cACNnC,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,0BAE/B,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBsC,UAAU,CAAChH,QAAQ,CAACoH,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CACxC,GAAG,CACHhD,eAAe,CAAC,KAAK,CAAC,CACrB,CAAC,EACH,CAAC,cAENhL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,cACxD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAChD,QAAQ,CAACqH,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cACNnO,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC1D1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAApJ,sBAAA,EAAAC,sBAAA,CACzByE,QAAQ,CAAC4F,gBAAgB,UAAArK,sBAAA,iBAAzBA,sBAAA,CAA2BiK,SAAS,UAAAlK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAC3C,CAAC,EACH,CAAC,cACNpC,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlJ,qBAAA,CACzBwE,QAAQ,CAACsH,gBAAgB,UAAA9L,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEP8B,UAAU,GAAK,sBAAsB,cACpCpE,KAAA,QAAAwL,QAAA,eACE1L,IAAA,QAAK2L,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvFxL,KAAA,QAAKyL,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjJ,qBAAA,CACzBuE,QAAQ,CAACsG,mBAAmB,UAAA7K,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNvC,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAChD,QAAQ,CAACuH,UAAU,CAAC,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,CAEH,CAAC,cAENvO,IAAA,QAAK2L,SAAS,CAAC,mDAAmD,CAAAD,QAAA,cAChExL,KAAA,QAAKyL,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrBxL,KAAA,QAAKyL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC1L,IAAA,QAAK2L,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAC/C1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,slBAAslB,CAAE,CAAC,CAC3oB,CAAC,CACH,CAAC,cACNnM,IAAA,OAAI2L,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,0BAAwB,CAAI,CAAC,EAC/E,CAAC,CAEL,EAAAhJ,qBAAA,CAAAsE,QAAQ,CAACwH,mBAAmB,UAAA9L,qBAAA,iBAA5BA,qBAAA,CAA8B+L,MAAM,EAAG,CAAC,cACvCzO,IAAA,QAAK2L,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvB1E,QAAQ,CAACwH,mBAAmB,CAAC1I,GAAG,CAAC,CAAC4I,cAAc,CAAErB,KAAK,QAAAsB,qBAAA,oBACtDzO,KAAA,QAAiByL,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAExE1L,IAAA,QAAK2L,SAAS,CAAC,oDAAoD,CAAAD,QAAA,cACjExL,KAAA,QAAKyL,SAAS,CAAC,mCAAmC,CAAAD,QAAA,eAChDxL,KAAA,QAAKyL,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC1L,IAAA,QAAK2L,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/D1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mOAAmO,CAAE,CAAC,CACxR,CAAC,CACH,CAAC,cACNjM,KAAA,OAAIyL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAC,eAAa,CAAC2B,KAAK,CAAG,CAAC,EAAK,CAAC,EACrE,CAAC,cACNrN,IAAA,QAAK2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzCgD,cAAc,CAACE,gBAAgB,eAC9B5O,IAAA,QAAK2L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1B,UAAU,CAAC0E,cAAc,CAACE,gBAAgB,CAAC,CACzC,CACN,CACE,CAAC,EACH,CAAC,CACH,CAAC,cAGN1O,KAAA,QAAKyL,SAAS,CAAC,WAAW,CAAAD,QAAA,eAExB1L,IAAA,QAAK2L,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBxL,KAAA,QAAKyL,SAAS,CAAC,uCAAuC,CAAAD,QAAA,EACnDgD,cAAc,CAACG,UAAU,eACxB3O,KAAA,QAAKyL,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1L,IAAA,QAAK2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mGAAmG,CAAE,CAAC,CACxJ,CAAC,CACH,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,wBAAsB,CAAM,CAAC,cAC3E1L,IAAA,SAAM2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAE1B,UAAU,CAAC0E,cAAc,CAACG,UAAU,CAAC,CAAO,CAAC,EAC/F,CAAC,EACH,CACN,CAEAH,cAAc,CAACI,QAAQ,eACtB5O,KAAA,QAAKyL,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1L,IAAA,QAAK2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvD1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mGAAmG,CAAE,CAAC,CACxJ,CAAC,CACH,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,sBAAoB,CAAM,CAAC,cACzE1L,IAAA,SAAM2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAE1B,UAAU,CAAC0E,cAAc,CAACI,QAAQ,CAAC,CAAO,CAAC,EAC7F,CAAC,EACH,CACN,CAEAJ,cAAc,CAACK,gBAAgB,eAC9B7O,KAAA,QAAKyL,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D1L,IAAA,QAAK2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDxL,KAAA,QAAK2L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,uCAAuC,CAAE,CAAC,cAC/FnM,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,gFAAgF,CAAE,CAAC,EACrI,CAAC,CACH,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,kBAAgB,CAAM,CAAC,cACrE1L,IAAA,SAAM2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAEgD,cAAc,CAACK,gBAAgB,CAAO,CAAC,EACzF,CAAC,EACH,CACN,EACE,CAAC,CACH,CAAC,CAGL,EAAAJ,qBAAA,CAAAD,cAAc,CAACM,iBAAiB,UAAAL,qBAAA,iBAAhCA,qBAAA,CAAkCF,MAAM,EAAG,CAAC,eAC3CvO,KAAA,QAAAwL,QAAA,eACExL,KAAA,QAAKyL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC1L,IAAA,QAAK2L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,CACH,CAAC,cACNnM,IAAA,OAAI2L,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,WAAS,CAAI,CAAC,EAC9D,CAAC,cAEN1L,IAAA,QAAK2L,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBgD,cAAc,CAACM,iBAAiB,CAAClJ,GAAG,CAAC,CAACmJ,eAAe,CAAEC,GAAG,QAAAC,qBAAA,CAAAC,sBAAA,oBACzDlP,KAAA,QAAeyL,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACjGxL,KAAA,QAAKyL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC1L,IAAA,QAAK2L,SAAS,CAAC,+EAA+E,CAAAD,QAAA,cAC5F1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAE,EAAAyD,qBAAA,CAAAF,eAAe,CAACI,QAAQ,UAAAF,qBAAA,iBAAxBA,qBAAA,CAA0B3C,SAAS,GAAI,KAAK,CAAO,CAAC,cACzGxM,IAAA,SAAM2L,SAAS,CAAC,mEAAmE,CAAAD,QAAA,CAAEuD,eAAe,CAACK,YAAY,EAAI,KAAK,CAAO,CAAC,EAC/H,CAAC,EACH,CAAC,cAENpP,KAAA,QAAKyL,SAAS,CAAC,6CAA6C,CAAAD,QAAA,EACzDuD,eAAe,CAACM,kBAAkB,eACjCrP,KAAA,QAAKyL,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACpJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,kdAAkd,CAAE,CAAC,CACvgB,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,cAC/D1L,IAAA,SAAM2L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAEuD,eAAe,CAACM,kBAAkB,CAAO,CAAC,EACjF,CAAC,EACH,CACN,CAEAN,eAAe,CAACO,aAAa,eAC5BtP,KAAA,QAAKyL,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACpJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mOAAmO,CAAE,CAAC,CACxR,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,cAC/D1L,IAAA,SAAM2L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAE1B,UAAU,CAACiF,eAAe,CAACO,aAAa,CAAC,CAAO,CAAC,EACxF,CAAC,EACH,CACN,CAEA,EAAAJ,sBAAA,CAAAH,eAAe,CAACI,QAAQ,UAAAD,sBAAA,iBAAxBA,sBAAA,CAA0BK,KAAK,gBAC9BvP,KAAA,QAAKyL,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACpJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,mWAAmW,CAAE,CAAC,CACxZ,CAAC,cACNjM,KAAA,QAAAwL,QAAA,eACE1L,IAAA,SAAM2L,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,cAC5D1L,IAAA,SAAM2L,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAEuD,eAAe,CAACI,QAAQ,CAACI,KAAK,CAAO,CAAC,EAC7E,CAAC,EACH,CACN,EACE,CAAC,GAjDEP,GAkDL,CAAC,EACP,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,GA7IE7B,KA8IL,CAAC,EACP,CAAC,CACC,CAAC,cAENnN,KAAA,QAAKyL,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D1L,IAAA,QAAK2L,SAAS,CAAC,mFAAmF,CAAAD,QAAA,cAChG1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAChJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,slBAAslB,CAAE,CAAC,CAC3oB,CAAC,CACH,CAAC,cACNnM,IAAA,OAAI2L,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAAC,6BAA2B,CAAI,CAAC,cAChF1L,IAAA,MAAG2L,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CAAC,0DAAwD,CAAG,CAAC,EAC9F,CACN,EACE,CAAC,CACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPpH,UAAU,GAAK,iBAAiB,cAC/BtE,IAAA,QAAK2L,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvFxL,KAAA,QAAKyL,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA/I,qBAAA,CAC5BqE,QAAQ,CAAC0I,eAAe,UAAA/M,qBAAA,iBAAxBA,qBAAA,CAA0BmD,GAAG,CAAC,CAAC6J,IAAI,CAAEtC,KAAK,gBACzCrN,IAAA,MACE4L,IAAI,CAAErM,WAAW,CAAGoQ,IAAI,CAAC5J,IAAK,CAC9B6J,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBlE,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CxL,KAAA,QAAKyL,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF1L,IAAA,QAAK2L,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5ExL,KAAA,QACE2L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB1L,IAAA,SAAMmM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnM,IAAA,SAAMmM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE1L,IAAA,QAAK2L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FiE,IAAI,CAACG,SAAS,CACZ,CAAC,cACN5P,KAAA,QAAAwL,QAAA,EAAMiE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPzL,UAAU,GAAK,UAAU,cACxBpE,KAAA,QAAKyL,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DxL,KAAA,QAAKyL,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCxL,KAAA,QAAKyL,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9I,qBAAA,CACzBoE,QAAQ,CAACgJ,cAAc,UAAApN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cACN1C,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAChD,QAAQ,CAACiJ,WAAW,CAAC,CAC9B,CAAC,EACH,CAAC,cACN/P,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cAC5CxL,KAAA,QAAKyL,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,IACzB,CAACsC,UAAU,CAAChH,QAAQ,CAACkJ,cAAc,CAAC,CAAChC,OAAO,CAAC,CAAC,CAAC,EAC9C,CAAC,EACH,CAAC,EACH,CAAC,cACNhO,KAAA,QAAKyL,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9C1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,EACH,CAAC,EACH,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA7I,qBAAA,CAC5BmE,QAAQ,CAACmJ,eAAe,UAAAtN,qBAAA,iBAAxBA,qBAAA,CAA0BiD,GAAG,CAAC,CAAC6J,IAAI,CAAEtC,KAAK,gBACzCrN,IAAA,MACE4L,IAAI,CAAErM,WAAW,CAAGoQ,IAAI,CAAC5J,IAAK,CAC9B6J,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBlE,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CxL,KAAA,QAAKyL,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF1L,IAAA,QAAK2L,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5ExL,KAAA,QACE2L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB1L,IAAA,SAAMmM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnM,IAAA,SAAMmM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE1L,IAAA,QAAK2L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FiE,IAAI,CAACG,SAAS,CACZ,CAAC,cACN5P,KAAA,QAAAwL,QAAA,EAAMiE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPzL,UAAU,GAAK,yBAAyB,cACvCpE,KAAA,QAAKyL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9D1L,IAAA,QAAK2L,SAAS,CAAC,2BAA2B,CAAAD,QAAA,cACxCxL,KAAA,QAAKyL,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,mBAExD,CAAK,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAE/B,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5I,qBAAA,CACzBkE,QAAQ,CAACoJ,gBAAgB,UAAAtN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACN5C,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yBAE/B,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3I,sBAAA,EAAAC,oBAAA,CACzBgE,QAAQ,CAAC+F,SAAS,UAAA/J,oBAAA,iBAAlBA,oBAAA,CAAoBgK,cAAc,UAAAjK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,cACN7C,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzI,sBAAA,CACzB+D,QAAQ,CAACqF,gBAAgB,UAAApJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACN/C,KAAA,QAAKyL,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE1L,IAAA,QAAK2L,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD1L,IAAA,QAAK2L,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAxI,qBAAA,CACzB8D,QAAQ,CAACqJ,aAAa,UAAAnN,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cACNhD,KAAA,QAAKyL,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAvI,qBAAA,CAC5B6D,QAAQ,CAACsJ,oBAAoB,UAAAnN,qBAAA,iBAA7BA,qBAAA,CAA+B2C,GAAG,CAAC,CAAC6J,IAAI,CAAEtC,KAAK,gBAC9CrN,IAAA,MACE4L,IAAI,CAAErM,WAAW,CAAGoQ,IAAI,CAAC5J,IAAK,CAC9B6J,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBlE,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CxL,KAAA,QAAKyL,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF1L,IAAA,QAAK2L,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5ExL,KAAA,QACE2L,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB1L,IAAA,SAAMmM,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOnM,IAAA,SAAMmM,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNjM,KAAA,QAAKyL,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE1L,IAAA,QAAK2L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FiE,IAAI,CAACG,SAAS,CACZ,CAAC,cACN5P,KAAA,QAAAwL,QAAA,EAAMiE,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAGPzL,UAAU,GAAK,SAAS,cACvBtE,IAAA,CAACV,WAAW,EACViR,WAAW,CAAE,CACXpH,OAAO,CAAEA,OAAO,CAChBzF,IAAI,CAAE0F,kBAAkB,CACxB9B,KAAK,CAAE+B,iBAAiB,CACxBmH,KAAK,CAAE,CAAArH,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEsF,MAAM,GAAI,CAC5B,CAAE,CACF/H,OAAO,CAAEuC,cAAe,CACxBtC,KAAK,CAAEuC,YAAa,CACrB,CAAC,CACA,IAAI,EAGL,CAAC,cAENlJ,IAAA,QAAK2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDxL,KAAA,QAAKyL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCxL,KAAA,QAAKyL,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC1L,IAAA,QAAK2L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BxL,KAAA,QAAKyL,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B1L,IAAA,UAAO2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE5D,CAAO,CAAC,cACR1L,IAAA,aACEyQ,KAAK,CAAEjM,YAAa,CACpBkM,QAAQ,CAAGC,CAAC,EAAKlM,eAAe,CAACkM,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CACjD9E,SAAS,CAAG,KACVjH,iBAAiB,CACb,eAAe,CACf,kBACL,6EAA6E,CACrE,CAAC,cACZ1E,IAAA,QAAK2L,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrChH,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,CACH,CAAC,cACN1E,IAAA,QAAK2L,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BxL,KAAA,QAAKyL,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1L,IAAA,UAAO2L,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,QAE5D,CAAO,CAAC,cACRxL,KAAA,WACMqF,eAAe,CAAC,CAClBoG,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,uFAAuF,CAAAD,QAAA,eAEjG1L,IAAA,aAAWyF,gBAAgB,CAAC,CAAC,CAAG,CAAC,cACjCzF,IAAA,QAAK2L,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNnM,IAAA,QAAK2L,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,8BAE9B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN1L,IAAA,UAAO4Q,KAAK,CAAEvQ,eAAgB,CAAAqL,QAAA,cAC5B1L,IAAA,QAAK2L,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCtG,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEsH,KAAK,gBAC9BnN,KAAA,QACEyL,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF1L,IAAA,QAAK2L,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD1L,IAAA,QACE6Q,GAAG,CAAE9K,IAAI,CAACG,OAAQ,CAClByF,SAAS,CAAC,QAAQ,CAClBmF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,cACN3Q,KAAA,QAAKyL,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD1L,IAAA,QAAK2L,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F3F,IAAI,CAACkL,IAAI,CACP,CAAC,cACN/Q,KAAA,QAAAwL,QAAA,EAAM,CAAC3F,IAAI,CAACmL,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEhD,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAK,CAAC,EACnD,CAAC,cACNlO,IAAA,WACEyM,OAAO,CAAEA,CAAA,GAAM,CACbpH,gBAAgB,CAAEQ,SAAS,EACzBA,SAAS,CAACsL,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GAAKhE,KAAK,GAAKgE,aAClC,CACF,CAAC,CACH,CAAE,CACF1F,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA1CJpG,IAAI,CAACkL,IA2CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,cACRjR,IAAA,QAAA0L,QAAA,cACE1L,IAAA,WACEsR,QAAQ,CAAEzJ,qBAAsB,CAChC4E,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAA8E,KAAK,CAAG,IAAI,CAChB5M,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIH,YAAY,GAAK,EAAE,EAAIY,aAAa,CAACqJ,MAAM,GAAK,CAAC,CAAE,CACrD9J,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C4M,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAjO,QAAQ,CACZ1E,iBAAiB,CACf,CACE4S,OAAO,CAAEhN,YAAY,CACrB;AACAiN,YAAY,CAAErM,aAChB,CAAC,CACD7B,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACL5D,KAAK,CAACgH,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFgF,SAAS,CAAC,yDAAyD,CAAAD,QAAA,CAElE7D,qBAAqB,CAAG,YAAY,CAAG,MAAM,CACxC,CAAC,CACN,CAAC,cACN7H,IAAA,QAAK2L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAClBtE,kBAAkB,cACjBpH,IAAA,CAACZ,MAAM,GAAE,CAAC,CACRiI,gBAAgB,cAClBrH,IAAA,CAACX,KAAK,EAACoK,IAAI,CAAE,OAAQ,CAAC2C,OAAO,CAAE/E,gBAAiB,CAAE,CAAC,CACjDF,QAAQ,cACVnH,IAAA,CAAAI,SAAA,EAAAsL,QAAA,CACGvE,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC4L,OAAO,CAAErE,KAAK,QAAAsE,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,cAAA,oBAC5BlS,KAAA,QAAKyL,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC1L,IAAA,QAAA0L,QAAA,CACGgG,OAAO,CAACW,WAAW,CAClB,CAAAV,oBAAA,CAAAD,OAAO,CAACW,WAAW,UAAAV,oBAAA,WAAnBA,oBAAA,CAAqBW,KAAK,cACxBtS,IAAA,QACE2L,SAAS,CAAC,uBAAuB,CACjCkF,GAAG,CAAEtR,WAAW,GAAAqS,qBAAA,CAAGF,OAAO,CAACW,WAAW,UAAAT,qBAAA,iBAAnBA,qBAAA,CAAqBU,KAAK,CAAC,CAC9CxB,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEF7Q,IAAA,QAAK2L,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/GxL,KAAA,QAAKyL,SAAS,CAAC,YAAY,CAAAD,QAAA,EACxB,CAAAmG,qBAAA,CAAAH,OAAO,CAACW,WAAW,UAAAR,qBAAA,WAAnBA,qBAAA,CAAqBU,UAAU,EAAAT,qBAAA,CAC5BJ,OAAO,CAACW,WAAW,UAAAP,qBAAA,iBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,CAClC,EAAE,CACL,CAAAR,qBAAA,CAAAL,OAAO,CAACW,WAAW,UAAAN,qBAAA,WAAnBA,qBAAA,CAAqBS,SAAS,EAAAR,qBAAA,CAC3BN,OAAO,CAACW,WAAW,UAAAL,qBAAA,iBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,CACjC,EAAE,EACH,CAAC,CACH,CACN,CACC,IAAI,CACL,CAAC,cACNtS,KAAA,QAAKyL,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BxL,KAAA,QAAKyL,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,6iBAA6iB,CAChjB,CAAC,CACC,CAAC,cAENjM,KAAA,QAAKyL,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7D1L,IAAA,MAAA0L,QAAA,CAAI1B,UAAU,CAAC0H,OAAO,CAACe,UAAU,CAAC,CAAI,CAAC,CACtCf,OAAO,CAACgB,UAAU,cACjBxS,KAAA,WACEuM,OAAO,CAAEA,CAAA,GAAM,CACbxH,gBAAgB,CAACyM,OAAO,CAACnO,EAAE,CAAC,CAC5B4B,YAAY,CAAC,QAAQ,CAAC,CACtBJ,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACF4G,SAAS,CAAC,oFAAoF,CAAAD,QAAA,eAE9F1L,IAAA,QACE6L,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd1L,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBmM,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,cACNnM,IAAA,MAAG2L,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAG,CAAC,EACzB,CAAC,CACP,IAAI,EACL,CAAC,EACH,CAAC,cACN1L,IAAA,QAAK2L,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAuG,qBAAA,EAAAC,qBAAA,CACxCR,OAAO,CAACW,WAAW,UAAAH,qBAAA,iBAAnBA,qBAAA,CAAqB1F,SAAS,UAAAyF,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAClC,CAAC,cACNjS,IAAA,QAAK2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,EAAAyG,gBAAA,CAC/CT,OAAO,CAACF,OAAO,UAAAW,gBAAA,UAAAA,gBAAA,CAAI,EAAE,CACnB,CAAC,cACNnS,IAAA,QAAK2L,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC/CgG,OAAO,SAAPA,OAAO,kBAAAU,cAAA,CAAPV,OAAO,CAAEiB,KAAK,UAAAP,cAAA,iBAAdA,cAAA,CAAgBtM,GAAG,CAAC,CAACC,IAAI,CAAEsH,KAAK,gBAC/BrN,IAAA,MACE4P,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBjE,IAAI,CAAErM,WAAW,CAAGwG,IAAI,CAACA,IAAK,CAAA2F,QAAA,cAE9B1L,IAAA,QACE6Q,GAAG,CAAEtR,WAAW,CAAGwG,IAAI,CAACA,IAAK,CAC7B4F,SAAS,CAAC,8BAA8B,CACxCmF,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACD,CACJ,CAAC,CACC,CAAC,cACN7Q,IAAA,OAAI2L,SAAS,CAAC,sEAAsE,CAAE,CAAC,EACpF,CAAC,EACH,CAAC,EACP,CAAC,CACF,CAAC,CACD,IAAI,CACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,cAEN3L,IAAA,CAACF,iBAAiB,EAChB8S,MAAM,CAAE9N,eAAgB,CACxBsH,OAAO,CACLlH,SAAS,GAAK,QAAQ,CAClB,+CAA+C,CAC/C,gBACL,CACD8F,KAAK,CAAE9F,SAAS,GAAK,QAAQ,CAAG,gBAAgB,CAAG,cAAe,CAClE+F,IAAI,CAAC,QAAQ,CACb4H,WAAW,CAAC,QAAQ,CACpBC,UAAU,CAAC,QAAQ,CACnBC,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI7N,SAAS,GAAK,QAAQ,EAAIF,aAAa,GAAK,EAAE,CAAE,CAClD1B,QAAQ,CAACzE,iBAAiB,CAACmG,aAAa,CAAC,CAAC,CAC1CD,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAClB,CAAC,IAAM,CACLJ,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAChBF,gBAAgB,CAAC,EAAE,CAAC,CACtB,CACF,CAAE,CACF+N,QAAQ,CAAEA,CAAA,GAAM,CACdjO,kBAAkB,CAAC,KAAK,CAAC,CACzBI,YAAY,CAAC,EAAE,CAAC,CAChBF,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAE,CACFgO,SAAS,CAAEzL,wBAAyB,CACrC,CAAC,cAEFxH,IAAA,CAACF,iBAAiB,EAChB8S,MAAM,CAAE5O,QAAS,CACjBgH,KAAK,cACH9K,KAAA,QAAKyL,SAAS,CAAC,kCAAkC,CAAAD,QAAA,eAC/C1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,cACNnM,IAAA,SAAA0L,QAAA,CAAM,yBAAuB,CAAM,CAAC,EACjC,CACN,CACDU,OAAO,cACLlM,KAAA,QAAKyL,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BxL,KAAA,QAAKyL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cACrJ1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,2XAA2X,CAAE,CAAC,CAChb,CAAC,cACNjM,KAAA,UAAOyL,SAAS,CAAC,oCAAoC,CAAAD,QAAA,EAAC,uBAC/B,cAAA1L,IAAA,SAAM2L,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,GAAC,CAAM,CAAC,EACvD,CAAC,EACL,CAAC,cACNxL,KAAA,QAAKyL,SAAS,CAAC,UAAU,CAAAD,QAAA,eACvB1L,IAAA,QAAK2L,SAAS,CAAC,sEAAsE,CAAAD,QAAA,cACnF1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,uBAAuB,CAAAD,QAAA,cAC/I1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,yJAAyJ,CAAE,CAAC,CAC9M,CAAC,CACH,CAAC,cACNjM,KAAA,WACEyL,SAAS,CAAG,mBACVvH,sBAAsB,CAClB,wDAAwD,CACxD,6DACL,iJAAiJ,CAClJqM,KAAK,CAAEvM,iBAAkB,CACzBwM,QAAQ,CAAGC,CAAC,EAAKxM,oBAAoB,CAACwM,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE,CAAA/E,QAAA,eAEtD1L,IAAA,WAAQyQ,KAAK,CAAE,EAAG,CAAA/E,QAAA,CAAC,yBAAuB,CAAQ,CAAC,CAClDxD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEpC,GAAG,CAAE6J,IAAI,eACtB3P,IAAA,WAAsByQ,KAAK,CAAEd,IAAI,CAACpM,EAAG,CAAAmI,QAAA,CAAEiE,IAAI,CAACnD,SAAS,EAAxCmD,IAAI,CAACpM,EAA4C,CAC/D,CAAC,EACI,CAAC,cACTvD,IAAA,QAAK2L,SAAS,CAAC,uEAAuE,CAAAD,QAAA,cACpF1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,uBAAuB,CAAAD,QAAA,cAC/I1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,4BAA4B,CAAE,CAAC,CACjF,CAAC,CACH,CAAC,EACH,CAAC,CACL/H,sBAAsB,eACrBlE,KAAA,QAAKyL,SAAS,CAAC,qCAAqC,CAAAD,QAAA,eAClD1L,IAAA,QAAK6L,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACc,WAAW,CAAC,KAAK,CAACb,MAAM,CAAC,cAAc,CAACL,SAAS,CAAC,cAAc,CAAAD,QAAA,cACtI1L,IAAA,SAAMiM,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,CAAC,CAAC,6EAA6E,CAAE,CAAC,CAClI,CAAC,cACNnM,IAAA,SAAM2L,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAEtH,sBAAsB,CAAO,CAAC,EACtD,CACN,EACE,CACN,CACD6G,IAAI,CAAC,MAAM,CACX4H,WAAW,CAAC,oBAAoB,CAChCC,UAAU,CAAC,QAAQ,CACnBI,kBAAkB,CAAC,2EAA2E,CAC9FC,iBAAiB,CAAC,6EAA6E,CAC/FJ,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB1O,yBAAyB,CAAC,EAAE,CAAC,CAE7B,GAAIH,iBAAiB,GAAK,EAAE,CAAE,CAC5BG,yBAAyB,CAAC,yBAAyB,CAAC,CACtD,CAAC,IAAM,CACLN,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAT,QAAQ,CACZpE,kBAAkB,CAACqE,EAAE,CAAE,CAAE8O,WAAW,CAAEnO,iBAAkB,CAAC,CAC3D,CAAC,CACDH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFiP,QAAQ,CAAEA,CAAA,GAAM,CACd7O,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFkP,SAAS,CAAEnP,SAAU,CACrBsP,WAAW,CAAC,0BAA0B,CACtCC,WAAW,cACTnT,KAAA,QAAKyL,SAAS,CAAC,iCAAiC,CAACE,KAAK,CAAC,4BAA4B,CAACC,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAAAL,QAAA,eACjH1L,IAAA,WAAQ2L,SAAS,CAAC,YAAY,CAAC2H,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,IAAI,CAACxH,MAAM,CAAC,cAAc,CAACa,WAAW,CAAC,GAAG,CAAS,CAAC,cACrG7M,IAAA,SAAM2L,SAAS,CAAC,YAAY,CAACG,IAAI,CAAC,cAAc,CAACK,CAAC,CAAC,iHAAiH,CAAO,CAAC,EACzK,CACN,CACF,CAAC,EACW,CAAC,CAEpB,CAEA,cAAe,CAAAzL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}