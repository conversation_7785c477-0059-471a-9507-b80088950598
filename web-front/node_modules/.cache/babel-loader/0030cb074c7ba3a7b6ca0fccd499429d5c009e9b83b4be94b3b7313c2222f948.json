{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewInsurance } from \"../../redux/actions/insuranceActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddInsuranceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const insuranceAdd = useSelector(state => state.addNewInsurance);\n  const {\n    loadingInsuranceAdd,\n    errorInsuranceAdd,\n    successInsuranceAdd\n  } = insuranceAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n    }\n  }, [successInsuranceAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/insurances-company\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Insurances Company\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Insurances\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Insurance Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 34\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Name\",\n                  value: insuranceName,\n                  onChange: v => setInsuranceName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceNameError ? insuranceNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 137,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceCountryError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Country\",\n                  value: insuranceCountry,\n                  onChange: v => setInsuranceCountry(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceCountryError ? insuranceCountryError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Email\",\n                  value: insuranceEmail,\n                  onChange: v => setInsuranceEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceEmailError ? insuranceEmailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Phone\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Insurance Phone\",\n                  value: insurancePhone,\n                  onChange: v => setInsurancePhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insurancePhoneError ? insurancePhoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Insurance Logo\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"file\",\n                  placeholder: \"Insurance Logo\",\n                  value: insuranceLogoValue,\n                  onChange: v => {\n                    setInsuranceLogo(v.target.files[0]);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: insuranceLogoError ? insuranceLogoError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/insurances-company\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setInsuranceNameError(\"\");\n                  setInsuranceCountryError(\"\");\n                  setInsuranceEmailError(\"\");\n                  setInsurancePhoneError(\"\");\n                  setInsuranceLogoError(\"\");\n                  if (insuranceName === \"\") {\n                    setInsuranceNameError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (check) {\n                    setLoadEvent(true);\n                    await dispatch(createNewInsurance({\n                      assurance_name: insuranceName,\n                      assurance_country: insuranceCountry,\n                      assurance_phone: insurancePhone,\n                      assurance_email: insuranceEmail,\n                      assurance_logo: insuranceLogo\n                    })).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n}\n_s(AddInsuranceScreen, \"+ipC5Tc2xtddI+J3WgbA4LIZBa0=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = AddInsuranceScreen;\nexport default AddInsuranceScreen;\nvar _c;\n$RefreshReg$(_c, \"AddInsuranceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewInsurance", "jsxDEV", "_jsxDEV", "AddInsuranceScreen", "_s", "navigate", "location", "dispatch", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoValue", "setInsuranceLogoValue", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "loading", "error", "insuranceAdd", "addNewInsurance", "loadingInsuranceAdd", "errorInsuranceAdd", "successInsuranceAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "files", "onClick", "check", "assurance_name", "assurance_country", "assurance_phone", "assurance_email", "assurance_logo", "then", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/AddInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewInsurance } from \"../../redux/actions/insuranceActions\";\n\nfunction AddInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const insuranceAdd = useSelector((state) => state.addNewInsurance);\n  const { loadingInsuranceAdd, errorInsuranceAdd, successInsuranceAdd } =\n    insuranceAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successInsuranceAdd) {\n      setInsuranceName(\"\");\n      setInsuranceNameError(\"\");\n      setInsuranceCountry(\"\");\n      setInsuranceCountryError(\"\");\n      setInsuranceEmail(\"\");\n      setInsuranceEmailError(\"\");\n      setInsurancePhone(\"\");\n      setInsurancePhoneError(\"\");\n      setInsuranceLogo(\"\");\n      setInsuranceLogoError(\"\");\n    }\n  }, [successInsuranceAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurances Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Insurances</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Insurances\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Name\"\n                    value={insuranceName}\n                    onChange={(v) => setInsuranceName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceNameError ? insuranceNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Country\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceCountryError\n                        ? \"border-danger\"\n                        : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Country\"\n                    value={insuranceCountry}\n                    onChange={(v) => setInsuranceCountry(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceCountryError ? insuranceCountryError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Email\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceEmailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Email\"\n                    value={insuranceEmail}\n                    onChange={(v) => setInsuranceEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceEmailError ? insuranceEmailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Phone\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insurancePhoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Insurance Phone\"\n                    value={insurancePhone}\n                    onChange={(v) => setInsurancePhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insurancePhoneError ? insurancePhoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Insurance Logo\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      insuranceLogoError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"file\"\n                    placeholder=\"Insurance Logo\"\n                    value={insuranceLogoValue}\n                    onChange={(v) => {\n                      setInsuranceLogo(v.target.files[0]);\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {insuranceLogoError ? insuranceLogoError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/insurances-company\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setInsuranceNameError(\"\");\n                    setInsuranceCountryError(\"\");\n                    setInsuranceEmailError(\"\");\n                    setInsurancePhoneError(\"\");\n                    setInsuranceLogoError(\"\");\n\n                    if (insuranceName === \"\") {\n                      setInsuranceNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewInsurance({\n                          assurance_name: insuranceName,\n                          assurance_country: insuranceCountry,\n                          assurance_phone: insurancePhone,\n                          assurance_email: insuranceEmail,\n                          assurance_logo: insuranceLogo,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingInsuranceAdd ? \"Loading ...\" : \"Create Insurance\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddInsuranceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,kBAAkB,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACuB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC6B,cAAc,EAAEC,iBAAiB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMuC,SAAS,GAAGrC,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,YAAY,GAAG1C,WAAW,CAAEsC,KAAK,IAAKA,KAAK,CAACK,eAAe,CAAC;EAClE,MAAM;IAAEC,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GACnEJ,YAAY;EAEd,MAAMK,QAAQ,GAAG,GAAG;EACpBlD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,QAAQ,EAAE;MACb7B,QAAQ,CAACqC,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACrC,QAAQ,EAAE6B,QAAQ,EAAE3B,QAAQ,CAAC,CAAC;EAElCf,SAAS,CAAC,MAAM;IACd,IAAIiD,mBAAmB,EAAE;MACvB9B,gBAAgB,CAAC,EAAE,CAAC;MACpBE,qBAAqB,CAAC,EAAE,CAAC;MACzBE,mBAAmB,CAAC,EAAE,CAAC;MACvBE,wBAAwB,CAAC,EAAE,CAAC;MAC5BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,gBAAgB,CAAC,EAAE,CAAC;MACpBI,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC,EAAE,CAACU,mBAAmB,CAAC,CAAC;EAEzB,oBACEvC,OAAA,CAACJ,aAAa;IAAA6C,QAAA,eACZzC,OAAA;MAAAyC,QAAA,gBACEzC,OAAA;QAAK0C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDzC,OAAA;UAAG2C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBzC,OAAA;YAAK0C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DzC,OAAA;cACE4C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBzC,OAAA;gBACEgD,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAM0C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJtD,OAAA;UAAG2C,IAAI,EAAC,qBAAqB;UAAAF,QAAA,eAC3BzC,OAAA;YAAK0C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DzC,OAAA;cAAAyC,QAAA,eACEzC,OAAA;gBACE4C,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnBzC,OAAA;kBACEgD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACPtD,OAAA;cAAK0C,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAkB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJtD,OAAA;UAAAyC,QAAA,eACEzC,OAAA;YACE4C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBzC,OAAA;cACEgD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPtD,OAAA;UAAK0C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAqB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAENtD,OAAA;QAAK0C,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7CzC,OAAA;UAAI0C,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENtD,OAAA;QAAK0C,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJzC,OAAA;UAAK0C,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjDzC,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CzC,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CzC,OAAA;gBAAK0C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,iBACzC,eAAAzC,OAAA;kBAAQ0C,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNtD,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBACE0C,SAAS,EAAG,wBACVhC,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC6C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAEjD,aAAc;kBACrBkD,QAAQ,EAAGC,CAAC,IAAKlD,gBAAgB,CAACkD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC,eACFtD,OAAA;kBAAK0C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC/B,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CzC,OAAA;gBAAK0C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtD,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBACE0C,SAAS,EAAG,wBACV5B,qBAAqB,GACjB,eAAe,GACf,kBACL,mCAAmC;kBACpCyC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,mBAAmB;kBAC/BC,KAAK,EAAE7C,gBAAiB;kBACxB8C,QAAQ,EAAGC,CAAC,IAAK9C,mBAAmB,CAAC8C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC,eACFtD,OAAA;kBAAK0C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC3B,qBAAqB,GAAGA,qBAAqB,GAAG;gBAAE;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1CzC,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CzC,OAAA;gBAAK0C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtD,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBACE0C,SAAS,EAAG,wBACVxB,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpCqC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,iBAAiB;kBAC7BC,KAAK,EAAEzC,cAAe;kBACtB0C,QAAQ,EAAGC,CAAC,IAAK1C,iBAAiB,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFtD,OAAA;kBAAK0C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCvB,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtD,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CzC,OAAA;gBAAK0C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtD,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBACE0C,SAAS,EAAG,wBACVpB,mBAAmB,GAAG,eAAe,GAAG,kBACzC,mCAAmC;kBACpCiC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,iBAAiB;kBAC7BC,KAAK,EAAErC,cAAe;kBACtBsC,QAAQ,EAAGC,CAAC,IAAKtC,iBAAiB,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACFtD,OAAA;kBAAK0C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCnB,mBAAmB,GAAGA,mBAAmB,GAAG;gBAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtD,OAAA;YAAK0C,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1CzC,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5CzC,OAAA;gBAAK0C,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNtD,OAAA;gBAAAyC,QAAA,gBACEzC,OAAA;kBACE0C,SAAS,EAAG,wBACVd,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBACpC2B,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,gBAAgB;kBAC5BC,KAAK,EAAE/B,kBAAmB;kBAC1BgC,QAAQ,EAAGC,CAAC,IAAK;oBACflC,gBAAgB,CAACkC,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;kBACrC;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFtD,OAAA;kBAAK0C,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCb,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtD,OAAA;YAAK0C,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpBzC,OAAA;cAAK0C,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1DzC,OAAA;gBACE2C,IAAI,EAAC,qBAAqB;gBAC1BD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJtD,OAAA;gBACE8D,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBpD,qBAAqB,CAAC,EAAE,CAAC;kBACzBI,wBAAwB,CAAC,EAAE,CAAC;kBAC5BI,sBAAsB,CAAC,EAAE,CAAC;kBAC1BI,sBAAsB,CAAC,EAAE,CAAC;kBAC1BM,qBAAqB,CAAC,EAAE,CAAC;kBAEzB,IAAIrB,aAAa,KAAK,EAAE,EAAE;oBACxBG,qBAAqB,CAAC,4BAA4B,CAAC;oBACnDoD,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBACTxD,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMF,QAAQ,CACZP,kBAAkB,CAAC;sBACjBkE,cAAc,EAAExD,aAAa;sBAC7ByD,iBAAiB,EAAErD,gBAAgB;sBACnCsD,eAAe,EAAE9C,cAAc;sBAC/B+C,eAAe,EAAEnD,cAAc;sBAC/BoD,cAAc,EAAE5C;oBAClB,CAAC,CACH,CAAC,CAAC6C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChB9D,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACLV,KAAK,CAACqC,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFQ,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjEJ,mBAAmB,GAAG,aAAa,GAAG;cAAkB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACpD,EAAA,CA5RQD,kBAAkB;EAAA,QACRN,WAAW,EACXD,WAAW,EACXF,WAAW,EAoBVC,WAAW,EAGRA,WAAW;AAAA;AAAA6E,EAAA,GA1BzBrE,kBAAkB;AA8R3B,eAAeA,kBAAkB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}