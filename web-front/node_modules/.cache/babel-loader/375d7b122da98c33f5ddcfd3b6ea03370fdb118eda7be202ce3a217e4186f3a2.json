{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/casos/BusquedaCasosScreen.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction BusquedaCasosScreen() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-row justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"UNIMEDCARE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        width: \"50\",\n        height: \"50\",\n        src: \"https://img.icons8.com/ios-filled/50/ms-excel.png\",\n        alt: \"ms-excel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"BUSQUEDA DE CASOS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = BusquedaCasosScreen;\nexport default BusquedaCasosScreen;\nvar _c;\n$RefreshReg$(_c, \"BusquedaCasosScreen\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "BusquedaCasosScreen", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/casos/BusquedaCasosScreen.js"], "sourcesContent": ["import React from \"react\";\n\nfunction BusquedaCasosScreen() {\n  return (\n    <div className=\"container mx-auto flex flex-col\">\n      <div className=\"flex flex-row justify-between\">\n        <div>UNIMEDCARE</div>\n        <img\n          width=\"50\"\n          height=\"50\"\n          src=\"https://img.icons8.com/ios-filled/50/ms-excel.png\"\n          alt=\"ms-excel\"\n        />\n      </div>\n      <div>BUSQUEDA DE CASOS</div>\n    </div>\n  );\n}\n\nexport default BusquedaCasosScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,oBACED,OAAA;IAAKE,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAC9CH,OAAA;MAAKE,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAC5CH,OAAA;QAAAG,QAAA,EAAK;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrBP,OAAA;QACEQ,KAAK,EAAC,IAAI;QACVC,MAAM,EAAC,IAAI;QACXC,GAAG,EAAC,mDAAmD;QACvDC,GAAG,EAAC;MAAU;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACNP,OAAA;MAAAG,QAAA,EAAK;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzB,CAAC;AAEV;AAACK,EAAA,GAfQX,mBAAmB;AAiB5B,eAAeA,mBAAmB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}