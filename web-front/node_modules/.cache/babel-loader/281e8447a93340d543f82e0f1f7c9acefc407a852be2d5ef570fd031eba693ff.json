{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewProvider } from \"../../redux/actions/providerActions\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport { COUNTRIES, SERVICESPECIALIST, SERVICETYPE, validateEmail, validateLocationX, validateLocationY, validatePhone } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddProviderScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [emailSecond, setEmailSecond] = useState(\"\");\n  const [emailSecondError, setEmailSecondError] = useState(\"\");\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [phoneSecond, setPhoneSecond] = useState(\"\");\n  const [phoneSecondError, setPhoneSecondError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [locationX, setLocationX] = useState(0);\n  const [locationXError, setLocationXError] = useState(\"\");\n  const [locationY, setLocationY] = useState(0);\n  const [locationYError, setLocationYError] = useState(\"\");\n  const [services, setServices] = useState([]);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const providerAdd = useSelector(state => state.addNewProvider);\n  const {\n    loadingProviderAdd,\n    errorProviderAdd,\n    successProviderAdd\n  } = providerAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successProviderAdd) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setEmailSecond(\"\");\n      setPhoneSecond(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setLocationX(0);\n      setLocationY(0);\n      setServiceType(\"\");\n      setServices([]);\n      setServiceSpecialist(\"\");\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setEmailSecondError(\"\");\n      setPhoneSecondError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n    }\n  }, [successProviderAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/providers-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Providers List\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"First Name\",\n                  value: firstName,\n                  onChange: v => setFirstName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: firstNameError ? firstNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                  type: \"text\",\n                  placeholder: \"Last Name\",\n                  value: lastName,\n                  onChange: v => setLastName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Email 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"email\",\n                  placeholder: \"Email 1\",\n                  value: email,\n                  onChange: v => setEmail(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: emailError ? emailError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: \"Email 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${emailSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"email\",\n                  placeholder: \"Email 2\",\n                  value: emailSecond,\n                  onChange: v => setEmailSecond(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: emailSecondError ? emailSecondError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Phone 1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"phone\",\n                  placeholder: \"Phone 1\",\n                  value: phone,\n                  onChange: v => setPhone(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: phoneError ? phoneError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: \"Phone 2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${phoneSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"phone\",\n                  placeholder: \"Phone 2\",\n                  value: phoneSecond,\n                  onChange: v => setPhoneSecond(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: phoneSecondError ? phoneSecondError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Service Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2\",\n                children: services === null || services === void 0 ? void 0 : services.map((itemService, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: [\"Service: \", itemService.service_type, \", Speciality:\", \" \", itemService.service_specialist]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: serviceType,\n                  onChange: option => {\n                    setServiceType(option);\n                    setServiceSpecialist(\"\");\n                  },\n                  className: \"text-sm\",\n                  options: SERVICETYPE.map(item => ({\n                    value: item,\n                    label: item\n                  })),\n                  placeholder: \"Select a Service Type...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: serviceTypeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: serviceTypeError ? serviceTypeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  var check = true;\n                  if (serviceType === \"\" || serviceType.value === \"\") {\n                    setServiceTypeError(\"These fields are required.\");\n                    toast.error(\" Service is required\");\n                    check = false;\n                  } else if (serviceType.value === \"Specialists\" && (serviceSpecialist === \"\" || serviceSpecialist.value === \"\")) {\n                    setServiceSpecialistError(\"These fields are required.\");\n                    toast.error(\" Specialist is required\");\n                    check = false;\n                  }\n                  if (check) {\n                    var serviceSpecialist = \"\";\n                    if (serviceType.value === \"Specialists\" && serviceSpecialist !== \"\" && serviceSpecialist.value !== \"\") {\n                      var _serviceSpecialist$va;\n                      serviceSpecialist = (_serviceSpecialist$va = serviceSpecialist.value) !== null && _serviceSpecialist$va !== void 0 ? _serviceSpecialist$va : \"\";\n                    }\n                    const exists = services.some(service => service.service_type === serviceType.value && service.service_specialist === serviceSpecialist);\n                    if (!exists) {\n                      var _serviceType$value;\n                      // Add the new item if it doesn't exist\n                      setServices([...services, {\n                        service_type: (_serviceType$value = serviceType.value) !== null && _serviceType$value !== void 0 ? _serviceType$value : \"\",\n                        service_specialist: serviceSpecialist\n                      }]);\n                      setServiceType(\"\");\n                      setServiceSpecialist(\"\");\n                    } else {\n                      setServiceTypeError(\"This service is already added!\");\n                      toast.error(\"This service is already added!\");\n                    }\n                  }\n                },\n                className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 424,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \" Add Service \"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), serviceType !== \"\" && serviceType.value === \"Specialists\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Service Specialist\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: serviceSpecialist,\n                  onChange: option => {\n                    setServiceSpecialist(option);\n                  },\n                  className: \"text-sm\",\n                  options: SERVICESPECIALIST.map(item => ({\n                    value: item,\n                    label: item\n                  })),\n                  placeholder: \"Select a Specialist...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: serviceSpecialistError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: serviceSpecialistError ? serviceSpecialistError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Address \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${addressError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Address\",\n                  value: address,\n                  onChange: v => setAddress(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: addressError ? addressError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 511,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: country,\n                  onChange: option => {\n                    setCountry(option);\n                  },\n                  className: \"text-sm\",\n                  options: COUNTRIES.map(country => ({\n                    value: country.title,\n                    label: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: country.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 529,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: country.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 530,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 524,\n                      columnNumber: 25\n                    }, this)\n                  })),\n                  placeholder: \"Select a country...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: countryError ? countryError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: \"City\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                  apiKey: \"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\",\n                  className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  onChange: v => {\n                    setCity(v.target.value);\n                  },\n                  onPlaceSelected: place => {\n                    if (place && place.geometry) {\n                      var _place$formatted_addr, _place$formatted_addr2;\n                      setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                      setCityVl((_place$formatted_addr2 = place.formatted_address) !== null && _place$formatted_addr2 !== void 0 ? _place$formatted_addr2 : \"\");\n                      //   const latitude = place.geometry.location.lat();\n                      //   const longitude = place.geometry.location.lng();\n                      //   setLocationX(latitude ?? \"\");\n                      //   setLocationY(longitude ?? \"\");\n                    }\n                  },\n                  defaultValue: city,\n                  types: [\"city\"],\n                  language: \"en\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: cityError ? cityError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Location X \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"number\",\n                  step: 0.01,\n                  placeholder: \"Location X\",\n                  value: locationX,\n                  onChange: v => setLocationX(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 607,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: locationXError ? locationXError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 602,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: [\"Location Y \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"number\",\n                  step: 0.01,\n                  placeholder: \"Location Y\",\n                  value: locationY,\n                  onChange: v => setLocationY(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: locationYError ? locationYError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/providers-list\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setFirstNameError(\"\");\n                  setAddressError(\"\");\n                  setServiceTypeError(\"\");\n                  setServiceSpecialistError(\"\");\n                  setLocationXError(\"\");\n                  setLocationYError(\"\");\n                  setPhoneError(\"\");\n                  setEmailError(\"\");\n                  if (firstName === \"\") {\n                    setFirstNameError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (email !== \"\" && !validateEmail(email)) {\n                    setEmailError(\"Invalid email address. Please correct it.\");\n                    check = false;\n                  }\n                  if (phone !== \"\" && !validatePhone(phone)) {\n                    setPhoneError(\"Invalid phone number. Please correct it.\");\n                    check = false;\n                  }\n                  if (serviceType === \"\" || serviceType.value === \"\") {\n                    setServiceTypeError(\"These fields are required.\");\n                    check = false;\n                  } else if (serviceType.value === \"Specialists\" && (serviceSpecialist === \"\" || serviceSpecialist.value === \"\")) {\n                    setServiceSpecialistError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (address === \"\") {\n                    setAddressError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (locationX === \"\") {\n                    setLocationXError(\"These fields are required.\");\n                    check = false;\n                  } else if (!validateLocationX(locationX)) {\n                    setLocationXError(\"Please enter a valid longitude (-180 to 180).\");\n                    check = false;\n                  }\n                  if (locationY === \"\") {\n                    setLocationYError(\"These fields are required.\");\n                    check = false;\n                  } else if (!validateLocationY(locationY)) {\n                    setLocationYError(\"Please enter a valid latitude (-90 to 90).\");\n                    check = false;\n                  }\n                  if (check) {\n                    var _serviceType$value2, _serviceSpecialist$va2, _country$value;\n                    setLoadEvent(true);\n                    await dispatch(createNewProvider({\n                      first_name: firstName,\n                      last_name: lastName !== null && lastName !== void 0 ? lastName : \"\",\n                      full_name: firstName + \" \" + lastName,\n                      service_type: (_serviceType$value2 = serviceType.value) !== null && _serviceType$value2 !== void 0 ? _serviceType$value2 : \"\",\n                      service_specialist: (_serviceSpecialist$va2 = serviceSpecialist.value) !== null && _serviceSpecialist$va2 !== void 0 ? _serviceSpecialist$va2 : \"\",\n                      email: email !== null && email !== void 0 ? email : \"\",\n                      second_email: emailSecond !== null && emailSecond !== void 0 ? emailSecond : \"\",\n                      phone: phone !== null && phone !== void 0 ? phone : \"\",\n                      second_phone: phoneSecond !== null && phoneSecond !== void 0 ? phoneSecond : \"\",\n                      address: address,\n                      country: (_country$value = country.value) !== null && _country$value !== void 0 ? _country$value : \"\",\n                      city: city !== null && city !== void 0 ? city : \"\",\n                      location_x: locationX,\n                      location_y: locationY\n                    })).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 646,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 5\n  }, this);\n}\n_s(AddProviderScreen, \"ah2mYvb7RyVC1P7UgcH3OFyb+qE=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = AddProviderScreen;\nexport default AddProviderScreen;\nvar _c;\n$RefreshReg$(_c, \"AddProviderScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewProvider", "axios", "Select", "COUNTRIES", "SERVICESPECIALIST", "SERVICETYPE", "validateEmail", "validateLocationX", "validateLocationY", "validatePhone", "GoogleComponent", "jsxDEV", "_jsxDEV", "AddProviderScreen", "_s", "navigate", "location", "dispatch", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "emailSecond", "setEmailSecond", "emailSecondError", "setEmailSecondError", "serviceType", "setServiceType", "serviceTypeError", "setServiceTypeError", "serviceSpecialist", "setServiceSpecialist", "serviceSpecialistError", "setServiceSpecialistError", "phone", "setPhone", "phoneError", "setPhoneError", "phoneSecond", "setPhoneSecond", "phoneSecondError", "setPhoneSecondError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "country", "setCountry", "countryError", "setCountryError", "cityVl", "setCityVl", "city", "setCity", "cityError", "setCityError", "locationX", "setLocationX", "locationXError", "setLocationXError", "locationY", "setLocationY", "locationYError", "setLocationYError", "services", "setServices", "userLogin", "state", "userInfo", "loading", "error", "providerAdd", "addNewProvider", "loadingProviderAdd", "errorProviderAdd", "successProviderAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "map", "itemService", "index", "service_type", "service_specialist", "option", "options", "item", "label", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "display", "alignItems", "singleValue", "onClick", "check", "_serviceSpecialist$va", "exists", "some", "service", "_serviceType$value", "class", "title", "icon", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "_place$formatted_addr2", "formatted_address", "defaultValue", "types", "language", "step", "_serviceType$value2", "_serviceSpecialist$va2", "_country$value", "first_name", "last_name", "full_name", "second_email", "second_phone", "location_x", "location_y", "then", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewProvider } from \"../../redux/actions/providerActions\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport {\n  COUNTRIES,\n  SERVICESPECIALIST,\n  SERVICETYPE,\n  validateEmail,\n  validateLocationX,\n  validateLocationY,\n  validatePhone,\n} from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nfunction AddProviderScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [emailSecond, setEmailSecond] = useState(\"\");\n  const [emailSecondError, setEmailSecondError] = useState(\"\");\n\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [phoneSecond, setPhoneSecond] = useState(\"\");\n  const [phoneSecondError, setPhoneSecondError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [locationX, setLocationX] = useState(0);\n  const [locationXError, setLocationXError] = useState(\"\");\n\n  const [locationY, setLocationY] = useState(0);\n  const [locationYError, setLocationYError] = useState(\"\");\n\n  const [services, setServices] = useState([]);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const providerAdd = useSelector((state) => state.addNewProvider);\n  const { loadingProviderAdd, errorProviderAdd, successProviderAdd } =\n    providerAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successProviderAdd) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setEmailSecond(\"\");\n      setPhoneSecond(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setLocationX(0);\n      setLocationY(0);\n      setServiceType(\"\");\n      setServices([]);\n      setServiceSpecialist(\"\");\n\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setEmailSecondError(\"\");\n      setPhoneSecondError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n    }\n  }, [successProviderAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-list\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Providers List</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Provider</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Provider\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {firstNameError ? firstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                    type=\"text\"\n                    placeholder=\"Last Name\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 1\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailError ? emailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 2\"\n                    value={emailSecond}\n                    onChange={(v) => setEmailSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailSecondError ? emailSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 1\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneError ? phoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 2\"\n                    value={phoneSecond}\n                    onChange={(v) => setPhoneSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneSecondError ? phoneSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Service Type\n                </div>\n                <div className=\"my-2\">\n                  {services?.map((itemService, index) => (\n                    <li key={index}>\n                      Service: {itemService.service_type}, Speciality:{\" \"}\n                      {itemService.service_specialist}\n                    </li>\n                  ))}\n                </div>\n                <div>\n                  <Select\n                    value={serviceType}\n                    onChange={(option) => {\n                      setServiceType(option);\n                      setServiceSpecialist(\"\");\n                    }}\n                    className=\"text-sm\"\n                    options={SERVICETYPE.map((item) => ({\n                      value: item,\n                      label: item,\n                    }))}\n                    placeholder=\"Select a Service Type...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: serviceTypeError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {serviceTypeError ? serviceTypeError : \"\"}\n                  </div>\n                </div>\n                <button\n                  onClick={() => {\n                    var check = true;\n                    if (serviceType === \"\" || serviceType.value === \"\") {\n                      setServiceTypeError(\"These fields are required.\");\n                      toast.error(\" Service is required\");\n                      check = false;\n                    } else if (\n                      serviceType.value === \"Specialists\" &&\n                      (serviceSpecialist === \"\" ||\n                        serviceSpecialist.value === \"\")\n                    ) {\n                      setServiceSpecialistError(\"These fields are required.\");\n                      toast.error(\" Specialist is required\");\n                      check = false;\n                    }\n                    if (check) {\n                      var serviceSpecialist = \"\";\n                      if (\n                        serviceType.value === \"Specialists\" &&\n                        serviceSpecialist !== \"\" &&\n                        serviceSpecialist.value !== \"\"\n                      ) {\n                        serviceSpecialist = serviceSpecialist.value ?? \"\";\n                      }\n                      const exists = services.some(\n                        (service) =>\n                          service.service_type === serviceType.value &&\n                          service.service_specialist === serviceSpecialist\n                      );\n\n                      if (!exists) {\n                        // Add the new item if it doesn't exist\n                        setServices([\n                          ...services,\n                          {\n                            service_type: serviceType.value ?? \"\",\n                            service_specialist: serviceSpecialist,\n                          },\n                        ]);\n                        setServiceType(\"\");\n                        setServiceSpecialist(\"\");\n                      } else {\n                        setServiceTypeError(\"This service is already added!\");\n                        toast.error(\"This service is already added!\");\n                      }\n                    }\n                  }}\n                  className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                  <span> Add Service </span>\n                </button>\n              </div>\n              {/*  */}\n              {serviceType !== \"\" && serviceType.value === \"Specialists\" ? (\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Service Specialist{\" \"}\n                    <strong className=\"text-danger\">*</strong>\n                  </div>\n                  <div>\n                    <Select\n                      value={serviceSpecialist}\n                      onChange={(option) => {\n                        setServiceSpecialist(option);\n                      }}\n                      className=\"text-sm\"\n                      options={SERVICESPECIALIST.map((item) => ({\n                        value: item,\n                        label: item,\n                      }))}\n                      placeholder=\"Select a Specialist...\"\n                      isSearchable\n                      styles={{\n                        control: (base, state) => ({\n                          ...base,\n                          background: \"#fff\",\n                          border: serviceSpecialistError\n                            ? \"1px solid #d34053\"\n                            : \"1px solid #F1F3FF\",\n                          boxShadow: state.isFocused ? \"none\" : \"none\",\n                          \"&:hover\": {\n                            border: \"1px solid #F1F3FF\",\n                          },\n                        }),\n                        option: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                        singleValue: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                      }}\n                    />\n                    <div className=\" text-[8px] text-danger\">\n                      {serviceSpecialistError ? serviceSpecialistError : \"\"}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Address <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      addressError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Address\"\n                    value={address}\n                    onChange={(v) => setAddress(v.target.value)}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {addressError ? addressError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Country\n                </div>\n                <div>\n                  <Select\n                    value={country}\n                    onChange={(option) => {\n                      setCountry(option);\n                    }}\n                    className=\"text-sm\"\n                    options={COUNTRIES.map((country) => ({\n                      value: country.title,\n                      label: (\n                        <div\n                          className={`${\n                            country.title === \"\" ? \"py-2\" : \"\"\n                          } flex flex-row items-center`}\n                        >\n                          <span className=\"mr-2\">{country.icon}</span>\n                          <span>{country.title}</span>\n                        </div>\n                      ),\n                    }))}\n                    placeholder=\"Select a country...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: countryError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {countryError ? countryError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  City\n                </div>\n                <div>\n                  <GoogleComponent\n                    apiKey=\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\"\n                    className={` outline-none border ${\n                      cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    onChange={(v) => {\n                      setCity(v.target.value);\n                    }}\n                    onPlaceSelected={(place) => {\n                      if (place && place.geometry) {\n                        setCity(place.formatted_address ?? \"\");\n                        setCityVl(place.formatted_address ?? \"\");\n                        //   const latitude = place.geometry.location.lat();\n                        //   const longitude = place.geometry.location.lng();\n                        //   setLocationX(latitude ?? \"\");\n                        //   setLocationY(longitude ?? \"\");\n                      }\n                    }}\n                    defaultValue={city}\n                    types={[\"city\"]}\n                    language=\"en\"\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {cityError ? cityError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Location X <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location X\"\n                    value={locationX}\n                    onChange={(v) => setLocationX(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationXError ? locationXError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Location Y <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location Y\"\n                    value={locationY}\n                    onChange={(v) => setLocationY(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationYError ? locationYError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/providers-list\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setAddressError(\"\");\n                    setServiceTypeError(\"\");\n                    setServiceSpecialistError(\"\");\n                    setLocationXError(\"\");\n                    setLocationYError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n\n                    if (firstName === \"\") {\n                      setFirstNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (email !== \"\" && !validateEmail(email)) {\n                      setEmailError(\n                        \"Invalid email address. Please correct it.\"\n                      );\n                      check = false;\n                    }\n                    if (phone !== \"\" && !validatePhone(phone)) {\n                      setPhoneError(\"Invalid phone number. Please correct it.\");\n                      check = false;\n                    }\n\n                    if (serviceType === \"\" || serviceType.value === \"\") {\n                      setServiceTypeError(\"These fields are required.\");\n                      check = false;\n                    } else if (\n                      serviceType.value === \"Specialists\" &&\n                      (serviceSpecialist === \"\" ||\n                        serviceSpecialist.value === \"\")\n                    ) {\n                      setServiceSpecialistError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (address === \"\") {\n                      setAddressError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (locationX === \"\") {\n                      setLocationXError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationX(locationX)) {\n                      setLocationXError(\n                        \"Please enter a valid longitude (-180 to 180).\"\n                      );\n                      check = false;\n                    }\n                    if (locationY === \"\") {\n                      setLocationYError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationY(locationY)) {\n                      setLocationYError(\n                        \"Please enter a valid latitude (-90 to 90).\"\n                      );\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewProvider({\n                          first_name: firstName,\n                          last_name: lastName ?? \"\",\n                          full_name: firstName + \" \" + lastName,\n                          service_type: serviceType.value ?? \"\",\n                          service_specialist: serviceSpecialist.value ?? \"\",\n                          email: email ?? \"\",\n                          second_email: emailSecond ?? \"\",\n                          phone: phone ?? \"\",\n                          second_phone: phoneSecond ?? \"\",\n                          address: address,\n                          country: country.value ?? \"\",\n                          city: city ?? \"\",\n                          location_x: locationX,\n                          location_y: locationY,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddProviderScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,SAAS,EACTC,iBAAiB,EACjBC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,QACR,iBAAiB;AACxB,OAAOC,eAAe,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACqE,MAAM,EAAEC,SAAS,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuE,IAAI,EAAEC,OAAO,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiF,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMqF,SAAS,GAAGnF,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,WAAW,GAAGxF,WAAW,CAAEoF,KAAK,IAAKA,KAAK,CAACK,cAAc,CAAC;EAChE,MAAM;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAmB,CAAC,GAChEJ,WAAW;EAEb,MAAMK,QAAQ,GAAG,GAAG;EACpBhG,SAAS,CAAC,MAAM;IACd,IAAI,CAACwF,QAAQ,EAAE;MACbjE,QAAQ,CAACyE,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAACzE,QAAQ,EAAEiE,QAAQ,EAAE/D,QAAQ,CAAC,CAAC;EAElCzB,SAAS,CAAC,MAAM;IACd,IAAI+F,kBAAkB,EAAE;MACtBhE,YAAY,CAAC,EAAE,CAAC;MAChBI,WAAW,CAAC,EAAE,CAAC;MACfI,QAAQ,CAAC,EAAE,CAAC;MACZgB,QAAQ,CAAC,EAAE,CAAC;MACZZ,cAAc,CAAC,EAAE,CAAC;MAClBgB,cAAc,CAAC,EAAE,CAAC;MAClBI,UAAU,CAAC,EAAE,CAAC;MACdI,UAAU,CAAC,EAAE,CAAC;MACdM,OAAO,CAAC,EAAE,CAAC;MACXI,YAAY,CAAC,CAAC,CAAC;MACfI,YAAY,CAAC,CAAC,CAAC;MACflC,cAAc,CAAC,EAAE,CAAC;MAClBsC,WAAW,CAAC,EAAE,CAAC;MACflC,oBAAoB,CAAC,EAAE,CAAC;MAExBlB,iBAAiB,CAAC,EAAE,CAAC;MACrBI,gBAAgB,CAAC,EAAE,CAAC;MACpBI,aAAa,CAAC,EAAE,CAAC;MACjBgB,aAAa,CAAC,EAAE,CAAC;MACjBZ,mBAAmB,CAAC,EAAE,CAAC;MACvBgB,mBAAmB,CAAC,EAAE,CAAC;MACvBI,eAAe,CAAC,EAAE,CAAC;MACnBI,eAAe,CAAC,EAAE,CAAC;MACnBM,YAAY,CAAC,EAAE,CAAC;MAChBI,iBAAiB,CAAC,EAAE,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBlC,mBAAmB,CAAC,EAAE,CAAC;MACvBI,yBAAyB,CAAC,EAAE,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC0C,kBAAkB,CAAC,CAAC;EAExB,oBACE3E,OAAA,CAACd,aAAa;IAAA2F,QAAA,eACZ7E,OAAA;MAAA6E,QAAA,gBACE7E,OAAA;QAAK8E,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD7E,OAAA;UAAG+E,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB7E,OAAA;YAAK8E,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D7E,OAAA;cACEgF,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB7E,OAAA;gBACEoF,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN1F,OAAA;cAAM8E,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ1F,OAAA;UAAG+E,IAAI,EAAC,iBAAiB;UAAAF,QAAA,eACvB7E,OAAA;YAAK8E,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D7E,OAAA;cAAA6E,QAAA,eACE7E,OAAA;gBACEgF,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB7E,OAAA;kBACEoF,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP1F,OAAA;cAAK8E,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ1F,OAAA;UAAA6E,QAAA,eACE7E,OAAA;YACEgF,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB7E,OAAA;cACEoF,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP1F,OAAA;UAAK8E,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAmB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEN1F,OAAA;QAAK8E,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C7E,OAAA;UAAI8E,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN1F,OAAA;QAAK8E,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ7E,OAAA;UAAK8E,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjD7E,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAA7E,OAAA;kBAAQ8E,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVlE,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpC+E,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEnF,SAAU;kBACjBoF,QAAQ,EAAGC,CAAC,IAAKpF,YAAY,CAACoF,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjE,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAEzD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,eACE7E,OAAA;kBACE8E,SAAS,EAAC,wEAAwE;kBAClFa,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,WAAW;kBACvBC,KAAK,EAAE/E,QAAS;kBAChBgF,QAAQ,EAAGC,CAAC,IAAKhF,WAAW,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACV1D,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;kBACpCuE,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAE3E,KAAM;kBACb4E,QAAQ,EAAGC,CAAC,IAAK5E,QAAQ,CAAC4E,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzD,UAAU,GAAGA,UAAU,GAAG;gBAAE;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAEzD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVtD,gBAAgB,GAAG,eAAe,GAAG,kBACtC,mCAAmC;kBACpCmE,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAEvE,WAAY;kBACnBwE,QAAQ,EAAGC,CAAC,IAAKxE,cAAc,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCrD,gBAAgB,GAAGA,gBAAgB,GAAG;gBAAE;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACV1C,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;kBACpCuD,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAE3D,KAAM;kBACb4D,QAAQ,EAAGC,CAAC,IAAK5D,QAAQ,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzC,UAAU,GAAGA,UAAU,GAAG;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAEzD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVtC,gBAAgB,GAAG,eAAe,GAAG,kBACtC,mCAAmC;kBACpCmD,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAEvD,WAAY;kBACnBwD,QAAQ,EAAGC,CAAC,IAAKxD,cAAc,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCrC,gBAAgB,GAAGA,gBAAgB,GAAG;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAK8E,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAClBb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBAChCnG,OAAA;kBAAA6E,QAAA,GAAgB,WACL,EAACqB,WAAW,CAACE,YAAY,EAAC,eAAa,EAAC,GAAG,EACnDF,WAAW,CAACG,kBAAkB;gBAAA,GAFxBF,KAAK;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA,CAACV,MAAM;kBACLuG,KAAK,EAAEnE,WAAY;kBACnBoE,QAAQ,EAAGQ,MAAM,IAAK;oBACpB3E,cAAc,CAAC2E,MAAM,CAAC;oBACtBvE,oBAAoB,CAAC,EAAE,CAAC;kBAC1B,CAAE;kBACF+C,SAAS,EAAC,SAAS;kBACnByB,OAAO,EAAE9G,WAAW,CAACwG,GAAG,CAAEO,IAAI,KAAM;oBAClCX,KAAK,EAAEW,IAAI;oBACXC,KAAK,EAAED;kBACT,CAAC,CAAC,CAAE;kBACJZ,WAAW,EAAC,0BAA0B;kBACtCc,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE1C,KAAK,MAAM;sBACzB,GAAG0C,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAEnF,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;sBACvBoF,SAAS,EAAE7C,KAAK,CAAC8C,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFT,MAAM,EAAGO,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjD,gBAAgB,GAAGA,gBAAgB,GAAG;gBAAE;kBAAA2D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1F,OAAA;gBACEqH,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIC,KAAK,GAAG,IAAI;kBAChB,IAAI5F,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACmE,KAAK,KAAK,EAAE,EAAE;oBAClDhE,mBAAmB,CAAC,4BAA4B,CAAC;oBACjD1C,KAAK,CAACmF,KAAK,CAAC,sBAAsB,CAAC;oBACnCgD,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IACL5F,WAAW,CAACmE,KAAK,KAAK,aAAa,KAClC/D,iBAAiB,KAAK,EAAE,IACvBA,iBAAiB,CAAC+D,KAAK,KAAK,EAAE,CAAC,EACjC;oBACA5D,yBAAyB,CAAC,4BAA4B,CAAC;oBACvD9C,KAAK,CAACmF,KAAK,CAAC,yBAAyB,CAAC;oBACtCgD,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIA,KAAK,EAAE;oBACT,IAAIxF,iBAAiB,GAAG,EAAE;oBAC1B,IACEJ,WAAW,CAACmE,KAAK,KAAK,aAAa,IACnC/D,iBAAiB,KAAK,EAAE,IACxBA,iBAAiB,CAAC+D,KAAK,KAAK,EAAE,EAC9B;sBAAA,IAAA0B,qBAAA;sBACAzF,iBAAiB,IAAAyF,qBAAA,GAAGzF,iBAAiB,CAAC+D,KAAK,cAAA0B,qBAAA,cAAAA,qBAAA,GAAI,EAAE;oBACnD;oBACA,MAAMC,MAAM,GAAGxD,QAAQ,CAACyD,IAAI,CACzBC,OAAO,IACNA,OAAO,CAACtB,YAAY,KAAK1E,WAAW,CAACmE,KAAK,IAC1C6B,OAAO,CAACrB,kBAAkB,KAAKvE,iBACnC,CAAC;oBAED,IAAI,CAAC0F,MAAM,EAAE;sBAAA,IAAAG,kBAAA;sBACX;sBACA1D,WAAW,CAAC,CACV,GAAGD,QAAQ,EACX;wBACEoC,YAAY,GAAAuB,kBAAA,GAAEjG,WAAW,CAACmE,KAAK,cAAA8B,kBAAA,cAAAA,kBAAA,GAAI,EAAE;wBACrCtB,kBAAkB,EAAEvE;sBACtB,CAAC,CACF,CAAC;sBACFH,cAAc,CAAC,EAAE,CAAC;sBAClBI,oBAAoB,CAAC,EAAE,CAAC;oBAC1B,CAAC,MAAM;sBACLF,mBAAmB,CAAC,gCAAgC,CAAC;sBACrD1C,KAAK,CAACmF,KAAK,CAAC,gCAAgC,CAAC;oBAC/C;kBACF;gBACF,CAAE;gBACFQ,SAAS,EAAC,uDAAuD;gBAAAD,QAAA,gBAEjE7E,OAAA;kBACEgF,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrByC,KAAK,EAAC,QAAQ;kBAAA/C,QAAA,eAEd7E,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBsF,CAAC,EAAC;kBAAmD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN1F,OAAA;kBAAA6E,QAAA,EAAM;gBAAa;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELhE,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACmE,KAAK,KAAK,aAAa,gBACxD7F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,oBACtC,EAAC,GAAG,eACtB7E,OAAA;kBAAQ8E,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA,CAACV,MAAM;kBACLuG,KAAK,EAAE/D,iBAAkB;kBACzBgE,QAAQ,EAAGQ,MAAM,IAAK;oBACpBvE,oBAAoB,CAACuE,MAAM,CAAC;kBAC9B,CAAE;kBACFxB,SAAS,EAAC,SAAS;kBACnByB,OAAO,EAAE/G,iBAAiB,CAACyG,GAAG,CAAEO,IAAI,KAAM;oBACxCX,KAAK,EAAEW,IAAI;oBACXC,KAAK,EAAED;kBACT,CAAC,CAAC,CAAE;kBACJZ,WAAW,EAAC,wBAAwB;kBACpCc,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE1C,KAAK,MAAM;sBACzB,GAAG0C,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAE/E,sBAAsB,GAC1B,mBAAmB,GACnB,mBAAmB;sBACvBgF,SAAS,EAAE7C,KAAK,CAAC8C,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFT,MAAM,EAAGO,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC7C,sBAAsB,GAAGA,sBAAsB,GAAG;gBAAE;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C7E,OAAA;cAAK8E,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnC7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,UAChD,eAAA7E,OAAA;kBAAQ8E,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVlC,YAAY,GAAG,eAAe,GAAG,kBAClC,mCAAmC;kBACpC+C,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAEnD,OAAQ;kBACfoD,QAAQ,EAAGC,CAAC,IAAKpD,UAAU,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eAEF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjC,YAAY,GAAGA,YAAY,GAAG;gBAAE;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA,CAACV,MAAM;kBACLuG,KAAK,EAAE/C,OAAQ;kBACfgD,QAAQ,EAAGQ,MAAM,IAAK;oBACpBvD,UAAU,CAACuD,MAAM,CAAC;kBACpB,CAAE;kBACFxB,SAAS,EAAC,SAAS;kBACnByB,OAAO,EAAEhH,SAAS,CAAC0G,GAAG,CAAEnD,OAAO,KAAM;oBACnC+C,KAAK,EAAE/C,OAAO,CAAC+E,KAAK;oBACpBpB,KAAK,eACHzG,OAAA;sBACE8E,SAAS,EAAG,GACVhC,OAAO,CAAC+E,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;sBAAAhD,QAAA,gBAE9B7E,OAAA;wBAAM8E,SAAS,EAAC,MAAM;wBAAAD,QAAA,EAAE/B,OAAO,CAACgF;sBAAI;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC5C1F,OAAA;wBAAA6E,QAAA,EAAO/B,OAAO,CAAC+E;sBAAK;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAET,CAAC,CAAC,CAAE;kBACJE,WAAW,EAAC,qBAAqB;kBACjCc,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAE1C,KAAK,MAAM;sBACzB,GAAG0C,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAE/D,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;sBACvBgE,SAAS,EAAE7C,KAAK,CAAC8C,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFT,MAAM,EAAGO,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC7B,YAAY,GAAGA,YAAY,GAAG;gBAAE;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAEzD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA,CAACF,eAAe;kBACdiI,MAAM,EAAC,yCAAyC;kBAChDjD,SAAS,EAAG,wBACVxB,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;kBACpCwC,QAAQ,EAAGC,CAAC,IAAK;oBACf1C,OAAO,CAAC0C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBACzB,CAAE;kBACFmC,eAAe,EAAGC,KAAK,IAAK;oBAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;sBAAA,IAAAC,qBAAA,EAAAC,sBAAA;sBAC3B/E,OAAO,EAAA8E,qBAAA,GAACF,KAAK,CAACI,iBAAiB,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;sBACtChF,SAAS,EAAAiF,sBAAA,GAACH,KAAK,CAACI,iBAAiB,cAAAD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;sBACxC;sBACA;sBACA;sBACA;oBACF;kBACF,CAAE;kBACFE,YAAY,EAAElF,IAAK;kBACnBmF,KAAK,EAAE,CAAC,MAAM,CAAE;kBAChBC,QAAQ,EAAC;gBAAI;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAEF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCvB,SAAS,GAAGA,SAAS,GAAG;gBAAE;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C7E,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAA7E,OAAA;kBAAQ8E,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVpB,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpCiC,IAAI,EAAC,QAAQ;kBACb8C,IAAI,EAAE,IAAK;kBACX7C,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAErC,SAAU;kBACjBsC,QAAQ,EAAGC,CAAC,IAAKtC,YAAY,CAACsC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCnB,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1F,OAAA;cAAK8E,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C7E,OAAA;gBAAK8E,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,GAAC,aAC5C,eAAA7E,OAAA;kBAAQ8E,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN1F,OAAA;gBAAA6E,QAAA,gBACE7E,OAAA;kBACE8E,SAAS,EAAG,wBACVhB,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpC6B,IAAI,EAAC,QAAQ;kBACb8C,IAAI,EAAE,IAAK;kBACX7C,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEjC,SAAU;kBACjBkC,QAAQ,EAAGC,CAAC,IAAKlC,YAAY,CAACkC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF1F,OAAA;kBAAK8E,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCf,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN1F,OAAA;YAAK8E,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpB7E,OAAA;cAAK8E,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1D7E,OAAA;gBACE+E,IAAI,EAAC,iBAAiB;gBACtBD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ1F,OAAA;gBACEqH,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBzG,iBAAiB,CAAC,EAAE,CAAC;kBACrBgC,eAAe,CAAC,EAAE,CAAC;kBACnBhB,mBAAmB,CAAC,EAAE,CAAC;kBACvBI,yBAAyB,CAAC,EAAE,CAAC;kBAC7B0B,iBAAiB,CAAC,EAAE,CAAC;kBACrBI,iBAAiB,CAAC,EAAE,CAAC;kBACrB1B,aAAa,CAAC,EAAE,CAAC;kBACjBhB,aAAa,CAAC,EAAE,CAAC;kBAEjB,IAAIX,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CyG,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIpG,KAAK,KAAK,EAAE,IAAI,CAACxB,aAAa,CAACwB,KAAK,CAAC,EAAE;oBACzCG,aAAa,CACX,2CACF,CAAC;oBACDiG,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIpF,KAAK,KAAK,EAAE,IAAI,CAACrC,aAAa,CAACqC,KAAK,CAAC,EAAE;oBACzCG,aAAa,CAAC,0CAA0C,CAAC;oBACzDiF,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAI5F,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACmE,KAAK,KAAK,EAAE,EAAE;oBAClDhE,mBAAmB,CAAC,4BAA4B,CAAC;oBACjDyF,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IACL5F,WAAW,CAACmE,KAAK,KAAK,aAAa,KAClC/D,iBAAiB,KAAK,EAAE,IACvBA,iBAAiB,CAAC+D,KAAK,KAAK,EAAE,CAAC,EACjC;oBACA5D,yBAAyB,CAAC,4BAA4B,CAAC;oBACvDqF,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAI5E,OAAO,KAAK,EAAE,EAAE;oBAClBG,eAAe,CAAC,4BAA4B,CAAC;oBAC7CyE,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAI9D,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/C2D,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IAAI,CAAC3H,iBAAiB,CAAC6D,SAAS,CAAC,EAAE;oBACxCG,iBAAiB,CACf,+CACF,CAAC;oBACD2D,KAAK,GAAG,KAAK;kBACf;kBACA,IAAI1D,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CuD,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IAAI,CAAC1H,iBAAiB,CAACgE,SAAS,CAAC,EAAE;oBACxCG,iBAAiB,CACf,4CACF,CAAC;oBACDuD,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBAAA,IAAAoB,mBAAA,EAAAC,sBAAA,EAAAC,cAAA;oBACTnI,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMJ,QAAQ,CACZjB,iBAAiB,CAAC;sBAChByJ,UAAU,EAAEnI,SAAS;sBACrBoI,SAAS,EAAEhI,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,EAAE;sBACzBiI,SAAS,EAAErI,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrCsF,YAAY,GAAAsC,mBAAA,GAAEhH,WAAW,CAACmE,KAAK,cAAA6C,mBAAA,cAAAA,mBAAA,GAAI,EAAE;sBACrCrC,kBAAkB,GAAAsC,sBAAA,GAAE7G,iBAAiB,CAAC+D,KAAK,cAAA8C,sBAAA,cAAAA,sBAAA,GAAI,EAAE;sBACjDzH,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE;sBAClB8H,YAAY,EAAE1H,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE;sBAC/BY,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE;sBAClB+G,YAAY,EAAE3G,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE;sBAC/BI,OAAO,EAAEA,OAAO;sBAChBI,OAAO,GAAA8F,cAAA,GAAE9F,OAAO,CAAC+C,KAAK,cAAA+C,cAAA,cAAAA,cAAA,GAAI,EAAE;sBAC5BxF,IAAI,EAAEA,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;sBAChB8F,UAAU,EAAE1F,SAAS;sBACrB2F,UAAU,EAAEvF;oBACd,CAAC,CACH,CAAC,CAACwF,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChB3I,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACLtB,KAAK,CAACmF,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFQ,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjEJ,kBAAkB,GAAG,aAAa,GAAG;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACxF,EAAA,CA/tBQD,iBAAiB;EAAA,QACPhB,WAAW,EACXD,WAAW,EACXF,WAAW,EA+CVC,WAAW,EAGTA,WAAW;AAAA;AAAAsK,EAAA,GArDxBpJ,iBAAiB;AAiuB1B,eAAeA,iBAAiB;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}