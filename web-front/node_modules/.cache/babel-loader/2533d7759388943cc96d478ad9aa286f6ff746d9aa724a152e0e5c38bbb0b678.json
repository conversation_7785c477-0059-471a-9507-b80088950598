{"ast": null, "code": "import { time } from '../../frameloop/sync-time.mjs';\nimport { DOMKeyframesResolver } from '../../render/dom/DOMKeyframesResolver.mjs';\nimport { memo } from '../../utils/memo.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { MainThreadAnimation } from './MainThreadAnimation.mjs';\nimport { animateStyle } from './waapi/index.mjs';\nimport { isWaapiSupportedEasing } from './waapi/easing.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\"opacity\", \"clipPath\", \"filter\", \"transform\"\n// TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n// or until we implement support for linear() easing.\n// \"background-color\"\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\n/**\n * Check if an animation can run natively via WAAPI or requires pregenerated keyframes.\n * WAAPI doesn't support spring or function easings so we run these as JS animation before\n * handing off.\n */\nfunction requiresPregeneratedKeyframes(options) {\n  return options.type === \"spring\" || options.name === \"backgroundColor\" || !isWaapiSupportedEasing(options.ease);\n}\nfunction pregenerateKeyframes(keyframes, options) {\n  /**\n   * Create a main-thread animation to pregenerate keyframes.\n   * We sample this at regular intervals to generate keyframes that we then\n   * linearly interpolate between.\n   */\n  const sampleAnimation = new MainThreadAnimation({\n    ...options,\n    keyframes,\n    repeat: 0,\n    delay: 0,\n    isGenerator: true\n  });\n  let state = {\n    done: false,\n    value: keyframes[0]\n  };\n  const pregeneratedKeyframes = [];\n  /**\n   * Bail after 20 seconds of pre-generated keyframes as it's likely\n   * we're heading for an infinite loop.\n   */\n  let t = 0;\n  while (!state.done && t < maxDuration) {\n    state = sampleAnimation.sample(t);\n    pregeneratedKeyframes.push(state.value);\n    t += sampleDelta;\n  }\n  return {\n    times: undefined,\n    keyframes: pregeneratedKeyframes,\n    duration: t - sampleDelta,\n    ease: \"linear\"\n  };\n}\nclass AcceleratedAnimation extends BaseAnimation {\n  constructor(options) {\n    super(options);\n    const {\n      name,\n      motionValue,\n      keyframes\n    } = this.options;\n    this.resolver = new DOMKeyframesResolver(keyframes, (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe), name, motionValue);\n    this.resolver.scheduleResolve();\n  }\n  initPlayback(keyframes, finalKeyframe) {\n    var _a;\n    let {\n      duration = 300,\n      times,\n      ease,\n      type,\n      motionValue,\n      name\n    } = this.options;\n    /**\n     * If element has since been unmounted, return false to indicate\n     * the animation failed to initialised.\n     */\n    if (!((_a = motionValue.owner) === null || _a === void 0 ? void 0 : _a.current)) {\n      return false;\n    }\n    /**\n     * If this animation needs pre-generated keyframes then generate.\n     */\n    if (requiresPregeneratedKeyframes(this.options)) {\n      const {\n        onComplete,\n        onUpdate,\n        motionValue,\n        ...options\n      } = this.options;\n      const pregeneratedAnimation = pregenerateKeyframes(keyframes, options);\n      keyframes = pregeneratedAnimation.keyframes;\n      // If this is a very short animation, ensure we have\n      // at least two keyframes to animate between as older browsers\n      // can't animate between a single keyframe.\n      if (keyframes.length === 1) {\n        keyframes[1] = keyframes[0];\n      }\n      duration = pregeneratedAnimation.duration;\n      times = pregeneratedAnimation.times;\n      ease = pregeneratedAnimation.ease;\n      type = \"keyframes\";\n    }\n    const animation = animateStyle(motionValue.owner.current, name, keyframes, {\n      ...this.options,\n      duration,\n      times,\n      ease\n    });\n    // Override the browser calculated startTime with one synchronised to other JS\n    // and WAAPI animations starting this event loop.\n    animation.startTime = time.now();\n    if (this.pendingTimeline) {\n      animation.timeline = this.pendingTimeline;\n      this.pendingTimeline = undefined;\n    } else {\n      /**\n       * Prefer the `onfinish` prop as it's more widely supported than\n       * the `finished` promise.\n       *\n       * Here, we synchronously set the provided MotionValue to the end\n       * keyframe. If we didn't, when the WAAPI animation is finished it would\n       * be removed from the element which would then revert to its old styles.\n       */\n      animation.onfinish = () => {\n        const {\n          onComplete\n        } = this.options;\n        motionValue.set(getFinalKeyframe(keyframes, this.options, finalKeyframe));\n        onComplete && onComplete();\n        this.cancel();\n        this.resolveFinishedPromise();\n      };\n    }\n    return {\n      animation,\n      duration,\n      times,\n      type,\n      ease,\n      keyframes: keyframes\n    };\n  }\n  get duration() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return 0;\n    const {\n      duration\n    } = resolved;\n    return millisecondsToSeconds(duration);\n  }\n  get time() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return 0;\n    const {\n      animation\n    } = resolved;\n    return millisecondsToSeconds(animation.currentTime || 0);\n  }\n  set time(newTime) {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    const {\n      animation\n    } = resolved;\n    animation.currentTime = secondsToMilliseconds(newTime);\n  }\n  get speed() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return 1;\n    const {\n      animation\n    } = resolved;\n    return animation.playbackRate;\n  }\n  set speed(newSpeed) {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    const {\n      animation\n    } = resolved;\n    animation.playbackRate = newSpeed;\n  }\n  get state() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return \"idle\";\n    const {\n      animation\n    } = resolved;\n    return animation.playState;\n  }\n  /**\n   * Replace the default DocumentTimeline with another AnimationTimeline.\n   * Currently used for scroll animations.\n   */\n  attachTimeline(timeline) {\n    if (!this._resolved) {\n      this.pendingTimeline = timeline;\n    } else {\n      const {\n        resolved\n      } = this;\n      if (!resolved) return noop;\n      const {\n        animation\n      } = resolved;\n      animation.timeline = timeline;\n      animation.onfinish = null;\n    }\n    return noop;\n  }\n  play() {\n    if (this.isStopped) return;\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    const {\n      animation\n    } = resolved;\n    if (animation.playState === \"finished\") {\n      this.updateFinishedPromise();\n    }\n    animation.play();\n  }\n  pause() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    const {\n      animation\n    } = resolved;\n    animation.pause();\n  }\n  stop() {\n    this.resolver.cancel();\n    this.isStopped = true;\n    if (this.state === \"idle\") return;\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    const {\n      animation,\n      keyframes,\n      duration,\n      type,\n      ease,\n      times\n    } = resolved;\n    if (animation.playState === \"idle\" || animation.playState === \"finished\") {\n      return;\n    }\n    /**\n     * WAAPI doesn't natively have any interruption capabilities.\n     *\n     * Rather than read commited styles back out of the DOM, we can\n     * create a renderless JS animation and sample it twice to calculate\n     * its current value, \"previous\" value, and therefore allow\n     * Motion to calculate velocity for any subsequent animation.\n     */\n    if (this.time) {\n      const {\n        motionValue,\n        onUpdate,\n        onComplete,\n        ...options\n      } = this.options;\n      const sampleAnimation = new MainThreadAnimation({\n        ...options,\n        keyframes,\n        duration,\n        type,\n        ease,\n        times,\n        isGenerator: true\n      });\n      const sampleTime = secondsToMilliseconds(this.time);\n      motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n    }\n    this.cancel();\n  }\n  complete() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    resolved.animation.finish();\n  }\n  cancel() {\n    const {\n      resolved\n    } = this;\n    if (!resolved) return;\n    resolved.animation.cancel();\n  }\n  static supports(options) {\n    const {\n      motionValue,\n      name,\n      repeatDelay,\n      repeatType,\n      damping,\n      type\n    } = options;\n    return supportsWaapi() && name && acceleratedValues.has(name) && motionValue && motionValue.owner && motionValue.owner.current instanceof HTMLElement &&\n    /**\n     * If we're outputting values to onUpdate then we can't use WAAPI as there's\n     * no way to read the value from WAAPI every frame.\n     */\n    !motionValue.owner.getProps().onUpdate && !repeatDelay && repeatType !== \"mirror\" && damping !== 0 && type !== \"inertia\";\n  }\n}\nexport { AcceleratedAnimation };", "map": {"version": 3, "names": ["time", "DOMKeyframesResolver", "memo", "noop", "millisecondsToSeconds", "secondsToMilliseconds", "BaseAnimation", "MainThreadAnimation", "animateStyle", "isWaapiSupportedEasing", "getFinalKeyframe", "supportsWaapi", "Object", "hasOwnProperty", "call", "Element", "prototype", "acceleratedValues", "Set", "sampleDelta", "maxDuration", "requiresPregeneratedKeyframes", "options", "type", "name", "ease", "pregenerateKeyframes", "keyframes", "sampleAnimation", "repeat", "delay", "isGenerator", "state", "done", "value", "pregeneratedKeyframes", "t", "sample", "push", "times", "undefined", "duration", "AcceleratedAnimation", "constructor", "motionValue", "resolver", "resolvedKeyframes", "finalKeyframe", "onKeyframesResolved", "scheduleResolve", "initPlayback", "_a", "owner", "current", "onComplete", "onUpdate", "pregeneratedAnimation", "length", "animation", "startTime", "now", "pendingTimeline", "timeline", "onfinish", "set", "cancel", "resolveFinishedPromise", "resolved", "currentTime", "newTime", "speed", "playbackRate", "newSpeed", "playState", "attachTimeline", "_resolved", "play", "isStopped", "updateFinishedPromise", "pause", "stop", "sampleTime", "setWithVelocity", "complete", "finish", "supports", "repeatDelay", "repeatType", "damping", "has", "HTMLElement", "getProps"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/animators/AcceleratedAnimation.mjs"], "sourcesContent": ["import { time } from '../../frameloop/sync-time.mjs';\nimport { DOMKeyframesResolver } from '../../render/dom/DOMKeyframesResolver.mjs';\nimport { memo } from '../../utils/memo.mjs';\nimport { noop } from '../../utils/noop.mjs';\nimport { millisecondsToSeconds, secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { BaseAnimation } from './BaseAnimation.mjs';\nimport { MainThreadAnimation } from './MainThreadAnimation.mjs';\nimport { animateStyle } from './waapi/index.mjs';\nimport { isWaapiSupportedEasing } from './waapi/easing.mjs';\nimport { getFinalKeyframe } from './waapi/utils/get-final-keyframe.mjs';\n\nconst supportsWaapi = memo(() => Object.hasOwnProperty.call(Element.prototype, \"animate\"));\n/**\n * A list of values that can be hardware-accelerated.\n */\nconst acceleratedValues = new Set([\n    \"opacity\",\n    \"clipPath\",\n    \"filter\",\n    \"transform\",\n    // TODO: Can be accelerated but currently disabled until https://issues.chromium.org/issues/41491098 is resolved\n    // or until we implement support for linear() easing.\n    // \"background-color\"\n]);\n/**\n * 10ms is chosen here as it strikes a balance between smooth\n * results (more than one keyframe per frame at 60fps) and\n * keyframe quantity.\n */\nconst sampleDelta = 10; //ms\n/**\n * Implement a practical max duration for keyframe generation\n * to prevent infinite loops\n */\nconst maxDuration = 20000;\n/**\n * Check if an animation can run natively via WAAPI or requires pregenerated keyframes.\n * WAAPI doesn't support spring or function easings so we run these as JS animation before\n * handing off.\n */\nfunction requiresPregeneratedKeyframes(options) {\n    return (options.type === \"spring\" ||\n        options.name === \"backgroundColor\" ||\n        !isWaapiSupportedEasing(options.ease));\n}\nfunction pregenerateKeyframes(keyframes, options) {\n    /**\n     * Create a main-thread animation to pregenerate keyframes.\n     * We sample this at regular intervals to generate keyframes that we then\n     * linearly interpolate between.\n     */\n    const sampleAnimation = new MainThreadAnimation({\n        ...options,\n        keyframes,\n        repeat: 0,\n        delay: 0,\n        isGenerator: true,\n    });\n    let state = { done: false, value: keyframes[0] };\n    const pregeneratedKeyframes = [];\n    /**\n     * Bail after 20 seconds of pre-generated keyframes as it's likely\n     * we're heading for an infinite loop.\n     */\n    let t = 0;\n    while (!state.done && t < maxDuration) {\n        state = sampleAnimation.sample(t);\n        pregeneratedKeyframes.push(state.value);\n        t += sampleDelta;\n    }\n    return {\n        times: undefined,\n        keyframes: pregeneratedKeyframes,\n        duration: t - sampleDelta,\n        ease: \"linear\",\n    };\n}\nclass AcceleratedAnimation extends BaseAnimation {\n    constructor(options) {\n        super(options);\n        const { name, motionValue, keyframes } = this.options;\n        this.resolver = new DOMKeyframesResolver(keyframes, (resolvedKeyframes, finalKeyframe) => this.onKeyframesResolved(resolvedKeyframes, finalKeyframe), name, motionValue);\n        this.resolver.scheduleResolve();\n    }\n    initPlayback(keyframes, finalKeyframe) {\n        var _a;\n        let { duration = 300, times, ease, type, motionValue, name, } = this.options;\n        /**\n         * If element has since been unmounted, return false to indicate\n         * the animation failed to initialised.\n         */\n        if (!((_a = motionValue.owner) === null || _a === void 0 ? void 0 : _a.current)) {\n            return false;\n        }\n        /**\n         * If this animation needs pre-generated keyframes then generate.\n         */\n        if (requiresPregeneratedKeyframes(this.options)) {\n            const { onComplete, onUpdate, motionValue, ...options } = this.options;\n            const pregeneratedAnimation = pregenerateKeyframes(keyframes, options);\n            keyframes = pregeneratedAnimation.keyframes;\n            // If this is a very short animation, ensure we have\n            // at least two keyframes to animate between as older browsers\n            // can't animate between a single keyframe.\n            if (keyframes.length === 1) {\n                keyframes[1] = keyframes[0];\n            }\n            duration = pregeneratedAnimation.duration;\n            times = pregeneratedAnimation.times;\n            ease = pregeneratedAnimation.ease;\n            type = \"keyframes\";\n        }\n        const animation = animateStyle(motionValue.owner.current, name, keyframes, { ...this.options, duration, times, ease });\n        // Override the browser calculated startTime with one synchronised to other JS\n        // and WAAPI animations starting this event loop.\n        animation.startTime = time.now();\n        if (this.pendingTimeline) {\n            animation.timeline = this.pendingTimeline;\n            this.pendingTimeline = undefined;\n        }\n        else {\n            /**\n             * Prefer the `onfinish` prop as it's more widely supported than\n             * the `finished` promise.\n             *\n             * Here, we synchronously set the provided MotionValue to the end\n             * keyframe. If we didn't, when the WAAPI animation is finished it would\n             * be removed from the element which would then revert to its old styles.\n             */\n            animation.onfinish = () => {\n                const { onComplete } = this.options;\n                motionValue.set(getFinalKeyframe(keyframes, this.options, finalKeyframe));\n                onComplete && onComplete();\n                this.cancel();\n                this.resolveFinishedPromise();\n            };\n        }\n        return {\n            animation,\n            duration,\n            times,\n            type,\n            ease,\n            keyframes: keyframes,\n        };\n    }\n    get duration() {\n        const { resolved } = this;\n        if (!resolved)\n            return 0;\n        const { duration } = resolved;\n        return millisecondsToSeconds(duration);\n    }\n    get time() {\n        const { resolved } = this;\n        if (!resolved)\n            return 0;\n        const { animation } = resolved;\n        return millisecondsToSeconds(animation.currentTime || 0);\n    }\n    set time(newTime) {\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        const { animation } = resolved;\n        animation.currentTime = secondsToMilliseconds(newTime);\n    }\n    get speed() {\n        const { resolved } = this;\n        if (!resolved)\n            return 1;\n        const { animation } = resolved;\n        return animation.playbackRate;\n    }\n    set speed(newSpeed) {\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        const { animation } = resolved;\n        animation.playbackRate = newSpeed;\n    }\n    get state() {\n        const { resolved } = this;\n        if (!resolved)\n            return \"idle\";\n        const { animation } = resolved;\n        return animation.playState;\n    }\n    /**\n     * Replace the default DocumentTimeline with another AnimationTimeline.\n     * Currently used for scroll animations.\n     */\n    attachTimeline(timeline) {\n        if (!this._resolved) {\n            this.pendingTimeline = timeline;\n        }\n        else {\n            const { resolved } = this;\n            if (!resolved)\n                return noop;\n            const { animation } = resolved;\n            animation.timeline = timeline;\n            animation.onfinish = null;\n        }\n        return noop;\n    }\n    play() {\n        if (this.isStopped)\n            return;\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        const { animation } = resolved;\n        if (animation.playState === \"finished\") {\n            this.updateFinishedPromise();\n        }\n        animation.play();\n    }\n    pause() {\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        const { animation } = resolved;\n        animation.pause();\n    }\n    stop() {\n        this.resolver.cancel();\n        this.isStopped = true;\n        if (this.state === \"idle\")\n            return;\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        const { animation, keyframes, duration, type, ease, times } = resolved;\n        if (animation.playState === \"idle\" ||\n            animation.playState === \"finished\") {\n            return;\n        }\n        /**\n         * WAAPI doesn't natively have any interruption capabilities.\n         *\n         * Rather than read commited styles back out of the DOM, we can\n         * create a renderless JS animation and sample it twice to calculate\n         * its current value, \"previous\" value, and therefore allow\n         * Motion to calculate velocity for any subsequent animation.\n         */\n        if (this.time) {\n            const { motionValue, onUpdate, onComplete, ...options } = this.options;\n            const sampleAnimation = new MainThreadAnimation({\n                ...options,\n                keyframes,\n                duration,\n                type,\n                ease,\n                times,\n                isGenerator: true,\n            });\n            const sampleTime = secondsToMilliseconds(this.time);\n            motionValue.setWithVelocity(sampleAnimation.sample(sampleTime - sampleDelta).value, sampleAnimation.sample(sampleTime).value, sampleDelta);\n        }\n        this.cancel();\n    }\n    complete() {\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        resolved.animation.finish();\n    }\n    cancel() {\n        const { resolved } = this;\n        if (!resolved)\n            return;\n        resolved.animation.cancel();\n    }\n    static supports(options) {\n        const { motionValue, name, repeatDelay, repeatType, damping, type } = options;\n        return (supportsWaapi() &&\n            name &&\n            acceleratedValues.has(name) &&\n            motionValue &&\n            motionValue.owner &&\n            motionValue.owner.current instanceof HTMLElement &&\n            /**\n             * If we're outputting values to onUpdate then we can't use WAAPI as there's\n             * no way to read the value from WAAPI every frame.\n             */\n            !motionValue.owner.getProps().onUpdate &&\n            !repeatDelay &&\n            repeatType !== \"mirror\" &&\n            damping !== 0 &&\n            type !== \"inertia\");\n    }\n}\n\nexport { AcceleratedAnimation };\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,+BAA+B;AACpD,SAASC,oBAAoB,QAAQ,2CAA2C;AAChF,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,SAASC,qBAAqB,EAAEC,qBAAqB,QAAQ,iCAAiC;AAC9F,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,mBAAmB,QAAQ,2BAA2B;AAC/D,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,sBAAsB,QAAQ,oBAAoB;AAC3D,SAASC,gBAAgB,QAAQ,sCAAsC;AAEvE,MAAMC,aAAa,GAAGT,IAAI,CAAC,MAAMU,MAAM,CAACC,cAAc,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC1F;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,SAAS,EACT,UAAU,EACV,QAAQ,EACR;AACA;AACA;AACA;AAAA,CACH,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,KAAK;AACzB;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,OAAO,EAAE;EAC5C,OAAQA,OAAO,CAACC,IAAI,KAAK,QAAQ,IAC7BD,OAAO,CAACE,IAAI,KAAK,iBAAiB,IAClC,CAACf,sBAAsB,CAACa,OAAO,CAACG,IAAI,CAAC;AAC7C;AACA,SAASC,oBAAoBA,CAACC,SAAS,EAAEL,OAAO,EAAE;EAC9C;AACJ;AACA;AACA;AACA;EACI,MAAMM,eAAe,GAAG,IAAIrB,mBAAmB,CAAC;IAC5C,GAAGe,OAAO;IACVK,SAAS;IACTE,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE;EACjB,CAAC,CAAC;EACF,IAAIC,KAAK,GAAG;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAEP,SAAS,CAAC,CAAC;EAAE,CAAC;EAChD,MAAMQ,qBAAqB,GAAG,EAAE;EAChC;AACJ;AACA;AACA;EACI,IAAIC,CAAC,GAAG,CAAC;EACT,OAAO,CAACJ,KAAK,CAACC,IAAI,IAAIG,CAAC,GAAGhB,WAAW,EAAE;IACnCY,KAAK,GAAGJ,eAAe,CAACS,MAAM,CAACD,CAAC,CAAC;IACjCD,qBAAqB,CAACG,IAAI,CAACN,KAAK,CAACE,KAAK,CAAC;IACvCE,CAAC,IAAIjB,WAAW;EACpB;EACA,OAAO;IACHoB,KAAK,EAAEC,SAAS;IAChBb,SAAS,EAAEQ,qBAAqB;IAChCM,QAAQ,EAAEL,CAAC,GAAGjB,WAAW;IACzBM,IAAI,EAAE;EACV,CAAC;AACL;AACA,MAAMiB,oBAAoB,SAASpC,aAAa,CAAC;EAC7CqC,WAAWA,CAACrB,OAAO,EAAE;IACjB,KAAK,CAACA,OAAO,CAAC;IACd,MAAM;MAAEE,IAAI;MAAEoB,WAAW;MAAEjB;IAAU,CAAC,GAAG,IAAI,CAACL,OAAO;IACrD,IAAI,CAACuB,QAAQ,GAAG,IAAI5C,oBAAoB,CAAC0B,SAAS,EAAE,CAACmB,iBAAiB,EAAEC,aAAa,KAAK,IAAI,CAACC,mBAAmB,CAACF,iBAAiB,EAAEC,aAAa,CAAC,EAAEvB,IAAI,EAAEoB,WAAW,CAAC;IACxK,IAAI,CAACC,QAAQ,CAACI,eAAe,CAAC,CAAC;EACnC;EACAC,YAAYA,CAACvB,SAAS,EAAEoB,aAAa,EAAE;IACnC,IAAII,EAAE;IACN,IAAI;MAAEV,QAAQ,GAAG,GAAG;MAAEF,KAAK;MAAEd,IAAI;MAAEF,IAAI;MAAEqB,WAAW;MAAEpB;IAAM,CAAC,GAAG,IAAI,CAACF,OAAO;IAC5E;AACR;AACA;AACA;IACQ,IAAI,EAAE,CAAC6B,EAAE,GAAGP,WAAW,CAACQ,KAAK,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,CAAC,EAAE;MAC7E,OAAO,KAAK;IAChB;IACA;AACR;AACA;IACQ,IAAIhC,6BAA6B,CAAC,IAAI,CAACC,OAAO,CAAC,EAAE;MAC7C,MAAM;QAAEgC,UAAU;QAAEC,QAAQ;QAAEX,WAAW;QAAE,GAAGtB;MAAQ,CAAC,GAAG,IAAI,CAACA,OAAO;MACtE,MAAMkC,qBAAqB,GAAG9B,oBAAoB,CAACC,SAAS,EAAEL,OAAO,CAAC;MACtEK,SAAS,GAAG6B,qBAAqB,CAAC7B,SAAS;MAC3C;MACA;MACA;MACA,IAAIA,SAAS,CAAC8B,MAAM,KAAK,CAAC,EAAE;QACxB9B,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAC,CAAC,CAAC;MAC/B;MACAc,QAAQ,GAAGe,qBAAqB,CAACf,QAAQ;MACzCF,KAAK,GAAGiB,qBAAqB,CAACjB,KAAK;MACnCd,IAAI,GAAG+B,qBAAqB,CAAC/B,IAAI;MACjCF,IAAI,GAAG,WAAW;IACtB;IACA,MAAMmC,SAAS,GAAGlD,YAAY,CAACoC,WAAW,CAACQ,KAAK,CAACC,OAAO,EAAE7B,IAAI,EAAEG,SAAS,EAAE;MAAE,GAAG,IAAI,CAACL,OAAO;MAAEmB,QAAQ;MAAEF,KAAK;MAAEd;IAAK,CAAC,CAAC;IACtH;IACA;IACAiC,SAAS,CAACC,SAAS,GAAG3D,IAAI,CAAC4D,GAAG,CAAC,CAAC;IAChC,IAAI,IAAI,CAACC,eAAe,EAAE;MACtBH,SAAS,CAACI,QAAQ,GAAG,IAAI,CAACD,eAAe;MACzC,IAAI,CAACA,eAAe,GAAGrB,SAAS;IACpC,CAAC,MACI;MACD;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;MACYkB,SAAS,CAACK,QAAQ,GAAG,MAAM;QACvB,MAAM;UAAET;QAAW,CAAC,GAAG,IAAI,CAAChC,OAAO;QACnCsB,WAAW,CAACoB,GAAG,CAACtD,gBAAgB,CAACiB,SAAS,EAAE,IAAI,CAACL,OAAO,EAAEyB,aAAa,CAAC,CAAC;QACzEO,UAAU,IAAIA,UAAU,CAAC,CAAC;QAC1B,IAAI,CAACW,MAAM,CAAC,CAAC;QACb,IAAI,CAACC,sBAAsB,CAAC,CAAC;MACjC,CAAC;IACL;IACA,OAAO;MACHR,SAAS;MACTjB,QAAQ;MACRF,KAAK;MACLhB,IAAI;MACJE,IAAI;MACJE,SAAS,EAAEA;IACf,CAAC;EACL;EACA,IAAIc,QAAQA,CAAA,EAAG;IACX,MAAM;MAAE0B;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT,OAAO,CAAC;IACZ,MAAM;MAAE1B;IAAS,CAAC,GAAG0B,QAAQ;IAC7B,OAAO/D,qBAAqB,CAACqC,QAAQ,CAAC;EAC1C;EACA,IAAIzC,IAAIA,CAAA,EAAG;IACP,MAAM;MAAEmE;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT,OAAO,CAAC;IACZ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9B,OAAO/D,qBAAqB,CAACsD,SAAS,CAACU,WAAW,IAAI,CAAC,CAAC;EAC5D;EACA,IAAIpE,IAAIA,CAACqE,OAAO,EAAE;IACd,MAAM;MAAEF;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9BT,SAAS,CAACU,WAAW,GAAG/D,qBAAqB,CAACgE,OAAO,CAAC;EAC1D;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,MAAM;MAAEH;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT,OAAO,CAAC;IACZ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9B,OAAOT,SAAS,CAACa,YAAY;EACjC;EACA,IAAID,KAAKA,CAACE,QAAQ,EAAE;IAChB,MAAM;MAAEL;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9BT,SAAS,CAACa,YAAY,GAAGC,QAAQ;EACrC;EACA,IAAIxC,KAAKA,CAAA,EAAG;IACR,MAAM;MAAEmC;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT,OAAO,MAAM;IACjB,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9B,OAAOT,SAAS,CAACe,SAAS;EAC9B;EACA;AACJ;AACA;AACA;EACIC,cAAcA,CAACZ,QAAQ,EAAE;IACrB,IAAI,CAAC,IAAI,CAACa,SAAS,EAAE;MACjB,IAAI,CAACd,eAAe,GAAGC,QAAQ;IACnC,CAAC,MACI;MACD,MAAM;QAAEK;MAAS,CAAC,GAAG,IAAI;MACzB,IAAI,CAACA,QAAQ,EACT,OAAOhE,IAAI;MACf,MAAM;QAAEuD;MAAU,CAAC,GAAGS,QAAQ;MAC9BT,SAAS,CAACI,QAAQ,GAAGA,QAAQ;MAC7BJ,SAAS,CAACK,QAAQ,GAAG,IAAI;IAC7B;IACA,OAAO5D,IAAI;EACf;EACAyE,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACC,SAAS,EACd;IACJ,MAAM;MAAEV;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9B,IAAIT,SAAS,CAACe,SAAS,KAAK,UAAU,EAAE;MACpC,IAAI,CAACK,qBAAqB,CAAC,CAAC;IAChC;IACApB,SAAS,CAACkB,IAAI,CAAC,CAAC;EACpB;EACAG,KAAKA,CAAA,EAAG;IACJ,MAAM;MAAEZ;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJ,MAAM;MAAET;IAAU,CAAC,GAAGS,QAAQ;IAC9BT,SAAS,CAACqB,KAAK,CAAC,CAAC;EACrB;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnC,QAAQ,CAACoB,MAAM,CAAC,CAAC;IACtB,IAAI,CAACY,SAAS,GAAG,IAAI;IACrB,IAAI,IAAI,CAAC7C,KAAK,KAAK,MAAM,EACrB;IACJ,MAAM;MAAEmC;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJ,MAAM;MAAET,SAAS;MAAE/B,SAAS;MAAEc,QAAQ;MAAElB,IAAI;MAAEE,IAAI;MAAEc;IAAM,CAAC,GAAG4B,QAAQ;IACtE,IAAIT,SAAS,CAACe,SAAS,KAAK,MAAM,IAC9Bf,SAAS,CAACe,SAAS,KAAK,UAAU,EAAE;MACpC;IACJ;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACzE,IAAI,EAAE;MACX,MAAM;QAAE4C,WAAW;QAAEW,QAAQ;QAAED,UAAU;QAAE,GAAGhC;MAAQ,CAAC,GAAG,IAAI,CAACA,OAAO;MACtE,MAAMM,eAAe,GAAG,IAAIrB,mBAAmB,CAAC;QAC5C,GAAGe,OAAO;QACVK,SAAS;QACTc,QAAQ;QACRlB,IAAI;QACJE,IAAI;QACJc,KAAK;QACLR,WAAW,EAAE;MACjB,CAAC,CAAC;MACF,MAAMkD,UAAU,GAAG5E,qBAAqB,CAAC,IAAI,CAACL,IAAI,CAAC;MACnD4C,WAAW,CAACsC,eAAe,CAACtD,eAAe,CAACS,MAAM,CAAC4C,UAAU,GAAG9D,WAAW,CAAC,CAACe,KAAK,EAAEN,eAAe,CAACS,MAAM,CAAC4C,UAAU,CAAC,CAAC/C,KAAK,EAAEf,WAAW,CAAC;IAC9I;IACA,IAAI,CAAC8C,MAAM,CAAC,CAAC;EACjB;EACAkB,QAAQA,CAAA,EAAG;IACP,MAAM;MAAEhB;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJA,QAAQ,CAACT,SAAS,CAAC0B,MAAM,CAAC,CAAC;EAC/B;EACAnB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEE;IAAS,CAAC,GAAG,IAAI;IACzB,IAAI,CAACA,QAAQ,EACT;IACJA,QAAQ,CAACT,SAAS,CAACO,MAAM,CAAC,CAAC;EAC/B;EACA,OAAOoB,QAAQA,CAAC/D,OAAO,EAAE;IACrB,MAAM;MAAEsB,WAAW;MAAEpB,IAAI;MAAE8D,WAAW;MAAEC,UAAU;MAAEC,OAAO;MAAEjE;IAAK,CAAC,GAAGD,OAAO;IAC7E,OAAQX,aAAa,CAAC,CAAC,IACnBa,IAAI,IACJP,iBAAiB,CAACwE,GAAG,CAACjE,IAAI,CAAC,IAC3BoB,WAAW,IACXA,WAAW,CAACQ,KAAK,IACjBR,WAAW,CAACQ,KAAK,CAACC,OAAO,YAAYqC,WAAW;IAChD;AACZ;AACA;AACA;IACY,CAAC9C,WAAW,CAACQ,KAAK,CAACuC,QAAQ,CAAC,CAAC,CAACpC,QAAQ,IACtC,CAAC+B,WAAW,IACZC,UAAU,KAAK,QAAQ,IACvBC,OAAO,KAAK,CAAC,IACbjE,IAAI,KAAK,SAAS;EAC1B;AACJ;AAEA,SAASmB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}