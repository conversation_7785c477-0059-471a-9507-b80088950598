{"ast": null, "code": "import { useCallback } from 'react';\nimport { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\nfunction useResetProjection() {\n  const reset = useCallback(() => {\n    const root = rootProjectionNode.current;\n    if (!root) return;\n    root.resetTree();\n  }, []);\n  return reset;\n}\nexport { useResetProjection };", "map": {"version": 3, "names": ["useCallback", "rootProjectionNode", "useResetProjection", "reset", "root", "current", "resetTree"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/projection/use-reset-projection.mjs"], "sourcesContent": ["import { useCallback } from 'react';\nimport { rootProjectionNode } from './node/HTMLProjectionNode.mjs';\n\nfunction useResetProjection() {\n    const reset = useCallback(() => {\n        const root = rootProjectionNode.current;\n        if (!root)\n            return;\n        root.resetTree();\n    }, []);\n    return reset;\n}\n\nexport { useResetProjection };\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,SAASC,kBAAkB,QAAQ,+BAA+B;AAElE,SAASC,kBAAkBA,CAAA,EAAG;EAC1B,MAAMC,KAAK,GAAGH,WAAW,CAAC,MAAM;IAC5B,MAAMI,IAAI,GAAGH,kBAAkB,CAACI,OAAO;IACvC,IAAI,CAACD,IAAI,EACL;IACJA,IAAI,CAACE,SAAS,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,KAAK;AAChB;AAEA,SAASD,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}