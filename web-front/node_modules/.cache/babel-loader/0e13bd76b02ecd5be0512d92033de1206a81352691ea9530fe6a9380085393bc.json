{"ast": null, "code": "var basePropertyOf = require('./_basePropertyOf');\n\n/** Used to map characters to HTML entities. */\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\nmodule.exports = escapeHtmlChar;", "map": {"version": 3, "names": ["basePropertyOf", "require", "htmlEscapes", "escapeHtmlChar", "module", "exports"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/lodash/_escapeHtmlChar.js"], "sourcesContent": ["var basePropertyOf = require('./_basePropertyOf');\n\n/** Used to map characters to HTML entities. */\nvar htmlEscapes = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;'\n};\n\n/**\n * Used by `_.escape` to convert characters to HTML entities.\n *\n * @private\n * @param {string} chr The matched character to escape.\n * @returns {string} Returns the escaped character.\n */\nvar escapeHtmlChar = basePropertyOf(htmlEscapes);\n\nmodule.exports = escapeHtmlChar;\n"], "mappings": "AAAA,IAAIA,cAAc,GAAGC,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA,IAAIC,WAAW,GAAG;EAChB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAGH,cAAc,CAACE,WAAW,CAAC;AAEhDE,MAAM,CAACC,OAAO,GAAGF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}