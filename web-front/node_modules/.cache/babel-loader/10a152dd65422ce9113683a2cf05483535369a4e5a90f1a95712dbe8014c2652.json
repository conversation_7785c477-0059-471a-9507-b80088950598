{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { USER_<PERSON>OGIN_REQUEST, USER_LOGIN_SUCCESS, USER_<PERSON>OGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_PASSWORD_UPDATE_SUCCESS, USER_PASSWORD_UPDATE_REQUEST, USER_PASSWORD_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDINATOR_ADD_FAIL,\n//\nCOORDINATOR_DETAIL_SUCCESS, COORDINATOR_DETAIL_REQUEST, COORDINATOR_DETAIL_FAIL,\n//\nCOORDINATOR_UPDATE_SUCCESS, COORDINATOR_UPDATE_REQUEST, COORDINATOR_UPDATE_FAIL,\n//\nUSER_UPDATE_LOGIN_SUCCESS, USER_UPDATE_LOGIN_REQUEST, USER_UPDATE_LOGIN_FAIL,\n//\nUSER_HISTORY_LOGED_SUCCESS, USER_HISTORY_LOGED_REQUEST, USER_HISTORY_LOGED_FAIL,\n//\nUSER_HISTORY_SUCCESS, USER_HISTORY_REQUEST, USER_HISTORY_FAIL,\n//\nUSER_LOGOUT_SAVE_SUCCESS, USER_LOGOUT_SAVE_REQUEST, USER_LOGOUT_SAVE_FAIL\n//\n} from \"../constants/userConstants\";\nexport const historyListCoordinatorReducer = (state = {\n  historyCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case USER_HISTORY_REQUEST:\n      return {\n        loadingHistoryCoordinator: true,\n        historyCoordinator: []\n      };\n    case USER_HISTORY_SUCCESS:\n      return {\n        loadingHistoryCoordinator: false,\n        historyCoordinator: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_HISTORY_FAIL:\n      return {\n        loadingHistoryCoordinator: false,\n        errorHistoryCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const historyListLoggedReducer = (state = {\n  historyLogged: []\n}, action) => {\n  switch (action.type) {\n    case USER_HISTORY_LOGED_REQUEST:\n      return {\n        loadingHistoryLogged: true,\n        historyLogged: []\n      };\n    case USER_HISTORY_LOGED_SUCCESS:\n      return {\n        loadingHistoryLogged: false,\n        historyLogged: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_HISTORY_LOGED_FAIL:\n      return {\n        loadingHistoryLogged: false,\n        errorHistoryLogged: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateLastLoginUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_UPDATE_LOGIN_REQUEST:\n      return {\n        loadingUpdateLastLogin: true\n      };\n    case USER_UPDATE_LOGIN_SUCCESS:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: true\n      };\n    case USER_UPDATE_LOGIN_FAIL:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: false,\n        errorUpdateLastLogin: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_UPDATE_REQUEST:\n      return {\n        loadingCoordinatorUpdate: true\n      };\n    case COORDINATOR_UPDATE_SUCCESS:\n      toast.success(\"This Coordinator has been updated successfully.\");\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: true\n      };\n    case COORDINATOR_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: false,\n        errorCoordinatorUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCoordinatorReducer = (state = {\n  coordinatorInfo: {}\n}, action) => {\n  switch (action.type) {\n    case COORDINATOR_DETAIL_REQUEST:\n      return {\n        loadingCoordinatorInfo: true\n      };\n    case COORDINATOR_DETAIL_SUCCESS:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: true,\n        coordinatorInfo: action.payload.coordinator\n      };\n    case COORDINATOR_DETAIL_FAIL:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: false,\n        errorCoordinatorInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updatePasswordUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PASSWORD_UPDATE_REQUEST:\n      return {\n        loadingUserPasswordUpdate: true\n      };\n    case USER_PASSWORD_UPDATE_SUCCESS:\n      toast.success(\"Your password has been successfully updated\");\n      return {\n        loadingUserPasswordUpdate: false,\n        successUserPasswordUpdate: true\n      };\n    case USER_PASSWORD_UPDATE_FAIL:\n      return {\n        loadingUserPasswordUpdate: false,\n        errorUserPasswordUpdate: action.payload,\n        successUserPasswordUpdate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return {\n        loadingCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const coordinatorsListReducer = (state = {\n  coordinators: []\n}, action) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCoordinators: true,\n        coordinators: []\n      };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COORDINATOR_LIST_FAIL:\n      return {\n        loadingCoordinators: false,\n        errorCoordinators: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return {\n        loadingUserDelete: true\n      };\n    case USER_DELETE_SUCCESS:\n      toast.success(\"This Coordinator has been successfully deleted.\");\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false\n      };\n    default:\n      return state;\n  }\n};\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return {\n        loadingUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      toast.success(\"Your profile has been successfully updated\");\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUserProfileUpdate: action.payload,\n        successUserProfileUpdate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const getProfileUserReducer = (state = {\n  userProfile: []\n}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return {\n        loadingUserProfile: true\n      };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return {\n        loadingUserAdd: true\n      };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const usersListReducer = (state = {\n  users: []\n}, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return {\n        loadingUsers: true,\n        users: []\n      };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_LIST_FAIL:\n      return {\n        loadingUsers: false,\n        errorUsers: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return {\n        loading: true\n      };\n    case USER_LOGIN_SUCCESS:\n      return {\n        loading: false,\n        userInfo: action.payload\n      };\n    case USER_LOGIN_FAIL:\n      return {\n        loading: false,\n        error: action.payload\n      };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};\nexport const logoutSavedUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGOUT_SAVE_REQUEST:\n      return {\n        loadingUserLogout: true\n      };\n    case USER_LOGOUT_SAVE_SUCCESS:\n      toast.success(\"You are has been logout successfully\");\n      return {\n        loadingUserLogout: false,\n        successUserLogout: true\n      };\n    case USER_LOGOUT_SAVE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserLogout: false,\n        successUserLogout: false,\n        errorUserLogout: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_SUCCESS", "USER_UPDATE_LOGIN_REQUEST", "USER_UPDATE_LOGIN_FAIL", "USER_HISTORY_LOGED_SUCCESS", "USER_HISTORY_LOGED_REQUEST", "USER_HISTORY_LOGED_FAIL", "USER_HISTORY_SUCCESS", "USER_HISTORY_REQUEST", "USER_HISTORY_FAIL", "USER_LOGOUT_SAVE_SUCCESS", "USER_LOGOUT_SAVE_REQUEST", "USER_LOGOUT_SAVE_FAIL", "historyListCoordinatorReducer", "state", "historyCoordinator", "action", "type", "loadingHistoryCoordinator", "payload", "historys", "pages", "page", "errorHistoryCoordinator", "historyListLoggedReducer", "historyLogged", "loadingHistoryLogged", "errorHistoryLogged", "updateLastLoginUserReducer", "loadingUpdateLastLogin", "successUpdateLastLogin", "errorUpdateLastLogin", "updateCoordinatorReducer", "loadingCoordinatorUpdate", "success", "successCoordinatorUpdate", "error", "errorCoordinatorUpdate", "detailCoordinatorReducer", "coordinatorInfo", "loadingCoordinatorInfo", "successCoordinatorInfo", "coordinator", "errorCoordinatorInfo", "updatePasswordUserReducer", "loadingUserPasswordUpdate", "successUserPasswordUpdate", "errorUserPasswordUpdate", "createCoordinatorReducer", "loadingCoordinatorAdd", "successCoordinatorAdd", "errorCoordinatorAdd", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinators", "loadingCoordinators", "users", "errorCoordinators", "deleteUserReducer", "loadingUserDelete", "successUserDelete", "errorUsersDelete", "updateProfileUserReducer", "loadingUserProfileUpdate", "successUserProfileUpdate", "errorUserProfileUpdate", "getProfileUserReducer", "userProfile", "loadingUserProfile", "profile", "successUserProfile", "errorUserProfile", "createNewUserReducer", "loadingUserAdd", "successUserAdd", "errorUserAdd", "usersListReducer", "loadingUsers", "errorUsers", "userLoginReducer", "loading", "userInfo", "logoutSavedUserReducer", "loadingUserLogout", "successUserLogout", "errorUserLogout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/userReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  USER_<PERSON>OGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_<PERSON>OGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_PASSWORD_UPDATE_SUCCESS,\n  USER_PASSWORD_UPDATE_REQUEST,\n  USER_PASSWORD_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATOR_ADD_FAIL,\n  //\n  COORDINATOR_DETAIL_SUCCESS,\n  COORDINATOR_DETAIL_REQUEST,\n  COORDINATOR_DETAIL_FAIL,\n  //\n  COORDINATOR_UPDATE_SUCCESS,\n  COORDINATOR_UPDATE_REQUEST,\n  COORDINATOR_UPDATE_FAIL,\n  //\n  USER_UPDATE_LOGIN_SUCCESS,\n  USER_UPDATE_LOGIN_REQUEST,\n  USER_UPDATE_LOGIN_FAIL,\n  //\n  USER_HISTORY_LOGED_SUCCESS,\n  USER_HISTORY_LOGED_REQUEST,\n  USER_HISTORY_LOGED_FAIL,\n  //\n  USER_HISTORY_SUCCESS,\n  USER_HISTORY_REQUEST,\n  USER_HISTORY_FAIL,\n  //\n  USER_LOGOUT_SAVE_SUCCESS,\n  USER_LOGOUT_SAVE_REQUEST,\n  USER_LOGOUT_SAVE_FAIL,\n  //\n} from \"../constants/userConstants\";\n\nexport const historyListCoordinatorReducer = (\n  state = { historyCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case USER_HISTORY_REQUEST:\n      return { loadingHistoryCoordinator: true, historyCoordinator: [] };\n    case USER_HISTORY_SUCCESS:\n      return {\n        loadingHistoryCoordinator: false,\n        historyCoordinator: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_HISTORY_FAIL:\n      return {\n        loadingHistoryCoordinator: false,\n        errorHistoryCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const historyListLoggedReducer = (\n  state = { historyLogged: [] },\n  action\n) => {\n  switch (action.type) {\n    case USER_HISTORY_LOGED_REQUEST:\n      return { loadingHistoryLogged: true, historyLogged: [] };\n    case USER_HISTORY_LOGED_SUCCESS:\n      return {\n        loadingHistoryLogged: false,\n        historyLogged: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_HISTORY_LOGED_FAIL:\n      return {\n        loadingHistoryLogged: false,\n        errorHistoryLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateLastLoginUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_UPDATE_LOGIN_REQUEST:\n      return { loadingUpdateLastLogin: true };\n    case USER_UPDATE_LOGIN_SUCCESS:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: true,\n      };\n    case USER_UPDATE_LOGIN_FAIL:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: false,\n        errorUpdateLastLogin: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_UPDATE_REQUEST:\n      return { loadingCoordinatorUpdate: true };\n    case COORDINATOR_UPDATE_SUCCESS:\n      toast.success(\"This Coordinator has been updated successfully.\");\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: true,\n      };\n    case COORDINATOR_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: false,\n        errorCoordinatorUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCoordinatorReducer = (\n  state = { coordinatorInfo: {} },\n  action\n) => {\n  switch (action.type) {\n    case COORDINATOR_DETAIL_REQUEST:\n      return { loadingCoordinatorInfo: true };\n    case COORDINATOR_DETAIL_SUCCESS:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: true,\n        coordinatorInfo: action.payload.coordinator,\n      };\n    case COORDINATOR_DETAIL_FAIL:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: false,\n        errorCoordinatorInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updatePasswordUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PASSWORD_UPDATE_REQUEST:\n      return { loadingUserPasswordUpdate: true };\n    case USER_PASSWORD_UPDATE_SUCCESS:\n      toast.success(\"Your password has been successfully updated\");\n      return {\n        loadingUserPasswordUpdate: false,\n        successUserPasswordUpdate: true,\n      };\n    case USER_PASSWORD_UPDATE_FAIL:\n      return {\n        loadingUserPasswordUpdate: false,\n        errorUserPasswordUpdate: action.payload,\n        successUserPasswordUpdate: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return { loadingCoordinatorAdd: true };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true,\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const coordinatorsListReducer = (\n  state = { coordinators: [] },\n  action\n) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return { loadingCoordinators: true, coordinators: [] };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COORDINATOR_LIST_FAIL:\n      return { loadingCoordinators: false, errorCoordinators: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return { loadingUserDelete: true };\n    case USER_DELETE_SUCCESS:\n      toast.success(\"This Coordinator has been successfully deleted.\");\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true,\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return { loadingUserProfileUpdate: true };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      toast.success(\"Your profile has been successfully updated\");\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true,\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUserProfileUpdate: action.payload,\n        successUserProfileUpdate: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const getProfileUserReducer = (state = { userProfile: [] }, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return { loadingUserProfile: true };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true,\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return { loadingUserAdd: true };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true,\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const usersListReducer = (state = { users: [] }, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return { loadingUsers: true, users: [] };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_LIST_FAIL:\n      return { loadingUsers: false, errorUsers: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return { loading: true };\n    case USER_LOGIN_SUCCESS:\n      return { loading: false, userInfo: action.payload };\n    case USER_LOGIN_FAIL:\n      return { loading: false, error: action.payload };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};\n\nexport const logoutSavedUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGOUT_SAVE_REQUEST:\n      return { loadingUserLogout: true };\n    case USER_LOGOUT_SAVE_SUCCESS:\n      toast.success(\"You are has been logout successfully\");\n      return {\n        loadingUserLogout: false,\n        successUserLogout: true,\n      };\n    case USER_LOGOUT_SAVE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserLogout: false,\n        successUserLogout: false,\n        errorUserLogout: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,6BAA6B,GAAGA,CAC3CC,KAAK,GAAG;EAAEC,kBAAkB,EAAE;AAAG,CAAC,EAClCC,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,oBAAoB;MACvB,OAAO;QAAEU,yBAAyB,EAAE,IAAI;QAAEH,kBAAkB,EAAE;MAAG,CAAC;IACpE,KAAKR,oBAAoB;MACvB,OAAO;QACLW,yBAAyB,EAAE,KAAK;QAChCH,kBAAkB,EAAEC,MAAM,CAACG,OAAO,CAACC,QAAQ;QAC3CC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAKb,iBAAiB;MACpB,OAAO;QACLS,yBAAyB,EAAE,KAAK;QAChCK,uBAAuB,EAAEP,MAAM,CAACG;MAClC,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMU,wBAAwB,GAAGA,CACtCV,KAAK,GAAG;EAAEW,aAAa,EAAE;AAAG,CAAC,EAC7BT,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,0BAA0B;MAC7B,OAAO;QAAEqB,oBAAoB,EAAE,IAAI;QAAED,aAAa,EAAE;MAAG,CAAC;IAC1D,KAAKrB,0BAA0B;MAC7B,OAAO;QACLsB,oBAAoB,EAAE,KAAK;QAC3BD,aAAa,EAAET,MAAM,CAACG,OAAO,CAACC,QAAQ;QACtCC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAKhB,uBAAuB;MAC1B,OAAO;QACLoB,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAEX,MAAM,CAACG;MAC7B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMc,0BAA0B,GAAGA,CAACd,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,yBAAyB;MAC5B,OAAO;QAAE2B,sBAAsB,EAAE;MAAK,CAAC;IACzC,KAAK5B,yBAAyB;MAC5B,OAAO;QACL4B,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE;MAC1B,CAAC;IACH,KAAK3B,sBAAsB;MACzB,OAAO;QACL0B,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,KAAK;QAC7BC,oBAAoB,EAAEf,MAAM,CAACG;MAC/B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkB,wBAAwB,GAAGA,CAAClB,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,0BAA0B;MAC7B,OAAO;QAAEkC,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAKnC,0BAA0B;MAC7BhC,KAAK,CAACoE,OAAO,CAAC,iDAAiD,CAAC;MAChE,OAAO;QACLD,wBAAwB,EAAE,KAAK;QAC/BE,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAKnC,uBAAuB;MAC1BlC,KAAK,CAACsE,KAAK,CAACpB,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACLc,wBAAwB,EAAE,KAAK;QAC/BE,wBAAwB,EAAE,KAAK;QAC/BE,sBAAsB,EAAErB,MAAM,CAACG;MACjC,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMwB,wBAAwB,GAAGA,CACtCxB,KAAK,GAAG;EAAEyB,eAAe,EAAE,CAAC;AAAE,CAAC,EAC/BvB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKrB,0BAA0B;MAC7B,OAAO;QAAE4C,sBAAsB,EAAE;MAAK,CAAC;IACzC,KAAK7C,0BAA0B;MAC7B,OAAO;QACL6C,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,IAAI;QAC5BF,eAAe,EAAEvB,MAAM,CAACG,OAAO,CAACuB;MAClC,CAAC;IACH,KAAK7C,uBAAuB;MAC1B,OAAO;QACL2C,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,KAAK;QAC7BE,oBAAoB,EAAE3B,MAAM,CAACG;MAC/B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM8B,yBAAyB,GAAGA,CAAC9B,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC/D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKjC,4BAA4B;MAC/B,OAAO;QAAE6D,yBAAyB,EAAE;MAAK,CAAC;IAC5C,KAAK9D,4BAA4B;MAC/BjB,KAAK,CAACoE,OAAO,CAAC,6CAA6C,CAAC;MAC5D,OAAO;QACLW,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE;MAC7B,CAAC;IACH,KAAK7D,yBAAyB;MAC5B,OAAO;QACL4D,yBAAyB,EAAE,KAAK;QAChCE,uBAAuB,EAAE/B,MAAM,CAACG,OAAO;QACvC2B,yBAAyB,EAAE;MAC7B,CAAC;IACH;MACE,OAAOhC,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkC,wBAAwB,GAAGA,CAAClC,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKxB,uBAAuB;MAC1B,OAAO;QAAEwD,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKzD,uBAAuB;MAC1B1B,KAAK,CAACoE,OAAO,CAAC,8CAA8C,CAAC;MAC7D,OAAO;QACLe,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAKxD,oBAAoB;MACvB5B,KAAK,CAACsE,KAAK,CAACpB,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACL8B,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAEnC,MAAM,CAACG;MAC9B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsC,uBAAuB,GAAGA,CACrCtC,KAAK,GAAG;EAAEuC,YAAY,EAAE;AAAG,CAAC,EAC5BrC,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK3B,wBAAwB;MAC3B,OAAO;QAAEgE,mBAAmB,EAAE,IAAI;QAAED,YAAY,EAAE;MAAG,CAAC;IACxD,KAAKhE,wBAAwB;MAC3B,OAAO;QACLiE,mBAAmB,EAAE,KAAK;QAC1BD,YAAY,EAAErC,MAAM,CAACG,OAAO,CAACoC,KAAK;QAClClC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAK/B,qBAAqB;MACxB,OAAO;QAAE+D,mBAAmB,EAAE,KAAK;QAAEE,iBAAiB,EAAExC,MAAM,CAACG;MAAQ,CAAC;IAC1E;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM2C,iBAAiB,GAAGA,CAAC3C,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK9B,mBAAmB;MACtB,OAAO;QAAEuE,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKxE,mBAAmB;MACtBpB,KAAK,CAACoE,OAAO,CAAC,iDAAiD,CAAC;MAChE,OAAO;QACLwB,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKvE,gBAAgB;MACnB,OAAO;QACLsE,iBAAiB,EAAE,KAAK;QACxBE,gBAAgB,EAAE5C,MAAM,CAACG,OAAO;QAChCwC,iBAAiB,EAAE;MACrB,CAAC;IACH;MACE,OAAO7C,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM+C,wBAAwB,GAAGA,CAAC/C,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKpC,2BAA2B;MAC9B,OAAO;QAAEiF,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAKlF,2BAA2B;MAC9Bd,KAAK,CAACoE,OAAO,CAAC,4CAA4C,CAAC;MAC3D,OAAO;QACL4B,wBAAwB,EAAE,KAAK;QAC/BC,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAKjF,wBAAwB;MAC3B,OAAO;QACLgF,wBAAwB,EAAE,KAAK;QAC/BE,sBAAsB,EAAEhD,MAAM,CAACG,OAAO;QACtC4C,wBAAwB,EAAE;MAC5B,CAAC;IACH;MACE,OAAOjD,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMmD,qBAAqB,GAAGA,CAACnD,KAAK,GAAG;EAAEoD,WAAW,EAAE;AAAG,CAAC,EAAElD,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKvC,oBAAoB;MACvB,OAAO;QAAEyF,kBAAkB,EAAE;MAAK,CAAC;IACrC,KAAK1F,oBAAoB;MACvB,OAAO;QACL0F,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAElD,MAAM,CAACG,OAAO,CAACiD,OAAO;QACnCC,kBAAkB,EAAE;MACtB,CAAC;IACH,KAAK1F,iBAAiB;MACpB,OAAO;QACLwF,kBAAkB,EAAE,KAAK;QACzBG,gBAAgB,EAAEtD,MAAM,CAACG,OAAO;QAChCkD,kBAAkB,EAAE;MACtB,CAAC;IACH;MACE,OAAOvD,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMyD,oBAAoB,GAAGA,CAACzD,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK7C,gBAAgB;MACnB,OAAO;QAAEoG,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKrG,gBAAgB;MACnBL,KAAK,CAACoE,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACLsC,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKpG,aAAa;MAChBP,KAAK,CAACsE,KAAK,CAACpB,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACLqD,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE1D,MAAM,CAACG;MACvB,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM6D,gBAAgB,GAAGA,CAAC7D,KAAK,GAAG;EAAEyC,KAAK,EAAE;AAAG,CAAC,EAAEvC,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK1C,iBAAiB;MACpB,OAAO;QAAEqG,YAAY,EAAE,IAAI;QAAErB,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKjF,iBAAiB;MACpB,OAAO;QACLsG,YAAY,EAAE,KAAK;QACnBrB,KAAK,EAAEvC,MAAM,CAACG,OAAO,CAACoC,KAAK;QAC3BlC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAK9C,cAAc;MACjB,OAAO;QAAEoG,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAE7D,MAAM,CAACG;MAAQ,CAAC;IAC5D;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgE,gBAAgB,GAAGA,CAAChE,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACtD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlD,kBAAkB;MACrB,OAAO;QAAEgH,OAAO,EAAE;MAAK,CAAC;IAC1B,KAAK/G,kBAAkB;MACrB,OAAO;QAAE+G,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAEhE,MAAM,CAACG;MAAQ,CAAC;IACrD,KAAKlD,eAAe;MAClB,OAAO;QAAE8G,OAAO,EAAE,KAAK;QAAE3C,KAAK,EAAEpB,MAAM,CAACG;MAAQ,CAAC;IAClD,KAAKjD,WAAW;MACd,OAAO,CAAC,CAAC;IACX;MACE,OAAO4C,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMmE,sBAAsB,GAAGA,CAACnE,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,wBAAwB;MAC3B,OAAO;QAAEuE,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKxE,wBAAwB;MAC3B5C,KAAK,CAACoE,OAAO,CAAC,sCAAsC,CAAC;MACrD,OAAO;QACLgD,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKvE,qBAAqB;MACxB9C,KAAK,CAACsE,KAAK,CAACpB,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACL+D,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEpE,MAAM,CAACG;MAC1B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}