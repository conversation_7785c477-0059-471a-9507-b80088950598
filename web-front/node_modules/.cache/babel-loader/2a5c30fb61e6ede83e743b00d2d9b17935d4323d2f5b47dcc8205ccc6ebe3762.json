{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersListEditCase}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase}from\"../../redux/actions/caseActions\";import LoadingSpinner from\"../../components/LoadingSpinner\";import GoogleComponent from\"react-google-autocomplete\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{insurancesListDashboard}from\"../../redux/actions/insuranceActions\";import{coordinatorsListDashboard}from\"../../redux/actions/userActions\";import{COUNTRIES,CURRENCYITEMS}from\"../../constants\";// Country to Currency mapping - using exact country names from COUNTRIES constant\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const COUNTRY_CURRENCY_MAP={\"Morocco\":\"MAD\",\"United States\":\"USD\",\"Canada\":\"CAD\",\"United Kingdom\":\"GBP\",\"France\":\"EUR\",\"Germany\":\"EUR\",\"Spain\":\"EUR\",\"Italy\":\"EUR\",\"Netherlands\":\"EUR\",\"Belgium\":\"EUR\",\"Portugal\":\"EUR\",\"Greece\":\"EUR\",\"Austria\":\"EUR\",\"Ireland\":\"EUR\",\"Finland\":\"EUR\",\"Luxembourg\":\"EUR\",\"Estonia\":\"EUR\",\"Slovenia\":\"EUR\",\"Slovakia\":\"EUR\",\"Malta\":\"EUR\",\"Cyprus\":\"EUR\",\"Lithuania\":\"EUR\",\"Latvia\":\"EUR\",\"Japan\":\"JPY\",\"China\":\"CNY\",\"India\":\"INR\",\"Australia\":\"AUD\",\"New Zealand\":\"NZD\",\"South Africa\":\"ZAR\",\"Brazil\":\"BRL\",\"Mexico\":\"MXN\",\"Argentina\":\"ARS\",\"Chile\":\"CLP\",\"Colombia\":\"COP\",\"Peru\":\"PEN\",\"Russia\":\"RUB\",\"Turkey\":\"TRY\",\"Egypt\":\"EGP\",\"Saudi Arabia\":\"SAR\",\"United Arab Emirates\":\"AED\",\"Qatar\":\"QAR\",\"Kuwait\":\"KWD\",\"Bahrain\":\"BHD\",\"Oman\":\"OMR\",\"Jordan\":\"JOD\",\"Lebanon\":\"LBP\",\"Israel\":\"ILS\",\"South Korea\":\"KRW\",\"Thailand\":\"THB\",\"Malaysia\":\"MYR\",\"Singapore\":\"SGD\",\"Indonesia\":\"IDR\",\"Philippines\":\"PHP\",\"Vietnam\":\"VND\",\"Pakistan\":\"PKR\",\"Bangladesh\":\"BDT\",\"Sri Lanka\":\"LKR\",\"Nepal\":\"NPR\",\"Switzerland\":\"CHF\",\"Norway\":\"NOK\",\"Sweden\":\"SEK\",\"Denmark\":\"DKK\",\"Iceland\":\"ISK\",\"Poland\":\"PLN\",\"Czech Republic\":\"CZK\",\"Hungary\":\"HUF\",\"Romania\":\"RON\",\"Bulgaria\":\"BGN\",\"Croatia\":\"HRK\",\"Serbia\":\"RSD\",\"Ukraine\":\"UAH\",\"Belarus\":\"BYN\",\"Algeria\":\"DZD\",\"Tunisia\":\"TND\",\"Libya\":\"LYD\",\"Sudan\":\"SDG\",\"Ethiopia\":\"ETB\",\"Kenya\":\"KES\",\"Uganda\":\"UGX\",\"Tanzania\":\"TZS\",\"Rwanda\":\"RWF\",\"Ghana\":\"GHS\",\"Nigeria\":\"NGN\",\"Senegal\":\"XOF\",\"Ivory Coast\":\"XOF\",\"Mali\":\"XOF\",\"Burkina Faso\":\"XOF\",\"Niger\":\"XOF\",\"Guinea\":\"GNF\",\"Sierra Leone\":\"SLL\",\"Liberia\":\"LRD\",\"Cameroon\":\"XAF\",\"Chad\":\"XAF\",\"Central African Republic\":\"XAF\",\"Democratic Republic of the Congo\":\"CDF\",\"Republic of the Congo\":\"XAF\",\"Gabon\":\"XAF\",\"Angola\":\"AOA\",\"Zambia\":\"ZMK\",\"Zimbabwe\":\"ZWL\",\"Botswana\":\"BWP\",\"Namibia\":\"NAD\",\"Lesotho\":\"LSL\",\"Swaziland\":\"SZL\",\"Mozambique\":\"MZN\",\"Madagascar\":\"MGA\",\"Mauritius\":\"MUR\",\"Seychelles\":\"SCR\",\"Afghanistan\":\"AFN\",\"Albania\":\"ALL\",\"Armenia\":\"AMD\",\"Azerbaijan\":\"AZN\",\"Brunei\":\"BND\",\"Cambodia\":\"KHR\",\"Cape Verde\":\"CVE\",\"Comoros\":\"KMF\",\"Costa Rica\":\"CRC\",\"Cuba\":\"CUP\",\"Dominican Republic\":\"DOP\",\"Ecuador\":\"USD\",\"El Salvador\":\"USD\",\"Eritrea\":\"ERN\",\"Fiji\":\"FJD\",\"Georgia\":\"GEL\",\"Guatemala\":\"GTQ\",\"Guinea-Bissau\":\"XOF\",\"Guyana\":\"GYD\",\"Haiti\":\"HTG\",\"Honduras\":\"HNL\",\"Hong Kong\":\"HKD\",\"Iran\":\"IRR\",\"Iraq\":\"IQD\",\"Jamaica\":\"JMD\",\"Kazakhstan\":\"KZT\",\"Kyrgyzstan\":\"KGS\",\"Laos\":\"LAK\",\"Macau\":\"MOP\",\"Macedonia\":\"MKD\",\"Malawi\":\"MWK\",\"Maldives\":\"MVR\",\"Marshall Islands\":\"USD\",\"Mauritania\":\"MRU\",\"Micronesia\":\"USD\",\"Moldova\":\"MDL\",\"Monaco\":\"EUR\",\"Mongolia\":\"MNT\",\"Montenegro\":\"EUR\",\"Myanmar\":\"MMK\",\"Nicaragua\":\"NIO\",\"North Korea\":\"KPW\",\"Panama\":\"PAB\",\"Papua New Guinea\":\"PGK\",\"Paraguay\":\"PYG\",\"Puerto Rico\":\"USD\",\"Samoa\":\"WST\",\"San Marino\":\"EUR\",\"Sao Tome and Principe\":\"STN\",\"Somalia\":\"SOS\",\"South Sudan\":\"SSP\",\"Suriname\":\"SRD\",\"Syria\":\"SYP\",\"Taiwan\":\"TWD\",\"Tajikistan\":\"TJS\",\"Togo\":\"XOF\",\"Tonga\":\"TOP\",\"Trinidad and Tobago\":\"TTD\",\"Turkmenistan\":\"TMT\",\"Tuvalu\":\"AUD\",\"Uruguay\":\"UYU\",\"Uzbekistan\":\"UZS\",\"Vanuatu\":\"VUV\",\"Venezuela\":\"VES\",\"Western Sahara\":\"MAD\",\"Yemen\":\"YER\"};const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){var _parseInt;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const section=searchParams.get(\"section\")||0;//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorId,setCoordinatorId]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[providerServices,setProviderServices]=useState([]);const[providerMultiSelect,setProviderMultiSelect]=useState([]);const[providerMultiSelectDelete,setProviderMultiSelectDelete]=useState([]);const[assistanceMultiSelect,setAssistanceMultiSelect]=useState([]);const[assistanceMultiSelectDelete,setAssistanceMultiSelectDelete]=useState([]);const[providerMultiSelectLast,setProviderMultiSelectLast]=useState([]);const[assistanceMultiSelectLast,setAssistanceMultiSelectLast]=useState([]);const[providerService,setProviderService]=useState(\"\");const[providerServiceError,setProviderServiceError]=useState(\"\");const[caseDate,setCaseDate]=useState(new Date().toISOString().split(\"T\")[0]);const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseTypeItem,setCaseTypeItem]=useState(\"\");const[caseTypeItemError,setCaseTypeItemError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");const[isPay,setIsPay]=useState(false);const[currencyCode,setCurrencyCode]=useState(\"\");const[currencyCodeError,setCurrencyCodeError]=useState(\"\");const[priceTotal,setPriceTotal]=useState(0);const[priceTotalError,setPriceTotalError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[coordinatStatusList,setCoordinatStatusList]=useState([]);const[coordinatStatusListError,setCoordinatStatusListError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[startDate,setStartDate]=useState(\"\");const[startDateError,setStartDateError]=useState(\"\");const[endDate,setEndDate]=useState(\"\");const[endDateError,setEndDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerDate,setProviderDate]=useState(\"\");const[providerDateError,setProviderDateError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState((_parseInt=parseInt(section))!==null&&_parseInt!==void 0?_parseInt:0);const[isLoading,setIsLoading]=useState(true);const[shouldNavigateToFinalStep,setShouldNavigateToFinalStep]=useState(false);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;// Debug log when providers data changes\nuseEffect(()=>{if(providers&&providers.length>0){console.log(\"Providers data loaded successfully:\",providers.length);}},[providers]);const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;// Update coordinator when coordinators are loaded\nuseEffect(()=>{console.log(\"Coordinator useEffect triggered\");if(coordinators&&coordinators.length>0&&coordinatorId){console.log(\"Trying to find coordinator with ID:\",coordinatorId);// Try to find coordinator by ID (as string to ensure type matching)\nconst foundCoordinator=coordinators.find(item=>String(item.id)===String(coordinatorId));if(foundCoordinator){console.log(\"Found coordinator:\",foundCoordinator.full_name);// Set the coordinator with a slight delay to ensure the UI updates\nsetTimeout(()=>{setCoordinator({value:foundCoordinator.id,label:foundCoordinator.full_name});// Force a re-render by updating the loading state\nsetIsLoading(false);},100);}else{console.log(\"Coordinator not found in the list\");// If coordinator not found, try to find it by name\nconst coordinatorById=coordinators.find(item=>item.id===coordinatorId);if(coordinatorById){console.log(\"Found coordinator by direct ID comparison:\",coordinatorById.full_name);setCoordinator({value:coordinatorById.id,label:coordinatorById.full_name});}}}},[coordinators,coordinatorId]);const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// Set loading state to true when starting to fetch data\nsetIsLoading(true);// Load all required data at once with optimized actions\ndispatch(coordinatorsListDashboard(\"0\"));dispatch(providersListEditCase(\"0\"));dispatch(insurancesListDashboard(\"0\"));dispatch(detailCase(id));// Set a maximum timeout for the loading indicator (30 seconds) as a fallback\nconst timeoutId=setTimeout(()=>{setIsLoading(false);console.log(\"Maximum loading time reached, hiding loading indicator\");},6000);// Clean up the timeout when the component unmounts\nreturn()=>clearTimeout(timeoutId);}},[navigate,userInfo,dispatch,id]);useEffect(()=>{if(successCaseUpdate){if(shouldNavigateToFinalStep){setStepSelect(5);setShouldNavigateToFinalStep(false);// Reset the flag\n}setIsLoading(false);}},[successCaseUpdate,shouldNavigateToFinalStep]);// Set loading state when case update is in progress\nuseEffect(()=>{if(loadingCaseUpdate){setIsLoading(true);}},[loadingCaseUpdate]);// Update loading state based on data loading status\nuseEffect(()=>{// Check if essential data is loaded\nif(!loadingProviders&&!loadingCaseInfo&&providers&&providers.length>0&&caseInfo){// Hide loading indicator as soon as we have the essential data\nsetIsLoading(false);}else if(loadingCaseUpdate){// Show loading during case update\nsetIsLoading(true);}},[loadingProviders,loadingCaseInfo,loadingCaseUpdate,providers,caseInfo]);useEffect(()=>{// Only proceed if caseInfo is available\nif(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$currency_pr,_caseInfo$price_tatal,_caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$case_status,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$start_date,_caseInfo$end_date,_caseInfo$case_type_i,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_n,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");const patientCountry=(_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}const patientCurrency=(_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){setCurrencyCode({value:foundCurrency.code,label:foundCurrency.name!==\"\"?foundCurrency.name+\" (\"+foundCurrency.code+\") \"||\"\":\"\"});}else{setCurrencyCode(\"\");}setIsPay(caseInfo.is_pay);setPriceTotal((_caseInfo$price_tatal=caseInfo.price_tatal)!==null&&_caseInfo$price_tatal!==void 0?_caseInfo$price_tatal:0);// Store coordinator ID for later use\nif(caseInfo.coordinator_user){var _caseInfo$coordinator,_caseInfo$coordinator2;const initialCoordinator=(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\";console.log(\"Setting coordinator ID from caseInfo:\",initialCoordinator);console.log(\"Coordinator user from caseInfo:\",caseInfo.coordinator_user);// Set coordinator ID with a slight delay to ensure it's properly updated\nsetTimeout(()=>{setCoordinatorId(initialCoordinator);console.log(\"CoordinatorId has been set to:\",initialCoordinator);},50);}setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nconst statuses=(caseInfo===null||caseInfo===void 0?void 0:(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map(status=>status===null||status===void 0?void 0:status.status_coordination))||[];// Default to an empty array if case_status is undefined or not an array\nsetCoordinatStatusList(statuses);//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setStartDate((_caseInfo$start_date=caseInfo.start_date)!==null&&_caseInfo$start_date!==void 0?_caseInfo$start_date:\"\");setEndDate((_caseInfo$end_date=caseInfo.end_date)!==null&&_caseInfo$end_date!==void 0?_caseInfo$end_date:\"\");setCaseTypeItem((_caseInfo$case_type_i=caseInfo.case_type_item)!==null&&_caseInfo$case_type_i!==void 0?_caseInfo$case_type_i:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;var initialProvider=(_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){setProviderName({value:foundProvider.id,label:foundProvider.full_name});}else{setProviderName(\"\");}}if(caseInfo.provider_services){var _caseInfo$provider_se;setProviderMultiSelectLast((_caseInfo$provider_se=caseInfo.provider_services)!==null&&_caseInfo$provider_se!==void 0?_caseInfo$provider_se:[]);}if(caseInfo.assistance_services){var _caseInfo$assistance_;setAssistanceMultiSelectLast((_caseInfo$assistance_=caseInfo.assistance_services)!==null&&_caseInfo$assistance_!==void 0?_caseInfo$assistance_:[]);}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;var initialInsurance=(_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\";var foundInsurance=insurances===null||insurances===void 0?void 0:insurances.find(item=>item.id===initialInsurance);if(foundInsurance){console.log(\"here 2\");setInsuranceCompany({value:foundInsurance.id,label:foundInsurance.assurance_name||\"\"});}else{console.log(\"here 3\");setInsuranceCompany({value:\"\",label:\"\"});}}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInsuranceNumber((_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);// Function to update case without advancing to next step\nconst handleUpdateCurrentStep=async()=>{try{var _coordinator$value,_providerName$value,_insuranceCompany$val,_currencyCode$value;let isValid=true;// Step-specific validation based on current step\nif(stepSelect===0){// Step 1: General Information validation\nsetFirstNameError(\"\");setLastNameError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCityError(\"\");setCountryError(\"\");setCaseDateError(\"\");setCaseTypeError(\"\");setCaseDescriptionError(\"\");setPriceTotalError(\"\");setCurrencyCodeError(\"\");setCoordinatorError(\"\");setBirthDateError(\"\");// Required field validations for Step 1\nif(!firstName||firstName.trim()===\"\"){setFirstNameError(\"First name is required.\");isValid=false;}if(!lastName||lastName.trim()===\"\"){setLastNameError(\"Last name is required.\");isValid=false;}if(!phone||phone.trim()===\"\"){setPhoneError(\"Phone number is required.\");isValid=false;}else{// Phone format validation\nconst phoneRegex=/^[\\+]?[1-9][\\d]{0,15}$/;if(!phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g,''))){setPhoneError(\"Please enter a valid phone number +212....\");isValid=false;}}if(email&&email.trim()!==\"\"){const emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(email)){setEmailError(\"Please enter a valid email address.\");isValid=false;}}if(!address||address.trim()===\"\"){setAddressError(\"Address is required.\");isValid=false;}if(!city||city.trim()===\"\"){setCityError(\"City is required.\");isValid=false;}if(!country||!country.value||country.value===\"\"){setCountryError(\"Country is required.\");isValid=false;}if(!caseDate||caseDate.trim()===\"\"){setCaseDateError(\"Case date is required.\");isValid=false;}if(!caseType||caseType.trim()===\"\"){setCaseTypeError(\"Case type is required.\");isValid=false;}if(!caseDescription||caseDescription.trim()===\"\"){setCaseDescriptionError(\"Case description is required.\");isValid=false;}if(!coordinator||!coordinator.value||coordinator.value===\"\"){setCoordinatorError(\"Coordinator is required.\");isValid=false;}if(!currencyCode||!currencyCode.value||currencyCode.value===\"\"){setCurrencyCodeError(\"Currency is required.\");isValid=false;}if(!priceTotal||priceTotal===\"\"||priceTotal<0){setPriceTotalError(\"Price total is required and must be greater than 0.\");isValid=false;}// Optional field validations for Step 1\nif(birthDate&&birthDate.trim()!==\"\"){const birthDateObj=new Date(birthDate);const today=new Date();if(birthDateObj>today){setBirthDateError(\"Birth date cannot be in the future.\");isValid=false;}}}else if(stepSelect===1){// Step 2: Coordination Details validation\nsetCoordinatStatusListError(\"\");// Coordination status validation\nif(!coordinatStatusList||coordinatStatusList.length===0){setCoordinatStatusListError(\"At least one coordination status is required.\");isValid=false;}}else if(stepSelect===2){// Step 3: Medical Reports validation\n// No specific required validations for medical reports step\n// Files are optional\n}else if(stepSelect===3){// Step 4: Invoices validation\nsetInvoiceNumberError(\"\");setAmountError(\"\");// Optional field validations for Step 4\nif(invoiceNumber&&invoiceNumber.trim()!==\"\"){if(invoiceNumber.length<3){setInvoiceNumberError(\"Invoice number must be at least 3 characters.\");isValid=false;}}if(amount&&amount!==\"\"&&amount<0){setAmountError(\"Amount cannot be negative.\");isValid=false;}}else if(stepSelect===4){// Step 5: Insurance Authorization validation\nsetInsuranceNumberError(\"\");setPolicyNumberError(\"\");// Optional field validations for Step 5\nif(insuranceNumber&&insuranceNumber.trim()!==\"\"){if(insuranceNumber.length<3){setInsuranceNumberError(\"Insurance number must be at least 3 characters.\");isValid=false;}}if(policyNumber&&policyNumber.trim()!==\"\"){if(policyNumber.length<3){setPolicyNumberError(\"Policy number must be at least 3 characters.\");isValid=false;}}}// If validation fails, show error and return\nif(!isValid){toast.error(\"Please fix the validation errors before updating.\");return;}setIsLoading(true);// Map assistance items with their provider services\nconst assistanceItems=assistanceMultiSelect.map(item=>({start_date:item.start_date,end_date:item.end_date,appointment_date:item.appointment_date,service_location:item.service_location,provider_services:item.provider_services.map(providerService=>{var _providerService$prov,_providerService$prov2;return{provider:(_providerService$prov=providerService.provider)===null||_providerService$prov===void 0?void 0:_providerService$prov.id,service:(_providerService$prov2=providerService.provider_service)===null||_providerService$prov2===void 0?void 0:_providerService$prov2.id,date:providerService.provider_date};})}));const providerItems=providerMultiSelect.map(item=>{var _item$service,_item$provider;return{service:(_item$service=item.service)===null||_item$service===void 0?void 0:_item$service.id,provider:(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.id,date:item.date};});// Update case with all current data\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value=coordinator.value)!==null&&_coordinator$value!==void 0?_coordinator$value:\"\",case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val=insuranceCompany.value)!==null&&_insuranceCompany$val!==void 0?_insuranceCompany$val:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted,providers:providerItems!==null&&providerItems!==void 0?providerItems:[],assistances:assistanceItems!==null&&assistanceItems!==void 0?assistanceItems:[],providers_deleted:providerMultiSelectDelete!==null&&providerMultiSelectDelete!==void 0?providerMultiSelectDelete:[],assistance_deleted:assistanceMultiSelectDelete!==null&&assistanceMultiSelectDelete!==void 0?assistanceMultiSelectDelete:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value=currencyCode.value)!==null&&_currencyCode$value!==void 0?_currencyCode$value:\"\"}));setIsLoading(false);toast.success(\"Case updated successfully!\");}catch(error){setIsLoading(false);toast.error(\"Failed to update case. Please try again.\");}};return/*#__PURE__*/_jsxs(DefaultLayout,{children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-gray-700 font-medium\",children:\"Loading data...\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{if(stepSelect>step.index&&stepSelect!==5){setStepSelect(step.index);}},className:`flex flex-row mb-3 md:min-h-20 ${stepSelect>step.index&&stepSelect!==5?\"cursor-pointer\":\"\"} md:items-start items-center`,children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${firstNameError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${emailError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${phoneError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);// Auto-update currency based on selected country\nif(option&&option.value){// The option.value contains the country title (name)\nconst countryName=option.value;const currencyCode=COUNTRY_CURRENCY_MAP[countryName];if(currencyCode){// Find the currency option in CURRENCYITEMS\nconst currencyOption=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(currency=>currency.code===currencyCode);if(currencyOption){setCurrencyCode({value:currencyOption.code,label:currencyOption.name!==\"\"?currencyOption.name+\" (\"+currencyOption.code+\")\":currencyOption.code});// Show success message\ntoast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);}}}},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:`${country.title===\"\"?\"py-2\":\"\"} flex flex-row items-center`,children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"City \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(GoogleComponent,{apiKey:\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\",className:` outline-none border ${cityError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,onChange:v=>{setCity(v.target.value);},onPlaceSelected:place=>{if(place&&place.geometry){var _place$formatted_addr;setCity((_place$formatted_addr=place.formatted_address)!==null&&_place$formatted_addr!==void 0?_place$formatted_addr:\"\");// setCityVl(place.formatted_address ?? \"\");\n//   const latitude = place.geometry.location.lat();\n//   const longitude = place.geometry.location.lng();\n//   setLocationX(latitude ?? \"\");\n//   setLocationY(longitude ?? \"\");\n}},defaultValue:city,types:[\"city\"],language:\"en\"}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA Reference\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"text\",placeholder:\"CIA Reference\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${caseDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:` outline-none border ${caseTypeError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})]}),caseType===\"Medical\"&&/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type Item \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseTypeItem,onChange:v=>setCaseTypeItem(v.target.value),className:` outline-none border ${caseTypeItemError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-3 w-full rounded text-sm`,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type Item\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Outpatient\",children:\"Outpatient\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Inpatient\",children:\"Inpatient\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeItemError?caseTypeItemError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Currency Code\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:currencyCode,onChange:option=>{setCurrencyCode(option);console.log(option);},options:CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.map(currency=>({value:currency.code,label:currency.name!==\"\"?currency.name+\" (\"+currency.code+\") \"||\"\":\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Currency Code ...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:currencyCodeError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:currencyCodeError?currencyCodeError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Price of service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${priceTotalError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"number\",min:0,step:0.01,placeholder:\"0.00\",value:priceTotal,onChange:v=>setPriceTotal(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:priceTotalError?priceTotalError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"ispay\",id:\"ispay\",checked:isPay===true,onChange:v=>{setIsPay(true);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"ispay\",children:\"Paid\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"notpay\",id:\"notpay\",checked:isPay===false,onChange:v=>{setIsPay(false);}}),/*#__PURE__*/_jsx(\"label\",{className:\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",for:\"notpay\",children:\"Unpaid\"})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseTypeItemError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");setCurrencyCodeError(\"\");setPriceTotalError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}else if(caseType===\"Medical\"&&caseTypeItem===\"\"){setCaseTypeItemError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(currencyCode===\"\"||currencyCode.value===\"\"){setCurrencyCodeError(\"This field is required.\");check=false;}if(priceTotal===\"\"){setPriceTotalError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-wrap\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-danger\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"pending-coordination\")){setCoordinatStatusList([...coordinatStatusList,\"pending-coordination\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"pending-coordination\"));}},id:\"pending-coordination\",type:\"checkbox\",checked:coordinatStatusList.includes(\"pending-coordination\"),className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"pending-coordination\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Pending Coordination\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-m-r\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-m-r\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-m-r\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-m-r\"),id:\"coordinated-Missing-m-r\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-Missing-m-r\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing M.R.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#FFA500]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-invoice\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-invoice\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-invoice\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-invoice\"),id:\"coordinated-missing-invoice\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-invoice\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Invoice\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")){setCoordinatStatusList([...coordinatStatusList,\"waiting-for-insurance-authorization\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"waiting-for-insurance-authorization\"));}},checked:coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),id:\"waiting-for-insurance-authorization\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"waiting-for-insurance-authorization\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Waiting for Insurance Authorization\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-patient-not-seen-yet\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-patient-not-seen-yet\"));}},checked:coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),id:\"coordinated-patient-not-seen-yet\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-patient-not-seen-yet\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Patient not seen yet\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordination-fee\")){setCoordinatStatusList([...coordinatStatusList,\"coordination-fee\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordination-fee\"));}},checked:coordinatStatusList.includes(\"coordination-fee\"),id:\"coordination-fee\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordination-fee\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordination Fee\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-primary\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"coordinated-missing-payment\")){setCoordinatStatusList([...coordinatStatusList,\"coordinated-missing-payment\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"coordinated-missing-payment\"));}},checked:coordinatStatusList.includes(\"coordinated-missing-payment\"),id:\"coordinated-missing-payment\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"coordinated-missing-payment\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Coordinated, Missing Payment\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#008000]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"fully-coordinated\")){setCoordinatStatusList([...coordinatStatusList,\"fully-coordinated\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"fully-coordinated\"));}},checked:coordinatStatusList.includes(\"fully-coordinated\"),id:\"fully-coordinated\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"fully-coordinated\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-xs items-center my-3 text-[#d34053]\",children:[/*#__PURE__*/_jsx(\"input\",{onChange:v=>{if(!coordinatStatusList.includes(\"failed\")){setCoordinatStatusList([...coordinatStatusList,\"failed\"]);}else{setCoordinatStatusList(coordinatStatusList.filter(status=>status!==\"failed\"));}},checked:coordinatStatusList.includes(\"failed\"),id:\"failed\",type:\"checkbox\",className:\"mx-1\"}),/*#__PURE__*/_jsx(\"label\",{for:\"failed\",className:\"flex-1 mx-1  cursor-pointer \",children:\"Failed\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusListError?coordinatStatusListError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Add new Assistance Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-2 my-1 p-2 shadow rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-2 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col w-full \",children:caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col w-full\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Hospital Starting Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"date\",className:` outline-none border ${startDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\"Hospital Starting Date\",value:startDate,onChange:v=>{setStartDate(v.target.value);// If end date is earlier than new start date, update end date\nif(endDate&&endDate<v.target.value){setEndDate(v.target.value);}}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:startDateError?startDateError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Hospital Ending Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"date\",className:` outline-none border ${endDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\"Hospital Ending Date\",value:endDate,onChange:v=>setEndDate(v.target.value),disabled:!startDate,min:startDate}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:endDateError?endDateError:\"\"})]})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Appointment Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:` outline-none border ${appointmentDateError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:appointmentDateError?appointmentDateError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:` outline-none border ${serviceLocationError?\"border-danger\":\"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:serviceLocationError?serviceLocationError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Provider Name\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{var _option$value;setProviderName(option);//\nvar initialProvider=(_option$value=option===null||option===void 0?void 0:option.value)!==null&&_option$value!==void 0?_option$value:\"\";// Show loading indicator while fetching provider services\nsetIsLoading(true);const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){var _foundProvider$servic;setProviderServices((_foundProvider$servic=foundProvider.services)!==null&&_foundProvider$servic!==void 0?_foundProvider$servic:[]);// Hide loading indicator after services are loaded\nsetTimeout(()=>{setIsLoading(false);},100);}else{setProviderServices([]);setIsLoading(false);}},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>{var _option$label;return(_option$label=option.label)===null||_option$label===void 0?void 0:_option$label.toLowerCase().includes(inputValue===null||inputValue===void 0?void 0:inputValue.toLowerCase());},placeholder:\"Select Provider...\",isSearchable:true// Add loading indicator\n,isLoading:loadingProviders// Show loading indicator when menu opens\n,onMenuOpen:()=>{console.log(\"Provider dropdown opened\");},styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerNameError?providerNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2  w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Provider Service\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:`outline-none border ${providerServiceError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,onChange:v=>{setProviderService(v.target.value);},value:providerService,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),providerServices===null||providerServices===void 0?void 0:providerServices.map((service,index)=>{var _service$service_type;return/*#__PURE__*/_jsxs(\"option\",{value:service.id,children:[(_service$service_type=service.service_type)!==null&&_service$service_type!==void 0?_service$service_type:\"\",service.service_specialist!==\"\"?\" : \"+service.service_specialist:\"\"]});})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerServiceError?providerServiceError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Visit Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:`outline-none border ${providerDateError?\"border-danger\":\"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,type:\"date\",placeholder:\" Visit Date\",value:providerDate,onChange:v=>setProviderDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:providerDateError?providerDateError:\"\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col  \",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// providerMultiSelect\nvar check=true;setProviderNameError(\"\");setProviderServiceError(\"\");setProviderDateError(\"\");if(providerName===\"\"||providerName.value===\"\"){setProviderNameError(\"These fields are required.\");toast.error(\" Provider is required\");check=false;}if(providerService===\"\"){setProviderServiceError(\"These fields are required.\");toast.error(\" Provider Service is required\");check=false;}if(providerDate===\"\"){setProviderDateError(\"These fields are required.\");toast.error(\" Visit Date is required\");check=false;}if(check){const exists=false;// const exists = providerMultiSelect.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.service?.id) ===\n//       String(providerService)\n// );\nconst existsLast=false;// const existsLast = providerMultiSelectLast.some(\n//   (provider) =>\n//     String(provider?.provider?.id) ===\n//       String(providerName.value) &&\n//     String(provider?.provider_service?.id) ===\n//       String(providerService)\n// );\nif(!exists&&!existsLast){var _providerName$value2;// find provider\nvar initialProvider=(_providerName$value2=providerName.value)!==null&&_providerName$value2!==void 0?_providerName$value2:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>String(item.id)===String(initialProvider));if(foundProvider){var _foundProvider$servic2,_foundProvider$servic3;// found service\nvar initialService=providerService!==null&&providerService!==void 0?providerService:\"\";foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic2=foundProvider.services)===null||_foundProvider$servic2===void 0?void 0:_foundProvider$servic2.forEach(element=>{console.log(element.id);});const foundService=foundProvider===null||foundProvider===void 0?void 0:(_foundProvider$servic3=foundProvider.services)===null||_foundProvider$servic3===void 0?void 0:_foundProvider$servic3.find(item=>String(item.id)===String(initialService));if(foundService){// Add the new item if it doesn't exist\nsetProviderMultiSelect([...providerMultiSelect,{provider:foundProvider,service:foundService,date:providerDate}]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");console.log(providerMultiSelect);}else{setProviderNameError(\"This provider service not exist!\");toast.error(\"This provider service not exist!\");}}else{setProviderNameError(\"This provider not exist!\");toast.error(\"This provider not exist!\");}}else{setProviderNameError(\"This provider or service is already added!\");toast.error(\"This provider or service is already added!\");}}},className:\"text-primary  flex flex-row items-center my-2 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\" Add Provider \"})]}),providerMultiSelect===null||providerMultiSelect===void 0?void 0:providerMultiSelect.map((itemProvider,index)=>{var _itemProvider$provide,_itemProvider$provide2,_itemProvider$service,_itemProvider$service2,_itemProvider$service3,_itemProvider$service4,_itemProvider$date;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"min-w-6 text-center\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=providerMultiSelect.filter((_,indexF)=>indexF!==index);setProviderMultiSelect(updatedServices);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1 border-l px-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Provider:\"}),\" \",(_itemProvider$provide=(_itemProvider$provide2=itemProvider.provider)===null||_itemProvider$provide2===void 0?void 0:_itemProvider$provide2.full_name)!==null&&_itemProvider$provide!==void 0?_itemProvider$provide:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Service:\"}),\" \",(_itemProvider$service=(_itemProvider$service2=itemProvider.service)===null||_itemProvider$service2===void 0?void 0:_itemProvider$service2.service_type)!==null&&_itemProvider$service!==void 0?_itemProvider$service:\"--\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Speciality:\"}),\" \",(_itemProvider$service3=(_itemProvider$service4=itemProvider.service)===null||_itemProvider$service4===void 0?void 0:_itemProvider$service4.service_specialist)!==null&&_itemProvider$service3!==void 0?_itemProvider$service3:\"---\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"b\",{children:\"Date:\"}),\" \",(_itemProvider$date=itemProvider.date)!==null&&_itemProvider$date!==void 0?_itemProvider$date:\"---\"]})]})]},index);})]})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{// Validate assistance fields\nlet isValid=true;setStartDateError(\"\");setEndDateError(\"\");setAppointmentDateError(\"\");setServiceLocationError(\"\");setProviderNameError(\"\");// Check if we have the required appointment date information\nif(caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"){// For inpatient, check start and end dates\nif(!startDate){setStartDateError(\"Hospital Starting Date is required\");toast.error(\"Hospital Starting Date is required\");isValid=false;}if(!endDate){setEndDateError(\"Hospital Ending Date is required\");toast.error(\"Hospital Ending Date is required\");isValid=false;}}else{// For outpatient, check appointment date\nif(!appointmentDate){setAppointmentDateError(\"Appointment Date is required\");toast.error(\"Appointment Date is required\");isValid=false;}}// Check service location\n// if (!serviceLocation) {\n//   setServiceLocationError(\n//     \"Service Location is required\"\n//   );\n//   toast.error(\"Service Location is required\");\n//   isValid = false;\n// }\n// Check if at least one provider is added\nif(providerMultiSelect.length===0){setProviderNameError(\"At least one provider must be added\");toast.error(\"At least one provider must be added\");isValid=false;}if(isValid){// Create new assistance object\nconst newAssistance={id:Date.now(),// Generate a temporary ID\nstart_date:startDate||null,end_date:endDate||null,appointment_date:appointmentDate||null,service_location:serviceLocation!==null&&serviceLocation!==void 0?serviceLocation:\"\",provider_services:providerMultiSelect.map(item=>({provider:item.provider,provider_service:item.service,provider_date:item.date}))};// Add to assistanceMultiSelect array\nsetAssistanceMultiSelect(prev=>[...prev,newAssistance]);// Also add to assistanceMultiSelectLast for display\n// setAssistanceMultiSelectLast(prev => [...prev, newAssistance]);\n// Clear all input fields\nsetStartDate(\"\");setEndDate(\"\");setAppointmentDate(\"\");setServiceLocation(\"\");setProviderMultiSelect([]);setProviderName(\"\");setProviderService(\"\");setProviderDate(\"\");toast.success(\"Assistance added successfully\");}},className:\"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-300 flex flex-row items-center justify-center py-2 px-4 rounded-md my-4 text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"size-4 mr-2\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Add New Assistance\"})]})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 0 0 2.25 2.25h.75m0-3.75h3.75M9 15h3.75M9 12h3.75m3-3h.008v.008h-.008V9Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Assistances\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"pl-10\",children:[(assistanceMultiSelectLast===null||assistanceMultiSelectLast===void 0?void 0:assistanceMultiSelectLast.length)>0?assistanceMultiSelectLast.map((itemAssistance,indexAssistance)=>{var _itemAssistance$start,_itemAssistance$end_d,_itemAssistance$appoi,_itemAssistance$servi,_itemAssistance$provi;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-medium text-sm text-gray-800\",children:[\"Appointment #\",indexAssistance+1]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{// Implement delete functionality here\nconst updatedAssistances=assistanceMultiSelectLast.filter((_,index)=>index!==indexAssistance);setAssistanceMultiSelectDelete([...assistanceMultiSelectDelete,itemAssistance.id]);setAssistanceMultiSelectLast(updatedAssistances);},className:\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\",\"aria-label\":\"Delete assistance\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Appointment Info\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-10 space-y-2\",children:[caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Starting Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$start=itemAssistance.start_date)!==null&&_itemAssistance$start!==void 0?_itemAssistance$start:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Ending Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$end_d=itemAssistance.end_date)!==null&&_itemAssistance$end_d!==void 0?_itemAssistance$end_d:\"---\"})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Appointment Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$appoi=itemAssistance.appointment_date)!==null&&_itemAssistance$appoi!==void 0?_itemAssistance$appoi:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$servi=itemAssistance.service_location)!==null&&_itemAssistance$servi!==void 0?_itemAssistance$servi:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#7C3AED\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Providers\"})]}),((_itemAssistance$provi=itemAssistance.provider_services)===null||_itemAssistance$provi===void 0?void 0:_itemAssistance$provi.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 space-y-4\",children:itemAssistance.provider_services.map((itemProvider,idx)=>{var _itemProvider$provide3,_itemProvider$provide4,_itemProvider$provide5,_itemProvider$provide6,_itemProvider$provide7,_itemProvider$provide8,_itemProvider$provide9;return/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-md border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Provider\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide3=(_itemProvider$provide4=itemProvider.provider)===null||_itemProvider$provide4===void 0?void 0:_itemProvider$provide4.full_name)!==null&&_itemProvider$provide3!==void 0?_itemProvider$provide3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Service\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide5=(_itemProvider$provide6=itemProvider.provider_service)===null||_itemProvider$provide6===void 0?void 0:_itemProvider$provide6.service_type)!==null&&_itemProvider$provide5!==void 0?_itemProvider$provide5:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide7=(_itemProvider$provide8=itemProvider.provider_service)===null||_itemProvider$provide8===void 0?void 0:_itemProvider$provide8.service_specialist)!==null&&_itemProvider$provide7!==void 0?_itemProvider$provide7:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide9=itemProvider.provider_date)!==null&&_itemProvider$provide9!==void 0?_itemProvider$provide9:\"---\"})]})]})},idx);})}):/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\",children:\"No providers assigned\"})]})]})]},indexAssistance);}):/*#__PURE__*/_jsx(\"div\",{className:\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\",children:\"No assistances added yet\"}),(assistanceMultiSelect===null||assistanceMultiSelect===void 0?void 0:assistanceMultiSelect.length)>0?assistanceMultiSelect.map((itemAssistance,indexAssistance)=>{var _itemAssistance$start2,_itemAssistance$end_d2,_itemAssistance$appoi2,_itemAssistance$servi2,_itemAssistance$provi2;return/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"font-medium text-sm text-gray-800\",children:[\"Appointment #\",indexAssistance+(assistanceMultiSelectLast===null||assistanceMultiSelectLast===void 0?void 0:assistanceMultiSelectLast.length)+1]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{const updatedServices=assistanceMultiSelect.filter((_,indexF)=>indexF!==indexAssistance);setAssistanceMultiSelect(updatedServices);// Implement delete functionality here\n// const updatedAssistances = assistanceMultiSelectLast.filter(\n//   (_, index) => index !== indexAssistance\n// );\n// setAssistanceMultiSelectDelete([\n//   ...assistanceMultiSelectDelete,\n//   itemAssistance.id,\n// ]);\n// setAssistanceMultiSelectLast(updatedAssistances);\n},className:\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\",\"aria-label\":\"Delete assistance\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#3C50E0\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Appointment Info\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"ml-10 space-y-2\",children:[caseType===\"Medical\"&&caseTypeItem===\"Inpatient\"?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Starting Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$start2=itemAssistance.start_date)!==null&&_itemAssistance$start2!==void 0?_itemAssistance$start2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Hospital Ending Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$end_d2=itemAssistance.end_date)!==null&&_itemAssistance$end_d2!==void 0?_itemAssistance$end_d2:\"---\"})]})]}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Appointment Date:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$appoi2=itemAssistance.appointment_date)!==null&&_itemAssistance$appoi2!==void 0?_itemAssistance$appoi2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500 w-40\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemAssistance$servi2=itemAssistance.service_location)!==null&&_itemAssistance$servi2!==void 0?_itemAssistance$servi2:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:1.5,stroke:\"#7C3AED\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-sm font-semibold text-gray-800\",children:\"Providers\"})]}),((_itemAssistance$provi2=itemAssistance.provider_services)===null||_itemAssistance$provi2===void 0?void 0:_itemAssistance$provi2.length)>0?/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 space-y-4\",children:itemAssistance.provider_services.map((itemProvider,idx)=>{var _itemProvider$provide10,_itemProvider$provide11,_itemProvider$provide12,_itemProvider$provide13,_itemProvider$provide14,_itemProvider$provide15,_itemProvider$provide16;return/*#__PURE__*/_jsx(\"div\",{className:\"p-3 bg-gray-50 rounded-md border border-gray-100\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Provider\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide10=(_itemProvider$provide11=itemProvider.provider)===null||_itemProvider$provide11===void 0?void 0:_itemProvider$provide11.full_name)!==null&&_itemProvider$provide10!==void 0?_itemProvider$provide10:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Service\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide12=(_itemProvider$provide13=itemProvider.provider_service)===null||_itemProvider$provide13===void 0?void 0:_itemProvider$provide13.service_type)!==null&&_itemProvider$provide12!==void 0?_itemProvider$provide12:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Speciality\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide14=(_itemProvider$provide15=itemProvider.provider_service)===null||_itemProvider$provide15===void 0?void 0:_itemProvider$provide15.service_specialist)!==null&&_itemProvider$provide14!==void 0?_itemProvider$provide14:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-gray-500\",children:\"Date\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xs font-medium\",children:(_itemProvider$provide16=itemProvider.provider_date)!==null&&_itemProvider$provide16!==void 0?_itemProvider$provide16:\"---\"})]})]})},idx);})}):/*#__PURE__*/_jsx(\"div\",{className:\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\",children:\"No providers assigned\"})]})]})]},indexAssistance);}):/*#__PURE__*/_jsx(\"div\",{className:\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\",children:\"No new assistances added yet\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");setCoordinatStatusListError(\"\");if(coordinatStatusList.length===0){toast.error(\"Initial Coordination Status empty or invalid. please try again\");setCoordinatStatusListError(\"Initial Coordination Status is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3 gap-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:handleUpdateCurrentStep,disabled:loadingCaseUpdate,className:\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\",children:loadingCaseUpdate?\"Updating...\":\"Update\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{var _coordinator$value2,_providerName$value3,_insuranceCompany$val2,_currencyCode$value2;// Show loading indicator while submitting the form\nsetIsLoading(true);setShouldNavigateToFinalStep(true);// Set flag to navigate to final step\n// Map assistance items with their provider services\nconst assistanceItems=assistanceMultiSelect.map(item=>({start_date:item.start_date,end_date:item.end_date,appointment_date:item.appointment_date,service_location:item.service_location,provider_services:item.provider_services.map(providerService=>{var _providerService$prov3,_providerService$prov4;return{provider:(_providerService$prov3=providerService.provider)===null||_providerService$prov3===void 0?void 0:_providerService$prov3.id,service:(_providerService$prov4=providerService.provider_service)===null||_providerService$prov4===void 0?void 0:_providerService$prov4.id,date:providerService.provider_date};})}));const providerItems=providerMultiSelect.map(item=>{var _item$service2,_item$provider2;return{service:(_item$service2=item.service)===null||_item$service2===void 0?void 0:_item$service2.id,provider:(_item$provider2=item.provider)===null||_item$provider2===void 0?void 0:_item$provider2.id,date:item.date};});// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value2=coordinator.value)!==null&&_coordinator$value2!==void 0?_coordinator$value2:\"\",case_date:caseDate,case_type:caseType,case_type_item:caseType===\"Medical\"?caseTypeItem:\"\",case_description:caseDescription,//\nstatus_coordination:coordinatStatus,case_status:coordinatStatusList,appointment_date:caseTypeItem===\"Inpatient\"?\"\":appointmentDate,start_date:caseTypeItem===\"Inpatient\"?startDate:\"\",end_date:caseTypeItem===\"Inpatient\"?endDate:\"\",service_location:serviceLocation,provider:(_providerName$value3=providerName.value)!==null&&_providerName$value3!==void 0?_providerName$value3:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val2=insuranceCompany.value)!==null&&_insuranceCompany$val2!==void 0?_insuranceCompany$val2:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted,providers:providerItems!==null&&providerItems!==void 0?providerItems:[],assistances:assistanceItems!==null&&assistanceItems!==void 0?assistanceItems:[],providers_deleted:providerMultiSelectDelete!==null&&providerMultiSelectDelete!==void 0?providerMultiSelectDelete:[],assistance_deleted:assistanceMultiSelectDelete!==null&&assistanceMultiSelectDelete!==void 0?assistanceMultiSelectDelete:[],//\nis_pay:isPay?\"True\":\"False\",price_tatal:priceTotal,currency_price:(_currencyCode$value2=currencyCode.value)!==null&&_currencyCode$value2!==void 0?_currencyCode$value2:\"\"}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Save & Complete\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})]});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "DefaultLayout", "addreactionface", "toast", "providersListEditCase", "addNewCase", "detailCase", "updateCase", "LoadingSpinner", "GoogleComponent", "Select", "useDropzone", "insurancesListDashboard", "coordinatorsList<PERSON>ash<PERSON>", "COUNTRIES", "CURRENCYITEMS", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "COUNTRY_CURRENCY_MAP", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_parseInt", "navigate", "location", "dispatch", "id", "searchParams", "section", "get", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinatorId", "setCoordinatorId", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "assistanceMultiSelect", "setAssistanceMultiSelect", "assistanceMultiSelectDelete", "setAssistanceMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "assistanceMultiSelectLast", "setAssistanceMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseTypeItem", "setCaseTypeItem", "caseTypeItemError", "setCaseTypeItemError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "startDate", "setStartDate", "startDateError", "setStartDateError", "endDate", "setEndDate", "endDateError", "setEndDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerDate", "setProviderDate", "providerDateError", "setProviderDateError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "parseInt", "isLoading", "setIsLoading", "shouldNavigateToFinalStep", "setShouldNavigateToFinalStep", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "length", "console", "log", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "foundCoordinator", "find", "item", "String", "full_name", "setTimeout", "value", "label", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "timeoutId", "clearTimeout", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$start_date", "_caseInfo$end_date", "_caseInfo$case_type_i", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "option", "className", "children", "icon", "patient_city", "patientCurrency", "currency_price", "foundCurrency", "code", "name", "is_pay", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "start_date", "end_date", "case_type_item", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "assistance_services", "_caseInfo$assistance_", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "handleUpdateCurrentStep", "_coordinator$value", "_providerName$value", "_insuranceCompany$val", "_currencyCode$value", "<PERSON><PERSON><PERSON><PERSON>", "trim", "phoneRegex", "test", "replace", "emailRegex", "birthDate<PERSON><PERSON><PERSON>", "today", "error", "assistanceItems", "_providerService$prov", "_providerService$prov2", "service", "provider_service", "date", "provider_date", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "assistances", "providers_deleted", "assistance_deleted", "success", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "countryName", "currencyOption", "currency", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "min", "checked", "for", "rows", "disabled", "check", "filter", "_option$value", "_foundProvider$servic", "services", "_option$label", "onMenuOpen", "_service$service_type", "service_type", "service_specialist", "exists", "existsLast", "_providerName$value2", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "_itemProvider$date", "updatedServices", "_", "indexF", "newAssistance", "now", "prev", "strokeWidth", "itemAssistance", "indexAssistance", "_itemAssistance$start", "_itemAssistance$end_d", "_itemAssistance$appoi", "_itemAssistance$servi", "_itemAssistance$provi", "updatedAssistances", "idx", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "_itemProvider$provide7", "_itemProvider$provide8", "_itemProvider$provide9", "_itemAssistance$start2", "_itemAssistance$end_d2", "_itemAssistance$appoi2", "_itemAssistance$servi2", "_itemAssistance$provi2", "_itemProvider$provide10", "_itemProvider$provide11", "_itemProvider$provide12", "_itemProvider$provide13", "_itemProvider$provide14", "_itemProvider$provide15", "_itemProvider$provide16", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "_coordinator$value2", "_providerName$value3", "_insuranceCompany$val2", "_currencyCode$value2", "_providerService$prov3", "_providerService$prov4", "_item$service2", "_item$provider2"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersListEditCase } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\nimport LoadingSpinner from \"../../components/LoadingSpinner\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { insurancesListDashboard } from \"../../redux/actions/insuranceActions\";\nimport { coordinatorsListDashboard } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\n// Country to Currency mapping - using exact country names from COUNTRIES constant\nconst COUNTRY_CURRENCY_MAP = {\n  \"Morocco\": \"MAD\",\n  \"United States\": \"USD\",\n  \"Canada\": \"CAD\",\n  \"United Kingdom\": \"GBP\",\n  \"France\": \"EUR\",\n  \"Germany\": \"EUR\",\n  \"Spain\": \"EUR\",\n  \"Italy\": \"EUR\",\n  \"Netherlands\": \"EUR\",\n  \"Belgium\": \"EUR\",\n  \"Portugal\": \"EUR\",\n  \"Greece\": \"EUR\",\n  \"Austria\": \"EUR\",\n  \"Ireland\": \"EUR\",\n  \"Finland\": \"EUR\",\n  \"Luxembourg\": \"EUR\",\n  \"Estonia\": \"EUR\",\n  \"Slovenia\": \"EUR\",\n  \"Slovakia\": \"EUR\",\n  \"Malta\": \"EUR\",\n  \"Cyprus\": \"EUR\",\n  \"Lithuania\": \"EUR\",\n  \"Latvia\": \"EUR\",\n  \"Japan\": \"JPY\",\n  \"China\": \"CNY\",\n  \"India\": \"INR\",\n  \"Australia\": \"AUD\",\n  \"New Zealand\": \"NZD\",\n  \"South Africa\": \"ZAR\",\n  \"Brazil\": \"BRL\",\n  \"Mexico\": \"MXN\",\n  \"Argentina\": \"ARS\",\n  \"Chile\": \"CLP\",\n  \"Colombia\": \"COP\",\n  \"Peru\": \"PEN\",\n  \"Russia\": \"RUB\",\n  \"Turkey\": \"TRY\",\n  \"Egypt\": \"EGP\",\n  \"Saudi Arabia\": \"SAR\",\n  \"United Arab Emirates\": \"AED\",\n  \"Qatar\": \"QAR\",\n  \"Kuwait\": \"KWD\",\n  \"Bahrain\": \"BHD\",\n  \"Oman\": \"OMR\",\n  \"Jordan\": \"JOD\",\n  \"Lebanon\": \"LBP\",\n  \"Israel\": \"ILS\",\n  \"South Korea\": \"KRW\",\n  \"Thailand\": \"THB\",\n  \"Malaysia\": \"MYR\",\n  \"Singapore\": \"SGD\",\n  \"Indonesia\": \"IDR\",\n  \"Philippines\": \"PHP\",\n  \"Vietnam\": \"VND\",\n  \"Pakistan\": \"PKR\",\n  \"Bangladesh\": \"BDT\",\n  \"Sri Lanka\": \"LKR\",\n  \"Nepal\": \"NPR\",\n  \"Switzerland\": \"CHF\",\n  \"Norway\": \"NOK\",\n  \"Sweden\": \"SEK\",\n  \"Denmark\": \"DKK\",\n  \"Iceland\": \"ISK\",\n  \"Poland\": \"PLN\",\n  \"Czech Republic\": \"CZK\",\n  \"Hungary\": \"HUF\",\n  \"Romania\": \"RON\",\n  \"Bulgaria\": \"BGN\",\n  \"Croatia\": \"HRK\",\n  \"Serbia\": \"RSD\",\n  \"Ukraine\": \"UAH\",\n  \"Belarus\": \"BYN\",\n  \"Algeria\": \"DZD\",\n  \"Tunisia\": \"TND\",\n  \"Libya\": \"LYD\",\n  \"Sudan\": \"SDG\",\n  \"Ethiopia\": \"ETB\",\n  \"Kenya\": \"KES\",\n  \"Uganda\": \"UGX\",\n  \"Tanzania\": \"TZS\",\n  \"Rwanda\": \"RWF\",\n  \"Ghana\": \"GHS\",\n  \"Nigeria\": \"NGN\",\n  \"Senegal\": \"XOF\",\n  \"Ivory Coast\": \"XOF\",\n  \"Mali\": \"XOF\",\n  \"Burkina Faso\": \"XOF\",\n  \"Niger\": \"XOF\",\n  \"Guinea\": \"GNF\",\n  \"Sierra Leone\": \"SLL\",\n  \"Liberia\": \"LRD\",\n  \"Cameroon\": \"XAF\",\n  \"Chad\": \"XAF\",\n  \"Central African Republic\": \"XAF\",\n  \"Democratic Republic of the Congo\": \"CDF\",\n  \"Republic of the Congo\": \"XAF\",\n  \"Gabon\": \"XAF\",\n  \"Angola\": \"AOA\",\n  \"Zambia\": \"ZMK\",\n  \"Zimbabwe\": \"ZWL\",\n  \"Botswana\": \"BWP\",\n  \"Namibia\": \"NAD\",\n  \"Lesotho\": \"LSL\",\n  \"Swaziland\": \"SZL\",\n  \"Mozambique\": \"MZN\",\n  \"Madagascar\": \"MGA\",\n  \"Mauritius\": \"MUR\",\n  \"Seychelles\": \"SCR\",\n  \"Afghanistan\": \"AFN\",\n  \"Albania\": \"ALL\",\n  \"Armenia\": \"AMD\",\n  \"Azerbaijan\": \"AZN\",\n  \"Brunei\": \"BND\",\n  \"Cambodia\": \"KHR\",\n  \"Cape Verde\": \"CVE\",\n  \"Comoros\": \"KMF\",\n  \"Costa Rica\": \"CRC\",\n  \"Cuba\": \"CUP\",\n  \"Dominican Republic\": \"DOP\",\n  \"Ecuador\": \"USD\",\n  \"El Salvador\": \"USD\",\n  \"Eritrea\": \"ERN\",\n  \"Fiji\": \"FJD\",\n  \"Georgia\": \"GEL\",\n  \"Guatemala\": \"GTQ\",\n  \"Guinea-Bissau\": \"XOF\",\n  \"Guyana\": \"GYD\",\n  \"Haiti\": \"HTG\",\n  \"Honduras\": \"HNL\",\n  \"Hong Kong\": \"HKD\",\n  \"Iran\": \"IRR\",\n  \"Iraq\": \"IQD\",\n  \"Jamaica\": \"JMD\",\n  \"Kazakhstan\": \"KZT\",\n  \"Kyrgyzstan\": \"KGS\",\n  \"Laos\": \"LAK\",\n  \"Macau\": \"MOP\",\n  \"Macedonia\": \"MKD\",\n  \"Malawi\": \"MWK\",\n  \"Maldives\": \"MVR\",\n  \"Marshall Islands\": \"USD\",\n  \"Mauritania\": \"MRU\",\n  \"Micronesia\": \"USD\",\n  \"Moldova\": \"MDL\",\n  \"Monaco\": \"EUR\",\n  \"Mongolia\": \"MNT\",\n  \"Montenegro\": \"EUR\",\n  \"Myanmar\": \"MMK\",\n  \"Nicaragua\": \"NIO\",\n  \"North Korea\": \"KPW\",\n  \"Panama\": \"PAB\",\n  \"Papua New Guinea\": \"PGK\",\n  \"Paraguay\": \"PYG\",\n  \"Puerto Rico\": \"USD\",\n  \"Samoa\": \"WST\",\n  \"San Marino\": \"EUR\",\n  \"Sao Tome and Principe\": \"STN\",\n  \"Somalia\": \"SOS\",\n  \"South Sudan\": \"SSP\",\n  \"Suriname\": \"SRD\",\n  \"Syria\": \"SYP\",\n  \"Taiwan\": \"TWD\",\n  \"Tajikistan\": \"TJS\",\n  \"Togo\": \"XOF\",\n  \"Tonga\": \"TOP\",\n  \"Trinidad and Tobago\": \"TTD\",\n  \"Turkmenistan\": \"TMT\",\n  \"Tuvalu\": \"AUD\",\n  \"Uruguay\": \"UYU\",\n  \"Uzbekistan\": \"UZS\",\n  \"Vanuatu\": \"VUV\",\n  \"Venezuela\": \"VES\",\n  \"Western Sahara\": \"MAD\",\n  \"Yemen\": \"YER\"\n};\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const section = searchParams.get(\"section\") || 0;\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorId, setCoordinatorId] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [assistanceMultiSelect, setAssistanceMultiSelect] = useState([]);\n  const [assistanceMultiSelectDelete, setAssistanceMultiSelectDelete] =\n    useState([]);\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n  const [assistanceMultiSelectLast, setAssistanceMultiSelectLast] = useState(\n    []\n  );\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseTypeItem, setCaseTypeItem] = useState(\"\");\n  const [caseTypeItemError, setCaseTypeItemError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [startDate, setStartDate] = useState(\"\");\n  const [startDateError, setStartDateError] = useState(\"\");\n\n  const [endDate, setEndDate] = useState(\"\");\n  const [endDateError, setEndDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerDate, setProviderDate] = useState(\"\");\n  const [providerDateError, setProviderDateError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(parseInt(section) ?? 0);\n  const [isLoading, setIsLoading] = useState(true);\n  const [shouldNavigateToFinalStep, setShouldNavigateToFinalStep] = useState(false);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  // Debug log when providers data changes\n  useEffect(() => {\n    if (providers && providers.length > 0) {\n      console.log(\"Providers data loaded successfully:\", providers.length);\n    }\n  }, [providers]);\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  // Update coordinator when coordinators are loaded\n  useEffect(() => {\n    console.log(\"Coordinator useEffect triggered\");\n\n    if (coordinators && coordinators.length > 0 && coordinatorId) {\n      console.log(\"Trying to find coordinator with ID:\", coordinatorId);\n\n      // Try to find coordinator by ID (as string to ensure type matching)\n      const foundCoordinator = coordinators.find(\n        (item) => String(item.id) === String(coordinatorId)\n      );\n\n      if (foundCoordinator) {\n        console.log(\"Found coordinator:\", foundCoordinator.full_name);\n        // Set the coordinator with a slight delay to ensure the UI updates\n        setTimeout(() => {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n          // Force a re-render by updating the loading state\n          setIsLoading(false);\n        }, 100);\n      } else {\n        console.log(\"Coordinator not found in the list\");\n        // If coordinator not found, try to find it by name\n        const coordinatorById = coordinators.find(\n          (item) => item.id === coordinatorId\n        );\n        if (coordinatorById) {\n          console.log(\n            \"Found coordinator by direct ID comparison:\",\n            coordinatorById.full_name\n          );\n          setCoordinator({\n            value: coordinatorById.id,\n            label: coordinatorById.full_name,\n          });\n        }\n      }\n    }\n  }, [coordinators, coordinatorId]);\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // Set loading state to true when starting to fetch data\n      setIsLoading(true);\n\n      // Load all required data at once with optimized actions\n      dispatch(coordinatorsListDashboard(\"0\"));\n      dispatch(providersListEditCase(\"0\"));\n      dispatch(insurancesListDashboard(\"0\"));\n      dispatch(detailCase(id));\n\n      // Set a maximum timeout for the loading indicator (30 seconds) as a fallback\n      const timeoutId = setTimeout(() => {\n        setIsLoading(false);\n        console.log(\"Maximum loading time reached, hiding loading indicator\");\n      }, 6000);\n\n      // Clean up the timeout when the component unmounts\n      return () => clearTimeout(timeoutId);\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      if (shouldNavigateToFinalStep) {\n        setStepSelect(5);\n        setShouldNavigateToFinalStep(false); // Reset the flag\n      }\n      setIsLoading(false);\n    }\n  }, [successCaseUpdate, shouldNavigateToFinalStep]);\n\n  // Set loading state when case update is in progress\n  useEffect(() => {\n    if (loadingCaseUpdate) {\n      setIsLoading(true);\n    }\n  }, [loadingCaseUpdate]);\n\n  // Update loading state based on data loading status\n  useEffect(() => {\n    // Check if essential data is loaded\n    if (\n      !loadingProviders &&\n      !loadingCaseInfo &&\n      providers &&\n      providers.length > 0 &&\n      caseInfo\n    ) {\n      // Hide loading indicator as soon as we have the essential data\n      setIsLoading(false);\n    } else if (loadingCaseUpdate) {\n      // Show loading during case update\n      setIsLoading(true);\n    }\n  }, [\n    loadingProviders,\n    loadingCaseInfo,\n    loadingCaseUpdate,\n    providers,\n    caseInfo,\n  ]);\n\n  useEffect(() => {\n    // Only proceed if caseInfo is available\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      const patientCurrency = caseInfo.currency_price ?? \"\";\n\n      const foundCurrency = CURRENCYITEMS?.find(\n        (option) => option.code === patientCurrency\n      );\n\n      if (foundCurrency) {\n        setCurrencyCode({\n          value: foundCurrency.code,\n          label:\n            foundCurrency.name !== \"\"\n              ? foundCurrency.name + \" (\" + foundCurrency.code + \") \" || \"\"\n              : \"\",\n        });\n      } else {\n        setCurrencyCode(\"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // Store coordinator ID for later use\n      if (caseInfo.coordinator_user) {\n        const initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        console.log(\n          \"Setting coordinator ID from caseInfo:\",\n          initialCoordinator\n        );\n        console.log(\n          \"Coordinator user from caseInfo:\",\n          caseInfo.coordinator_user\n        );\n\n        // Set coordinator ID with a slight delay to ensure it's properly updated\n        setTimeout(() => {\n          setCoordinatorId(initialCoordinator);\n          console.log(\"CoordinatorId has been set to:\", initialCoordinator);\n        }, 50);\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setStartDate(caseInfo.start_date ?? \"\");\n      setEndDate(caseInfo.end_date ?? \"\");\n      setCaseTypeItem(caseInfo.case_type_item ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n\n      if (caseInfo.assistance_services) {\n        setAssistanceMultiSelectLast(caseInfo.assistance_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  // Function to update case without advancing to next step\n  const handleUpdateCurrentStep = async () => {\n    try {\n      let isValid = true;\n\n      // Step-specific validation based on current step\n      if (stepSelect === 0) {\n        // Step 1: General Information validation\n        setFirstNameError(\"\");\n        setLastNameError(\"\");\n        setPhoneError(\"\");\n        setEmailError(\"\");\n        setAddressError(\"\");\n        setCityError(\"\");\n        setCountryError(\"\");\n        setCaseDateError(\"\");\n        setCaseTypeError(\"\");\n        setCaseDescriptionError(\"\");\n        setPriceTotalError(\"\");\n        setCurrencyCodeError(\"\");\n        setCoordinatorError(\"\");\n        setBirthDateError(\"\");\n\n        // Required field validations for Step 1\n        if (!firstName || firstName.trim() === \"\") {\n          setFirstNameError(\"First name is required.\");\n          isValid = false;\n        }\n\n        if (!lastName || lastName.trim() === \"\") {\n          setLastNameError(\"Last name is required.\");\n          isValid = false;\n        }\n\n        if (!phone || phone.trim() === \"\") {\n          setPhoneError(\"Phone number is required.\");\n          isValid = false;\n        } else {\n          // Phone format validation\n          const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n          if (!phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n            setPhoneError(\"Please enter a valid phone number +212....\");\n            isValid = false;\n          }\n        }\n\n\n        if (email && email.trim() !== \"\"){\n\n       \n          const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n          if (!emailRegex.test(email)) {\n            setEmailError(\"Please enter a valid email address.\");\n            isValid = false;\n          }\n        }\n\n        if (!address || address.trim() === \"\") {\n          setAddressError(\"Address is required.\");\n          isValid = false;\n        }\n\n        if (!city || city.trim() === \"\") {\n          setCityError(\"City is required.\");\n          isValid = false;\n        }\n\n        if (!country || !country.value || country.value === \"\") {\n          setCountryError(\"Country is required.\");\n          isValid = false;\n        }\n\n        if (!caseDate || caseDate.trim() === \"\") {\n          setCaseDateError(\"Case date is required.\");\n          isValid = false;\n        }\n\n        if (!caseType || caseType.trim() === \"\") {\n          setCaseTypeError(\"Case type is required.\");\n          isValid = false;\n        }\n\n        if (!caseDescription || caseDescription.trim() === \"\") {\n          setCaseDescriptionError(\"Case description is required.\");\n          isValid = false;\n        }\n\n        if (!coordinator || !coordinator.value || coordinator.value === \"\") {\n          setCoordinatorError(\"Coordinator is required.\");\n          isValid = false;\n        }\n\n        if (!currencyCode || !currencyCode.value || currencyCode.value === \"\") {\n          setCurrencyCodeError(\"Currency is required.\");\n          isValid = false;\n        }\n\n        if (!priceTotal || priceTotal === \"\" || priceTotal < 0) {\n          setPriceTotalError(\"Price total is required and must be greater than 0.\");\n          isValid = false;\n        }\n\n        // Optional field validations for Step 1\n        if (birthDate && birthDate.trim() !== \"\") {\n          const birthDateObj = new Date(birthDate);\n          const today = new Date();\n          if (birthDateObj > today) {\n            setBirthDateError(\"Birth date cannot be in the future.\");\n            isValid = false;\n          }\n        }\n\n      } else if (stepSelect === 1) {\n        // Step 2: Coordination Details validation\n        setCoordinatStatusListError(\"\");\n\n        // Coordination status validation\n        if (!coordinatStatusList || coordinatStatusList.length === 0) {\n          setCoordinatStatusListError(\"At least one coordination status is required.\");\n          isValid = false;\n        }\n\n      } else if (stepSelect === 2) {\n        // Step 3: Medical Reports validation\n        // No specific required validations for medical reports step\n        // Files are optional\n\n      } else if (stepSelect === 3) {\n        // Step 4: Invoices validation\n        setInvoiceNumberError(\"\");\n        setAmountError(\"\");\n\n        // Optional field validations for Step 4\n        if (invoiceNumber && invoiceNumber.trim() !== \"\") {\n          if (invoiceNumber.length < 3) {\n            setInvoiceNumberError(\"Invoice number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n\n        if (amount && amount !== \"\" && amount < 0) {\n          setAmountError(\"Amount cannot be negative.\");\n          isValid = false;\n        }\n\n      } else if (stepSelect === 4) {\n        // Step 5: Insurance Authorization validation\n        setInsuranceNumberError(\"\");\n        setPolicyNumberError(\"\");\n\n        // Optional field validations for Step 5\n        if (insuranceNumber && insuranceNumber.trim() !== \"\") {\n          if (insuranceNumber.length < 3) {\n            setInsuranceNumberError(\"Insurance number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n\n        if (policyNumber && policyNumber.trim() !== \"\") {\n          if (policyNumber.length < 3) {\n            setPolicyNumberError(\"Policy number must be at least 3 characters.\");\n            isValid = false;\n          }\n        }\n      }\n\n      // If validation fails, show error and return\n      if (!isValid) {\n        toast.error(\"Please fix the validation errors before updating.\");\n        return;\n      }\n\n      setIsLoading(true);\n\n      // Map assistance items with their provider services\n      const assistanceItems = assistanceMultiSelect.map((item) => ({\n        start_date: item.start_date,\n        end_date: item.end_date,\n        appointment_date: item.appointment_date,\n        service_location: item.service_location,\n        provider_services: item.provider_services.map((providerService) => ({\n          provider: providerService.provider?.id,\n          service: providerService.provider_service?.id,\n          date: providerService.provider_date,\n        })),\n      }));\n\n      const providerItems = providerMultiSelect.map((item) => ({\n        service: item.service?.id,\n        provider: item.provider?.id,\n        date: item.date,\n      }));\n\n      // Update case with all current data\n      await dispatch(\n        updateCase(id, {\n          first_name: firstName,\n          last_name: lastName,\n          full_name: firstName + \" \" + lastName,\n          birth_day: birthDate ?? \"\",\n          patient_phone: phone,\n          patient_email: email,\n          patient_address: address,\n          patient_city: city,\n          patient_country: country.value,\n          //\n          coordinator: coordinator.value ?? \"\",\n          case_date: caseDate,\n          case_type: caseType,\n          case_type_item: caseType === \"Medical\" ? caseTypeItem : \"\",\n          case_description: caseDescription,\n          //\n          status_coordination: coordinatStatus,\n          case_status: coordinatStatusList,\n          appointment_date: caseTypeItem === \"Inpatient\" ? \"\" : appointmentDate,\n          start_date: caseTypeItem === \"Inpatient\" ? startDate : \"\",\n          end_date: caseTypeItem === \"Inpatient\" ? endDate : \"\",\n          service_location: serviceLocation,\n          provider: providerName.value ?? \"\",\n          //\n          invoice_number: invoiceNumber,\n          date_issued: dateIssued,\n          invoice_amount: amount,\n          assurance: insuranceCompany.value ?? \"\",\n          assurance_number: insuranceNumber,\n          policy_number: policyNumber,\n          assurance_status: initialStatus,\n          // files\n          initial_medical_reports: filesInitialMedicalReports,\n          upload_invoice: filesUploadInvoice,\n          upload_authorization_documents: filesUploadAuthorizationDocuments,\n          files_deleted: fileDeleted,\n          providers: providerItems ?? [],\n          assistances: assistanceItems ?? [],\n          providers_deleted: providerMultiSelectDelete ?? [],\n          assistance_deleted: assistanceMultiSelectDelete ?? [],\n          //\n          is_pay: isPay ? \"True\" : \"False\",\n          price_tatal: priceTotal,\n          currency_price: currencyCode.value ?? \"\",\n        })\n      );\n\n      setIsLoading(false);\n      toast.success(\"Case updated successfully!\");\n    } catch (error) {\n      setIsLoading(false);\n      toast.error(\"Failed to update case. Please try again.\");\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      {/* Global Loading Indicator */}\n      {isLoading && (\n        <div className=\"fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white p-5 rounded-lg shadow-lg flex flex-col items-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#0388A6] mb-3\"></div>\n            <div className=\"text-gray-700 font-medium\">Loading data...</div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n\n                              // Auto-update currency based on selected country\n                              if (option && option.value) {\n                                // The option.value contains the country title (name)\n                                const countryName = option.value;\n                                const currencyCode = COUNTRY_CURRENCY_MAP[countryName];\n\n                                if (currencyCode) {\n                                  // Find the currency option in CURRENCYITEMS\n                                  const currencyOption = CURRENCYITEMS?.find(\n                                    (currency) => currency.code === currencyCode\n                                  );\n\n                                  if (currencyOption) {\n                                    setCurrencyCode({\n                                      value: currencyOption.code,\n                                      label: currencyOption.name !== \"\"\n                                        ? currencyOption.name + \" (\" + currencyOption.code + \")\"\n                                        : currencyOption.code\n                                    });\n\n                                    // Show success message\n                                    toast.success(`Currency automatically updated to ${currencyOption.name} (${currencyOption.code})`);\n                                  }\n                                }\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyD5rhJGfXPbttVQzftoMPVjJOWbBvfcTFE\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {caseType === \"Medical\" && (\n                      <div className=\"md:w-1/2  w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type Item <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseTypeItem}\n                            onChange={(v) => setCaseTypeItem(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeItemError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-3 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type Item</option>\n                            <option value={\"Outpatient\"}>Outpatient</option>\n                            <option value={\"Inpatient\"}>Inpatient</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeItemError ? caseTypeItemError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                              console.log(option);\n                              \n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: currencyCodeError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Price of service{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseTypeItemError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        } else if (\n                          caseType === \"Medical\" &&\n                          caseTypeItem === \"\"\n                        ) {\n                          setCaseTypeItemError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordination-fee\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordination-fee\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordination-fee\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordination-fee\"\n                                )}\n                                id=\"coordination-fee\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordination-fee\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordination Fee\n                              </label>\n                            </div>\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-payment\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-payment\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-payment\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-payment\"\n                                )}\n                                id=\"coordinated-missing-payment\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-payment\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Payment\n                              </label>\n                            </div>\n\n                            {/*  */}\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Assistance Details:\n                  </div>\n\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    {/* form add new assistance */}\n                    <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                      Add new Assistance Details:\n                    </div>\n                    <div className=\"mx-2 my-1 p-2 shadow rounded\">\n                      {/* Appointment Details: */}\n                      <div className=\"text-xs font-medium mt-2 mb-2 text-black\">\n                        Appointment Details:\n                      </div>\n                      <div className=\"flex md:flex-row flex-col w-full \">\n                        {caseType === \"Medical\" &&\n                        caseTypeItem === \"Inpatient\" ? (\n                          <div className=\"flex md:flex-row flex-col w-full\">\n                            <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                                Hospital Starting Date{\" \"}\n                                <strong className=\"text-danger\">*</strong>\n                              </div>\n                              <div>\n                                <input\n                                  type=\"date\"\n                                  className={` outline-none border ${\n                                    startDateError\n                                      ? \"border-danger\"\n                                      : \"border-[#F1F3FF]\"\n                                  } px-3 py-2 w-full rounded text-sm`}\n                                  placeholder=\"Hospital Starting Date\"\n                                  value={startDate}\n                                  onChange={(v) => {\n                                    setStartDate(v.target.value);\n                                    // If end date is earlier than new start date, update end date\n                                    if (endDate && endDate < v.target.value) {\n                                      setEndDate(v.target.value);\n                                    }\n                                  }}\n                                />\n                                <div className=\" text-[8px] text-danger\">\n                                  {startDateError ? startDateError : \"\"}\n                                </div>\n                              </div>\n                            </div>\n                            <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                                Hospital Ending Date{\" \"}\n                                <strong className=\"text-danger\">*</strong>\n                              </div>\n                              <div>\n                                <input\n                                  type=\"date\"\n                                  className={` outline-none border ${\n                                    endDateError\n                                      ? \"border-danger\"\n                                      : \"border-[#F1F3FF]\"\n                                  } px-3 py-2 w-full rounded text-sm`}\n                                  placeholder=\"Hospital Ending Date\"\n                                  value={endDate}\n                                  onChange={(v) => setEndDate(v.target.value)}\n                                  disabled={!startDate}\n                                  min={startDate}\n                                />\n                                <div className=\" text-[8px] text-danger\">\n                                  {endDateError ? endDateError : \"\"}\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                        ) : (\n                          <div className=\" w-full  md:pr-1 my-1\">\n                            <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                              Appointment Date{\" \"}\n                              <strong className=\"text-danger\">*</strong>\n                            </div>\n                            <div>\n                              <input\n                                className={` outline-none border ${\n                                  appointmentDateError\n                                    ? \"border-danger\"\n                                    : \"border-[#F1F3FF]\"\n                                } px-3 py-2 w-full rounded text-sm`}\n                                type=\"date\"\n                                placeholder=\"Appointment Date\"\n                                value={appointmentDate}\n                                onChange={(v) =>\n                                  setAppointmentDate(v.target.value)\n                                }\n                              />\n                              <div className=\" text-[8px] text-danger\">\n                                {appointmentDateError\n                                  ? appointmentDateError\n                                  : \"\"}\n                              </div>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n\n                      <div className=\"flex md:flex-row flex-col  \">\n                        {/*  */}\n                        <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Service Location\n                          </div>\n                          <div>\n                            <input\n                              type=\"text\"\n                              className={` outline-none border ${\n                                serviceLocationError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              } px-3 py-2 w-full rounded text-sm`}\n                              placeholder=\" Service Location\"\n                              value={serviceLocation}\n                              onChange={(v) =>\n                                setServiceLocation(v.target.value)\n                              }\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {serviceLocationError ? serviceLocationError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Provider Information: */}\n                      <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Provider Information:\n                      </div>\n                      <div className=\"flex md:flex-row flex-col  \">\n                        <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Provider Name{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <Select\n                              value={providerName}\n                              onChange={(option) => {\n                                setProviderName(option);\n                                //\n                                var initialProvider = option?.value ?? \"\";\n                                // Show loading indicator while fetching provider services\n                                setIsLoading(true);\n\n                                const foundProvider = providers?.find(\n                                  (item) => item.id === initialProvider\n                                );\n                                if (foundProvider) {\n                                  setProviderServices(\n                                    foundProvider.services ?? []\n                                  );\n                                  // Hide loading indicator after services are loaded\n                                  setTimeout(() => {\n                                    setIsLoading(false);\n                                  }, 100);\n                                } else {\n                                  setProviderServices([]);\n                                  setIsLoading(false);\n                                }\n                              }}\n                              className=\"text-sm\"\n                              options={providers?.map((item) => ({\n                                value: item.id,\n                                label: item.full_name || \"\",\n                              }))}\n                              filterOption={(option, inputValue) =>\n                                option.label\n                                  ?.toLowerCase()\n                                  .includes(inputValue?.toLowerCase())\n                              }\n                              placeholder=\"Select Provider...\"\n                              isSearchable\n                              // Add loading indicator\n                              isLoading={loadingProviders}\n                              // Show loading indicator when menu opens\n                              onMenuOpen={() => {\n                                console.log(\"Provider dropdown opened\");\n                              }}\n                              styles={{\n                                control: (base, state) => ({\n                                  ...base,\n                                  background: \"#fff\",\n                                  border: providerNameError\n                                    ? \"1px solid #d34053\"\n                                    : \"1px solid #F1F3FF\",\n                                  boxShadow: state.isFocused ? \"none\" : \"none\",\n                                  \"&:hover\": {\n                                    border: \"1px solid #F1F3FF\",\n                                  },\n                                }),\n                                option: (base) => ({\n                                  ...base,\n                                  display: \"flex\",\n                                  alignItems: \"center\",\n                                }),\n                                singleValue: (base) => ({\n                                  ...base,\n                                  display: \"flex\",\n                                  alignItems: \"center\",\n                                }),\n                              }}\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {providerNameError ? providerNameError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                        {/*  */}\n                        <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Provider Service{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <select\n                              className={`outline-none border ${\n                                providerServiceError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              }  px-3 py-2 w-full rounded text-sm`}\n                              onChange={(v) => {\n                                setProviderService(v.target.value);\n                              }}\n                              value={providerService}\n                            >\n                              <option value={\"\"}></option>\n                              {providerServices?.map((service, index) => (\n                                <option value={service.id}>\n                                  {service.service_type ?? \"\"}\n                                  {service.service_specialist !== \"\"\n                                    ? \" : \" + service.service_specialist\n                                    : \"\"}\n                                </option>\n                              ))}\n                            </select>\n                            <div className=\" text-[8px] text-danger\">\n                              {providerServiceError ? providerServiceError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"flex md:flex-row flex-col  \">\n                        <div className=\" w-full  md:pr-1 my-1\">\n                          <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                            Visit Date{\" \"}\n                            <strong className=\"text-danger\">*</strong>\n                          </div>\n                          <div>\n                            <input\n                              className={`outline-none border ${\n                                providerDateError\n                                  ? \"border-danger\"\n                                  : \"border-[#F1F3FF]\"\n                              }  px-3 py-2 w-full rounded text-sm`}\n                              type=\"date\"\n                              placeholder=\" Visit Date\"\n                              value={providerDate}\n                              onChange={(v) => setProviderDate(v.target.value)}\n                            />\n                            <div className=\" text-[8px] text-danger\">\n                              {providerDateError ? providerDateError : \"\"}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                      {/* add  */}\n                      <div className=\"flex flex-col  \">\n                        <button\n                          onClick={() => {\n                            // providerMultiSelect\n                            var check = true;\n                            setProviderNameError(\"\");\n                            setProviderServiceError(\"\");\n                            setProviderDateError(\"\");\n                            if (\n                              providerName === \"\" ||\n                              providerName.value === \"\"\n                            ) {\n                              setProviderNameError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Provider is required\");\n                              check = false;\n                            }\n                            if (providerService === \"\") {\n                              setProviderServiceError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Provider Service is required\");\n                              check = false;\n                            }\n                            if (providerDate === \"\") {\n                              setProviderDateError(\n                                \"These fields are required.\"\n                              );\n                              toast.error(\" Visit Date is required\");\n                              check = false;\n                            }\n\n                            if (check) {\n                              const exists = false;\n                              // const exists = providerMultiSelect.some(\n                              //   (provider) =>\n                              //     String(provider?.provider?.id) ===\n                              //       String(providerName.value) &&\n                              //     String(provider?.service?.id) ===\n                              //       String(providerService)\n                              // );\n                              const existsLast = false;\n\n                              // const existsLast = providerMultiSelectLast.some(\n                              //   (provider) =>\n                              //     String(provider?.provider?.id) ===\n                              //       String(providerName.value) &&\n                              //     String(provider?.provider_service?.id) ===\n                              //       String(providerService)\n                              // );\n\n                              if (!exists && !existsLast) {\n                                // find provider\n                                var initialProvider = providerName.value ?? \"\";\n                                const foundProvider = providers?.find(\n                                  (item) =>\n                                    String(item.id) === String(initialProvider)\n                                );\n                                \n\n                                if (foundProvider) {\n                                  // found service\n                                  var initialService = providerService ?? \"\";\n\n                                  foundProvider?.services?.forEach(\n                                    (element) => {\n                                      console.log(element.id);\n                                    }\n                                  );\n\n                                  const foundService =\n                                    foundProvider?.services?.find(\n                                      (item) =>\n                                        String(item.id) ===\n                                        String(initialService)\n                                    );\n\n                                  if (foundService) {\n                                    // Add the new item if it doesn't exist\n                                    setProviderMultiSelect([\n                                      ...providerMultiSelect,\n                                      {\n                                        provider: foundProvider,\n                                        service: foundService,\n                                        date: providerDate,\n                                      },\n                                    ]);\n                                    setProviderName(\"\");\n                                    setProviderService(\"\");\n                                    setProviderDate(\"\");\n                                    console.log(providerMultiSelect);\n                                  } else {\n                                    setProviderNameError(\n                                      \"This provider service not exist!\"\n                                    );\n                                    toast.error(\n                                      \"This provider service not exist!\"\n                                    );\n                                  }\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider not exist!\"\n                                  );\n                                  toast.error(\"This provider not exist!\");\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider or service is already added!\"\n                                );\n                                toast.error(\n                                  \"This provider or service is already added!\"\n                                );\n                              }\n                            }\n                          }}\n                          className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-4\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                            />\n                          </svg>\n                          <span> Add Provider </span>\n                        </button>\n                        {/* providers list added */}\n                        {providerMultiSelect?.map((itemProvider, index) => (\n                          <div\n                            key={index}\n                            className=\"flex flex-row items-center my-1\"\n                          >\n                            <div className=\"min-w-6 text-center\">\n                              <button\n                                onClick={() => {\n                                  const updatedServices =\n                                    providerMultiSelect.filter(\n                                      (_, indexF) => indexF !== index\n                                    );\n                                  setProviderMultiSelect(updatedServices);\n                                }}\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-6\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                            <div className=\"flex-1 mx-1 border-l px-1\">\n                              <div>\n                                <b>Provider:</b>{\" \"}\n                                {itemProvider.provider?.full_name ?? \"---\"}\n                              </div>\n                              <div>\n                                <b>Service:</b>{\" \"}\n                                {itemProvider.service?.service_type ?? \"--\"}\n                              </div>\n                              <div>\n                                <b>Speciality:</b>{\" \"}\n                                {itemProvider.service?.service_specialist ??\n                                  \"---\"}\n                              </div>\n                              <div>\n                                <b>Date:</b> {itemProvider.date ?? \"---\"}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                        {/* end providers list added */}\n                      </div>\n                    </div>\n                    <div>\n                      <button\n                        onClick={() => {\n                          // Validate assistance fields\n                          let isValid = true;\n                          setStartDateError(\"\");\n                          setEndDateError(\"\");\n                          setAppointmentDateError(\"\");\n                          setServiceLocationError(\"\");\n                          setProviderNameError(\"\");\n                          // Check if we have the required appointment date information\n                          if (\n                            caseType === \"Medical\" &&\n                            caseTypeItem === \"Inpatient\"\n                          ) {\n                            // For inpatient, check start and end dates\n                            if (!startDate) {\n                              setStartDateError(\n                                \"Hospital Starting Date is required\"\n                              );\n                              toast.error(\"Hospital Starting Date is required\");\n                              isValid = false;\n                            }\n                            if (!endDate) {\n                              setEndDateError(\n                                \"Hospital Ending Date is required\"\n                              );\n                              toast.error(\"Hospital Ending Date is required\");\n                              isValid = false;\n                            }\n                          } else {\n                            // For outpatient, check appointment date\n                            if (!appointmentDate) {\n                              setAppointmentDateError(\n                                \"Appointment Date is required\"\n                              );\n                              toast.error(\"Appointment Date is required\");\n                              isValid = false;\n                            }\n                          }\n\n                          // Check service location\n                          // if (!serviceLocation) {\n                          //   setServiceLocationError(\n                          //     \"Service Location is required\"\n                          //   );\n                          //   toast.error(\"Service Location is required\");\n                          //   isValid = false;\n                          // }\n\n                          // Check if at least one provider is added\n                          if (providerMultiSelect.length === 0) {\n                            setProviderNameError(\n                              \"At least one provider must be added\"\n                            );\n                            toast.error(\"At least one provider must be added\");\n                            isValid = false;\n                          }\n\n                          if (isValid) {\n                            // Create new assistance object\n                            const newAssistance = {\n                              id: Date.now(), // Generate a temporary ID\n                              start_date: startDate || null,\n                              end_date: endDate || null,\n                              appointment_date: appointmentDate || null,\n                              service_location: serviceLocation??\"\",\n                              provider_services: providerMultiSelect.map(\n                                (item) => ({\n                                  provider: item.provider,\n                                  provider_service: item.service,\n                                  provider_date: item.date,\n                                })\n                              ),\n                            };\n\n                            // Add to assistanceMultiSelect array\n                            setAssistanceMultiSelect((prev) => [\n                              ...prev,\n                              newAssistance,\n                            ]);\n\n                            // Also add to assistanceMultiSelectLast for display\n                            // setAssistanceMultiSelectLast(prev => [...prev, newAssistance]);\n\n                            // Clear all input fields\n                            setStartDate(\"\");\n                            setEndDate(\"\");\n                            setAppointmentDate(\"\");\n                            setServiceLocation(\"\");\n                            setProviderMultiSelect([]);\n                            setProviderName(\"\");\n                            setProviderService(\"\");\n                            setProviderDate(\"\");\n\n                            toast.success(\"Assistance added successfully\");\n                          }\n                        }}\n                        className=\"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-300 flex flex-row items-center justify-center py-2 px-4 rounded-md my-4 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          strokeWidth=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-4 mr-2\"\n                        >\n                          <path\n                            strokeLinecap=\"round\"\n                            strokeLinejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span>Add New Assistance</span>\n                      </button>\n                    </div>\n                    {/* end form add new assistance */}\n                    <div>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"flex items-center mb-3\">\n                          <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              strokeWidth={1.5}\n                              stroke=\"#3C50E0\"\n                              className=\"w-4 h-4\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 0 0 2.25 2.25h.75m0-3.75h3.75M9 15h3.75M9 12h3.75m3-3h.008v.008h-.008V9Z\"\n                              />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-sm font-semibold text-gray-800\">\n                            Assistances\n                          </h4>\n                        </div>\n\n                        <div className=\"pl-10\">\n                          {/* last data */}\n                          {assistanceMultiSelectLast?.length > 0 ? (\n                            assistanceMultiSelectLast.map(\n                              (itemAssistance, indexAssistance) => (\n                                <div\n                                  key={indexAssistance}\n                                  className=\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\"\n                                >\n                                  {/* Card Header */}\n                                  <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\">\n                                    <h3 className=\"font-medium text-sm text-gray-800\">\n                                      Appointment #{indexAssistance + 1}\n                                    </h3>\n                                    <button\n                                      onClick={() => {\n                                        // Implement delete functionality here\n                                        const updatedAssistances =\n                                          assistanceMultiSelectLast.filter(\n                                            (_, index) =>\n                                              index !== indexAssistance\n                                          );\n                                        setAssistanceMultiSelectDelete([\n                                          ...assistanceMultiSelectDelete,\n                                          itemAssistance.id,\n                                        ]);\n                                        setAssistanceMultiSelectLast(\n                                          updatedAssistances\n                                        );\n                                      }}\n                                      className=\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\"\n                                      aria-label=\"Delete assistance\"\n                                    >\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        strokeWidth=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-5 h-5\"\n                                      >\n                                        <path\n                                          strokeLinecap=\"round\"\n                                          strokeLinejoin=\"round\"\n                                          d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                        />\n                                      </svg>\n                                    </button>\n                                  </div>\n\n                                  {/* Card Content */}\n                                  <div className=\"p-4\">\n                                    {/* Appointment Info Section */}\n                                    <div className=\"mb-4\">\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#3C50E0\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Appointment Info\n                                        </h4>\n                                      </div>\n\n                                      <div className=\"ml-10 space-y-2\">\n                                        {caseType === \"Medical\" &&\n                                        caseTypeItem === \"Inpatient\" ? (\n                                          <>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Starting Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.start_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Ending Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.end_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                          </>\n                                        ) : (\n                                          <div className=\"flex\">\n                                            <span className=\"text-xs text-gray-500 w-40\">\n                                              Appointment Date:\n                                            </span>\n                                            <span className=\"text-xs font-medium\">\n                                              {itemAssistance.appointment_date ??\n                                                \"---\"}\n                                            </span>\n                                          </div>\n                                        )}\n                                        <div className=\"flex\">\n                                          <span className=\"text-xs text-gray-500 w-40\">\n                                            Service Location:\n                                          </span>\n                                          <span className=\"text-xs font-medium\">\n                                            {itemAssistance.service_location ??\n                                              \"---\"}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n\n                                    {/* Providers Section */}\n                                    <div>\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#7C3AED\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Providers\n                                        </h4>\n                                      </div>\n\n                                      {itemAssistance.provider_services\n                                        ?.length > 0 ? (\n                                        <div className=\"ml-10 space-y-4\">\n                                          {itemAssistance.provider_services.map(\n                                            (itemProvider, idx) => (\n                                              <div\n                                                key={idx}\n                                                className=\"p-3 bg-gray-50 rounded-md border border-gray-100\"\n                                              >\n                                                <div className=\"grid grid-cols-2 gap-2\">\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Provider\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider\n                                                        ?.full_name ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Service\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_type ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Speciality\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_specialist ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Date\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider_date ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                </div>\n                                              </div>\n                                            )\n                                          )}\n                                        </div>\n                                      ) : (\n                                        <div className=\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\">\n                                          No providers assigned\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )\n                          ) : (\n                            <div className=\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\">\n                              No assistances added yet\n                            </div>\n                          )}\n\n                          {/* end last data */}\n\n                          {/* new data */}\n                          {assistanceMultiSelect?.length > 0 ? (\n                            assistanceMultiSelect.map(\n                              (itemAssistance, indexAssistance) => (\n                                <div\n                                  key={indexAssistance}\n                                  className=\"bg-white rounded-lg shadow-sm border border-gray-100 mb-4 overflow-hidden hover:shadow-md transition-all duration-300\"\n                                >\n                                  {/* Card Header */}\n                                  <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-4 py-3 flex justify-between items-center border-b border-gray-100\">\n                                    <h3 className=\"font-medium text-sm text-gray-800\">\n                                      Appointment #\n                                      {indexAssistance +\n                                        assistanceMultiSelectLast?.length +\n                                        1}\n                                    </h3>\n                                    <button\n                                      onClick={() => {\n                                        const updatedServices =\n                                          assistanceMultiSelect.filter(\n                                            (_, indexF) =>\n                                              indexF !== indexAssistance\n                                          );\n                                        setAssistanceMultiSelect(\n                                          updatedServices\n                                        );\n                                        // Implement delete functionality here\n                                        // const updatedAssistances = assistanceMultiSelectLast.filter(\n                                        //   (_, index) => index !== indexAssistance\n                                        // );\n                                        // setAssistanceMultiSelectDelete([\n                                        //   ...assistanceMultiSelectDelete,\n                                        //   itemAssistance.id,\n                                        // ]);\n                                        // setAssistanceMultiSelectLast(updatedAssistances);\n                                      }}\n                                      className=\"text-gray-400 hover:text-red-500 transition-colors duration-200 p-1 rounded-full hover:bg-red-50\"\n                                      aria-label=\"Delete assistance\"\n                                    >\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        strokeWidth=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-5 h-5\"\n                                      >\n                                        <path\n                                          strokeLinecap=\"round\"\n                                          strokeLinejoin=\"round\"\n                                          d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                        />\n                                      </svg>\n                                    </button>\n                                  </div>\n\n                                  {/* Card Content */}\n                                  <div className=\"p-4\">\n                                    {/* Appointment Info Section */}\n                                    <div className=\"mb-4\">\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#3C50E0\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Appointment Info\n                                        </h4>\n                                      </div>\n\n                                      <div className=\"ml-10 space-y-2\">\n                                        {caseType === \"Medical\" &&\n                                        caseTypeItem === \"Inpatient\" ? (\n                                          <>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Starting Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.start_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                            <div className=\"flex\">\n                                              <span className=\"text-xs text-gray-500 w-40\">\n                                                Hospital Ending Date:\n                                              </span>\n                                              <span className=\"text-xs font-medium\">\n                                                {itemAssistance.end_date ??\n                                                  \"---\"}\n                                              </span>\n                                            </div>\n                                          </>\n                                        ) : (\n                                          <div className=\"flex\">\n                                            <span className=\"text-xs text-gray-500 w-40\">\n                                              Appointment Date:\n                                            </span>\n                                            <span className=\"text-xs font-medium\">\n                                              {itemAssistance.appointment_date ??\n                                                \"---\"}\n                                            </span>\n                                          </div>\n                                        )}\n                                        <div className=\"flex\">\n                                          <span className=\"text-xs text-gray-500 w-40\">\n                                            Service Location:\n                                          </span>\n                                          <span className=\"text-xs font-medium\">\n                                            {itemAssistance.service_location ??\n                                              \"---\"}\n                                          </span>\n                                        </div>\n                                      </div>\n                                    </div>\n\n                                    {/* Providers Section */}\n                                    <div>\n                                      <div className=\"flex items-center mb-2\">\n                                        <div className=\"w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-2\">\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            strokeWidth={1.5}\n                                            stroke=\"#7C3AED\"\n                                            className=\"w-4 h-4\"\n                                          >\n                                            <path\n                                              strokeLinecap=\"round\"\n                                              strokeLinejoin=\"round\"\n                                              d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                            />\n                                          </svg>\n                                        </div>\n                                        <h4 className=\"text-sm font-semibold text-gray-800\">\n                                          Providers\n                                        </h4>\n                                      </div>\n\n                                      {itemAssistance.provider_services\n                                        ?.length > 0 ? (\n                                        <div className=\"ml-10 space-y-4\">\n                                          {itemAssistance.provider_services.map(\n                                            (itemProvider, idx) => (\n                                              <div\n                                                key={idx}\n                                                className=\"p-3 bg-gray-50 rounded-md border border-gray-100\"\n                                              >\n                                                <div className=\"grid grid-cols-2 gap-2\">\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Provider\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider\n                                                        ?.full_name ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Service\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_type ?? \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Speciality\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider\n                                                        .provider_service\n                                                        ?.service_specialist ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                  <div className=\"flex flex-col\">\n                                                    <span className=\"text-xs text-gray-500\">\n                                                      Date\n                                                    </span>\n                                                    <span className=\"text-xs font-medium\">\n                                                      {itemProvider.provider_date ??\n                                                        \"---\"}\n                                                    </span>\n                                                  </div>\n                                                </div>\n                                              </div>\n                                            )\n                                          )}\n                                        </div>\n                                      ) : (\n                                        <div className=\"ml-10 py-2 px-3 bg-gray-50 rounded-md text-xs text-gray-500\">\n                                          No providers assigned\n                                        </div>\n                                      )}\n                                    </div>\n                                  </div>\n                                </div>\n                              )\n                            )\n                          ) : (\n                            <div className=\"py-3 px-4 bg-gray-50 rounded-md text-sm text-gray-500 text-center\">\n                              No new assistances added yet\n                            </div>\n                          )}\n\n                          {/* end new data */}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          toast.error(\n                            \"Initial Coordination Status empty or invalid. please try again\"\n                          );\n                          setCoordinatStatusListError(\n                            \"Initial Coordination Status is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3 gap-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={handleUpdateCurrentStep}\n                      disabled={loadingCaseUpdate}\n                      className=\"bg-gray-100 hover:bg-gray-200 text-[#344054] text-sm px-5 py-3 rounded-full transition-colors duration-200 disabled:opacity-50\"\n                    >\n                      {loadingCaseUpdate ? \"Updating...\" : \"Update\"}\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // Show loading indicator while submitting the form\n                        setIsLoading(true);\n                        setShouldNavigateToFinalStep(true); // Set flag to navigate to final step\n\n                        // Map assistance items with their provider services\n                        const assistanceItems = assistanceMultiSelect.map(\n                          (item) => ({\n                            start_date: item.start_date,\n                            end_date: item.end_date,\n                            appointment_date: item.appointment_date,\n                            service_location: item.service_location,\n                            provider_services: item.provider_services.map(\n                              (providerService) => ({\n                                provider: providerService.provider?.id,\n                                service: providerService.provider_service?.id,\n                                date: providerService.provider_date,\n                              })\n                            ),\n                          })\n                        );\n\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                            date: item.date,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_type_item:\n                              caseType === \"Medical\" ? caseTypeItem : \"\",\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date:\n                              caseTypeItem === \"Inpatient\"\n                                ? \"\"\n                                : appointmentDate,\n                            start_date:\n                              caseTypeItem === \"Inpatient\" ? startDate : \"\",\n                            end_date:\n                              caseTypeItem === \"Inpatient\" ? endDate : \"\",\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            assistances: assistanceItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                            assistance_deleted:\n                              assistanceMultiSelectDelete ?? [],\n                            //\n                            is_pay: isPay ? \"True\" : \"False\",\n                            price_tatal: priceTotal,\n                            currency_price: currencyCode.value ?? \"\",\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Save & Complete\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,qBAAqB,KAAQ,qCAAqC,CAC3E,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,KACL,iCAAiC,CACxC,MAAO,CAAAC,cAAc,KAAM,iCAAiC,CAC5D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CAEvD,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,uBAAuB,KAAQ,sCAAsC,CAC9E,OAASC,yBAAyB,KAAQ,iCAAiC,CAC3E,OAASC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAE1D;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,oBAAoB,CAAG,CAC3B,SAAS,CAAE,KAAK,CAChB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,cAAc,CAAE,KAAK,CACrB,sBAAsB,CAAE,KAAK,CAC7B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,OAAO,CAAE,KAAK,CACd,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,gBAAgB,CAAE,KAAK,CACvB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,OAAO,CAAE,KAAK,CACd,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,MAAM,CAAE,KAAK,CACb,cAAc,CAAE,KAAK,CACrB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,cAAc,CAAE,KAAK,CACrB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,KAAK,CACjB,MAAM,CAAE,KAAK,CACb,0BAA0B,CAAE,KAAK,CACjC,kCAAkC,CAAE,KAAK,CACzC,uBAAuB,CAAE,KAAK,CAC9B,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,UAAU,CAAE,KAAK,CACjB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,WAAW,CAAE,KAAK,CAClB,YAAY,CAAE,KAAK,CACnB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,oBAAoB,CAAE,KAAK,CAC3B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,SAAS,CAAE,KAAK,CAChB,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,eAAe,CAAE,KAAK,CACtB,QAAQ,CAAE,KAAK,CACf,OAAO,CAAE,KAAK,CACd,UAAU,CAAE,KAAK,CACjB,WAAW,CAAE,KAAK,CAClB,MAAM,CAAE,KAAK,CACb,MAAM,CAAE,KAAK,CACb,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,WAAW,CAAE,KAAK,CAClB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,kBAAkB,CAAE,KAAK,CACzB,YAAY,CAAE,KAAK,CACnB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,QAAQ,CAAE,KAAK,CACf,UAAU,CAAE,KAAK,CACjB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,aAAa,CAAE,KAAK,CACpB,QAAQ,CAAE,KAAK,CACf,kBAAkB,CAAE,KAAK,CACzB,UAAU,CAAE,KAAK,CACjB,aAAa,CAAE,KAAK,CACpB,OAAO,CAAE,KAAK,CACd,YAAY,CAAE,KAAK,CACnB,uBAAuB,CAAE,KAAK,CAC9B,SAAS,CAAE,KAAK,CAChB,aAAa,CAAE,KAAK,CACpB,UAAU,CAAE,KAAK,CACjB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,KAAK,CACf,YAAY,CAAE,KAAK,CACnB,MAAM,CAAE,KAAK,CACb,OAAO,CAAE,KAAK,CACd,qBAAqB,CAAE,KAAK,CAC5B,cAAc,CAAE,KAAK,CACrB,QAAQ,CAAE,KAAK,CACf,SAAS,CAAE,KAAK,CAChB,YAAY,CAAE,KAAK,CACnB,SAAS,CAAE,KAAK,CAChB,WAAW,CAAE,KAAK,CAClB,gBAAgB,CAAE,KAAK,CACvB,OAAO,CAAE,KACX,CAAC,CAED,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,KAAAC,SAAA,CACxB,KAAM,CAAAC,QAAQ,CAAGpC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqC,QAAQ,CAAGtC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAuC,QAAQ,CAAGzC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE0C,EAAG,CAAC,CAAGtC,SAAS,CAAC,CAAC,CACxB,KAAM,CAACuC,YAAY,CAAC,CAAGtC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAuC,OAAO,CAAGD,YAAY,CAACE,GAAG,CAAC,SAAS,CAAC,EAAI,CAAC,CAEhD;AACA,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiD,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACqD,aAAa,CAAEC,gBAAgB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACuD,KAAK,CAAEC,QAAQ,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACyD,UAAU,CAAEC,aAAa,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC2D,SAAS,CAAEC,YAAY,CAAC,CAAG5D,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC6D,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAAC+D,KAAK,CAAEC,QAAQ,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACiE,UAAU,CAAEC,aAAa,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACmE,OAAO,CAAEC,UAAU,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACuE,IAAI,CAAEC,OAAO,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACyE,SAAS,CAAEC,YAAY,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC2E,OAAO,CAAEC,UAAU,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC6E,YAAY,CAAEC,eAAe,CAAC,CAAG9E,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAAC+E,WAAW,CAAEC,cAAc,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACiF,aAAa,CAAEC,gBAAgB,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACmF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACqF,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAACuF,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGxF,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAACyF,yBAAyB,CAAEC,4BAA4B,CAAC,CAAG1F,QAAQ,CACxE,EACF,CAAC,CACD,KAAM,CAAC2F,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CACtE,KAAM,CAAC6F,2BAA2B,CAAEC,8BAA8B,CAAC,CACjE9F,QAAQ,CAAC,EAAE,CAAC,CACd,KAAM,CAAC+F,uBAAuB,CAAEC,0BAA0B,CAAC,CAAGhG,QAAQ,CAAC,EAAE,CAAC,CAC1E,KAAM,CAACiG,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGlG,QAAQ,CACxE,EACF,CAAC,CAED,KAAM,CAACmG,eAAe,CAAEC,kBAAkB,CAAC,CAAGpG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACqG,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGtG,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACuG,QAAQ,CAAEC,WAAW,CAAC,CAAGxG,QAAQ,CACtC,GAAI,CAAAyG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC,CACD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC8G,QAAQ,CAAEC,WAAW,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACgH,aAAa,CAAEC,gBAAgB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACkH,YAAY,CAAEC,eAAe,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACoH,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACsH,eAAe,CAAEC,kBAAkB,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACwH,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC0H,KAAK,CAAEC,QAAQ,CAAC,CAAG3H,QAAQ,CAAC,KAAK,CAAC,CAEzC,KAAM,CAAC4H,YAAY,CAAEC,eAAe,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC8H,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACgI,UAAU,CAAEC,aAAa,CAAC,CAAGjI,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACkI,eAAe,CAAEC,kBAAkB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAC1D;AACA,KAAM,CAACoI,eAAe,CAAEC,kBAAkB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwI,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGzI,QAAQ,CAAC,EAAE,CAAC,CAClE,KAAM,CAAC0I,wBAAwB,CAAEC,2BAA2B,CAAC,CAAG3I,QAAQ,CAAC,EAAE,CAAC,CAE5E,KAAM,CAAC4I,eAAe,CAAEC,kBAAkB,CAAC,CAAG7I,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8I,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/I,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACgJ,SAAS,CAAEC,YAAY,CAAC,CAAGjJ,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkJ,cAAc,CAAEC,iBAAiB,CAAC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACoJ,OAAO,CAAEC,UAAU,CAAC,CAAGrJ,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACsJ,YAAY,CAAEC,eAAe,CAAC,CAAGvJ,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACwJ,eAAe,CAAEC,kBAAkB,CAAC,CAAGzJ,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0J,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3J,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC4J,YAAY,CAAEC,eAAe,CAAC,CAAG7J,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC8J,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/J,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACgK,YAAY,CAAEC,eAAe,CAAC,CAAGjK,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkK,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnK,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACoK,aAAa,CAAEC,gBAAgB,CAAC,CAAGrK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvK,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACwK,aAAa,CAAEC,gBAAgB,CAAC,CAAGzK,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0K,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3K,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC4K,eAAe,CAAEC,kBAAkB,CAAC,CAAG7K,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8K,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/K,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgL,aAAa,CAAEC,gBAAgB,CAAC,CAAGjL,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkL,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnL,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoL,UAAU,CAAEC,aAAa,CAAC,CAAGrL,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsL,eAAe,CAAEC,kBAAkB,CAAC,CAAGvL,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACwL,MAAM,CAAEC,SAAS,CAAC,CAAGzL,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAAC0L,WAAW,CAAEC,cAAc,CAAC,CAAG3L,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC4L,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7L,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8L,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG/L,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACgM,eAAe,CAAEC,kBAAkB,CAAC,CAAGjM,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkM,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnM,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoM,YAAY,CAAEC,eAAe,CAAC,CAAGrM,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsM,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvM,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwM,aAAa,CAAEC,gBAAgB,CAAC,CAAGzM,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0M,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3M,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC4M,WAAW,CAAEC,cAAc,CAAC,CAAG7M,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8M,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG/M,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAACgN,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjN,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJkN,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGnN,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAACoN,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGrN,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJsN,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAGxM,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhO,SAAS,CAAC,IAAM,CACd,MAAO,IACLqN,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxO,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJsN,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGzN,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhO,SAAS,CAAC,IAAM,CACd,MAAO,IACLwO,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG5O,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJsN,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAG7N,WAAW,CAAC,CACdyM,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhO,SAAS,CAAC,IAAM,CACd,MAAO,IACL4O,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGhP,QAAQ,EAAAuC,SAAA,CAAC0M,QAAQ,CAACpM,OAAO,CAAC,UAAAN,SAAA,UAAAA,SAAA,CAAI,CAAC,CAAC,CACpE,KAAM,CAAC2M,SAAS,CAAEC,YAAY,CAAC,CAAGnP,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACoP,yBAAyB,CAAEC,4BAA4B,CAAC,CAAGrP,QAAQ,CAAC,KAAK,CAAC,CAEjF,KAAM,CAAAsP,SAAS,CAAGpP,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGvP,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE;AACA1P,SAAS,CAAC,IAAM,CACd,GAAI4P,SAAS,EAAIA,SAAS,CAACG,MAAM,CAAG,CAAC,CAAE,CACrCC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAEL,SAAS,CAACG,MAAM,CAAC,CACtE,CACF,CAAC,CAAE,CAACH,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAM,cAAc,CAAG/P,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAACW,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAGpQ,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAAC3O,UAAU,CAAC,CAC3D,KAAM,CAAE2P,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAGzQ,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAACqB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB;AACA5Q,SAAS,CAAC,IAAM,CACdgQ,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAE9C,GAAIa,YAAY,EAAIA,YAAY,CAACf,MAAM,CAAG,CAAC,EAAI7K,aAAa,CAAE,CAC5D8K,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAE/K,aAAa,CAAC,CAEjE;AACA,KAAM,CAAA+L,gBAAgB,CAAGH,YAAY,CAACI,IAAI,CACvCC,IAAI,EAAKC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GAAKwO,MAAM,CAAClM,aAAa,CACpD,CAAC,CAED,GAAI+L,gBAAgB,CAAE,CACpBjB,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAEgB,gBAAgB,CAACI,SAAS,CAAC,CAC7D;AACAC,UAAU,CAAC,IAAM,CACfrM,cAAc,CAAC,CACbsM,KAAK,CAAEN,gBAAgB,CAACrO,EAAE,CAC1B4O,KAAK,CAAEP,gBAAgB,CAACI,SAC1B,CAAC,CAAC,CACF;AACAjC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLY,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAChD;AACA,KAAM,CAAAwB,eAAe,CAAGX,YAAY,CAACI,IAAI,CACtCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAKsC,aACxB,CAAC,CACD,GAAIuM,eAAe,CAAE,CACnBzB,OAAO,CAACC,GAAG,CACT,4CAA4C,CAC5CwB,eAAe,CAACJ,SAClB,CAAC,CACDpM,cAAc,CAAC,CACbsM,KAAK,CAAEE,eAAe,CAAC7O,EAAE,CACzB4O,KAAK,CAAEC,eAAe,CAACJ,SACzB,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAAC,CAAE,CAACP,YAAY,CAAE5L,aAAa,CAAC,CAAC,CAEjC,KAAM,CAAAwM,UAAU,CAAGvR,WAAW,CAAEqP,KAAK,EAAKA,KAAK,CAAC1O,UAAU,CAAC,CAC3D,KAAM,CAAE6Q,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB9R,SAAS,CAAC,IAAM,CACd,GAAI,CAACyP,QAAQ,CAAE,CACbhN,QAAQ,CAACqP,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AACA1C,YAAY,CAAC,IAAI,CAAC,CAElB;AACAzM,QAAQ,CAACvB,yBAAyB,CAAC,GAAG,CAAC,CAAC,CACxCuB,QAAQ,CAAChC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CACpCgC,QAAQ,CAACxB,uBAAuB,CAAC,GAAG,CAAC,CAAC,CACtCwB,QAAQ,CAAC9B,UAAU,CAAC+B,EAAE,CAAC,CAAC,CAExB;AACA,KAAM,CAAAmP,SAAS,CAAGT,UAAU,CAAC,IAAM,CACjClC,YAAY,CAAC,KAAK,CAAC,CACnBY,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC,CACvE,CAAC,CAAE,IAAI,CAAC,CAER;AACA,MAAO,IAAM+B,YAAY,CAACD,SAAS,CAAC,CACtC,CACF,CAAC,CAAE,CAACtP,QAAQ,CAAEgN,QAAQ,CAAE9M,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtC5C,SAAS,CAAC,IAAM,CACd,GAAI6R,iBAAiB,CAAE,CACrB,GAAIxC,yBAAyB,CAAE,CAC7BJ,aAAa,CAAC,CAAC,CAAC,CAChBK,4BAA4B,CAAC,KAAK,CAAC,CAAE;AACvC,CACAF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACyC,iBAAiB,CAAExC,yBAAyB,CAAC,CAAC,CAElD;AACArP,SAAS,CAAC,IAAM,CACd,GAAI2R,iBAAiB,CAAE,CACrBvC,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CAACuC,iBAAiB,CAAC,CAAC,CAEvB;AACA3R,SAAS,CAAC,IAAM,CACd;AACA,GACE,CAAC6P,gBAAgB,EACjB,CAACW,eAAe,EAChBZ,SAAS,EACTA,SAAS,CAACG,MAAM,CAAG,CAAC,EACpBY,QAAQ,CACR,CACA;AACAvB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIuC,iBAAiB,CAAE,CAC5B;AACAvC,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAAE,CACDS,gBAAgB,CAChBW,eAAe,CACfmB,iBAAiB,CACjB/B,SAAS,CACTe,QAAQ,CACT,CAAC,CAEF3Q,SAAS,CAAC,IAAM,CACd;AACA,GAAI2Q,QAAQ,GAAKsB,SAAS,EAAItB,QAAQ,GAAK,IAAI,CAAE,KAAAuB,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAIxC,QAAQ,CAACyC,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpB3Q,YAAY,EAAAoQ,qBAAA,CAAC1C,QAAQ,CAACyC,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/ChQ,WAAW,EAAAiQ,qBAAA,CAAC3C,QAAQ,CAACyC,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7CzP,YAAY,EAAA0P,qBAAA,CAAC5C,QAAQ,CAACyC,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CtP,QAAQ,EAAAuP,qBAAA,CAAC7C,QAAQ,CAACyC,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9C/P,QAAQ,EAAAgQ,sBAAA,CAAC9C,QAAQ,CAACyC,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9CpP,UAAU,EAAAqP,sBAAA,CAAC/C,QAAQ,CAACyC,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAElD,KAAM,CAAAS,cAAc,EAAAR,sBAAA,CAAGhD,QAAQ,CAACyC,OAAO,CAACgB,eAAe,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7D,KAAM,CAAAU,YAAY,CAAGhT,SAAS,CAAC6P,IAAI,CAChCoD,MAAM,EAAKA,MAAM,CAACtS,KAAK,GAAKmS,cAC/B,CAAC,CAED,GAAIE,YAAY,CAAE,CAChBxP,UAAU,CAAC,CACT0M,KAAK,CAAE8C,YAAY,CAACrS,KAAK,CACzBwP,KAAK,cACH9P,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChT,IAAA,SAAM+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEH,YAAY,CAACI,IAAI,CAAO,CAAC,cACjDjT,IAAA,SAAAgT,QAAA,CAAOH,YAAY,CAACrS,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACL6C,UAAU,CAAC,EAAE,CAAC,CAChB,CAEAJ,OAAO,EAAAmP,sBAAA,CAACjD,QAAQ,CAACyC,OAAO,CAACsB,YAAY,UAAAd,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CAEA,KAAM,CAAAe,eAAe,EAAAzC,qBAAA,CAAGvB,QAAQ,CAACiE,cAAc,UAAA1C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAErD,KAAM,CAAA2C,aAAa,CAAGvT,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACtCoD,MAAM,EAAKA,MAAM,CAACQ,IAAI,GAAKH,eAC9B,CAAC,CAED,GAAIE,aAAa,CAAE,CACjB/M,eAAe,CAAC,CACdyJ,KAAK,CAAEsD,aAAa,CAACC,IAAI,CACzBtD,KAAK,CACHqD,aAAa,CAACE,IAAI,GAAK,EAAE,CACrBF,aAAa,CAACE,IAAI,CAAG,IAAI,CAAGF,aAAa,CAACC,IAAI,CAAG,IAAI,EAAI,EAAE,CAC3D,EACR,CAAC,CAAC,CACJ,CAAC,IAAM,CACLhN,eAAe,CAAC,EAAE,CAAC,CACrB,CAEAF,QAAQ,CAAC+I,QAAQ,CAACqE,MAAM,CAAC,CACzB9M,aAAa,EAAAiK,qBAAA,CAACxB,QAAQ,CAACsE,WAAW,UAAA9C,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACxC;AACA,GAAIxB,QAAQ,CAACuE,gBAAgB,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,KAAM,CAAAC,kBAAkB,EAAAF,qBAAA,EAAAC,sBAAA,CAAGzE,QAAQ,CAACuE,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2BxS,EAAE,UAAAuS,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC9DnF,OAAO,CAACC,GAAG,CACT,uCAAuC,CACvCoF,kBACF,CAAC,CACDrF,OAAO,CAACC,GAAG,CACT,iCAAiC,CACjCU,QAAQ,CAACuE,gBACX,CAAC,CAED;AACA5D,UAAU,CAAC,IAAM,CACfnM,gBAAgB,CAACkQ,kBAAkB,CAAC,CACpCrF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEoF,kBAAkB,CAAC,CACnE,CAAC,CAAE,EAAE,CAAC,CACR,CACA5O,WAAW,EAAA2L,mBAAA,CAACzB,QAAQ,CAAC2E,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCpL,WAAW,EAAAqL,mBAAA,CAAC1B,QAAQ,CAAC4E,SAAS,UAAAlD,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrC7K,kBAAkB,EAAA8K,qBAAA,CAAC3B,QAAQ,CAAC6E,gBAAgB,UAAAlD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACA,KAAM,CAAAmD,QAAQ,CACZ,CAAA9E,QAAQ,SAARA,QAAQ,kBAAA4B,qBAAA,CAAR5B,QAAQ,CAAE+E,WAAW,UAAAnD,qBAAA,iBAArBA,qBAAA,CAAuBxE,GAAG,CAAE4H,MAAM,EAAKA,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEC,mBAAmB,CAAC,GACnE,EAAE,CAAE;AAENlN,sBAAsB,CAAC+M,QAAQ,CAAC,CAEhC;AACAnN,kBAAkB,EAAAkK,qBAAA,CAAC7B,QAAQ,CAACiF,mBAAmB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtD1J,kBAAkB,EAAA2J,qBAAA,CAAC9B,QAAQ,CAACkF,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDvJ,YAAY,EAAAwJ,oBAAA,CAAC/B,QAAQ,CAACmF,UAAU,UAAApD,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAAC,CACvCpJ,UAAU,EAAAqJ,kBAAA,CAAChC,QAAQ,CAACoF,QAAQ,UAAApD,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CAAC,CACnCvL,eAAe,EAAAwL,qBAAA,CAACjC,QAAQ,CAACqF,cAAc,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9ClJ,kBAAkB,EAAAmJ,qBAAA,CAAClC,QAAQ,CAACsF,gBAAgB,UAAApD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAIlC,QAAQ,CAACuF,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB,GAAI,CAAAC,eAAe,EAAAF,qBAAA,EAAAC,kBAAA,CAAGzF,QAAQ,CAACuF,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmBxT,EAAE,UAAAuT,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjD,KAAM,CAAAG,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAKyT,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,CACjBxM,eAAe,CAAC,CACdyH,KAAK,CAAE+E,aAAa,CAAC1T,EAAE,CACvB4O,KAAK,CAAE8E,aAAa,CAACjF,SACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLvH,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACA,GAAI6G,QAAQ,CAAC4F,iBAAiB,CAAE,KAAAC,qBAAA,CAC9BvQ,0BAA0B,EAAAuQ,qBAAA,CAAC7F,QAAQ,CAAC4F,iBAAiB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9D,CAEA,GAAI7F,QAAQ,CAAC8F,mBAAmB,CAAE,KAAAC,qBAAA,CAChCvQ,4BAA4B,EAAAuQ,qBAAA,CAAC/F,QAAQ,CAAC8F,mBAAmB,UAAAC,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAClE,CACA;AACA1J,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAI2D,QAAQ,CAACgG,eAAe,CAAE,CAC5B3J,6BAA6B,CAAC2D,QAAQ,CAACgG,eAAe,CAAC,CACzD,CACA;AACAzL,gBAAgB,EAAA4H,qBAAA,CAACnC,QAAQ,CAACiG,cAAc,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/CxH,aAAa,EAAAyH,qBAAA,CAACpC,QAAQ,CAACkG,WAAW,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzCrH,SAAS,EAAAsH,qBAAA,CAACrC,QAAQ,CAACmG,cAAc,UAAA9D,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvC9F,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIyD,QAAQ,CAACoG,eAAe,CAAE,CAC5B7J,qBAAqB,CAACyD,QAAQ,CAACoG,eAAe,CAAC,CACjD,CACA;AACA,GAAIpG,QAAQ,CAACqG,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB,GAAI,CAAAC,gBAAgB,EAAAF,qBAAA,EAAAC,mBAAA,CAAGvG,QAAQ,CAACqG,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoBtU,EAAE,UAAAqU,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAEnD,GAAI,CAAAG,cAAc,CAAGhH,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEc,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAKuU,gBACxB,CAAC,CAED,GAAIC,cAAc,CAAE,CAClBpH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBnE,mBAAmB,CAAC,CAClByF,KAAK,CAAE6F,cAAc,CAACxU,EAAE,CACxB4O,KAAK,CAAE4F,cAAc,CAACC,cAAc,EAAI,EAC1C,CAAC,CAAC,CACJ,CAAC,IAAM,CACLrH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBnE,mBAAmB,CAAC,CAClByF,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CACF,CACAlF,eAAe,EAAA2G,qBAAA,CAACtC,QAAQ,CAAC2G,aAAa,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C/G,kBAAkB,EAAAgH,qBAAA,CAACvC,QAAQ,CAAC4G,gBAAgB,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDxG,gBAAgB,EAAAyG,qBAAA,CAACxC,QAAQ,CAAC6G,gBAAgB,UAAArE,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjD/F,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAIuD,QAAQ,CAAC8G,oBAAoB,CAAE,CACjCrK,oCAAoC,CAACuD,QAAQ,CAAC8G,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAAC9G,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAA+G,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1C,GAAI,KAAAC,kBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CACF,GAAI,CAAAC,OAAO,CAAG,IAAI,CAElB;AACA,GAAI/I,UAAU,GAAK,CAAC,CAAE,CACpB;AACA7L,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBY,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBI,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnB+B,gBAAgB,CAAC,EAAE,CAAC,CACpBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,uBAAuB,CAAC,EAAE,CAAC,CAC3BU,kBAAkB,CAAC,EAAE,CAAC,CACtBJ,oBAAoB,CAAC,EAAE,CAAC,CACxB3C,mBAAmB,CAAC,EAAE,CAAC,CACvBtB,iBAAiB,CAAC,EAAE,CAAC,CAErB;AACA,GAAI,CAACf,SAAS,EAAIA,SAAS,CAACgV,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACzC7U,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C4U,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC3U,QAAQ,EAAIA,QAAQ,CAAC4U,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvCzU,gBAAgB,CAAC,wBAAwB,CAAC,CAC1CwU,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC/T,KAAK,EAAIA,KAAK,CAACgU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACjC7T,aAAa,CAAC,2BAA2B,CAAC,CAC1C4T,OAAO,CAAG,KAAK,CACjB,CAAC,IAAM,CACL;AACA,KAAM,CAAAE,UAAU,CAAG,wBAAwB,CAC3C,GAAI,CAACA,UAAU,CAACC,IAAI,CAAClU,KAAK,CAACmU,OAAO,CAAC,aAAa,CAAE,EAAE,CAAC,CAAC,CAAE,CACtDhU,aAAa,CAAC,4CAA4C,CAAC,CAC3D4T,OAAO,CAAG,KAAK,CACjB,CACF,CAGA,GAAIvU,KAAK,EAAIA,KAAK,CAACwU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAG/B,KAAM,CAAAI,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACF,IAAI,CAAC1U,KAAK,CAAC,CAAE,CAC3BG,aAAa,CAAC,qCAAqC,CAAC,CACpDoU,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAI,CAAC3T,OAAO,EAAIA,OAAO,CAAC4T,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACrCzT,eAAe,CAAC,sBAAsB,CAAC,CACvCwT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACvT,IAAI,EAAIA,IAAI,CAACwT,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/BrT,YAAY,CAAC,mBAAmB,CAAC,CACjCoT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACnT,OAAO,EAAI,CAACA,OAAO,CAAC2M,KAAK,EAAI3M,OAAO,CAAC2M,KAAK,GAAK,EAAE,CAAE,CACtDxM,eAAe,CAAC,sBAAsB,CAAC,CACvCgT,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACvR,QAAQ,EAAIA,QAAQ,CAACwR,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvClR,gBAAgB,CAAC,wBAAwB,CAAC,CAC1CiR,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAChR,QAAQ,EAAIA,QAAQ,CAACiR,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvC9Q,gBAAgB,CAAC,wBAAwB,CAAC,CAC1C6Q,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAACxQ,eAAe,EAAIA,eAAe,CAACyQ,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACrDtQ,uBAAuB,CAAC,+BAA+B,CAAC,CACxDqQ,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC/S,WAAW,EAAI,CAACA,WAAW,CAACuM,KAAK,EAAIvM,WAAW,CAACuM,KAAK,GAAK,EAAE,CAAE,CAClElM,mBAAmB,CAAC,0BAA0B,CAAC,CAC/C0S,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAClQ,YAAY,EAAI,CAACA,YAAY,CAAC0J,KAAK,EAAI1J,YAAY,CAAC0J,KAAK,GAAK,EAAE,CAAE,CACrEvJ,oBAAoB,CAAC,uBAAuB,CAAC,CAC7C+P,OAAO,CAAG,KAAK,CACjB,CAEA,GAAI,CAAC9P,UAAU,EAAIA,UAAU,GAAK,EAAE,EAAIA,UAAU,CAAG,CAAC,CAAE,CACtDG,kBAAkB,CAAC,qDAAqD,CAAC,CACzE2P,OAAO,CAAG,KAAK,CACjB,CAEA;AACA,GAAInU,SAAS,EAAIA,SAAS,CAACoU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACxC,KAAM,CAAAK,YAAY,CAAG,GAAI,CAAA3R,IAAI,CAAC9C,SAAS,CAAC,CACxC,KAAM,CAAA0U,KAAK,CAAG,GAAI,CAAA5R,IAAI,CAAC,CAAC,CACxB,GAAI2R,YAAY,CAAGC,KAAK,CAAE,CACxBvU,iBAAiB,CAAC,qCAAqC,CAAC,CACxDgU,OAAO,CAAG,KAAK,CACjB,CACF,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACApG,2BAA2B,CAAC,EAAE,CAAC,CAE/B;AACA,GAAI,CAACH,mBAAmB,EAAIA,mBAAmB,CAACsH,MAAM,GAAK,CAAC,CAAE,CAC5DnH,2BAA2B,CAAC,+CAA+C,CAAC,CAC5EmP,OAAO,CAAG,KAAK,CACjB,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA;AACA;AAAA,CAED,IAAM,IAAIA,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA5D,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,cAAc,CAAC,EAAE,CAAC,CAElB;AACA,GAAIX,aAAa,EAAIA,aAAa,CAAC+M,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAChD,GAAI/M,aAAa,CAAC8E,MAAM,CAAG,CAAC,CAAE,CAC5B3E,qBAAqB,CAAC,+CAA+C,CAAC,CACtE2M,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAItM,MAAM,EAAIA,MAAM,GAAK,EAAE,EAAIA,MAAM,CAAG,CAAC,CAAE,CACzCG,cAAc,CAAC,4BAA4B,CAAC,CAC5CmM,OAAO,CAAG,KAAK,CACjB,CAEF,CAAC,IAAM,IAAI/I,UAAU,GAAK,CAAC,CAAE,CAC3B;AACA5C,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CAExB;AACA,GAAIP,eAAe,EAAIA,eAAe,CAAC+L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpD,GAAI/L,eAAe,CAAC8D,MAAM,CAAG,CAAC,CAAE,CAC9B3D,uBAAuB,CAAC,iDAAiD,CAAC,CAC1E2L,OAAO,CAAG,KAAK,CACjB,CACF,CAEA,GAAI1L,YAAY,EAAIA,YAAY,CAAC2L,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9C,GAAI3L,YAAY,CAAC0D,MAAM,CAAG,CAAC,CAAE,CAC3BvD,oBAAoB,CAAC,8CAA8C,CAAC,CACpEuL,OAAO,CAAG,KAAK,CACjB,CACF,CACF,CAEA;AACA,GAAI,CAACA,OAAO,CAAE,CACZrX,KAAK,CAAC6X,KAAK,CAAC,mDAAmD,CAAC,CAChE,OACF,CAEAnJ,YAAY,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAoJ,eAAe,CAAG5S,qBAAqB,CAACmI,GAAG,CAAEoD,IAAI,GAAM,CAC3D2E,UAAU,CAAE3E,IAAI,CAAC2E,UAAU,CAC3BC,QAAQ,CAAE5E,IAAI,CAAC4E,QAAQ,CACvBF,gBAAgB,CAAE1E,IAAI,CAAC0E,gBAAgB,CACvCI,gBAAgB,CAAE9E,IAAI,CAAC8E,gBAAgB,CACvCM,iBAAiB,CAAEpF,IAAI,CAACoF,iBAAiB,CAACxI,GAAG,CAAE3H,eAAe,OAAAqS,qBAAA,CAAAC,sBAAA,OAAM,CAClExC,QAAQ,EAAAuC,qBAAA,CAAErS,eAAe,CAAC8P,QAAQ,UAAAuC,qBAAA,iBAAxBA,qBAAA,CAA0B7V,EAAE,CACtC+V,OAAO,EAAAD,sBAAA,CAAEtS,eAAe,CAACwS,gBAAgB,UAAAF,sBAAA,iBAAhCA,sBAAA,CAAkC9V,EAAE,CAC7CiW,IAAI,CAAEzS,eAAe,CAAC0S,aACxB,CAAC,EAAC,CACJ,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAC,aAAa,CAAGvT,mBAAmB,CAACuI,GAAG,CAAEoD,IAAI,OAAA6H,aAAA,CAAAC,cAAA,OAAM,CACvDN,OAAO,EAAAK,aAAA,CAAE7H,IAAI,CAACwH,OAAO,UAAAK,aAAA,iBAAZA,aAAA,CAAcpW,EAAE,CACzBsT,QAAQ,EAAA+C,cAAA,CAAE9H,IAAI,CAAC+E,QAAQ,UAAA+C,cAAA,iBAAbA,cAAA,CAAerW,EAAE,CAC3BiW,IAAI,CAAE1H,IAAI,CAAC0H,IACb,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAlW,QAAQ,CACZ7B,UAAU,CAAC8B,EAAE,CAAE,CACbiR,UAAU,CAAE7Q,SAAS,CACrB8Q,SAAS,CAAE1Q,QAAQ,CACnBiO,SAAS,CAAErO,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC2Q,SAAS,CAAEnQ,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1BoQ,aAAa,CAAEhQ,KAAK,CACpBiQ,aAAa,CAAEzQ,KAAK,CACpB0Q,eAAe,CAAE9P,OAAO,CACxBsQ,YAAY,CAAElQ,IAAI,CAClB4P,eAAe,CAAExP,OAAO,CAAC2M,KAAK,CAC9B;AACAvM,WAAW,EAAA2S,kBAAA,CAAE3S,WAAW,CAACuM,KAAK,UAAAoG,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACpCrC,SAAS,CAAE9O,QAAQ,CACnB+O,SAAS,CAAExO,QAAQ,CACnBiP,cAAc,CAAEjP,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC1DqO,gBAAgB,CAAEjO,eAAe,CACjC;AACAqO,mBAAmB,CAAEvN,eAAe,CACpCqN,WAAW,CAAEjN,mBAAmB,CAChCoN,gBAAgB,CAAE1O,YAAY,GAAK,WAAW,CAAG,EAAE,CAAG0B,eAAe,CACrEiN,UAAU,CAAE3O,YAAY,GAAK,WAAW,CAAG8B,SAAS,CAAG,EAAE,CACzD8M,QAAQ,CAAE5O,YAAY,GAAK,WAAW,CAAGkC,OAAO,CAAG,EAAE,CACrD4M,gBAAgB,CAAExM,eAAe,CACjCyM,QAAQ,EAAA0B,mBAAA,CAAE/N,YAAY,CAAC0H,KAAK,UAAAqG,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAClC;AACAhB,cAAc,CAAE3L,aAAa,CAC7B4L,WAAW,CAAExL,UAAU,CACvByL,cAAc,CAAErL,MAAM,CACtBuL,SAAS,EAAAa,qBAAA,CAAEhM,gBAAgB,CAAC0F,KAAK,UAAAsG,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACvCN,gBAAgB,CAAEtL,eAAe,CACjCqL,aAAa,CAAEjL,YAAY,CAC3BmL,gBAAgB,CAAE/K,aAAa,CAC/B;AACAyM,uBAAuB,CAAE7L,0BAA0B,CACnD8L,cAAc,CAAE3K,kBAAkB,CAClC4K,8BAA8B,CAAExK,iCAAiC,CACjEyK,aAAa,CAAExM,WAAW,CAC1B+C,SAAS,CAAEmJ,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9BO,WAAW,CAAEd,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAClCe,iBAAiB,CAAE7T,yBAAyB,SAAzBA,yBAAyB,UAAzBA,yBAAyB,CAAI,EAAE,CAClD8T,kBAAkB,CAAE1T,2BAA2B,SAA3BA,2BAA2B,UAA3BA,2BAA2B,CAAI,EAAE,CACrD;AACAkP,MAAM,CAAErN,KAAK,CAAG,MAAM,CAAG,OAAO,CAChCsN,WAAW,CAAEhN,UAAU,CACvB2M,cAAc,EAAAkD,mBAAA,CAAEjQ,YAAY,CAAC0J,KAAK,UAAAuG,mBAAA,UAAAA,mBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CAED1I,YAAY,CAAC,KAAK,CAAC,CACnB1O,KAAK,CAAC+Y,OAAO,CAAC,4BAA4B,CAAC,CAC7C,CAAE,MAAOlB,KAAK,CAAE,CACdnJ,YAAY,CAAC,KAAK,CAAC,CACnB1O,KAAK,CAAC6X,KAAK,CAAC,0CAA0C,CAAC,CACzD,CACF,CAAC,CAED,mBACE7W,KAAA,CAAClB,aAAa,EAAAgU,QAAA,EAEXrF,SAAS,eACR3N,IAAA,QAAK+S,SAAS,CAAC,+FAA+F,CAAAC,QAAA,cAC5G9S,KAAA,QAAK6S,SAAS,CAAC,8DAA8D,CAAAC,QAAA,eAC3EhT,IAAA,QAAK+S,SAAS,CAAC,iFAAiF,CAAM,CAAC,cACvG/S,IAAA,QAAK+S,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CAAC,iBAAe,CAAK,CAAC,EAC7D,CAAC,CACH,CACN,cAED9S,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9S,KAAA,QAAK6S,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtDhT,IAAA,MAAGkY,IAAI,CAAC,YAAY,CAAAlF,QAAA,cAClB9S,KAAA,QAAK6S,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5DhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzY,IAAA,SAAM+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJhT,IAAA,SAAAgT,QAAA,cACEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzY,IAAA,QAAK+S,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7ChT,IAAA,OAAI+S,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,WAEpE,CAAI,CAAC,CACF,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJ9S,KAAA,QAAK6S,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC9S,KAAA,QAAK6S,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxEhT,IAAA,QAAK+S,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7GzS,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEiM,GAAG,CAAC,CAACmM,IAAI,CAAEnY,KAAK,gBAC1BL,KAAA,QACEyY,OAAO,CAAEA,CAAA,GAAM,CACb,GAAInL,UAAU,CAAGkL,IAAI,CAACnY,KAAK,EAAIiN,UAAU,GAAK,CAAC,CAAE,CAC/CC,aAAa,CAACiL,IAAI,CAACnY,KAAK,CAAC,CAC3B,CACF,CAAE,CACFwS,SAAS,CAAG,kCACVvF,UAAU,CAAGkL,IAAI,CAACnY,KAAK,EAAIiN,UAAU,GAAK,CAAC,CACvC,gBAAgB,CAChB,EACL,8BAA8B,CAAAwF,QAAA,EAE9BxF,UAAU,CAAGkL,IAAI,CAACnY,KAAK,cACtBP,IAAA,QAAK+S,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHhT,IAAA,QACE4Y,GAAG,CAAE3Z,eAAgB,CACrB8T,SAAS,CAAC,QAAQ,CAClB8F,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJpL,UAAU,GAAKkL,IAAI,CAACnY,KAAK,cAC3BP,IAAA,QAAK+S,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExE/S,IAAA,QAAK+S,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjHhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAElBhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDvY,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE0F,IAAI,CAAClY,KAAK,CAAM,CAAC,CACtDgN,UAAU,GAAKkL,IAAI,CAACnY,KAAK,cACxBP,IAAA,QAAK+S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChD0F,IAAI,CAACjY,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNP,KAAA,QAAK6S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAEtDxF,UAAU,GAAK,CAAC,cACftN,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfhT,IAAA,QAAK+S,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACVrR,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpCuX,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBnJ,KAAK,CAAEvO,SAAU,CACjB2X,QAAQ,CAAGC,CAAC,EAAK3X,YAAY,CAAC2X,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC/C,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtR,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAE7C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,UACE+S,SAAS,CAAC,wEAAwE,CAClFkG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBnJ,KAAK,CAAEnO,QAAS,CAChBuX,QAAQ,CAAGC,CAAC,EAAKvX,WAAW,CAACuX,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN7P,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACV7Q,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpC+W,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BnJ,KAAK,CAAE/N,KAAM,CACbmX,QAAQ,CAAGC,CAAC,EAAKnX,QAAQ,CAACmX,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC3C,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9Q,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENhC,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QACrC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,uBACVrQ,UAAU,CAAG,eAAe,CAAG,kBAChC,mCAAmC,CACpCuW,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBnJ,KAAK,CAAEvN,KAAM,CACb2W,QAAQ,CAAGC,CAAC,EAAK3W,QAAQ,CAAC2W,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC3C,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCtQ,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9S,KAAA,QAAK6S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,UACpC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE3M,OAAQ,CACf+V,QAAQ,CAAGrG,MAAM,EAAK,CACpBzP,UAAU,CAACyP,MAAM,CAAC,CAElB;AACA,GAAIA,MAAM,EAAIA,MAAM,CAAC/C,KAAK,CAAE,CAC1B;AACA,KAAM,CAAAsJ,WAAW,CAAGvG,MAAM,CAAC/C,KAAK,CAChC,KAAM,CAAA1J,YAAY,CAAGhG,oBAAoB,CAACgZ,WAAW,CAAC,CAEtD,GAAIhT,YAAY,CAAE,CAChB;AACA,KAAM,CAAAiT,cAAc,CAAGxZ,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE4P,IAAI,CACvC6J,QAAQ,EAAKA,QAAQ,CAACjG,IAAI,GAAKjN,YAClC,CAAC,CAED,GAAIiT,cAAc,CAAE,CAClBhT,eAAe,CAAC,CACdyJ,KAAK,CAAEuJ,cAAc,CAAChG,IAAI,CAC1BtD,KAAK,CAAEsJ,cAAc,CAAC/F,IAAI,GAAK,EAAE,CAC7B+F,cAAc,CAAC/F,IAAI,CAAG,IAAI,CAAG+F,cAAc,CAAChG,IAAI,CAAG,GAAG,CACtDgG,cAAc,CAAChG,IACrB,CAAC,CAAC,CAEF;AACApU,KAAK,CAAC+Y,OAAO,CAAE,qCAAoCqB,cAAc,CAAC/F,IAAK,KAAI+F,cAAc,CAAChG,IAAK,GAAE,CAAC,CACpG,CACF,CACF,CACF,CAAE,CACFP,SAAS,CAAC,SAAS,CACnByG,OAAO,CAAE3Z,SAAS,CAAC0M,GAAG,CAAEnJ,OAAO,GAAM,CACnC2M,KAAK,CAAE3M,OAAO,CAAC5C,KAAK,CACpBwP,KAAK,cACH9P,KAAA,QACE6S,SAAS,CAAG,GACV3P,OAAO,CAAC5C,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EACjC,6BAA6B,CAAAwS,QAAA,eAE9BhT,IAAA,SAAM+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAE5P,OAAO,CAAC6P,IAAI,CAAO,CAAC,cAC5CjT,IAAA,SAAAgT,QAAA,CAAO5P,OAAO,CAAC5C,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ0Y,WAAW,CAAC,qBAAqB,CACjCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAExW,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvByW,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFja,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC1P,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACNpD,KAAA,QAAK6S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACR,eAAe,EACd2a,MAAM,CAAC,yCAAyC,CAChDpH,SAAS,CAAG,wBACV7P,SAAS,CAAG,eAAe,CAAG,kBAC/B,mCAAmC,CACpCiW,QAAQ,CAAGC,CAAC,EAAK,CACfnW,OAAO,CAACmW,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAC,CACzB,CAAE,CACFqK,eAAe,CAAGC,KAAK,EAAK,CAC1B,GAAIA,KAAK,EAAIA,KAAK,CAACC,QAAQ,CAAE,KAAAC,qBAAA,CAC3BtX,OAAO,EAAAsX,qBAAA,CAACF,KAAK,CAACG,iBAAiB,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtC;AACA;AACA;AACA;AACA;AACF,CACF,CAAE,CACFE,YAAY,CAAEzX,IAAK,CACnB0X,KAAK,CAAE,CAAC,MAAM,CAAE,CAChBC,QAAQ,CAAC,IAAI,CACd,CAAC,cAUF3a,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC9P,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENhD,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1F,gBAAiB,CACxB8O,QAAQ,CAAGrG,MAAM,EAAK,CACpBxI,mBAAmB,CAACwI,MAAM,CAAC,CAC7B,CAAE,CACF0G,OAAO,CAAE5K,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAErC,GAAG,CAAEiJ,SAAS,GAAM,CACvCzF,KAAK,CAAEyF,SAAS,CAACpU,EAAE,CACnB4O,KAAK,CAAEwF,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ+E,YAAY,CAAEA,CAAC9H,MAAM,CAAE+H,UAAU,GAC/B/H,MAAM,CAAC9C,KAAK,CACT8K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/H,SAAS,CAAC,SAAS,CACnBmG,WAAW,CAAC,qBAAqB,CACjCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEvP,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBwP,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFja,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzI,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNrK,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACVpI,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCsO,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BnJ,KAAK,CAAEtF,eAAgB,CACvB0O,QAAQ,CAAGC,CAAC,EAAK1O,kBAAkB,CAAC0O,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CACrD,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrI,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN3K,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAEvM,WAAY,CACnB2V,QAAQ,CAAGrG,MAAM,EAAK,CACpBrP,cAAc,CAACqP,MAAM,CAAC,CACxB,CAAE,CACFC,SAAS,CAAC,SAAS,CACnByG,OAAO,CAAElK,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAE/C,GAAG,CAAEoD,IAAI,GAAM,CACpCI,KAAK,CAAEJ,IAAI,CAACvO,EAAE,CACd4O,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ+K,YAAY,CAAEA,CAAC9H,MAAM,CAAE+H,UAAU,GAC/B/H,MAAM,CAAC9C,KAAK,CACT8K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD5B,WAAW,CAAC,uBAAuB,CACnCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElW,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvBmW,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFja,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCpP,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,CAEH,CAAC,cAEN1D,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACV1N,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CACpC4T,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCnJ,KAAK,CAAE/K,QAAS,CAChBmU,QAAQ,CAAGC,CAAC,EAAKnU,WAAW,CAACmU,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC9C,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3N,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACNnF,KAAA,QAAK6S,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACE9S,KAAA,WACE6P,KAAK,CAAExK,QAAS,CAChB4T,QAAQ,CAAGC,CAAC,EAAK5T,WAAW,CAAC4T,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC7CgD,SAAS,CAAG,wBACVtN,aAAa,CACT,eAAe,CACf,kBACL,mCAAmC,CAAAuN,QAAA,eAEpChT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvChT,IAAA,WAAQ+P,KAAK,CAAE,SAAU,CAAAiD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1ChT,IAAA,WAAQ+P,KAAK,CAAE,WAAY,CAAAiD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACThT,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvN,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACLF,QAAQ,GAAK,SAAS,eACrBrF,KAAA,QAAK6S,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,YAClC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACjD,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACE9S,KAAA,WACE6P,KAAK,CAAEpK,YAAa,CACpBwT,QAAQ,CAAGC,CAAC,EAAKxT,eAAe,CAACwT,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CACjDgD,SAAS,CAAG,wBACVlN,iBAAiB,CACb,eAAe,CACf,kBACL,mCAAmC,CAAAmN,QAAA,eAEpChT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,kBAAgB,CAAQ,CAAC,cAC5ChT,IAAA,WAAQ+P,KAAK,CAAE,YAAa,CAAAiD,QAAA,CAAC,YAAU,CAAQ,CAAC,cAChDhT,IAAA,WAAQ+P,KAAK,CAAE,WAAY,CAAAiD,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACThT,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnN,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CACN,cAED3F,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1J,YAAa,CACpB8S,QAAQ,CAAGrG,MAAM,EAAK,CACpBxM,eAAe,CAACwM,MAAM,CAAC,CACvBtE,OAAO,CAACC,GAAG,CAACqE,MAAM,CAAC,CAErB,CAAE,CACF0G,OAAO,CAAE1Z,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEyM,GAAG,CAAEgN,QAAQ,GAAM,CACzCxJ,KAAK,CAAEwJ,QAAQ,CAACjG,IAAI,CACpBtD,KAAK,CACHuJ,QAAQ,CAAChG,IAAI,GAAK,EAAE,CAChBgG,QAAQ,CAAChG,IAAI,CACX,IAAI,CACJgG,QAAQ,CAACjG,IAAI,CACb,IAAI,EAAI,EAAE,CACZ,EACR,CAAC,CAAC,CAAE,CACJsH,YAAY,CAAEA,CAAC9H,MAAM,CAAE+H,UAAU,GAC/B/H,MAAM,CAAC9C,KAAK,CACT8K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/H,SAAS,CAAC,SAAS,CACnBmG,WAAW,CAAC,0BAA0B,CACtCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEvT,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBwT,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFja,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzM,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNrG,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACVpM,eAAe,CACX,eAAe,CACf,kBACL,oCAAoC,CACrCsS,IAAI,CAAC,QAAQ,CACb+B,GAAG,CAAE,CAAE,CACPtC,IAAI,CAAE,IAAK,CACXQ,WAAW,CAAC,MAAM,CAClBnJ,KAAK,CAAEtJ,UAAW,CAClB0S,QAAQ,CAAGC,CAAC,EAAK1S,aAAa,CAAC0S,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAChD,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrM,eAAe,CAAGA,eAAe,CAAG,EAAE,CACpC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNzG,KAAA,QAAK6S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzChT,IAAA,QAAK+S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACEiZ,IAAI,CAAE,UAAW,CACjB1F,IAAI,CAAC,OAAO,CACZnS,EAAE,CAAC,OAAO,CACV6Z,OAAO,CAAE9U,KAAK,GAAK,IAAK,CACxBgT,QAAQ,CAAGC,CAAC,EAAK,CACfhT,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,CACH,CAAC,cACFpG,IAAA,UACE+S,SAAS,CAAC,6CAA6C,CACvDmI,GAAG,CAAC,OAAO,CAAAlI,QAAA,CACZ,MAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5C9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACEiZ,IAAI,CAAE,UAAW,CACjB1F,IAAI,CAAC,QAAQ,CACbnS,EAAE,CAAC,QAAQ,CACX6Z,OAAO,CAAE9U,KAAK,GAAK,KAAM,CACzBgT,QAAQ,CAAGC,CAAC,EAAK,CACfhT,QAAQ,CAAC,KAAK,CAAC,CACjB,CAAE,CACH,CAAC,cACFpG,IAAA,UACE+S,SAAS,CAAC,6CAA6C,CACvDmI,GAAG,CAAC,QAAQ,CAAAlI,QAAA,CACb,QAED,CAAO,CAAC,EACL,CAAC,CACH,CAAC,EACH,CAAC,cAGNhT,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,aACE+P,KAAK,CAAEhK,eAAgB,CACvBoV,IAAI,CAAE,CAAE,CACRhC,QAAQ,CAAGC,CAAC,EAAKpT,kBAAkB,CAACoT,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CACpDgD,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN7S,KAAA,QAAK6S,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhT,IAAA,WACE2Y,OAAO,CAAEzC,uBAAwB,CACjCkF,QAAQ,CAAEjL,iBAAkB,CAC5B4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,iBAAiB,CAAG,aAAa,CAAG,QAAQ,CACvC,CAAC,cACTnQ,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA0C,KAAK,CAAG,IAAI,CAChB1Z,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnB2C,gBAAgB,CAAC,EAAE,CAAC,CACpBI,oBAAoB,CAAC,EAAE,CAAC,CACxBR,gBAAgB,CAAC,EAAE,CAAC,CACpBzB,mBAAmB,CAAC,EAAE,CAAC,CACvBV,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CACnBiD,oBAAoB,CAAC,EAAE,CAAC,CACxBI,kBAAkB,CAAC,EAAE,CAAC,CAEtB,GAAIpF,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5C0Z,KAAK,CAAG,KAAK,CACf,CAEA,GAAI7Y,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxC0Y,KAAK,CAAG,KAAK,CACf,CAEA,GAAIjY,OAAO,GAAK,EAAE,EAAIA,OAAO,CAAC2M,KAAK,GAAK,EAAE,CAAE,CAC1CxM,eAAe,CAAC,yBAAyB,CAAC,CAC1C8X,KAAK,CAAG,KAAK,CACf,CAEA,GAAI7X,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACuM,KAAK,GAAK,EAAE,CAAE,CAClDlM,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CwX,KAAK,CAAG,KAAK,CACf,CAEA,GAAI9V,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C2V,KAAK,CAAG,KAAK,CACf,CAAC,IAAM,IACL9V,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,EAAE,CACnB,CACAG,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CuV,KAAK,CAAG,KAAK,CACf,CACA,GAAIrW,QAAQ,GAAK,EAAE,CAAE,CACnBM,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C+V,KAAK,CAAG,KAAK,CACf,CACA,GAAIhV,YAAY,GAAK,EAAE,EAAIA,YAAY,CAAC0J,KAAK,GAAK,EAAE,CAAE,CACpDvJ,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C6U,KAAK,CAAG,KAAK,CACf,CACA,GAAI5U,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,yBAAyB,CAAC,CAC7CyU,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT5N,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLvO,KAAK,CAAC6X,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFhE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxF,UAAU,GAAK,CAAC,cACftN,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfhT,IAAA,QAAK+S,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhT,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,SACrC,cAAAhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACE9S,KAAA,QAAK6S,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B9S,KAAA,QAAK6S,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,sBACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,sBAAsB,CACvB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GAAK,sBACf,CACF,CAAC,CACH,CACF,CAAE,CACF/S,EAAE,CAAC,sBAAsB,CACzB6X,IAAI,CAAE,UAAW,CACjBgC,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,sBACF,CAAE,CACFhI,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,sBAAsB,CAC1BnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,sBAED,CAAO,CAAC,EACL,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,yBACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,yBAAyB,CAC1B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GAAK,yBACf,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,yBACF,CAAE,CACF3Z,EAAE,CAAC,yBAAyB,CAC5B6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,yBAAyB,CAC7BnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,2BAED,CAAO,CAAC,EACL,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,6BACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,6BACF,CAAE,CACF3Z,EAAE,CAAC,6BAA6B,CAChC6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,6BAA6B,CACjCnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,qCACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,qCAAqC,CACtC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GACN,qCACJ,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,qCACF,CAAE,CACF3Z,EAAE,CAAC,qCAAqC,CACxC6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,qCAAqC,CACzCnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,qCAED,CAAO,CAAC,EACL,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,kCACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kCAAkC,CACnC,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GACN,kCACJ,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,kCACF,CAAE,CACF3Z,EAAE,CAAC,kCAAkC,CACrC6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,kCAAkC,CACtCnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mCAED,CAAO,CAAC,EACL,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,kBACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,kBAAkB,CACnB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GAAK,kBACf,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,kBACF,CAAE,CACF3Z,EAAE,CAAC,kBAAkB,CACrB6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,kBAAkB,CACtBnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,kBAED,CAAO,CAAC,EACL,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,6BACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,6BAA6B,CAC9B,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GACN,6BACJ,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,6BACF,CAAE,CACF3Z,EAAE,CAAC,6BAA6B,CAChC6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,6BAA6B,CACjCnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,8BAED,CAAO,CAAC,EACL,CAAC,cAGN9S,KAAA,QAAK6S,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GACE,CAACnS,mBAAmB,CAAC8T,QAAQ,CAC3B,mBACF,CAAC,CACD,CACA7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,mBAAmB,CACpB,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EACLA,MAAM,GAAK,mBACf,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CACnC,mBACF,CAAE,CACF3Z,EAAE,CAAC,mBAAmB,CACtB6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,mBAAmB,CACvBnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,mBAED,CAAO,CAAC,EACL,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,wDAAwD,CAAAC,QAAA,eACrEhT,IAAA,UACEmZ,QAAQ,CAAGC,CAAC,EAAK,CACf,GAAI,CAACnS,mBAAmB,CAAC8T,QAAQ,CAAC,QAAQ,CAAC,CAAE,CAC3C7T,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,QAAQ,CACT,CAAC,CACJ,CAAC,IAAM,CACLC,sBAAsB,CACpBD,mBAAmB,CAACqU,MAAM,CACvBnH,MAAM,EAAKA,MAAM,GAAK,QACzB,CACF,CAAC,CACH,CACF,CAAE,CACF8G,OAAO,CAAEhU,mBAAmB,CAAC8T,QAAQ,CAAC,QAAQ,CAAE,CAChD3Z,EAAE,CAAC,QAAQ,CACX6X,IAAI,CAAE,UAAW,CACjBlG,SAAS,CAAC,MAAM,CACjB,CAAC,cACF/S,IAAA,UACEkb,GAAG,CAAC,QAAQ,CACZnI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CACzC,QAED,CAAO,CAAC,EACL,CAAC,EACH,CAAC,cAiCNhT,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7L,wBAAwB,CACrBA,wBAAwB,CACxB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENnH,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,qBAE1D,CAAK,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eAEjDhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,6BAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAE3ChT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC/CzN,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,QAAK6S,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,wBACtB,CAAC,GAAG,cAC1BhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACEiZ,IAAI,CAAC,MAAM,CACXlG,SAAS,CAAG,wBACVpL,cAAc,CACV,eAAe,CACf,kBACL,mCAAmC,CACpCuR,WAAW,CAAC,wBAAwB,CACpCnJ,KAAK,CAAEtI,SAAU,CACjB0R,QAAQ,CAAGC,CAAC,EAAK,CACf1R,YAAY,CAAC0R,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAC,CAC5B;AACA,GAAIlI,OAAO,EAAIA,OAAO,CAAGuR,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CACvCjI,UAAU,CAACsR,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAC,CAC5B,CACF,CAAE,CACH,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrL,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cACNzH,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACEiZ,IAAI,CAAC,MAAM,CACXlG,SAAS,CAAG,wBACVhL,YAAY,CACR,eAAe,CACf,kBACL,mCAAmC,CACpCmR,WAAW,CAAC,sBAAsB,CAClCnJ,KAAK,CAAElI,OAAQ,CACfsR,QAAQ,CAAGC,CAAC,EAAKtR,UAAU,CAACsR,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC5CqL,QAAQ,CAAE,CAAC3T,SAAU,CACrBuT,GAAG,CAAEvT,SAAU,CAChB,CAAC,cACFzH,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCjL,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN7H,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,wBACVxL,oBAAoB,CAChB,eAAe,CACf,kBACL,mCAAmC,CACpC0R,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BnJ,KAAK,CAAE1I,eAAgB,CACvB8R,QAAQ,CAAGC,CAAC,EACV9R,kBAAkB,CAAC8R,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAClC,CACF,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzL,oBAAoB,CACjBA,oBAAoB,CACpB,EAAE,CACH,CAAC,EACH,CAAC,EACH,CACN,CACE,CAAC,cAENvH,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAE1C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACEiZ,IAAI,CAAC,MAAM,CACXlG,SAAS,CAAG,wBACV5K,oBAAoB,CAChB,eAAe,CACf,kBACL,mCAAmC,CACpC+Q,WAAW,CAAC,mBAAmB,CAC/BnJ,KAAK,CAAE9H,eAAgB,CACvBkR,QAAQ,CAAGC,CAAC,EACVlR,kBAAkB,CAACkR,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAClC,CACF,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC7K,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNnI,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9S,KAAA,QAAK6S,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,eAC/B,CAAC,GAAG,cACjBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1H,YAAa,CACpB8Q,QAAQ,CAAGrG,MAAM,EAAK,KAAAyI,aAAA,CACpBjT,eAAe,CAACwK,MAAM,CAAC,CACvB;AACA,GAAI,CAAA+B,eAAe,EAAA0G,aAAA,CAAGzI,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAE/C,KAAK,UAAAwL,aAAA,UAAAA,aAAA,CAAI,EAAE,CACzC;AACA3N,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAAkH,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EAAKA,IAAI,CAACvO,EAAE,GAAKyT,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,KAAA0G,qBAAA,CACjBzX,mBAAmB,EAAAyX,qBAAA,CACjB1G,aAAa,CAAC2G,QAAQ,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAC5B,CAAC,CACD;AACA1L,UAAU,CAAC,IAAM,CACflC,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACL7J,mBAAmB,CAAC,EAAE,CAAC,CACvB6J,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFmF,SAAS,CAAC,SAAS,CACnByG,OAAO,CAAEpL,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE7B,GAAG,CAAEoD,IAAI,GAAM,CACjCI,KAAK,CAAEJ,IAAI,CAACvO,EAAE,CACd4O,KAAK,CAAEL,IAAI,CAACE,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ+K,YAAY,CAAEA,CAAC9H,MAAM,CAAE+H,UAAU,QAAAa,aAAA,QAAAA,aAAA,CAC/B5I,MAAM,CAAC9C,KAAK,UAAA0L,aAAA,iBAAZA,aAAA,CACIZ,WAAW,CAAC,CAAC,CACdC,QAAQ,CAACF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEC,WAAW,CAAC,CAAC,CAAC,EACvC,CACD5B,WAAW,CAAC,oBAAoB,CAChCO,YAAY,KACZ;AAAA,CACA9L,SAAS,CAAEU,gBACX;AAAA,CACAsN,UAAU,CAAEA,CAAA,GAAM,CAChBnN,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC,CACzC,CAAE,CACFiL,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEvR,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvBwR,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFja,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCzK,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cAENrI,KAAA,QAAK6S,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7C9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,kBAC5B,CAAC,GAAG,cACpBhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACE9S,KAAA,WACE6S,SAAS,CAAG,uBACVjO,oBAAoB,CAChB,eAAe,CACf,kBACL,oCAAoC,CACrCqU,QAAQ,CAAGC,CAAC,EAAK,CACfvU,kBAAkB,CAACuU,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAC,CACpC,CAAE,CACFA,KAAK,CAAEnL,eAAgB,CAAAoO,QAAA,eAEvBhT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BjM,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyI,GAAG,CAAC,CAAC4K,OAAO,CAAE5W,KAAK,QAAAqb,qBAAA,oBACpC1b,KAAA,WAAQ6P,KAAK,CAAEoH,OAAO,CAAC/V,EAAG,CAAA4R,QAAA,GAAA4I,qBAAA,CACvBzE,OAAO,CAAC0E,YAAY,UAAAD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC1BzE,OAAO,CAAC2E,kBAAkB,GAAK,EAAE,CAC9B,KAAK,CAAG3E,OAAO,CAAC2E,kBAAkB,CAClC,EAAE,EACA,CAAC,EACV,CAAC,EACI,CAAC,cACT9b,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrClO,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACN9E,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9S,KAAA,QAAK6S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,YAClC,CAAC,GAAG,cACdhT,IAAA,WAAQ+S,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,UACE+S,SAAS,CAAG,uBACVpK,iBAAiB,CACb,eAAe,CACf,kBACL,oCAAoC,CACrCsQ,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,aAAa,CACzBnJ,KAAK,CAAEtH,YAAa,CACpB0Q,QAAQ,CAAGC,CAAC,EAAK1Q,eAAe,CAAC0Q,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAClD,CAAC,cACF/P,IAAA,QAAK+S,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCrK,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENzI,KAAA,QAAK6S,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B9S,KAAA,WACEyY,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAA0C,KAAK,CAAG,IAAI,CAChB7S,oBAAoB,CAAC,EAAE,CAAC,CACxBzD,uBAAuB,CAAC,EAAE,CAAC,CAC3B6D,oBAAoB,CAAC,EAAE,CAAC,CACxB,GACEP,YAAY,GAAK,EAAE,EACnBA,YAAY,CAAC0H,KAAK,GAAK,EAAE,CACzB,CACAvH,oBAAoB,CAClB,4BACF,CAAC,CACDtJ,KAAK,CAAC6X,KAAK,CAAC,uBAAuB,CAAC,CACpCsE,KAAK,CAAG,KAAK,CACf,CACA,GAAIzW,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CACrB,4BACF,CAAC,CACD7F,KAAK,CAAC6X,KAAK,CAAC,+BAA+B,CAAC,CAC5CsE,KAAK,CAAG,KAAK,CACf,CACA,GAAI5S,YAAY,GAAK,EAAE,CAAE,CACvBG,oBAAoB,CAClB,4BACF,CAAC,CACD1J,KAAK,CAAC6X,KAAK,CAAC,yBAAyB,CAAC,CACtCsE,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAAU,MAAM,CAAG,KAAK,CACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAC,UAAU,CAAG,KAAK,CAExB;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,GAAI,CAACD,MAAM,EAAI,CAACC,UAAU,CAAE,KAAAC,oBAAA,CAC1B;AACA,GAAI,CAAApH,eAAe,EAAAoH,oBAAA,CAAG5T,YAAY,CAAC0H,KAAK,UAAAkM,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAC9C,KAAM,CAAAnH,aAAa,CAAG1G,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsB,IAAI,CAClCC,IAAI,EACHC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GAAKwO,MAAM,CAACiF,eAAe,CAC9C,CAAC,CAGD,GAAIC,aAAa,CAAE,KAAAoH,sBAAA,CAAAC,sBAAA,CACjB;AACA,GAAI,CAAAC,cAAc,CAAGxX,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAE1CkQ,aAAa,SAAbA,aAAa,kBAAAoH,sBAAA,CAAbpH,aAAa,CAAE2G,QAAQ,UAAAS,sBAAA,iBAAvBA,sBAAA,CAAyBpP,OAAO,CAC7BuP,OAAO,EAAK,CACX7N,OAAO,CAACC,GAAG,CAAC4N,OAAO,CAACjb,EAAE,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAkb,YAAY,CAChBxH,aAAa,SAAbA,aAAa,kBAAAqH,sBAAA,CAAbrH,aAAa,CAAE2G,QAAQ,UAAAU,sBAAA,iBAAvBA,sBAAA,CAAyBzM,IAAI,CAC1BC,IAAI,EACHC,MAAM,CAACD,IAAI,CAACvO,EAAE,CAAC,GACfwO,MAAM,CAACwM,cAAc,CACzB,CAAC,CAEH,GAAIE,YAAY,CAAE,CAChB;AACArY,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,CACtB,CACE0Q,QAAQ,CAAEI,aAAa,CACvBqC,OAAO,CAAEmF,YAAY,CACrBjF,IAAI,CAAE5O,YACR,CAAC,CACF,CAAC,CACFH,eAAe,CAAC,EAAE,CAAC,CACnBzD,kBAAkB,CAAC,EAAE,CAAC,CACtB6D,eAAe,CAAC,EAAE,CAAC,CACnB8F,OAAO,CAACC,GAAG,CAACzK,mBAAmB,CAAC,CAClC,CAAC,IAAM,CACLwE,oBAAoB,CAClB,kCACF,CAAC,CACDtJ,KAAK,CAAC6X,KAAK,CACT,kCACF,CAAC,CACH,CACF,CAAC,IAAM,CACLvO,oBAAoB,CAClB,0BACF,CAAC,CACDtJ,KAAK,CAAC6X,KAAK,CAAC,0BAA0B,CAAC,CACzC,CACF,CAAC,IAAM,CACLvO,oBAAoB,CAClB,4CACF,CAAC,CACDtJ,KAAK,CAAC6X,KAAK,CACT,4CACF,CAAC,CACH,CACF,CACF,CAAE,CACFhE,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eAEjEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNzY,IAAA,SAAAgT,QAAA,CAAM,gBAAc,CAAM,CAAC,EACrB,CAAC,CAERhP,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEuI,GAAG,CAAC,CAACiQ,YAAY,CAAEjc,KAAK,QAAAkc,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,kBAAA,oBAC5C7c,KAAA,QAEE6S,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAE3ChT,IAAA,QAAK+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClChT,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAqE,eAAe,CACnBhZ,mBAAmB,CAACsX,MAAM,CACxB,CAAC2B,CAAC,CAAEC,MAAM,GAAKA,MAAM,GAAK3c,KAC5B,CAAC,CACH0D,sBAAsB,CAAC+Y,eAAe,CAAC,CACzC,CAAE,CAAAhK,QAAA,cAEFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,CACN,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC9S,KAAA,QAAA8S,QAAA,eACEhT,IAAA,MAAAgT,QAAA,CAAG,WAAS,CAAG,CAAC,CAAC,GAAG,EAAAyJ,qBAAA,EAAAC,sBAAA,CACnBF,YAAY,CAAC9H,QAAQ,UAAAgI,sBAAA,iBAArBA,sBAAA,CAAuB7M,SAAS,UAAA4M,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,cACNvc,KAAA,QAAA8S,QAAA,eACEhT,IAAA,MAAAgT,QAAA,CAAG,UAAQ,CAAG,CAAC,CAAC,GAAG,EAAA2J,qBAAA,EAAAC,sBAAA,CAClBJ,YAAY,CAACrF,OAAO,UAAAyF,sBAAA,iBAApBA,sBAAA,CAAsBf,YAAY,UAAAc,qBAAA,UAAAA,qBAAA,CAAI,IAAI,EACxC,CAAC,cACNzc,KAAA,QAAA8S,QAAA,eACEhT,IAAA,MAAAgT,QAAA,CAAG,aAAW,CAAG,CAAC,CAAC,GAAG,EAAA6J,sBAAA,EAAAC,sBAAA,CACrBN,YAAY,CAACrF,OAAO,UAAA2F,sBAAA,iBAApBA,sBAAA,CAAsBhB,kBAAkB,UAAAe,sBAAA,UAAAA,sBAAA,CACvC,KAAK,EACJ,CAAC,cACN3c,KAAA,QAAA8S,QAAA,eACEhT,IAAA,MAAAgT,QAAA,CAAG,OAAK,CAAG,CAAC,IAAC,EAAA+J,kBAAA,CAACP,YAAY,CAACnF,IAAI,UAAA0F,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EACrC,CAAC,EACH,CAAC,GA9CDxc,KA+CF,CAAC,EACP,CAAC,EAEC,CAAC,EACH,CAAC,cACNP,IAAA,QAAAgT,QAAA,cACE9S,KAAA,WACEyY,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,GAAI,CAAApC,OAAO,CAAG,IAAI,CAClB3O,iBAAiB,CAAC,EAAE,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CACnBR,uBAAuB,CAAC,EAAE,CAAC,CAC3BY,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,oBAAoB,CAAC,EAAE,CAAC,CACxB;AACA,GACEjD,QAAQ,GAAK,SAAS,EACtBI,YAAY,GAAK,WAAW,CAC5B,CACA;AACA,GAAI,CAAC8B,SAAS,CAAE,CACdG,iBAAiB,CACf,oCACF,CAAC,CACD1I,KAAK,CAAC6X,KAAK,CAAC,oCAAoC,CAAC,CACjDR,OAAO,CAAG,KAAK,CACjB,CACA,GAAI,CAAC1O,OAAO,CAAE,CACZG,eAAe,CACb,kCACF,CAAC,CACD9I,KAAK,CAAC6X,KAAK,CAAC,kCAAkC,CAAC,CAC/CR,OAAO,CAAG,KAAK,CACjB,CACF,CAAC,IAAM,CACL;AACA,GAAI,CAAClP,eAAe,CAAE,CACpBG,uBAAuB,CACrB,8BACF,CAAC,CACDtI,KAAK,CAAC6X,KAAK,CAAC,8BAA8B,CAAC,CAC3CR,OAAO,CAAG,KAAK,CACjB,CACF,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA,GAAIvS,mBAAmB,CAACuK,MAAM,GAAK,CAAC,CAAE,CACpC/F,oBAAoB,CAClB,qCACF,CAAC,CACDtJ,KAAK,CAAC6X,KAAK,CAAC,qCAAqC,CAAC,CAClDR,OAAO,CAAG,KAAK,CACjB,CAEA,GAAIA,OAAO,CAAE,CACX;AACA,KAAM,CAAA4G,aAAa,CAAG,CACpB/b,EAAE,CAAE8D,IAAI,CAACkY,GAAG,CAAC,CAAC,CAAE;AAChB9I,UAAU,CAAE7M,SAAS,EAAI,IAAI,CAC7B8M,QAAQ,CAAE1M,OAAO,EAAI,IAAI,CACzBwM,gBAAgB,CAAEhN,eAAe,EAAI,IAAI,CACzCoN,gBAAgB,CAAExM,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAE,EAAE,CACrC8M,iBAAiB,CAAE/Q,mBAAmB,CAACuI,GAAG,CACvCoD,IAAI,GAAM,CACT+E,QAAQ,CAAE/E,IAAI,CAAC+E,QAAQ,CACvB0C,gBAAgB,CAAEzH,IAAI,CAACwH,OAAO,CAC9BG,aAAa,CAAE3H,IAAI,CAAC0H,IACtB,CAAC,CACH,CACF,CAAC,CAED;AACAhT,wBAAwB,CAAEgZ,IAAI,EAAK,CACjC,GAAGA,IAAI,CACPF,aAAa,CACd,CAAC,CAEF;AACA;AAEA;AACAzV,YAAY,CAAC,EAAE,CAAC,CAChBI,UAAU,CAAC,EAAE,CAAC,CACdR,kBAAkB,CAAC,EAAE,CAAC,CACtBY,kBAAkB,CAAC,EAAE,CAAC,CACtBjE,sBAAsB,CAAC,EAAE,CAAC,CAC1BqE,eAAe,CAAC,EAAE,CAAC,CACnBzD,kBAAkB,CAAC,EAAE,CAAC,CACtB6D,eAAe,CAAC,EAAE,CAAC,CAEnBxJ,KAAK,CAAC+Y,OAAO,CAAC,+BAA+B,CAAC,CAChD,CACF,CAAE,CACFlF,SAAS,CAAC,uJAAuJ,CAAAC,QAAA,eAEjKhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAC,KAAK,CACjBhF,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,aAAa,CAAAC,QAAA,cAEvBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mDAAmD,CACtD,CAAC,CACC,CAAC,cACNzY,IAAA,SAAAgT,QAAA,CAAM,oBAAkB,CAAM,CAAC,EACzB,CAAC,CACN,CAAC,cAENhT,IAAA,QAAAgT,QAAA,cACE9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAE,GAAI,CACjBhF,MAAM,CAAC,SAAS,CAChBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,0cAA0c,CAC7c,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,OAAI+S,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,aAEpD,CAAI,CAAC,EACF,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,OAAO,CAAAC,QAAA,EAEnB,CAAAtO,yBAAyB,SAAzBA,yBAAyB,iBAAzBA,yBAAyB,CAAE6J,MAAM,EAAG,CAAC,CACpC7J,yBAAyB,CAAC6H,GAAG,CAC3B,CAACgR,cAAc,CAAEC,eAAe,QAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,oBAC9B3d,KAAA,QAEE6S,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eAGjI9S,KAAA,QAAK6S,SAAS,CAAC,+GAA+G,CAAAC,QAAA,eAC5H9S,KAAA,OAAI6S,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,eACnC,CAACwK,eAAe,CAAG,CAAC,EAC/B,CAAC,cACLxd,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,KAAM,CAAAmF,kBAAkB,CACtBpZ,yBAAyB,CAAC4W,MAAM,CAC9B,CAAC2B,CAAC,CAAE1c,KAAK,GACPA,KAAK,GAAKid,eACd,CAAC,CACHjZ,8BAA8B,CAAC,CAC7B,GAAGD,2BAA2B,CAC9BiZ,cAAc,CAACnc,EAAE,CAClB,CAAC,CACFuD,4BAA4B,CAC1BmZ,kBACF,CAAC,CACH,CAAE,CACF/K,SAAS,CAAC,kGAAkG,CAC5G,aAAW,mBAAmB,CAAAC,QAAA,cAE9BhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAC,KAAK,CACjBhF,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,EACN,CAAC,cAGNvY,KAAA,QAAK6S,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElB9S,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAE,GAAI,CACjBhF,MAAM,CAAC,SAAS,CAChBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,OAAI+S,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAEpD,CAAI,CAAC,EACF,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7BzN,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,CAAAE,SAAA,EAAA4S,QAAA,eACE9S,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAyK,qBAAA,CAClCF,cAAc,CAACjJ,UAAU,UAAAmJ,qBAAA,UAAAA,qBAAA,CACxB,KAAK,CACH,CAAC,EACJ,CAAC,cACNvd,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA0K,qBAAA,CAClCH,cAAc,CAAChJ,QAAQ,UAAAmJ,qBAAA,UAAAA,qBAAA,CACtB,KAAK,CACH,CAAC,EACJ,CAAC,EACN,CAAC,cAEHxd,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA2K,qBAAA,CAClCJ,cAAc,CAAClJ,gBAAgB,UAAAsJ,qBAAA,UAAAA,qBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CACN,cACDzd,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA4K,qBAAA,CAClCL,cAAc,CAAC9I,gBAAgB,UAAAmJ,qBAAA,UAAAA,qBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGN1d,KAAA,QAAA8S,QAAA,eACE9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAE,GAAI,CACjBhF,MAAM,CAAC,SAAS,CAChBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,OAAI+S,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,WAEpD,CAAI,CAAC,EACF,CAAC,CAEL,EAAA6K,qBAAA,CAAAN,cAAc,CAACxI,iBAAiB,UAAA8I,qBAAA,iBAAhCA,qBAAA,CACGtP,MAAM,EAAG,CAAC,cACZvO,IAAA,QAAK+S,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BuK,cAAc,CAACxI,iBAAiB,CAACxI,GAAG,CACnC,CAACiQ,YAAY,CAAEuB,GAAG,QAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAChBte,IAAA,QAEE+S,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAE5D9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC9S,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAgL,sBAAA,EAAAC,sBAAA,CAClCzB,YAAY,CAAC9H,QAAQ,UAAAuJ,sBAAA,iBAArBA,sBAAA,CACGpO,SAAS,UAAAmO,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClB,CAAC,EACJ,CAAC,cACN9d,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAkL,sBAAA,EAAAC,sBAAA,CAClC3B,YAAY,CACVpF,gBAAgB,UAAA+G,sBAAA,iBADlBA,sBAAA,CAEGtC,YAAY,UAAAqC,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACrB,CAAC,EACJ,CAAC,cACNhe,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAoL,sBAAA,EAAAC,sBAAA,CAClC7B,YAAY,CACVpF,gBAAgB,UAAAiH,sBAAA,iBADlBA,sBAAA,CAEGvC,kBAAkB,UAAAsC,sBAAA,UAAAA,sBAAA,CACpB,KAAK,CACH,CAAC,EACJ,CAAC,cACNle,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAsL,sBAAA,CAClC9B,YAAY,CAAClF,aAAa,UAAAgH,sBAAA,UAAAA,sBAAA,CACzB,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EA3CDP,GA4CF,CAAC,EAEV,CAAC,CACE,CAAC,cAEN/d,IAAA,QAAK+S,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,uBAE7E,CAAK,CACN,EACE,CAAC,EACH,CAAC,GAxMDwK,eAyMF,CAAC,EAEV,CAAC,cAEDxd,IAAA,QAAK+S,SAAS,CAAC,mEAAmE,CAAAC,QAAA,CAAC,0BAEnF,CAAK,CACN,CAKA,CAAA5O,qBAAqB,SAArBA,qBAAqB,iBAArBA,qBAAqB,CAAEmK,MAAM,EAAG,CAAC,CAChCnK,qBAAqB,CAACmI,GAAG,CACvB,CAACgR,cAAc,CAAEC,eAAe,QAAAe,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC9Bze,KAAA,QAEE6S,SAAS,CAAC,uHAAuH,CAAAC,QAAA,eAGjI9S,KAAA,QAAK6S,SAAS,CAAC,+GAA+G,CAAAC,QAAA,eAC5H9S,KAAA,OAAI6S,SAAS,CAAC,mCAAmC,CAAAC,QAAA,EAAC,eAEhD,CAACwK,eAAe,EACd9Y,yBAAyB,SAAzBA,yBAAyB,iBAAzBA,yBAAyB,CAAE6J,MAAM,EACjC,CAAC,EACD,CAAC,cACLvO,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb,KAAM,CAAAqE,eAAe,CACnB5Y,qBAAqB,CAACkX,MAAM,CAC1B,CAAC2B,CAAC,CAAEC,MAAM,GACRA,MAAM,GAAKM,eACf,CAAC,CACHnZ,wBAAwB,CACtB2Y,eACF,CAAC,CACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACF,CAAE,CACFjK,SAAS,CAAC,kGAAkG,CAC5G,aAAW,mBAAmB,CAAAC,QAAA,cAE9BhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAC,KAAK,CACjBhF,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,uEAAuE,CAC1E,CAAC,CACC,CAAC,CACA,CAAC,EACN,CAAC,cAGNvY,KAAA,QAAK6S,SAAS,CAAC,KAAK,CAAAC,QAAA,eAElB9S,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnB9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,uEAAuE,CAAAC,QAAA,cACpFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAE,GAAI,CACjBhF,MAAM,CAAC,SAAS,CAChBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,OAAI+S,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,kBAEpD,CAAI,CAAC,EACF,CAAC,cAEN9S,KAAA,QAAK6S,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC7BzN,QAAQ,GAAK,SAAS,EACvBI,YAAY,GAAK,WAAW,cAC1BzF,KAAA,CAAAE,SAAA,EAAA4S,QAAA,eACE9S,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAuL,sBAAA,CAClChB,cAAc,CAACjJ,UAAU,UAAAiK,sBAAA,UAAAA,sBAAA,CACxB,KAAK,CACH,CAAC,EACJ,CAAC,cACNre,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,uBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAwL,sBAAA,CAClCjB,cAAc,CAAChJ,QAAQ,UAAAiK,sBAAA,UAAAA,sBAAA,CACtB,KAAK,CACH,CAAC,EACJ,CAAC,EACN,CAAC,cAEHte,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAyL,sBAAA,CAClClB,cAAc,CAAClJ,gBAAgB,UAAAoK,sBAAA,UAAAA,sBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CACN,cACDve,KAAA,QAAK6S,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhT,IAAA,SAAM+S,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mBAE7C,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA0L,sBAAA,CAClCnB,cAAc,CAAC9I,gBAAgB,UAAAiK,sBAAA,UAAAA,sBAAA,CAC9B,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNxe,KAAA,QAAA8S,QAAA,eACE9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrChT,IAAA,QAAK+S,SAAS,CAAC,yEAAyE,CAAAC,QAAA,cACtFhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBiF,WAAW,CAAE,GAAI,CACjBhF,MAAM,CAAC,SAAS,CAChBvF,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnBhT,IAAA,SACEuY,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,OAAI+S,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,WAEpD,CAAI,CAAC,EACF,CAAC,CAEL,EAAA2L,sBAAA,CAAApB,cAAc,CAACxI,iBAAiB,UAAA4J,sBAAA,iBAAhCA,sBAAA,CACGpQ,MAAM,EAAG,CAAC,cACZvO,IAAA,QAAK+S,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BuK,cAAc,CAACxI,iBAAiB,CAACxI,GAAG,CACnC,CAACiQ,YAAY,CAAEuB,GAAG,QAAAa,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,oBAChBlf,IAAA,QAEE+S,SAAS,CAAC,kDAAkD,CAAAC,QAAA,cAE5D9S,KAAA,QAAK6S,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC9S,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,UAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA4L,uBAAA,EAAAC,uBAAA,CAClCrC,YAAY,CAAC9H,QAAQ,UAAAmK,uBAAA,iBAArBA,uBAAA,CACGhP,SAAS,UAAA+O,uBAAA,UAAAA,uBAAA,CAAI,KAAK,CAClB,CAAC,EACJ,CAAC,cACN1e,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,SAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAA8L,uBAAA,EAAAC,uBAAA,CAClCvC,YAAY,CACVpF,gBAAgB,UAAA2H,uBAAA,iBADlBA,uBAAA,CAEGlD,YAAY,UAAAiD,uBAAA,UAAAA,uBAAA,CAAI,KAAK,CACrB,CAAC,EACJ,CAAC,cACN5e,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAgM,uBAAA,EAAAC,uBAAA,CAClCzC,YAAY,CACVpF,gBAAgB,UAAA6H,uBAAA,iBADlBA,uBAAA,CAEGnD,kBAAkB,UAAAkD,uBAAA,UAAAA,uBAAA,CACpB,KAAK,CACH,CAAC,EACJ,CAAC,cACN9e,KAAA,QAAK6S,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BhT,IAAA,SAAM+S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,MAExC,CAAM,CAAC,cACPhT,IAAA,SAAM+S,SAAS,CAAC,qBAAqB,CAAAC,QAAA,EAAAkM,uBAAA,CAClC1C,YAAY,CAAClF,aAAa,UAAA4H,uBAAA,UAAAA,uBAAA,CACzB,KAAK,CACH,CAAC,EACJ,CAAC,EACH,CAAC,EA3CDnB,GA4CF,CAAC,EAEV,CAAC,CACE,CAAC,cAEN/d,IAAA,QAAK+S,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CAAC,uBAE7E,CAAK,CACN,EACE,CAAC,EACH,CAAC,GA/MDwK,eAgNF,CAAC,EAEV,CAAC,cAEDxd,IAAA,QAAK+S,SAAS,CAAC,mEAAmE,CAAAC,QAAA,CAAC,8BAEnF,CAAK,CACN,EAGE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN9S,KAAA,QAAK6S,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhT,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACThT,IAAA,WACE2Y,OAAO,CAAEzC,uBAAwB,CACjCkF,QAAQ,CAAEjL,iBAAkB,CAC5B4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,iBAAiB,CAAG,aAAa,CAAG,QAAQ,CACvC,CAAC,cACTnQ,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAA0C,KAAK,CAAG,IAAI,CAChBrU,uBAAuB,CAAC,EAAE,CAAC,CAC3BI,2BAA2B,CAAC,EAAE,CAAC,CAE/B,GAAIH,mBAAmB,CAACsH,MAAM,GAAK,CAAC,CAAE,CACpCrP,KAAK,CAAC6X,KAAK,CACT,gEACF,CAAC,CACD3P,2BAA2B,CACzB,0CACF,CAAC,CACDiU,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT5N,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLvO,KAAK,CAAC6X,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACFhE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxF,UAAU,GAAK,CAAC,cACftN,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfhT,IAAA,QAAK+S,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9S,KAAA,WACM8L,0BAA0B,CAAC,CAAE+G,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFhT,IAAA,aAAWkM,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5ClM,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNhT,IAAA,UAAOmf,KAAK,CAAEze,eAAgB,CAAAsS,QAAA,cAC5B9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCzH,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvB+P,MAAM,CAAE9O,IAAI,EAAK,CAACnB,WAAW,CAAC0P,QAAQ,CAACvO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC4S,SAAS,CACZ,CAAC,cACNlf,KAAA,QAAA8S,QAAA,EACGqM,UAAU,CAAC7S,IAAI,CAAC8S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACbrN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF2R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJjM,IAAI,CAAC4S,SA0CP,CACN,CAAC,CACHvT,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBAC3CL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC+G,IAAI,CACP,CAAC,cACNrT,KAAA,QAAA8S,QAAA,EACG,CAACxG,IAAI,CAACgT,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb7M,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAACgP,MAAM,CACd,CAAC2B,CAAC,CAAEwC,aAAa,GACflf,KAAK,GAAKkf,aACd,CACF,CAAC,CACH,CAAE,CACF1M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJjM,IAAI,CAAC+G,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAENrT,KAAA,QAAK6S,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhT,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACThT,IAAA,WACE2Y,OAAO,CAAEzC,uBAAwB,CACjCkF,QAAQ,CAAEjL,iBAAkB,CAC5B4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,iBAAiB,CAAG,aAAa,CAAG,QAAQ,CACvC,CAAC,cACTnQ,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxF,UAAU,GAAK,CAAC,cACftN,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfhT,IAAA,QAAK+S,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,UAEtD,CAAK,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,UACE+S,SAAS,CAAC,wEAAwE,CAClFkG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCnJ,KAAK,CAAEtG,aAAc,CACrB0P,QAAQ,CAAGC,CAAC,EAAK1P,gBAAgB,CAAC0P,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAEN7P,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,UACE+S,SAAS,CAAC,wEAAwE,CAClFkG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCnJ,KAAK,CAAElG,UAAW,CAClBsP,QAAQ,CAAGC,CAAC,EAAKtP,aAAa,CAACsP,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN/P,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,UACE+S,SAAS,CAAC,wEAAwE,CAClFkG,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BnJ,KAAK,CAAE9F,MAAO,CACdkP,QAAQ,CAAGC,CAAC,EAAKlP,SAAS,CAACkP,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN/P,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9S,KAAA,WACMgN,yBAAyB,CAAC,CAAE6F,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFhT,IAAA,aAAWmN,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CnN,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNhT,IAAA,UAAOmf,KAAK,CAAEze,eAAgB,CAAAsS,QAAA,cAC5B9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCvH,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACf6P,MAAM,CAAE9O,IAAI,EAAK,CAACnB,WAAW,CAAC0P,QAAQ,CAACvO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC4S,SAAS,CACZ,CAAC,cACNlf,KAAA,QAAA8S,QAAA,EACGqM,UAAU,CAAC7S,IAAI,CAAC8S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACbrN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF2R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJjM,IAAI,CAAC4S,SA0CP,CACN,CAAC,CACHpS,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACnCL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC+G,IAAI,CACP,CAAC,cACNrT,KAAA,QAAA8S,QAAA,EACG,CAACxG,IAAI,CAACgT,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACb1L,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAACgP,MAAM,CACd,CAAC2B,CAAC,CAAEwC,aAAa,GACflf,KAAK,GAAKkf,aACd,CACF,CAAC,CACH,CAAE,CACF1M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJjM,IAAI,CAAC+G,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGNrT,KAAA,QAAK6S,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhT,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACThT,IAAA,WACE2Y,OAAO,CAAEzC,uBAAwB,CACjCkF,QAAQ,CAAEjL,iBAAkB,CAC5B4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,iBAAiB,CAAG,aAAa,CAAG,QAAQ,CACvC,CAAC,cACTnQ,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPxF,UAAU,GAAK,CAAC,cACftN,KAAA,QAAK6S,SAAS,CAAC,EAAE,CAAAC,QAAA,eACfhT,IAAA,QAAK+S,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD9S,KAAA,QAAK6S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C9S,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,CAACP,MAAM,EACLsQ,KAAK,CAAE1F,gBAAiB,CACxB8O,QAAQ,CAAGrG,MAAM,EAAK,CACpBxI,mBAAmB,CAACwI,MAAM,CAAC,CAC7B,CAAE,CACF0G,OAAO,CAAE5K,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAErC,GAAG,CAAEiJ,SAAS,GAAM,CACvCzF,KAAK,CAAEyF,SAAS,CAACpU,EAAE,CACnB4O,KAAK,CAAEwF,SAAS,CAACK,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJ+E,YAAY,CAAEA,CAAC9H,MAAM,CAAE+H,UAAU,GAC/B/H,MAAM,CAAC9C,KAAK,CACT8K,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACD/H,SAAS,CAAC,SAAS,CACnBmG,WAAW,CAAC,qBAAqB,CACjCO,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5L,KAAK,IAAM,CACzB,GAAG4L,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAEvP,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBwP,SAAS,CAAE/L,KAAK,CAACgM,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFhH,MAAM,CAAG8G,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPjZ,OAAO,CAAE,MAAM,CACfsZ,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN/Z,KAAA,QAAK6S,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5ChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACEhT,IAAA,UACE+S,SAAS,CAAC,wEAAwE,CAClFkG,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BnJ,KAAK,CAAElF,YAAa,CACpBsO,QAAQ,CAAGC,CAAC,EAAKtO,eAAe,CAACsO,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN/P,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjDhT,IAAA,QAAK+S,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C9S,KAAA,QAAK6S,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnChT,IAAA,QAAK+S,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACNhT,IAAA,QAAAgT,QAAA,cACE9S,KAAA,WACE6P,KAAK,CAAE9E,aAAc,CACrBkO,QAAQ,CAAGC,CAAC,EAAKlO,gBAAgB,CAACkO,CAAC,CAACL,MAAM,CAAChJ,KAAK,CAAE,CAClDgD,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFhT,IAAA,WAAQ+P,KAAK,CAAE,EAAG,CAAAiD,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzChT,IAAA,WAAQ+P,KAAK,CAAE,SAAU,CAAAiD,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1ChT,IAAA,WAAQ+P,KAAK,CAAE,UAAW,CAAAiD,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5ChT,IAAA,WAAQ+P,KAAK,CAAE,QAAS,CAAAiD,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENhT,IAAA,QAAK+S,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN9S,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9S,KAAA,WACMoN,wCAAwC,CAAC,CAC3CyF,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElFhT,IAAA,aAAWuN,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DvN,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3DhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNzY,IAAA,QAAK+S,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACNhT,IAAA,UAAOmf,KAAK,CAAEze,eAAgB,CAAAsS,QAAA,cAC5B9S,KAAA,QAAK6S,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCrH,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9B2P,MAAM,CAAE9O,IAAI,EAAK,CAACnB,WAAW,CAAC0P,QAAQ,CAACvO,IAAI,CAACpL,EAAE,CAAC,CAAC,CACjDmL,GAAG,CAAC,CAACC,IAAI,CAAEjM,KAAK,gBACfL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC4S,SAAS,CACZ,CAAC,cACNlf,KAAA,QAAA8S,QAAA,EACGqM,UAAU,CAAC7S,IAAI,CAAC8S,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACbrN,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAACpL,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF2R,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJjM,IAAI,CAAC4S,SA0CP,CACN,CAAC,CACHhS,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAEjM,KAAK,gBACVL,KAAA,QACE6S,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpFhT,IAAA,QAAK+S,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E9S,KAAA,QACEiY,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBmE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,eAEdhT,IAAA,SAAMyY,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOzY,IAAA,SAAMyY,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNvY,KAAA,QAAK6S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjDhT,IAAA,QAAK+S,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FxG,IAAI,CAAC+G,IAAI,CACP,CAAC,cACNrT,KAAA,QAAA8S,QAAA,EACG,CAACxG,IAAI,CAACgT,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNvf,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAM,CACbtL,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAACgP,MAAM,CACd,CAAC2B,CAAC,CAAEwC,aAAa,GACflf,KAAK,GAAKkf,aACd,CACJ,CAAC,CACH,CAAE,CACF1M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBiE,KAAK,CAAC,QAAQ,CAAAvJ,QAAA,cAEdhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJjM,IAAI,CAAC+G,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAENrT,KAAA,QAAK6S,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhT,IAAA,WACE2Y,OAAO,CAAEA,CAAA,GAAMlL,aAAa,CAAC,CAAC,CAAE,CAChCsF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,MAED,CAAQ,CAAC,cACThT,IAAA,WACE2Y,OAAO,CAAEzC,uBAAwB,CACjCkF,QAAQ,CAAEjL,iBAAkB,CAC5B4C,SAAS,CAAC,gIAAgI,CAAAC,QAAA,CAEzI7C,iBAAiB,CAAG,aAAa,CAAG,QAAQ,CACvC,CAAC,cACTnQ,IAAA,WACEob,QAAQ,CAAEjL,iBAAkB,CAC5BwI,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAA+G,mBAAA,CAAAC,oBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CACnB;AACAjS,YAAY,CAAC,IAAI,CAAC,CAClBE,4BAA4B,CAAC,IAAI,CAAC,CAAE;AAEpC;AACA,KAAM,CAAAkJ,eAAe,CAAG5S,qBAAqB,CAACmI,GAAG,CAC9CoD,IAAI,GAAM,CACT2E,UAAU,CAAE3E,IAAI,CAAC2E,UAAU,CAC3BC,QAAQ,CAAE5E,IAAI,CAAC4E,QAAQ,CACvBF,gBAAgB,CAAE1E,IAAI,CAAC0E,gBAAgB,CACvCI,gBAAgB,CAAE9E,IAAI,CAAC8E,gBAAgB,CACvCM,iBAAiB,CAAEpF,IAAI,CAACoF,iBAAiB,CAACxI,GAAG,CAC1C3H,eAAe,OAAAkb,sBAAA,CAAAC,sBAAA,OAAM,CACpBrL,QAAQ,EAAAoL,sBAAA,CAAElb,eAAe,CAAC8P,QAAQ,UAAAoL,sBAAA,iBAAxBA,sBAAA,CAA0B1e,EAAE,CACtC+V,OAAO,EAAA4I,sBAAA,CAAEnb,eAAe,CAACwS,gBAAgB,UAAA2I,sBAAA,iBAAhCA,sBAAA,CAAkC3e,EAAE,CAC7CiW,IAAI,CAAEzS,eAAe,CAAC0S,aACxB,CAAC,EACH,CACF,CAAC,CACH,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGvT,mBAAmB,CAACuI,GAAG,CAC1CoD,IAAI,OAAAqQ,cAAA,CAAAC,eAAA,OAAM,CACT9I,OAAO,EAAA6I,cAAA,CAAErQ,IAAI,CAACwH,OAAO,UAAA6I,cAAA,iBAAZA,cAAA,CAAc5e,EAAE,CACzBsT,QAAQ,EAAAuL,eAAA,CAAEtQ,IAAI,CAAC+E,QAAQ,UAAAuL,eAAA,iBAAbA,eAAA,CAAe7e,EAAE,CAC3BiW,IAAI,CAAE1H,IAAI,CAAC0H,IACb,CAAC,EACH,CAAC,CACD;AACA,KAAM,CAAAlW,QAAQ,CACZ7B,UAAU,CAAC8B,EAAE,CAAE,CACbiR,UAAU,CAAE7Q,SAAS,CACrB8Q,SAAS,CAAE1Q,QAAQ,CACnBiO,SAAS,CAAErO,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC2Q,SAAS,CAAEnQ,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1BoQ,aAAa,CAAEhQ,KAAK,CACpBiQ,aAAa,CAAEzQ,KAAK,CACpB0Q,eAAe,CAAE9P,OAAO,CACxBsQ,YAAY,CAAElQ,IAAI,CAClB4P,eAAe,CAAExP,OAAO,CAAC2M,KAAK,CAC9B;AACAvM,WAAW,EAAAkc,mBAAA,CAAElc,WAAW,CAACuM,KAAK,UAAA2P,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CACpC5L,SAAS,CAAE9O,QAAQ,CACnB+O,SAAS,CAAExO,QAAQ,CACnBiP,cAAc,CACZjP,QAAQ,GAAK,SAAS,CAAGI,YAAY,CAAG,EAAE,CAC5CqO,gBAAgB,CAAEjO,eAAe,CACjC;AACAqO,mBAAmB,CAAEvN,eAAe,CACpCqN,WAAW,CAAEjN,mBAAmB,CAChCoN,gBAAgB,CACd1O,YAAY,GAAK,WAAW,CACxB,EAAE,CACF0B,eAAe,CACrBiN,UAAU,CACR3O,YAAY,GAAK,WAAW,CAAG8B,SAAS,CAAG,EAAE,CAC/C8M,QAAQ,CACN5O,YAAY,GAAK,WAAW,CAAGkC,OAAO,CAAG,EAAE,CAC7C4M,gBAAgB,CAAExM,eAAe,CACjCyM,QAAQ,EAAAiL,oBAAA,CAAEtX,YAAY,CAAC0H,KAAK,UAAA4P,oBAAA,UAAAA,oBAAA,CAAI,EAAE,CAClC;AACAvK,cAAc,CAAE3L,aAAa,CAC7B4L,WAAW,CAAExL,UAAU,CACvByL,cAAc,CAAErL,MAAM,CACtBuL,SAAS,EAAAoK,sBAAA,CAAEvV,gBAAgB,CAAC0F,KAAK,UAAA6P,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CACvC7J,gBAAgB,CAAEtL,eAAe,CACjCqL,aAAa,CAAEjL,YAAY,CAC3BmL,gBAAgB,CAAE/K,aAAa,CAC/B;AACAyM,uBAAuB,CAAE7L,0BAA0B,CACnD8L,cAAc,CAAE3K,kBAAkB,CAClC4K,8BAA8B,CAC5BxK,iCAAiC,CACnCyK,aAAa,CAAExM,WAAW,CAC1B+C,SAAS,CAAEmJ,aAAa,SAAbA,aAAa,UAAbA,aAAa,CAAI,EAAE,CAC9BO,WAAW,CAAEd,eAAe,SAAfA,eAAe,UAAfA,eAAe,CAAI,EAAE,CAClCe,iBAAiB,CAAE7T,yBAAyB,SAAzBA,yBAAyB,UAAzBA,yBAAyB,CAAI,EAAE,CAClD8T,kBAAkB,CAChB1T,2BAA2B,SAA3BA,2BAA2B,UAA3BA,2BAA2B,CAAI,EAAE,CACnC;AACAkP,MAAM,CAAErN,KAAK,CAAG,MAAM,CAAG,OAAO,CAChCsN,WAAW,CAAEhN,UAAU,CACvB2M,cAAc,EAAAyM,oBAAA,CAAExZ,YAAY,CAAC0J,KAAK,UAAA8P,oBAAA,UAAAA,oBAAA,CAAI,EACxC,CAAC,CACH,CAAC,CACH,CAAE,CACF9M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjE7C,iBAAiB,CAAG,WAAW,CAAG,iBAAiB,CAC9C,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP3C,UAAU,GAAK,CAAC,cACfxN,IAAA,QAAK+S,SAAS,CAAC,EAAE,CAAAC,QAAA,cACfhT,IAAA,QAAK+S,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD9S,KAAA,QAAK6S,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjEhT,IAAA,QACEmY,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBvF,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9EhT,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByY,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNzY,IAAA,QAAK+S,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4BAExD,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACNhT,IAAA,QAAK+S,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAS1DhT,IAAA,MACEkY,IAAI,CAAC,YAAY,CACjBnF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACO,CAAC,CAEpB,CAEA,cAAe,CAAAjS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}