{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { CASE_LIST_REQUEST, CASE_LIST_SUCCESS, CASE_LIST_FAIL,\n//\nCASE_ADD_REQUEST, CASE_ADD_SUCCESS, CASE_ADD_FAIL,\n//\nCASE_DETAIL_REQUEST, CASE_DETAIL_SUCCESS, CASE_DETAIL_FAIL,\n//\nCASE_UPDATE_REQUEST, CASE_UPDATE_SUCCESS, CASE_UPDATE_FAIL,\n//\nCASE_DELETE_REQUEST, CASE_DELETE_SUCCESS, CASE_DELETE_FAIL,\n//\nCASE_COORDINATOR_LIST_REQUEST, CASE_COORDINATOR_LIST_SUCCESS, CASE_COORDINATOR_LIST_FAIL,\n//\nCOMMENT_CASE_LIST_REQUEST, COMMENT_CASE_LIST_SUCCESS, COMMENT_CASE_LIST_FAIL\n//\n} from \"../constants/caseConstants\";\nexport const caseListCoordinatorReducer = (state = {\n  casesCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCasesCoordinator: true,\n        casesCoordinator: []\n      };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return {\n        loadingCaseUpdate: true\n      };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return {\n        loadingCaseDelete: true\n      };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return {\n        loadingCaseAdd: true\n      };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCaseReducer = (state = {\n  caseInfo: {}\n}, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return {\n        loadingCaseInfo: true\n      };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const caseListReducer = (state = {\n  cases: []\n}, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return {\n        loadingCases: true,\n        cases: []\n      };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case CASE_LIST_FAIL:\n      return {\n        loadingCases: false,\n        errorCases: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "CASE_LIST_REQUEST", "CASE_LIST_SUCCESS", "CASE_LIST_FAIL", "CASE_ADD_REQUEST", "CASE_ADD_SUCCESS", "CASE_ADD_FAIL", "CASE_DETAIL_REQUEST", "CASE_DETAIL_SUCCESS", "CASE_DETAIL_FAIL", "CASE_UPDATE_REQUEST", "CASE_UPDATE_SUCCESS", "CASE_UPDATE_FAIL", "CASE_DELETE_REQUEST", "CASE_DELETE_SUCCESS", "CASE_DELETE_FAIL", "CASE_COORDINATOR_LIST_REQUEST", "CASE_COORDINATOR_LIST_SUCCESS", "CASE_COORDINATOR_LIST_FAIL", "COMMENT_CASE_LIST_REQUEST", "COMMENT_CASE_LIST_SUCCESS", "COMMENT_CASE_LIST_FAIL", "caseListCoordinatorReducer", "state", "casesCoordinator", "action", "type", "loadingCasesCoordinator", "payload", "cases", "pages", "page", "errorCasesCoordinator", "updateCaseReducer", "loadingCaseUpdate", "success", "successCaseUpdate", "error", "errorCaseUpdate", "deleteCaseReducer", "loadingCaseDelete", "successCaseDelete", "errorCaseDelete", "createNewCaseReducer", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "detailCaseReducer", "caseInfo", "loadingCaseInfo", "successCaseInfo", "errorCaseInfo", "caseListReducer", "loadingCases", "errorCases"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/caseReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  CASE_LIST_REQUEST,\n  CASE_LIST_SUCCESS,\n  CASE_LIST_FAIL,\n  //\n  CASE_ADD_REQUEST,\n  CASE_ADD_SUCCESS,\n  CASE_ADD_FAIL,\n  //\n  CASE_DETAIL_REQUEST,\n  CASE_DETAIL_SUCCESS,\n  CASE_DETAIL_FAIL,\n  //\n  CASE_UPDATE_REQUEST,\n  CASE_UPDATE_SUCCESS,\n  CASE_UPDATE_FAIL,\n  //\n  CASE_DELETE_REQUEST,\n  CASE_DELETE_SUCCESS,\n  CASE_DELETE_FAIL,\n  //\n  CASE_COORDINATOR_LIST_REQUEST,\n  CASE_COORDINATOR_LIST_SUCCESS,\n  CASE_COORDINATOR_LIST_FAIL,\n  //\n  COMMENT_CASE_LIST_REQUEST,\n  COMMENT_CASE_LIST_SUCCESS,\n  COMMENT_CASE_LIST_FAIL,\n  //\n} from \"../constants/caseConstants\";\n\nexport const caseListCoordinatorReducer = (\n  state = { casesCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case CASE_COORDINATOR_LIST_REQUEST:\n      return { loadingCasesCoordinator: true, casesCoordinator: [] };\n    case CASE_COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCasesCoordinator: false,\n        casesCoordinator: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_COORDINATOR_LIST_FAIL:\n      return {\n        loadingCasesCoordinator: false,\n        errorCasesCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_UPDATE_REQUEST:\n      return { loadingCaseUpdate: true };\n    case CASE_UPDATE_SUCCESS:\n      toast.success(\"This Case has been updated successfully.\");\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: true,\n      };\n    case CASE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseUpdate: false,\n        successCaseUpdate: false,\n        errorCaseUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const deleteCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_DELETE_REQUEST:\n      return { loadingCaseDelete: true };\n    case CASE_DELETE_SUCCESS:\n      toast.success(\"This Case has been successfully deleted.\");\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: true,\n      };\n    case CASE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseDelete: false,\n        successCaseDelete: false,\n        errorCaseDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewCaseReducer = (state = {}, action) => {\n  switch (action.type) {\n    case CASE_ADD_REQUEST:\n      return { loadingCaseAdd: true };\n    case CASE_ADD_SUCCESS:\n      toast.success(\"Ce Case a été ajouté avec succès\");\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: true,\n      };\n    case CASE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCaseAdd: false,\n        successCaseAdd: false,\n        errorCaseAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCaseReducer = (state = { caseInfo: {} }, action) => {\n  switch (action.type) {\n    case CASE_DETAIL_REQUEST:\n      return { loadingCaseInfo: true };\n    case CASE_DETAIL_SUCCESS:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: true,\n        caseInfo: action.payload,\n      };\n    case CASE_DETAIL_FAIL:\n      return {\n        loadingCaseInfo: false,\n        successCaseInfo: false,\n        errorCaseInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const caseListReducer = (state = { cases: [] }, action) => {\n  switch (action.type) {\n    case CASE_LIST_REQUEST:\n      return { loadingCases: true, cases: [] };\n    case CASE_LIST_SUCCESS:\n      return {\n        loadingCases: false,\n        cases: action.payload.cases,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case CASE_LIST_FAIL:\n      return { loadingCases: false, errorCases: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,0BAA0B;AAC1B;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,0BAA0B,GAAGA,CACxCC,KAAK,GAAG;EAAEC,gBAAgB,EAAE;AAAG,CAAC,EAChCC,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKV,6BAA6B;MAChC,OAAO;QAAEW,uBAAuB,EAAE,IAAI;QAAEH,gBAAgB,EAAE;MAAG,CAAC;IAChE,KAAKP,6BAA6B;MAChC,OAAO;QACLU,uBAAuB,EAAE,KAAK;QAC9BH,gBAAgB,EAAEC,MAAM,CAACG,OAAO,CAACC,KAAK;QACtCC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAKb,0BAA0B;MAC7B,OAAO;QACLS,uBAAuB,EAAE,KAAK;QAC9BK,qBAAqB,EAAEP,MAAM,CAACG;MAChC,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMU,iBAAiB,GAAGA,CAACV,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhB,mBAAmB;MACtB,OAAO;QAAEwB,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAKvB,mBAAmB;MACtBX,KAAK,CAACmC,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLD,iBAAiB,EAAE,KAAK;QACxBE,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAKxB,gBAAgB;MACnBZ,KAAK,CAACqC,KAAK,CAACZ,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACLM,iBAAiB,EAAE,KAAK;QACxBE,iBAAiB,EAAE,KAAK;QACxBE,eAAe,EAAEb,MAAM,CAACG;MAC1B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgB,iBAAiB,GAAGA,CAAChB,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKb,mBAAmB;MACtB,OAAO;QAAE2B,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK1B,mBAAmB;MACtBd,KAAK,CAACmC,OAAO,CAAC,0CAA0C,CAAC;MACzD,OAAO;QACLK,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK1B,gBAAgB;MACnBf,KAAK,CAACqC,KAAK,CAACZ,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACLY,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEjB,MAAM,CAACG;MAC1B,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoB,oBAAoB,GAAGA,CAACpB,KAAK,GAAG,CAAC,CAAC,EAAEE,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKtB,gBAAgB;MACnB,OAAO;QAAEwC,cAAc,EAAE;MAAK,CAAC;IACjC,KAAKvC,gBAAgB;MACnBL,KAAK,CAACmC,OAAO,CAAC,kCAAkC,CAAC;MACjD,OAAO;QACLS,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAKvC,aAAa;MAChBN,KAAK,CAACqC,KAAK,CAACZ,MAAM,CAACG,OAAO,CAAC;MAC3B,OAAO;QACLgB,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAErB,MAAM,CAACG;MACvB,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMwB,iBAAiB,GAAGA,CAACxB,KAAK,GAAG;EAAEyB,QAAQ,EAAE,CAAC;AAAE,CAAC,EAAEvB,MAAM,KAAK;EACrE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnB,mBAAmB;MACtB,OAAO;QAAE0C,eAAe,EAAE;MAAK,CAAC;IAClC,KAAKzC,mBAAmB;MACtB,OAAO;QACLyC,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,IAAI;QACrBF,QAAQ,EAAEvB,MAAM,CAACG;MACnB,CAAC;IACH,KAAKnB,gBAAgB;MACnB,OAAO;QACLwC,eAAe,EAAE,KAAK;QACtBC,eAAe,EAAE,KAAK;QACtBC,aAAa,EAAE1B,MAAM,CAACG;MACxB,CAAC;IACH;MACE,OAAOL,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM6B,eAAe,GAAGA,CAAC7B,KAAK,GAAG;EAAEM,KAAK,EAAE;AAAG,CAAC,EAAEJ,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKzB,iBAAiB;MACpB,OAAO;QAAEoD,YAAY,EAAE,IAAI;QAAExB,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAK3B,iBAAiB;MACpB,OAAO;QACLmD,YAAY,EAAE,KAAK;QACnBxB,KAAK,EAAEJ,MAAM,CAACG,OAAO,CAACC,KAAK;QAC3BC,KAAK,EAAEL,MAAM,CAACG,OAAO,CAACE,KAAK;QAC3BC,IAAI,EAAEN,MAAM,CAACG,OAAO,CAACG;MACvB,CAAC;IACH,KAAK5B,cAAc;MACjB,OAAO;QAAEkD,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAE7B,MAAM,CAACG;MAAQ,CAAC;IAC5D;MACE,OAAOL,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}