{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{addNewCommentCase,detailCase,duplicateCase,getListCommentCase,updateAssignedCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile,COUNTRIES,CURRENCYITEMS}from\"../../constants\";import{useDropzone}from\"react-dropzone\";import{toast}from\"react-toastify\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{CASE_DUPLICATE_REQUEST}from\"../../redux/constants/caseConstants\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function DetailCaseScreen(){var _caseInfo$created_use,_caseInfo$created_use2,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$pat,_caseInfo$patient2,_caseInfo$patient3,_caseInfo$case_status,_caseInfo$patient$ful2,_caseInfo$patient4,_caseInfo$patient$bir,_caseInfo$patient5,_caseInfo$patient$pat2,_caseInfo$patient6,_caseInfo$patient$pat3,_caseInfo$patient7,_caseInfo$patient$pat4,_caseInfo$patient8,_caseInfo$patient$pat5,_caseInfo$patient9,_caseInfo$currency_pr,_caseInfo$coordinator5,_caseInfo$coordinator6,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$service_loc,_caseInfo$provider_se,_caseInfo$medical_rep,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$assurance_s,_caseInfo$assurance$a2,_caseInfo$assurance2,_caseInfo$assurance_n,_caseInfo$policy_numb,_caseInfo$upload_auth;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const[isLoading,setIsLoading]=useState(false);const[openDiag,setOpenDiag]=useState(false);const[selectCoordinator,setSelectCoordinator]=useState(\"\");const[selectCoordinatorError,setSelectCoordinatorError]=useState(\"\");const[selectPage,setSelectPage]=useState(\"General Information\");const[commentInput,setCommentInput]=useState(\"\");const[commentInputError,setCommentInputError]=useState(\"\");const[isDuplicate,setIsDuplicate]=useState(false);// files comment\n// initialMedicalReports\nconst[filesComments,setFilesComments]=useState([]);const{getRootProps:getRootComments,getInputProps:getInputComments}=useDropzone({accept:{\"image/*\":[]},onDrop:acceptedFiles=>{setFilesComments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesComments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCommentCase=useSelector(state=>state.commentCaseList);const{comments,loadingCommentCase,errorCommentCase,pages}=listCommentCase;const createCommentCase=useSelector(state=>state.createNewCommentCase);const{loadingCommentCaseAdd,successCommentCaseAdd,errorCommentCaseAdd}=createCommentCase;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseAssignedUpdate=useSelector(state=>state.updateCaseAssigned);const{loadingCaseAssignedUpdate,errorCaseAssignedUpdate,successCaseAssignedUpdate}=caseAssignedUpdate;const caseDuplicat=useSelector(state=>state.duplicateCase);const{loadingCaseDuplicate,errorCaseDuplicate,successCaseDuplicate,caseDuplicate}=caseDuplicat;//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[navigate,userInfo,dispatch,id,page]);useEffect(()=>{if(successCommentCaseAdd){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseAdd]);useEffect(()=>{if(successCaseDuplicate&&caseDuplicate){navigate(\"/cases/edit/\"+caseDuplicate);dispatch({type:\"RESET_DUPLICATE_CASE\"});}},[successCaseDuplicate,caseDuplicate]);// Reset flag on navigation back\nuseEffect(()=>{return()=>setIsDuplicate(false);},[]);useEffect(()=>{if(successCaseAssignedUpdate){setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));dispatch(getListCoordinators(\"0\"));}},[successCaseAssignedUpdate]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";case\"failed\":return\"Failed\";default:return casestatus;}};const caseStatusColor=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"text-danger\";case\"coordinated-missing-m-r\":return\"text-[#FFA500]\";case\"coordinated-missing-invoice\":return\"text-[#FFA500]\";case\"waiting-for-insurance-authorization\":return\"text-primary\";case\"coordinated-patient-not-seen-yet\":return\"text-primary\";case\"fully-coordinated\":return\"text-[#008000]\";case\"failed\":return\"text-[#d34053]\";default:return\"\";}};const getIconCountry=country=>{const foundCountry=COUNTRIES.find(option=>option.title===country);if(foundCountry){return foundCountry.icon;}else{return\"\";}};//\nconst getCurrencyCode=code=>{const patientCurrency=code!==null&&code!==void 0?code:\"\";const foundCurrency=CURRENCYITEMS===null||CURRENCYITEMS===void 0?void 0:CURRENCYITEMS.find(option=>option.code===patientCurrency);if(foundCurrency){var _foundCurrency$symbol;return(_foundCurrency$symbol=foundCurrency.symbol)!==null&&_foundCurrency$symbol!==void 0?_foundCurrency$symbol:code;}else{return code;}};const getSectionIndex=selectItem=>{if(selectItem===\"General Information\"){return 0;}else if(selectItem===\"Coordination Details\"){return 1;}else if(selectItem===\"Medical Reports\"){return 2;}else if(selectItem===\"Invoices\"){return 3;}else if(selectItem===\"Insurance Authorization\"){return 4;}else{return 0;}};//\nreturn/*#__PURE__*/_jsxs(DefaultLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col justify-between my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:hidden\",children:[\"#\",caseInfo.id]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-3\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Created By:\"}),\" \",(_caseInfo$created_use=(_caseInfo$created_use2=caseInfo.created_user)===null||_caseInfo$created_use2===void 0?void 0:_caseInfo$created_use2.full_name)!==null&&_caseInfo$created_use!==void 0?_caseInfo$created_use:\"---\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:flex hidden  flex-row justify-end\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator,_caseInfo$coordinator2;setSelectCoordinator((_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-sm\",children:\" Assigned Coordinator \"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:block hidden\",children:[\"#\",caseInfo.id]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:caseInfo.is_pay?/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold bg-primary px-2 text-white\",children:\"Paid\"}):/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold bg-danger  px-2 text-white\",children:\"Unpaid\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"CIA:\"}),\" \",(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Full Name:\"}),\" \",(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1  text-sm items-center  \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold text-[#303030] opacity-80\",children:\"Country:\"}),\" \",getIconCountry((_caseInfo$patient$pat=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.patient_country)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\"),\" \",/*#__PURE__*/_jsx(\"span\",{className:\"text-[#303030] opacity-80\",children:caseStatus((_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.patient_country)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1 md:my-0 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-80 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-80 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Status:\"}),\" \",(_caseInfo$case_status=caseInfo.case_status)===null||_caseInfo$case_status===void 0?void 0:_caseInfo$case_status.map((stat,index)=>/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:caseStatusColor(stat.status_coordination),children:caseStatus(stat.status_coordination)}),\"- \"]}))]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row justify-end md:hidden\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{var _caseInfo$coordinator3,_caseInfo$coordinator4;setSelectCoordinator((_caseInfo$coordinator3=(_caseInfo$coordinator4=caseInfo.coordinator_user)===null||_caseInfo$coordinator4===void 0?void 0:_caseInfo$coordinator4.id)!==null&&_caseInfo$coordinator3!==void 0?_caseInfo$coordinator3:\"\");setSelectCoordinatorError(\"\");setOpenDiag(true);setIsLoading(false);},className:\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 text-sm\",children:\" Assigned Coordinator \"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsxs(\"a\",{className:\"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",href:\"/cases/edit/\"+caseInfo.id+\"?section=\"+getSectionIndex(selectPage),children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsxs(\"button\",{disabled:loadingCaseDuplicate,onClick:()=>{dispatch(duplicateCase(caseInfo.id));},className:\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"// href={\"/cases/edit/\" + caseInfo.id}\n,children:[/*#__PURE__*/_jsx(\"span\",{children:loadingCaseDuplicate?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",class:\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{class:\"sr-only\",children:\"Loading...\"})]}):/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Duplicate Case\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectPage(select),className:\"px-4 py-1 md:my-0 my-1  text-sm \".concat(selectPage===select?\"rounded-full bg-[#0388A6] text-white font-medium \":\"font-normal text-[#838383]\"),children:select}))}),selectPage===\"General Information\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Patient Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$ful2=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date of Birth:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$bir=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat2=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_phone)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat3=(_caseInfo$patient7=caseInfo.patient)===null||_caseInfo$patient7===void 0?void 0:_caseInfo$patient7.patient_email)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Country:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat4=(_caseInfo$patient8=caseInfo.patient)===null||_caseInfo$patient8===void 0?void 0:_caseInfo$patient8.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"City:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat5=(_caseInfo$patient9=caseInfo.patient)===null||_caseInfo$patient9===void 0?void 0:_caseInfo$patient9.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Case Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Price of service :\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:parseFloat(caseInfo.price_tatal).toFixed(2)+\"\"+getCurrencyCode((_caseInfo$currency_pr=caseInfo.currency_price)!==null&&_caseInfo$currency_pr!==void 0?_caseInfo$currency_pr:\"\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Price of service (EUR) :\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:parseFloat(caseInfo.eur_price).toFixed(2)+\"\"+getCurrencyCode(\"EUR\")})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Case Creation Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Assigned Coordinator:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$coordinator5=(_caseInfo$coordinator6=caseInfo.coordinator_user)===null||_caseInfo$coordinator6===void 0?void 0:_caseInfo$coordinator6.full_name)!==null&&_caseInfo$coordinator5!==void 0?_caseInfo$coordinator5:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Description:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Coordination Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Current Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Last Updated Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.updated_at)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Appointment Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Scheduled Appointment Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.appointment_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"---\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Providers Informations\"}),(_caseInfo$provider_se=caseInfo.provider_services)===null||_caseInfo$provider_se===void 0?void 0:_caseInfo$provider_se.map((provider,index)=>{var _provider$provider,_provider$provider$fu,_provider$provider2,_provider$provider_se,_provider$provider_se2,_provider$provider_se3;return/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"a\",{href:\"/providers-list/profile/\".concat((_provider$provider=provider.provider)===null||_provider$provider===void 0?void 0:_provider$provider.id),className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\",children:[/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Provider Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_provider$provider$fu=(_provider$provider2=provider.provider)===null||_provider$provider2===void 0?void 0:_provider$provider2.full_name)!==null&&_provider$provider$fu!==void 0?_provider$provider$fu:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Service:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:((_provider$provider_se=provider.provider_service)===null||_provider$provider_se===void 0?void 0:_provider$provider_se.service_type)+(((_provider$provider_se2=provider.provider_service)===null||_provider$provider_se2===void 0?void 0:_provider$provider_se2.service_specialist)!==\"\"?\": \"+((_provider$provider_se3=provider.provider_service)===null||_provider$provider_se3===void 0?void 0:_provider$provider_se3.service_specialist):\"\")})]}),/*#__PURE__*/_jsx(\"div\",{children:\"---------\"})]});})]})})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Invoice Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date Issued:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.date_issued)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Amount:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[\"$\",parseFloat(caseInfo.invoice_amount).toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Due Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Insurance Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Insurance Company Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance$a2=(_caseInfo$assurance2=caseInfo.assurance)===null||_caseInfo$assurance2===void 0?void 0:_caseInfo$assurance2.assurance_name)!==null&&_caseInfo$assurance$a2!==void 0?_caseInfo$assurance$a2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"CIA Reference:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Policy Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-80\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 b py-3  px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1  py-1 px-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Comment\"}),/*#__PURE__*/_jsx(\"textarea\",{value:commentInput,onChange:v=>setCommentInput(v.target.value),className:\"  \".concat(commentInputError?\"border-danger\":\"border-[#F1F3FF]\",\" min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3\")}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:commentInputError?commentInputError:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 bg-white py-1 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Images\"}),/*#__PURE__*/_jsxs(\"div\",{...getRootComments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputComments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-7 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-sm\",children:\"Drag & Drop Images or BROWSE\"})]})]})})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesComments===null||filesComments===void 0?void 0:filesComments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-[#81838E] text-center  shadow-1 \",children:/*#__PURE__*/_jsx(\"img\",{src:file.preview,className:\"size-8\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesComments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{disabled:loadingCommentCaseAdd,onClick:async()=>{var check=true;setCommentInputError(\"\");if(commentInput===\"\"&&filesComments.length===0){setCommentInputError(\"This field is required.\");check=false;}if(check){await dispatch(addNewCommentCase({content:commentInput,// files\nfiles_commet:filesComments},id));}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",children:loadingCommentCaseAdd?\"Loading ..\":\"Save\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5\",children:loadingCommentCase?/*#__PURE__*/_jsx(Loader,{}):errorCommentCase?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCommentCase}):comments?/*#__PURE__*/_jsx(_Fragment,{children:comments===null||comments===void 0?void 0:comments.map((comment,index)=>{var _comment$coordinator,_comment$coordinator2,_comment$coordinator3,_comment$coordinator4,_comment$coordinator5,_comment$coordinator6,_comment$coordinator$,_comment$coordinator7,_comment$content,_comment$files;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-start\",children:[/*#__PURE__*/_jsx(\"div\",{children:comment.coordinator?(_comment$coordinator=comment.coordinator)!==null&&_comment$coordinator!==void 0&&_comment$coordinator.photo?/*#__PURE__*/_jsx(\"img\",{className:\" size-12 rounded-full\",src:baseURLFile+((_comment$coordinator2=comment.coordinator)===null||_comment$coordinator2===void 0?void 0:_comment$coordinator2.photo),onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"div\",{className:\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" uppercase\",children:[(_comment$coordinator3=comment.coordinator)!==null&&_comment$coordinator3!==void 0&&_comment$coordinator3.first_name?(_comment$coordinator4=comment.coordinator)===null||_comment$coordinator4===void 0?void 0:_comment$coordinator4.first_name[0]:\"\",(_comment$coordinator5=comment.coordinator)!==null&&_comment$coordinator5!==void 0&&_comment$coordinator5.last_name?(_comment$coordinator6=comment.coordinator)===null||_comment$coordinator6===void 0?void 0:_comment$coordinator6.last_name[0]:\"\"]})}):null}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row mb-1 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1 text-xs\",children:formatDate(comment.created_at)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1 font-semibold\",children:(_comment$coordinator$=(_comment$coordinator7=comment.coordinator)===null||_comment$coordinator7===void 0?void 0:_comment$coordinator7.full_name)!==null&&_comment$coordinator$!==void 0?_comment$coordinator$:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1\",children:(_comment$content=comment.content)!==null&&_comment$content!==void 0?_comment$content:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap items-center  my-1\",children:comment===null||comment===void 0?void 0:(_comment$files=comment.files)===null||_comment$files===void 0?void 0:_comment$files.map((file,index)=>/*#__PURE__*/_jsx(\"a\",{target:\"_blank\",rel:\"noopener noreferrer\",href:baseURLFile+file.file,children:/*#__PURE__*/_jsx(\"img\",{src:baseURLFile+file.file,className:\"size-30 shadow-1 rounded m-1\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}))}),/*#__PURE__*/_jsx(\"hr\",{className:\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"})]})]});})}):null})]})})]}):null]}),openDiag?/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-bold mb-4\",children:\"Assigned Coordinator\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mb-4 text-xs\",children:\"Please Select Coordinator.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\" w-full   my-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{className:\" outline-none border \".concat(selectCoordinatorError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),value:selectCoordinator,onChange:v=>setSelectCoordinator(v.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Coordinator\"}),coordinators===null||coordinators===void 0?void 0:coordinators.map((item,index)=>/*#__PURE__*/_jsx(\"option\",{value:item.id,children:item.full_name}))]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:selectCoordinatorError?selectCoordinatorError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end mt-4\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",onClick:async()=>{setSelectCoordinatorError(\"\");if(selectCoordinator===\"\"){setSelectCoordinatorError(\"This field is required.\");}else{setIsLoading(true);await dispatch(updateAssignedCase(id,{coordinator:selectCoordinator}));setIsLoading(false);}},disabled:isLoading,children:[\" \",isLoading?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",className:\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Loading...\"})]}):\"Confirm\",\" \"]}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",onClick:()=>{setSelectCoordinator(\"\");setSelectCoordinatorError(\"\");setOpenDiag(false);setIsLoading(false);},disabled:isLoading,children:\"Cancel\"})]})]})}):null]});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "detailCase", "duplicateCase", "getListCommentCase", "updateAssignedCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$currency_pr", "_caseInfo$coordinator5", "_caseInfo$coordinator6", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider_se", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "redirect", "type", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "message", "class", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "_caseInfo$coordinator3", "_caseInfo$coordinator4", "disabled", "role", "select", "concat", "birth_day", "patient_phone", "patient_email", "patient_city", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "case_description", "updated_at", "appointment_date", "service_location", "provider_services", "provider", "_provider$provider", "_provider$provider$fu", "_provider$provider2", "_provider$provider_se", "_provider$provider_se2", "_provider$provider_se3", "provider_service", "service_type", "service_specialist", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "assurance_number", "policy_number", "upload_authorization", "value", "onChange", "v", "style", "src", "onError", "e", "onerror", "name", "size", "filter", "_", "indexToRemove", "check", "length", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator$", "_comment$coordinator7", "_comment$content", "_comment$files", "coordinator", "photo", "first_name", "last_name", "created_at", "files"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  detailCase,\n  duplicateCase,\n  getListCommentCase,\n  updateAssignedCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex md:flex-row flex-col justify-between my-1\">\n                <div className=\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:hidden\">\n                  #{caseInfo.id}\n                </div>\n                <div className=\"w-3\"></div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Created By:</span>{\" \"}\n                    {caseInfo.created_user?.full_name ?? \"---\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:flex hidden  flex-row justify-end\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\" text-[#32475C] text-md font-medium opacity-85 ml-1 md:my-0 my-1 md:block hidden\">\n                  #{caseInfo.id}\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017a4.559 4.559 0 0 0-.018.402c0 .464.336.844.775.994l2.95 1.012c.44.15.775.53.775.994 0 .136-.006.27-.018.402-.047.539-.485.945-1.021 1.017a9.077 9.077 0 0 1-3.461-.203M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    {caseInfo.is_pay ? (\n                      <span className=\"font-semibold bg-primary px-2 text-white\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"font-semibold bg-danger  px-2 text-white\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">CIA:</span>{\" \"}\n                    {caseInfo.assurance?.assurance_name ?? \"---\"}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                      />\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1  text-sm items-center  \">\n                    <span className=\"font-semibold text-[#303030] opacity-80\">\n                      Country:\n                    </span>{\" \"}\n                    {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}{\" \"}\n                    <span className=\"text-[#303030] opacity-80\">\n                      {caseStatus(caseInfo.patient?.patient_country)}\n                    </span>\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n\n              <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                <div className=\"flex flex-row items-center ml-1 md:my-0 my-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-80 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-80 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseInfo.case_status?.map((stat, index) => (\n                      <>\n                        <span\n                          className={caseStatusColor(stat.status_coordination)}\n                        >\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                        {\"- \"}\n                      </>\n                    ))}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n              <div className=\"flex flex-row justify-end md:hidden\">\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex flex-row items-center bg-primary text-white px-2 py-1 rounded \"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"size-4 mx-1\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                    />\n                  </svg>\n                  <div className=\"mx-1 text-sm\"> Assigned Coordinator </div>\n                </button>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-primary px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button>\n              </div>\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div> */}\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Country:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_country ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">City:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_city ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Price of service :</div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.price_tatal).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">\n                        Price of service (EUR) :\n                      </div>\n                      <div className=\"flex-1 mx-1\">\n                        {parseFloat(caseInfo.eur_price).toFixed(2) +\n                          \"\" +\n                          getCurrencyCode(\"EUR\")}\n                      </div>\n                    </div>\n\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator_user?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\" w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Providers Informations\n                      </div>\n                      {caseInfo.provider_services?.map((provider, index) => (\n                        <div>\n                          <a\n                            href={`/providers-list/profile/${provider.provider?.id}`}\n                            className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row items-center hover:text-primary\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              class=\"size-4 mx-1\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                              />\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                              />\n                            </svg>\n\n                            <div className=\"font-semibold\">Provider Name:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider?.full_name ?? \"---\"}\n                            </div>\n                          </a>\n                          <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                            <div className=\"font-semibold\">Service:</div>\n                            <div className=\"flex-1 mx-1\">\n                              {provider.provider_service?.service_type +\n                                (provider.provider_service\n                                  ?.service_specialist !== \"\"\n                                  ? \": \" +\n                                    provider.provider_service\n                                      ?.service_specialist\n                                  : \"\")}\n                            </div>\n                          </div>\n                          <div>---------</div>\n                        </div>\n                      ))}\n                      {/* <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\"> */}\n                      {/* <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div> */}\n                    </div>\n                    {/* <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div> */}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">CIA Reference:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-80 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-80\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img\n                            src={file.preview}\n                            className=\"size-8\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                          />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs\">\n                                {formatDate(comment.created_at)}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment?.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                    onError={(e) => {\n                                      e.target.onerror = null;\n                                      e.target.src = \"/assets/placeholder.png\";\n                                    }}\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n      {openDiag ? (\n        <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center z-99999 bg-black bg-opacity-20\">\n          <div className=\"bg-white p-6 rounded shadow-md mx-3 md:w-1/2 w-full m-2\">\n            <h3 className=\"text-lg font-bold mb-4\">Assigned Coordinator</h3>\n            <p className=\"mb-4 text-xs\">Please Select Coordinator.</p>\n\n            <div className=\" w-full   my-2\">\n              <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                Assigned Coordinator <strong className=\"text-danger\">*</strong>\n              </div>\n              <div>\n                <select\n                  className={` outline-none border ${\n                    selectCoordinatorError\n                      ? \"border-danger\"\n                      : \"border-[#F1F3FF]\"\n                  } px-3 py-2 w-full rounded text-sm`}\n                  value={selectCoordinator}\n                  onChange={(v) => setSelectCoordinator(v.target.value)}\n                >\n                  <option value={\"\"}>Select Coordinator</option>\n                  {coordinators?.map((item, index) => (\n                    <option value={item.id}>{item.full_name}</option>\n                  ))}\n                </select>\n                <div className=\" text-[8px] text-danger\">\n                  {selectCoordinatorError ? selectCoordinatorError : \"\"}\n                </div>\n              </div>\n            </div>\n            <div className=\"flex justify-end mt-4\">\n              <button\n                className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                onClick={async () => {\n                  setSelectCoordinatorError(\"\");\n\n                  if (selectCoordinator === \"\") {\n                    setSelectCoordinatorError(\"This field is required.\");\n                  } else {\n                    setIsLoading(true);\n                    await dispatch(\n                      updateAssignedCase(id, { coordinator: selectCoordinator })\n                    );\n                    setIsLoading(false);\n                  }\n                }}\n                disabled={isLoading}\n              >\n                {\" \"}\n                {isLoading ? (\n                  <div role=\"status\">\n                    <svg\n                      aria-hidden=\"true\"\n                      className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                      viewBox=\"0 0 100 101\"\n                      fill=\"none\"\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                    >\n                      <path\n                        d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                        fill=\"currentColor\"\n                      />\n                      <path\n                        d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                        fill=\"currentFill\"\n                      />\n                    </svg>\n                    <span className=\"sr-only\">Loading...</span>\n                  </div>\n                ) : (\n                  \"Confirm\"\n                )}{\" \"}\n              </button>\n              <button\n                className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                onClick={() => {\n                  setSelectCoordinator(\"\");\n                  setSelectCoordinatorError(\"\");\n                  setOpenDiag(false);\n                  setIsLoading(false);\n                }}\n                disabled={isLoading}\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : null}\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,iBAAiB,CACjBC,UAAU,CACVC,aAAa,CACbC,kBAAkB,CAClBC,kBAAkB,KACb,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,CAAEC,SAAS,CAAEC,aAAa,KAAQ,iBAAiB,CAEvE,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,sBAAsB,KAAQ,qCAAqC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE7E,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAGpE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqE,QAAQ,CAAGtE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAuE,QAAQ,CAAGzE,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE0E,EAAG,CAAC,CAAGtE,SAAS,CAAC,CAAC,CACxB,KAAM,CAACuE,YAAY,CAAC,CAAGtE,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAuE,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACiF,QAAQ,CAAEC,WAAW,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACmF,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGpF,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACqF,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGtF,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACuF,UAAU,CAAEC,aAAa,CAAC,CAAGxF,QAAQ,CAAC,qBAAqB,CAAC,CACnE,KAAM,CAACyF,YAAY,CAAEC,eAAe,CAAC,CAAG1F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC2F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5F,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAAC6F,WAAW,CAAEC,cAAc,CAAC,CAAG9F,QAAQ,CAAC,KAAK,CAAC,CAErD;AACA;AACA,KAAM,CAAC+F,aAAa,CAAEC,gBAAgB,CAAC,CAAGhG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAEiG,YAAY,CAAEC,eAAe,CAAEC,aAAa,CAAEC,gBAAiB,CAAC,CACtElF,WAAW,CAAC,CACVmF,MAAM,CAAE,CACN,SAAS,CAAE,EACb,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,gBAAgB,CAAEQ,SAAS,EAAK,CAC9B,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEJ3G,SAAS,CAAC,IAAM,CACd,MAAO,IACLgG,aAAa,CAACiB,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAK,SAAS,CAAGhH,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAGrH,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAAC3G,UAAU,CAAC,CAC3D,KAAM,CAAEgH,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,eAAe,CAAG1H,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAACU,eAAe,CAAC,CACrE,KAAM,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,KAAM,CAAC,CAC7DL,eAAe,CAEjB,KAAM,CAAAM,iBAAiB,CAAGhI,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAACgB,oBAAoB,CAAC,CAC5E,KAAM,CAAEC,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CACzEJ,iBAAiB,CAEnB,KAAM,CAAAK,gBAAgB,CAAGrI,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAACqB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,kBAAkB,CAAG1I,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAAC0B,kBAAkB,CAAC,CAC3E,KAAM,CACJC,yBAAyB,CACzBC,uBAAuB,CACvBC,yBACF,CAAC,CAAGJ,kBAAkB,CAEtB,KAAM,CAAAK,YAAY,CAAG/I,WAAW,CAAEiH,KAAK,EAAKA,KAAK,CAAC1G,aAAa,CAAC,CAChE,KAAM,CACJyI,oBAAoB,CACpBC,kBAAkB,CAClBC,oBAAoB,CACpBC,aACF,CAAC,CAAGJ,YAAY,CAChB;AACA,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBvJ,SAAS,CAAC,IAAM,CACd,GAAI,CAACqH,QAAQ,CAAE,CACb5C,QAAQ,CAAC8E,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL5E,QAAQ,CAAClE,UAAU,CAACmE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAAChE,kBAAkB,CAAC,GAAG,CAAEiE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAACtD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAACoD,QAAQ,CAAE4C,QAAQ,CAAE1C,QAAQ,CAAEC,EAAE,CAAEE,IAAI,CAAC,CAAC,CAE5C9E,SAAS,CAAC,IAAM,CACd,GAAIsI,qBAAqB,CAAE,CACzB3C,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBI,gBAAgB,CAAC,EAAE,CAAC,CACpBtB,QAAQ,CAAChE,kBAAkB,CAAC,GAAG,CAAEiE,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAAC0D,qBAAqB,CAAC,CAAC,CAE3BtI,SAAS,CAAC,IAAM,CACd,GAAIqJ,oBAAoB,EAAIC,aAAa,CAAE,CACzC7E,QAAQ,CAAC,cAAc,CAAG6E,aAAa,CAAC,CACxC3E,QAAQ,CAAC,CAAE6E,IAAI,CAAE,sBAAuB,CAAC,CAAC,CAC5C,CACF,CAAC,CAAE,CAACH,oBAAoB,CAAEC,aAAa,CAAC,CAAC,CAEzC;AACAtJ,SAAS,CAAC,IAAM,CACd,MAAO,IAAM+F,cAAc,CAAC,KAAK,CAAC,CACpC,CAAC,CAAE,EAAE,CAAC,CAEN/F,SAAS,CAAC,IAAM,CACd,GAAIiJ,yBAAyB,CAAE,CAC7B5D,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBR,QAAQ,CAAClE,UAAU,CAACmE,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAAChE,kBAAkB,CAAC,GAAG,CAAEiE,EAAE,CAAC,CAAC,CACrCD,QAAQ,CAACtD,mBAAmB,CAAC,GAAG,CAAC,CAAC,CACpC,CACF,CAAC,CAAE,CAAC4H,yBAAyB,CAAC,CAAC,CAE/B,KAAM,CAAAQ,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,IAAK,QAAQ,CACX,MAAO,QAAQ,CACjB,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAC,eAAe,CAAID,UAAU,EAAK,CACtC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,aAAa,CACtB,IAAK,yBAAyB,CAC5B,MAAO,gBAAgB,CACzB,IAAK,6BAA6B,CAChC,MAAO,gBAAgB,CACzB,IAAK,qCAAqC,CACxC,MAAO,cAAc,CACvB,IAAK,kCAAkC,CACrC,MAAO,cAAc,CACvB,IAAK,mBAAmB,CACtB,MAAO,gBAAgB,CACzB,IAAK,QAAQ,CACX,MAAO,gBAAgB,CACzB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAE,cAAc,CAAIC,OAAO,EAAK,CAClC,KAAM,CAAAC,YAAY,CAAGrJ,SAAS,CAACsJ,IAAI,CAAEC,MAAM,EAAKA,MAAM,CAACC,KAAK,GAAKJ,OAAO,CAAC,CAEzE,GAAIC,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CAACI,IAAI,CAC1B,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAIC,IAAI,EAAK,CAChC,KAAM,CAAAC,eAAe,CAAGD,IAAI,SAAJA,IAAI,UAAJA,IAAI,CAAI,EAAE,CAElC,KAAM,CAAAE,aAAa,CAAG5J,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEqJ,IAAI,CACtCC,MAAM,EAAKA,MAAM,CAACI,IAAI,GAAKC,eAC9B,CAAC,CAED,GAAIC,aAAa,CAAE,KAAAC,qBAAA,CACjB,OAAAA,qBAAA,CAAOD,aAAa,CAACE,MAAM,UAAAD,qBAAA,UAAAA,qBAAA,CAAIH,IAAI,CACrC,CAAC,IAAM,CACL,MAAO,CAAAA,IAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAK,eAAe,CAAIC,UAAU,EAAK,CACtC,GAAIA,UAAU,GAAK,qBAAqB,CAAE,CACxC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,sBAAsB,CAAE,CAChD,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,iBAAiB,CAAE,CAC3C,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,UAAU,CAAE,CACpC,MAAO,EAAC,CACV,CAAC,IAAM,IAAIA,UAAU,GAAK,yBAAyB,CAAE,CACnD,MAAO,EAAC,CACV,CAAC,IAAM,CACL,MAAO,EAAC,CACV,CACF,CAAC,CAED;AACA,mBACExJ,KAAA,CAACb,aAAa,EAAAsK,QAAA,eACZzJ,KAAA,QAAK0J,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfzJ,KAAA,QAAK0J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD3J,IAAA,MAAG6J,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBzJ,KAAA,QAAK0J,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3J,IAAA,SACEkK,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpK,IAAA,SAAM4J,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ3J,IAAA,SAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3J,IAAA,SACEkK,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpK,IAAA,MAAG6J,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB3J,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ3J,IAAA,SAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3J,IAAA,SACEkK,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpK,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGL1D,eAAe,cACdjG,IAAA,CAACV,MAAM,GAAE,CAAC,CACR4G,aAAa,cACflG,IAAA,CAACT,KAAK,EAACyI,IAAI,CAAE,OAAQ,CAACqC,OAAO,CAAEnE,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACVlG,KAAA,QAAAyJ,QAAA,eAEEzJ,KAAA,QAAK0J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvDzJ,KAAA,QAAK0J,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DzJ,KAAA,QAAK0J,SAAS,CAAC,4EAA4E,CAAAD,QAAA,EAAC,GACzF,CAACvD,QAAQ,CAAChD,EAAE,EACV,CAAC,cACNpD,IAAA,QAAK4J,SAAS,CAAC,KAAK,CAAM,CAAC,cAE3B1J,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,cAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,2JAA2J,CAC9J,CAAC,CACC,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3J,IAAA,SAAM4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,aAAW,CAAM,CAAC,CAAC,GAAG,EAAAhJ,qBAAA,EAAAC,sBAAA,CACrDwF,QAAQ,CAACmE,YAAY,UAAA3J,sBAAA,iBAArBA,sBAAA,CAAuB4J,SAAS,UAAA7J,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACvC,CAAC,EACH,CAAC,EACH,CAAC,cACNX,IAAA,QAAK4J,SAAS,CAAC,sCAAsC,CAAAD,QAAA,cACnDzJ,KAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CACb9G,oBAAoB,EAAA6G,qBAAA,EAAAC,sBAAA,CAACvE,QAAQ,CAACwE,gBAAgB,UAAAD,sBAAA,iBAAzBA,sBAAA,CAA2BvH,EAAE,UAAAsH,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzD3G,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFmG,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/E3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,aAAa,CAAAX,QAAA,cAEnB3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,cACNpK,IAAA,QAAK4J,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,wBAAsB,CAAK,CAAC,EACpD,CAAC,CACN,CAAC,cAENzJ,KAAA,QAAK0J,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DzJ,KAAA,QAAK0J,SAAS,CAAC,kFAAkF,CAAAD,QAAA,EAAC,GAC/F,CAACvD,QAAQ,CAAChD,EAAE,EACV,CAAC,cACNlD,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,cAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,8fAA8f,CACjgB,CAAC,CACC,CAAC,CACH,CAAC,cACNpK,IAAA,QAAK4J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CACrDvD,QAAQ,CAACyE,MAAM,cACd7K,IAAA,SAAM4J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,MAE3D,CAAM,CAAC,cAEP3J,IAAA,SAAM4J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,QAE3D,CAAM,CACP,CACE,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACN1J,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,cAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,oIAAoI,CACvI,CAAC,CACC,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3J,IAAA,SAAM4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,CAAC,GAAG,EAAA9I,qBAAA,EAAAC,mBAAA,CAC9CsF,QAAQ,CAAC0E,SAAS,UAAAhK,mBAAA,iBAAlBA,mBAAA,CAAoBiK,cAAc,UAAAlK,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACzC,CAAC,cACNb,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,cACN1J,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,cAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3J,IAAA,SAAM4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,CAAC,GAAG,EAAA5I,qBAAA,EAAAC,iBAAA,CACpDoF,QAAQ,CAAC4E,OAAO,UAAAhK,iBAAA,iBAAhBA,iBAAA,CAAkBwJ,SAAS,UAAAzJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAClC,CAAC,EACH,CAAC,cAENb,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACEzJ,KAAA,QACE4J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,eAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,uCAAuC,CAC1C,CAAC,cACFpK,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,gFAAgF,CACnF,CAAC,EACC,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C3J,IAAA,SAAM4J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,CAAC,UAE1D,CAAM,CAAC,CAAC,GAAG,CACVf,cAAc,EAAA3H,qBAAA,EAAAC,kBAAA,CAACkF,QAAQ,CAAC4E,OAAO,UAAA9J,kBAAA,iBAAhBA,kBAAA,CAAkB+J,eAAe,UAAAhK,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAAE,GAAG,cAC7DjB,IAAA,SAAM4J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACxClB,UAAU,EAAAtH,kBAAA,CAACiF,QAAQ,CAAC4E,OAAO,UAAA7J,kBAAA,iBAAhBA,kBAAA,CAAkB8J,eAAe,CAAC,CAC1C,CAAC,EACJ,CAAC,cACNjL,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,EACH,CAAC,cAEN5J,IAAA,QAAK4J,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC7DzJ,KAAA,QAAK0J,SAAS,CAAC,8CAA8C,CAAAD,QAAA,eAC3D3J,IAAA,QAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,yCAAyC,CAAAX,QAAA,cAE/C3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3J,IAAA,SAAM4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAC,GAAG,EAAAvI,qBAAA,CACjDgF,QAAQ,CAAC8E,WAAW,UAAA9J,qBAAA,iBAApBA,qBAAA,CAAsB8D,GAAG,CAAC,CAACiG,IAAI,CAAEC,KAAK,gBACrClL,KAAA,CAAAE,SAAA,EAAAuJ,QAAA,eACE3J,IAAA,SACE4J,SAAS,CAAEjB,eAAe,CAACwC,IAAI,CAACE,mBAAmB,CAAE,CAAA1B,QAAA,CAEpDlB,UAAU,CAAC0C,IAAI,CAACE,mBAAmB,CAAC,CACjC,CAAC,CACN,IAAI,EACL,CACH,CAAC,EACC,CAAC,cACNrL,IAAA,QAAK4J,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,CACH,CAAC,cACN5J,IAAA,QAAK4J,SAAS,CAAC,qCAAqC,CAAAD,QAAA,cAClDzJ,KAAA,WACEuK,OAAO,CAAEA,CAAA,GAAM,KAAAa,sBAAA,CAAAC,sBAAA,CACb1H,oBAAoB,EAAAyH,sBAAA,EAAAC,sBAAA,CAACnF,QAAQ,CAACwE,gBAAgB,UAAAW,sBAAA,iBAAzBA,sBAAA,CAA2BnI,EAAE,UAAAkI,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CACzDvH,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,IAAI,CAAC,CACjBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFmG,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAE/E3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,aAAa,CAAAX,QAAA,cAEnB3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,cACNpK,IAAA,QAAK4J,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,wBAAsB,CAAK,CAAC,EACpD,CAAC,CACN,CAAC,EACH,CAAC,cAENzJ,KAAA,QAAK0J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvDzJ,KAAA,QAAK0J,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCzJ,KAAA,MACE0J,SAAS,CAAC,iGAAiG,CAC3GC,IAAI,CACF,cAAc,CACdzD,QAAQ,CAAChD,EAAE,CACX,WAAW,CACXqG,eAAe,CAACzF,UAAU,CAC3B,CAAA2F,QAAA,eAED3J,IAAA,SAAA2J,QAAA,cACE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEd3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,2gBAA2gB,CAC9gB,CAAC,CACC,CAAC,CACF,CAAC,cACPpK,IAAA,SAAM4J,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACtC,CAAC,cACJzJ,KAAA,WACEsL,QAAQ,CAAE7D,oBAAqB,CAC/B8C,OAAO,CAAEA,CAAA,GAAM,CACbtH,QAAQ,CAACjE,aAAa,CAACkH,QAAQ,CAAChD,EAAE,CAAC,CAAC,CACtC,CAAE,CACFwG,SAAS,CAAC,iGACV;AAAA,CAAAD,QAAA,eAEA3J,IAAA,SAAA2J,QAAA,CACGhC,oBAAoB,cACnBzH,KAAA,QAAKuL,IAAI,CAAC,QAAQ,CAAA9B,QAAA,eAChBzJ,KAAA,QACE,cAAY,MAAM,CAClBoK,KAAK,CAAC,oEAAoE,CAC1EN,OAAO,CAAC,aAAa,CACrBD,IAAI,CAAC,MAAM,CACXD,KAAK,CAAC,4BAA4B,CAAAH,QAAA,eAElC3J,IAAA,SACEoK,CAAC,CAAC,8WAA8W,CAChXL,IAAI,CAAC,cAAc,CACpB,CAAC,cACF/J,IAAA,SACEoK,CAAC,CAAC,+kBAA+kB,CACjlBL,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACN/J,IAAA,SAAMsK,KAAK,CAAC,SAAS,CAAAX,QAAA,CAAC,YAAU,CAAM,CAAC,EACpC,CAAC,cAEN3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEd3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,slBAAslB,CACzlB,CAAC,CACC,CACN,CACG,CAAC,cACPpK,IAAA,SAAM4J,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,gBAAc,CAAM,CAAC,EACtC,CAAC,EACN,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,iGAAiG,CAAAD,QAAA,CAC7G,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CAC1B,CAACzE,GAAG,CAAC,CAACwG,MAAM,CAAEN,KAAK,gBAClBpL,IAAA,WACEyK,OAAO,CAAEA,CAAA,GAAMxG,aAAa,CAACyH,MAAM,CAAE,CACrC9B,SAAS,oCAAA+B,MAAA,CACP3H,UAAU,GAAK0H,MAAM,CACjB,mDAAmD,CACnD,4BAA4B,CAC/B,CAAA/B,QAAA,CAEF+B,MAAM,CACD,CACT,CAAC,CACC,CAAC,CAEL1H,UAAU,GAAK,qBAAqB,cACnC9D,KAAA,QAAK0J,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFzJ,KAAA,QAAK0J,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtI,sBAAA,EAAAC,kBAAA,CACzB8E,QAAQ,CAAC4E,OAAO,UAAA1J,kBAAA,iBAAhBA,kBAAA,CAAkBkJ,SAAS,UAAAnJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNnB,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAApI,qBAAA,EAAAC,kBAAA,CACzB4E,QAAQ,CAAC4E,OAAO,UAAAxJ,kBAAA,iBAAhBA,kBAAA,CAAkBoK,SAAS,UAAArK,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNrB,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlI,sBAAA,EAAAC,kBAAA,CACzB0E,QAAQ,CAAC4E,OAAO,UAAAtJ,kBAAA,iBAAhBA,kBAAA,CAAkBmK,aAAa,UAAApK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNvB,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAhI,sBAAA,EAAAC,kBAAA,CACzBwE,QAAQ,CAAC4E,OAAO,UAAApJ,kBAAA,iBAAhBA,kBAAA,CAAkBkK,aAAa,UAAAnK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cAONzB,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9H,sBAAA,EAAAC,kBAAA,CACzBsE,QAAQ,CAAC4E,OAAO,UAAAlJ,kBAAA,iBAAhBA,kBAAA,CAAkBmJ,eAAe,UAAApJ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACxC,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5H,sBAAA,EAAAC,kBAAA,CACzBoE,QAAQ,CAAC4E,OAAO,UAAAhJ,kBAAA,iBAAhBA,kBAAA,CAAkB+J,YAAY,UAAAhK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACrC,CAAC,EACH,CAAC,EACH,CAAC,cACN7B,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,cAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBqC,UAAU,CAAC5F,QAAQ,CAAC6F,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAC1C,EAAE,CACF/C,eAAe,EAAAlH,qBAAA,CAACmE,QAAQ,CAAC+F,cAAc,UAAAlK,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C,CAAC,EACH,CAAC,cACN/B,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,0BAE/B,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBqC,UAAU,CAAC5F,QAAQ,CAACgG,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,CACxC,EAAE,CACF/C,eAAe,CAAC,KAAK,CAAC,CACrB,CAAC,EACH,CAAC,cAENjJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,cACxD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAC7B,QAAQ,CAACiG,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cACNnM,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC1D3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzH,sBAAA,EAAAC,sBAAA,CACzBiE,QAAQ,CAACwE,gBAAgB,UAAAzI,sBAAA,iBAAzBA,sBAAA,CAA2BqI,SAAS,UAAAtI,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAC3C,CAAC,EACH,CAAC,cACNhC,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvH,qBAAA,CACzBgE,QAAQ,CAACkG,gBAAgB,UAAAlK,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEP4B,UAAU,GAAK,sBAAsB,cACpC9D,KAAA,QAAAyJ,QAAA,eACEzJ,KAAA,QAAK0J,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFzJ,KAAA,QAAK0J,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtH,qBAAA,CACzB+D,QAAQ,CAACiF,mBAAmB,UAAAhJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNnC,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAC7B,QAAQ,CAACmG,UAAU,CAAC,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,cACNrM,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6BAE/B,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAC7B,QAAQ,CAACoG,gBAAgB,CAAC,CACnC,CAAC,EACH,CAAC,cACNtM,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cACtD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArH,qBAAA,CACzB8D,QAAQ,CAACqG,gBAAgB,UAAAnK,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENtC,IAAA,QAAK4J,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvFzJ,KAAA,QAAK0J,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/B3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,wBAExD,CAAK,CAAC,EAAApH,qBAAA,CACL6D,QAAQ,CAACsG,iBAAiB,UAAAnK,qBAAA,iBAA1BA,qBAAA,CAA4B2C,GAAG,CAAC,CAACyH,QAAQ,CAAEvB,KAAK,QAAAwB,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC/C/M,KAAA,QAAAyJ,QAAA,eACEzJ,KAAA,MACE2J,IAAI,4BAAA8B,MAAA,EAAAiB,kBAAA,CAA6BD,QAAQ,CAACA,QAAQ,UAAAC,kBAAA,iBAAjBA,kBAAA,CAAmBxJ,EAAE,CAAG,CACzDwG,SAAS,CAAC,sFAAsF,CAAAD,QAAA,eAEhGzJ,KAAA,QACE4J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,aAAa,CAAAX,QAAA,eAEnB3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFpK,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,cAENpK,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAkD,qBAAA,EAAAC,mBAAA,CACzBH,QAAQ,CAACA,QAAQ,UAAAG,mBAAA,iBAAjBA,mBAAA,CAAmBtC,SAAS,UAAAqC,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACL,CAAC,cACJ3M,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB,EAAAoD,qBAAA,CAAAJ,QAAQ,CAACO,gBAAgB,UAAAH,qBAAA,iBAAzBA,qBAAA,CAA2BI,YAAY,GACrC,EAAAH,sBAAA,CAAAL,QAAQ,CAACO,gBAAgB,UAAAF,sBAAA,iBAAzBA,sBAAA,CACGI,kBAAkB,IAAK,EAAE,CACzB,IAAI,GAAAH,sBAAA,CACJN,QAAQ,CAACO,gBAAgB,UAAAD,sBAAA,iBAAzBA,sBAAA,CACIG,kBAAkB,EACtB,EAAE,CAAC,CACN,CAAC,EACH,CAAC,cACNpN,IAAA,QAAA2J,QAAA,CAAK,WAAS,CAAK,CAAC,EACjB,CAAC,EACP,CAAC,EAaC,CAAC,CAkBH,CAAC,EACH,CAAC,CACJ,IAAI,CAEP3F,UAAU,GAAK,iBAAiB,cAC/BhE,IAAA,QAAK4J,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvFzJ,KAAA,QAAK0J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAnH,qBAAA,CAC5B4D,QAAQ,CAACiH,eAAe,UAAA7K,qBAAA,iBAAxBA,qBAAA,CAA0B0C,GAAG,CAAC,CAACoI,IAAI,CAAElC,KAAK,gBACzCpL,IAAA,MACE6J,IAAI,CAAErK,WAAW,CAAG8N,IAAI,CAACnI,IAAK,CAC9BoI,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB5D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzJ,KAAA,QAAK0J,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3J,IAAA,QAAK4J,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzJ,KAAA,QACE4J,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3J,IAAA,SAAMoK,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpK,IAAA,SAAMoK,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3J,IAAA,QAAK4J,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F2D,IAAI,CAACG,SAAS,CACZ,CAAC,cACNvN,KAAA,QAAAyJ,QAAA,EAAM2D,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEP1J,UAAU,GAAK,UAAU,cACxB9D,KAAA,QAAK0J,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DzJ,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCzJ,KAAA,QAAK0J,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlH,qBAAA,CACzB2D,QAAQ,CAACuH,cAAc,UAAAlL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cACNvC,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzB1B,UAAU,CAAC7B,QAAQ,CAACwH,WAAW,CAAC,CAC9B,CAAC,EACH,CAAC,cACN1N,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cAC5CzJ,KAAA,QAAK0J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,GAC1B,CAACqC,UAAU,CAAC5F,QAAQ,CAACyH,cAAc,CAAC,CAAC3B,OAAO,CAAC,CAAC,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,cACNhM,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9C3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,EACH,CAAC,EACH,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAjH,qBAAA,CAC5B0D,QAAQ,CAAC0H,eAAe,UAAApL,qBAAA,iBAAxBA,qBAAA,CAA0BwC,GAAG,CAAC,CAACoI,IAAI,CAAElC,KAAK,gBACzCpL,IAAA,MACE6J,IAAI,CAAErK,WAAW,CAAG8N,IAAI,CAACnI,IAAK,CAC9BoI,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB5D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzJ,KAAA,QAAK0J,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3J,IAAA,QAAK4J,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzJ,KAAA,QACE4J,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3J,IAAA,SAAMoK,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpK,IAAA,SAAMoK,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3J,IAAA,QAAK4J,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F2D,IAAI,CAACG,SAAS,CACZ,CAAC,cACNvN,KAAA,QAAAyJ,QAAA,EAAM2D,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1J,UAAU,GAAK,yBAAyB,cACvC9D,KAAA,QAAK0J,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9DzJ,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCzJ,KAAA,QAAK0J,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,mBAExD,CAAK,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAE/B,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAhH,qBAAA,CACzByD,QAAQ,CAAC2H,gBAAgB,UAAApL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACNzC,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yBAE/B,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/G,sBAAA,EAAAC,oBAAA,CACzBuD,QAAQ,CAAC0E,SAAS,UAAAjI,oBAAA,iBAAlBA,oBAAA,CAAoBkI,cAAc,UAAAnI,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,cACN1C,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA7G,qBAAA,CACzBsD,QAAQ,CAAC4H,gBAAgB,UAAAlL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNzJ,KAAA,QAAK0J,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3J,IAAA,QAAK4J,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3J,IAAA,QAAK4J,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5G,qBAAA,CACzBqD,QAAQ,CAAC6H,aAAa,UAAAlL,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,QAAK0J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3J,IAAA,QAAK4J,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA3G,qBAAA,CAC5BoD,QAAQ,CAAC8H,oBAAoB,UAAAlL,qBAAA,iBAA7BA,qBAAA,CAA+BkC,GAAG,CAAC,CAACoI,IAAI,CAAElC,KAAK,gBAC9CpL,IAAA,MACE6J,IAAI,CAAErK,WAAW,CAAG8N,IAAI,CAACnI,IAAK,CAC9BoI,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB5D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzJ,KAAA,QAAK0J,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3J,IAAA,QAAK4J,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzJ,KAAA,QACE4J,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3J,IAAA,SAAMoK,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpK,IAAA,SAAMoK,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlK,KAAA,QAAK0J,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3J,IAAA,QAAK4J,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F2D,IAAI,CAACG,SAAS,CACZ,CAAC,cACNvN,KAAA,QAAAyJ,QAAA,EAAM2D,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EAGL,CAAC,cAEN1N,IAAA,QAAK4J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDzJ,KAAA,QAAK0J,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCzJ,KAAA,QAAK0J,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC3J,IAAA,QAAK4J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BzJ,KAAA,QAAK0J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3J,IAAA,UAAO4J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE5D,CAAO,CAAC,cACR3J,IAAA,aACEmO,KAAK,CAAEjK,YAAa,CACpBkK,QAAQ,CAAGC,CAAC,EAAKlK,eAAe,CAACkK,CAAC,CAACd,MAAM,CAACY,KAAK,CAAE,CACjDvE,SAAS,MAAA+B,MAAA,CACPvH,iBAAiB,CACb,eAAe,CACf,kBAAkB,+EACsD,CACrE,CAAC,cACZpE,IAAA,QAAK4J,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvF,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,CACH,CAAC,cACNpE,IAAA,QAAK4J,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BzJ,KAAA,QAAK0J,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3J,IAAA,UAAO4J,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,QAE5D,CAAO,CAAC,cACRzJ,KAAA,WACMyE,eAAe,CAAC,CAClBiF,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,uFAAuF,CAAAD,QAAA,eAEjG3J,IAAA,aAAW6E,gBAAgB,CAAC,CAAC,CAAG,CAAC,cACjC7E,IAAA,QAAK4J,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNpK,IAAA,QAAK4J,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,8BAE9B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN3J,IAAA,UAAOsO,KAAK,CAAEjO,eAAgB,CAAAsJ,QAAA,cAC5B3J,IAAA,QAAK4J,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCnF,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEiG,KAAK,gBAC9BlL,KAAA,QACE0J,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF3J,IAAA,QAAK4J,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD3J,IAAA,QACEuO,GAAG,CAAEpJ,IAAI,CAACG,OAAQ,CAClBsE,SAAS,CAAC,QAAQ,CAClB4E,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,cACNrO,KAAA,QAAK0J,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3J,IAAA,QAAK4J,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FxE,IAAI,CAACwJ,IAAI,CACP,CAAC,cACNzO,KAAA,QAAAyJ,QAAA,EAAM,CAACxE,IAAI,CAACyJ,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAE1C,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAK,CAAC,EACnD,CAAC,cACNlM,IAAA,WACEyK,OAAO,CAAEA,CAAA,GAAM,CACbhG,gBAAgB,CAAEQ,SAAS,EACzBA,SAAS,CAAC4J,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GAAK3D,KAAK,GAAK2D,aAClC,CACF,CAAC,CACH,CAAE,CACFnF,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEd3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA1CJjF,IAAI,CAACwJ,IA2CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,cACR3O,IAAA,QAAA2J,QAAA,cACE3J,IAAA,WACEwL,QAAQ,CAAE3E,qBAAsB,CAChC4D,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAuE,KAAK,CAAG,IAAI,CAChB3K,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIH,YAAY,GAAK,EAAE,EAAIM,aAAa,CAACyK,MAAM,GAAK,CAAC,CAAE,CACrD5K,oBAAoB,CAAC,yBAAyB,CAAC,CAC/C2K,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAA7L,QAAQ,CACZnE,iBAAiB,CACf,CACEkQ,OAAO,CAAEhL,YAAY,CACrB;AACAiL,YAAY,CAAE3K,aAChB,CAAC,CACDpB,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACLxD,KAAK,CAACmG,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF6D,SAAS,CAAC,yDAAyD,CAAAD,QAAA,CAElE9C,qBAAqB,CAAG,YAAY,CAAG,MAAM,CACxC,CAAC,CACN,CAAC,cACN7G,IAAA,QAAK4J,SAAS,CAAC,MAAM,CAAAD,QAAA,CAClBnD,kBAAkB,cACjBxG,IAAA,CAACV,MAAM,GAAE,CAAC,CACRmH,gBAAgB,cAClBzG,IAAA,CAACT,KAAK,EAACyI,IAAI,CAAE,OAAQ,CAACqC,OAAO,CAAE5D,gBAAiB,CAAE,CAAC,CACjDF,QAAQ,cACVvG,IAAA,CAAAI,SAAA,EAAAuJ,QAAA,CACGpD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAErB,GAAG,CAAC,CAACkK,OAAO,CAAEhE,KAAK,QAAAiE,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,cAAA,oBAC5B5P,KAAA,QAAK0J,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3J,IAAA,QAAA2J,QAAA,CACGyF,OAAO,CAACW,WAAW,CAClB,CAAAV,oBAAA,CAAAD,OAAO,CAACW,WAAW,UAAAV,oBAAA,WAAnBA,oBAAA,CAAqBW,KAAK,cACxBhQ,IAAA,QACE4J,SAAS,CAAC,uBAAuB,CACjC2E,GAAG,CAAE/O,WAAW,GAAA8P,qBAAA,CAAGF,OAAO,CAACW,WAAW,UAAAT,qBAAA,iBAAnBA,qBAAA,CAAqBU,KAAK,CAAC,CAC9CxB,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEFvO,IAAA,QAAK4J,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/GzJ,KAAA,QAAK0J,SAAS,CAAC,YAAY,CAAAD,QAAA,EACxB,CAAA4F,qBAAA,CAAAH,OAAO,CAACW,WAAW,UAAAR,qBAAA,WAAnBA,qBAAA,CAAqBU,UAAU,EAAAT,qBAAA,CAC5BJ,OAAO,CAACW,WAAW,UAAAP,qBAAA,iBAAnBA,qBAAA,CAAqBS,UAAU,CAAC,CAAC,CAAC,CAClC,EAAE,CACL,CAAAR,qBAAA,CAAAL,OAAO,CAACW,WAAW,UAAAN,qBAAA,WAAnBA,qBAAA,CAAqBS,SAAS,EAAAR,qBAAA,CAC3BN,OAAO,CAACW,WAAW,UAAAL,qBAAA,iBAAnBA,qBAAA,CAAqBQ,SAAS,CAAC,CAAC,CAAC,CACjC,EAAE,EACH,CAAC,CACH,CACN,CACC,IAAI,CACL,CAAC,cACNhQ,KAAA,QAAK0J,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BzJ,KAAA,QAAK0J,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3J,IAAA,QACE8J,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBK,KAAK,CAAC,QAAQ,CAAAX,QAAA,cAEd3J,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoK,CAAC,CAAC,6iBAA6iB,CAChjB,CAAC,CACC,CAAC,cAENpK,IAAA,QAAK4J,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CACjC1B,UAAU,CAACmH,OAAO,CAACe,UAAU,CAAC,CAC5B,CAAC,EACH,CAAC,cACNnQ,IAAA,QAAK4J,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAgG,qBAAA,EAAAC,qBAAA,CACxCR,OAAO,CAACW,WAAW,UAAAH,qBAAA,iBAAnBA,qBAAA,CAAqBpF,SAAS,UAAAmF,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAClC,CAAC,cACN3P,IAAA,QAAK4J,SAAS,CAAC,cAAc,CAAAD,QAAA,EAAAkG,gBAAA,CAC1BT,OAAO,CAACF,OAAO,UAAAW,gBAAA,UAAAA,gBAAA,CAAI,EAAE,CACnB,CAAC,cACN7P,IAAA,QAAK4J,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC/CyF,OAAO,SAAPA,OAAO,kBAAAU,cAAA,CAAPV,OAAO,CAAEgB,KAAK,UAAAN,cAAA,iBAAdA,cAAA,CAAgB5K,GAAG,CAAC,CAACC,IAAI,CAAEiG,KAAK,gBAC/BpL,IAAA,MACEuN,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzB3D,IAAI,CAAErK,WAAW,CAAG2F,IAAI,CAACA,IAAK,CAAAwE,QAAA,cAE9B3J,IAAA,QACEuO,GAAG,CAAE/O,WAAW,CAAG2F,IAAI,CAACA,IAAK,CAC7ByE,SAAS,CAAC,8BAA8B,CACxC4E,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAAClB,MAAM,CAACmB,OAAO,CAAG,IAAI,CACvBD,CAAC,CAAClB,MAAM,CAACgB,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACD,CACJ,CAAC,CACC,CAAC,cACNvO,IAAA,OAAI4J,SAAS,CAAC,sEAAsE,CAAE,CAAC,EACpF,CAAC,EACH,CAAC,EACP,CAAC,CACF,CAAC,CACD,IAAI,CACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,CACLlG,QAAQ,cACP1D,IAAA,QAAK4J,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/GzJ,KAAA,QAAK0J,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtE3J,IAAA,OAAI4J,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,sBAAoB,CAAI,CAAC,cAChE3J,IAAA,MAAG4J,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,4BAA0B,CAAG,CAAC,cAE1DzJ,KAAA,QAAK0J,SAAS,CAAC,gBAAgB,CAAAD,QAAA,eAC7BzJ,KAAA,QAAK0J,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,uBACvB,cAAA3J,IAAA,WAAQ4J,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5D,CAAC,cACNzJ,KAAA,QAAAyJ,QAAA,eACEzJ,KAAA,WACE0J,SAAS,yBAAA+B,MAAA,CACP7H,sBAAsB,CAClB,eAAe,CACf,kBAAkB,qCACY,CACpCqK,KAAK,CAAEvK,iBAAkB,CACzBwK,QAAQ,CAAGC,CAAC,EAAKxK,oBAAoB,CAACwK,CAAC,CAACd,MAAM,CAACY,KAAK,CAAE,CAAAxE,QAAA,eAEtD3J,IAAA,WAAQmO,KAAK,CAAE,EAAG,CAAAxE,QAAA,CAAC,oBAAkB,CAAQ,CAAC,CAC7CzC,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEhC,GAAG,CAAC,CAACoI,IAAI,CAAElC,KAAK,gBAC7BpL,IAAA,WAAQmO,KAAK,CAAEb,IAAI,CAAClK,EAAG,CAAAuG,QAAA,CAAE2D,IAAI,CAAC9C,SAAS,CAAS,CACjD,CAAC,EACI,CAAC,cACTxK,IAAA,QAAK4J,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC7F,sBAAsB,CAAGA,sBAAsB,CAAG,EAAE,CAClD,CAAC,EACH,CAAC,EACH,CAAC,cACN5D,KAAA,QAAK0J,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpCzJ,KAAA,WACE0J,SAAS,CAAC,0EAA0E,CACpFa,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB1G,yBAAyB,CAAC,EAAE,CAAC,CAE7B,GAAIH,iBAAiB,GAAK,EAAE,CAAE,CAC5BG,yBAAyB,CAAC,yBAAyB,CAAC,CACtD,CAAC,IAAM,CACLN,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAN,QAAQ,CACZ/D,kBAAkB,CAACgE,EAAE,CAAE,CAAE2M,WAAW,CAAEnM,iBAAkB,CAAC,CAC3D,CAAC,CACDH,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACF+H,QAAQ,CAAEhI,SAAU,CAAAmG,QAAA,EAEnB,GAAG,CACHnG,SAAS,cACRtD,KAAA,QAAKuL,IAAI,CAAC,QAAQ,CAAA9B,QAAA,eAChBzJ,KAAA,QACE,cAAY,MAAM,CAClB0J,SAAS,CAAC,wEAAwE,CAClFI,OAAO,CAAC,aAAa,CACrBD,IAAI,CAAC,MAAM,CACXD,KAAK,CAAC,4BAA4B,CAAAH,QAAA,eAElC3J,IAAA,SACEoK,CAAC,CAAC,8WAA8W,CAChXL,IAAI,CAAC,cAAc,CACpB,CAAC,cACF/J,IAAA,SACEoK,CAAC,CAAC,+kBAA+kB,CACjlBL,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACN/J,IAAA,SAAM4J,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,EACxC,CAAC,CAEN,SACD,CAAE,GAAG,EACA,CAAC,cACT3J,IAAA,WACE4J,SAAS,CAAC,oEAAoE,CAC9Ea,OAAO,CAAEA,CAAA,GAAM,CACb5G,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BJ,WAAW,CAAC,KAAK,CAAC,CAClBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACF+H,QAAQ,CAAEhI,SAAU,CAAAmG,QAAA,CACrB,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,EACK,CAAC,CAEpB,CAEA,cAAe,CAAAjJ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}