{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { USER_<PERSON>OGIN_REQUEST, USER_LOGIN_SUCCESS, USER_<PERSON>OGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_PASSWORD_UPDATE_SUCCESS, USER_PASSWORD_UPDATE_REQUEST, USER_PASSWORD_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDINATOR_ADD_FAIL,\n//\nCOORDINATOR_DETAIL_SUCCESS, COORDINATOR_DETAIL_REQUEST, COORDINATOR_DETAIL_FAIL,\n//\nCOORDINATOR_UPDATE_SUCCESS, COORDINATOR_UPDATE_REQUEST, COORDINATOR_UPDATE_FAIL,\n//\nUSER_UPDATE_LOGIN_SUCCESS, USER_UPDATE_LOGIN_REQUEST, USER_UPDATE_LOGIN_FAIL,\n//\nUSER_HISTORY_LOGED_SUCCESS, USER_HISTORY_LOGED_REQUEST, USER_HISTORY_LOGED_FAIL,\n//\nUSER_HISTORY_SUCCESS, USER_HISTORY_REQUEST, USER_HISTORY_FAIL,\n//\nUSER_LOGOUT_SAVE_SUCCESS, USER_LOGOUT_SAVE_REQUEST, USER_LOGOUT_SAVE_FAIL,\n//\nUSER_RESET_SEND_SUCCESS, USER_RESET_SEND_REQUEST, USER_RESET_SEND_FAIL\n//\n} from \"../constants/userConstants\";\nexport const resetPasswordReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_RESET_SEND_REQUEST:\n      return {\n        loadingResetPassword: true\n      };\n    case USER_RESET_SEND_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingResetPassword: false,\n        successResetPassword: true\n      };\n    case USER_RESET_SEND_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingResetPassword: false,\n        successResetPassword: false,\n        errorResetPassword: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const historyListCoordinatorReducer = (state = {\n  historyCoordinator: []\n}, action) => {\n  switch (action.type) {\n    case USER_HISTORY_REQUEST:\n      return {\n        loadingHistoryCoordinator: true,\n        historyCoordinator: []\n      };\n    case USER_HISTORY_SUCCESS:\n      return {\n        loadingHistoryCoordinator: false,\n        historyCoordinator: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_HISTORY_FAIL:\n      return {\n        loadingHistoryCoordinator: false,\n        errorHistoryCoordinator: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const historyListLoggedReducer = (state = {\n  historyLogged: []\n}, action) => {\n  switch (action.type) {\n    case USER_HISTORY_LOGED_REQUEST:\n      return {\n        loadingHistoryLogged: true,\n        historyLogged: []\n      };\n    case USER_HISTORY_LOGED_SUCCESS:\n      return {\n        loadingHistoryLogged: false,\n        historyLogged: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_HISTORY_LOGED_FAIL:\n      return {\n        loadingHistoryLogged: false,\n        errorHistoryLogged: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateLastLoginUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_UPDATE_LOGIN_REQUEST:\n      return {\n        loadingUpdateLastLogin: true\n      };\n    case USER_UPDATE_LOGIN_SUCCESS:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: true\n      };\n    case USER_UPDATE_LOGIN_FAIL:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: false,\n        errorUpdateLastLogin: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_UPDATE_REQUEST:\n      return {\n        loadingCoordinatorUpdate: true\n      };\n    case COORDINATOR_UPDATE_SUCCESS:\n      toast.success(\"This Coordinator has been updated successfully.\");\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: true\n      };\n    case COORDINATOR_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: false,\n        errorCoordinatorUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailCoordinatorReducer = (state = {\n  coordinatorInfo: {}\n}, action) => {\n  switch (action.type) {\n    case COORDINATOR_DETAIL_REQUEST:\n      return {\n        loadingCoordinatorInfo: true\n      };\n    case COORDINATOR_DETAIL_SUCCESS:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: true,\n        coordinatorInfo: action.payload.coordinator\n      };\n    case COORDINATOR_DETAIL_FAIL:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: false,\n        errorCoordinatorInfo: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updatePasswordUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PASSWORD_UPDATE_REQUEST:\n      return {\n        loadingUserPasswordUpdate: true\n      };\n    case USER_PASSWORD_UPDATE_SUCCESS:\n      toast.success(\"Your password has been successfully updated\");\n      return {\n        loadingUserPasswordUpdate: false,\n        successUserPasswordUpdate: true\n      };\n    case USER_PASSWORD_UPDATE_FAIL:\n      return {\n        loadingUserPasswordUpdate: false,\n        errorUserPasswordUpdate: action.payload,\n        successUserPasswordUpdate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return {\n        loadingCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const coordinatorsListReducer = (state = {\n  coordinators: []\n}, action) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return {\n        loadingCoordinators: true,\n        coordinators: []\n      };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case COORDINATOR_LIST_FAIL:\n      return {\n        loadingCoordinators: false,\n        errorCoordinators: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return {\n        loadingUserDelete: true\n      };\n    case USER_DELETE_SUCCESS:\n      toast.success(\"This Coordinator has been successfully deleted.\");\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false\n      };\n    default:\n      return state;\n  }\n};\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return {\n        loadingUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      toast.success(\"Your profile has been successfully updated\");\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUserProfileUpdate: action.payload,\n        successUserProfileUpdate: false\n      };\n    default:\n      return state;\n  }\n};\nexport const getProfileUserReducer = (state = {\n  userProfile: []\n}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return {\n        loadingUserProfile: true\n      };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return {\n        loadingUserAdd: true\n      };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const usersListReducer = (state = {\n  users: []\n}, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return {\n        loadingUsers: true,\n        users: []\n      };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case USER_LIST_FAIL:\n      return {\n        loadingUsers: false,\n        errorUsers: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return {\n        loading: true\n      };\n    case USER_LOGIN_SUCCESS:\n      return {\n        loading: false,\n        userInfo: action.payload\n      };\n    case USER_LOGIN_FAIL:\n      return {\n        loading: false,\n        error: action.payload\n      };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};\nexport const logoutSavedUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGOUT_SAVE_REQUEST:\n      return {\n        loadingUserLogout: true\n      };\n    case USER_LOGOUT_SAVE_SUCCESS:\n      toast.success(\"You are has been logout successfully\");\n      return {\n        loadingUserLogout: false,\n        successUserLogout: true\n      };\n    case USER_LOGOUT_SAVE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserLogout: false,\n        successUserLogout: false,\n        errorUserLogout: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_SUCCESS", "USER_UPDATE_LOGIN_REQUEST", "USER_UPDATE_LOGIN_FAIL", "USER_HISTORY_LOGED_SUCCESS", "USER_HISTORY_LOGED_REQUEST", "USER_HISTORY_LOGED_FAIL", "USER_HISTORY_SUCCESS", "USER_HISTORY_REQUEST", "USER_HISTORY_FAIL", "USER_LOGOUT_SAVE_SUCCESS", "USER_LOGOUT_SAVE_REQUEST", "USER_LOGOUT_SAVE_FAIL", "USER_RESET_SEND_SUCCESS", "USER_RESET_SEND_REQUEST", "USER_RESET_SEND_FAIL", "resetPasswordReducer", "state", "action", "type", "loadingResetPassword", "success", "successResetPassword", "error", "payload", "errorResetPassword", "historyListCoordinatorReducer", "historyCoordinator", "loadingHistoryCoordinator", "historys", "pages", "page", "errorHistoryCoordinator", "historyListLoggedReducer", "historyLogged", "loadingHistoryLogged", "errorHistoryLogged", "updateLastLoginUserReducer", "loadingUpdateLastLogin", "successUpdateLastLogin", "errorUpdateLastLogin", "updateCoordinatorReducer", "loadingCoordinatorUpdate", "successCoordinatorUpdate", "errorCoordinatorUpdate", "detailCoordinatorReducer", "coordinatorInfo", "loadingCoordinatorInfo", "successCoordinatorInfo", "coordinator", "errorCoordinatorInfo", "updatePasswordUserReducer", "loadingUserPasswordUpdate", "successUserPasswordUpdate", "errorUserPasswordUpdate", "createCoordinatorReducer", "loadingCoordinatorAdd", "successCoordinatorAdd", "errorCoordinatorAdd", "coordinators<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinators", "loadingCoordinators", "users", "errorCoordinators", "deleteUserReducer", "loadingUserDelete", "successUserDelete", "errorUsersDelete", "updateProfileUserReducer", "loadingUserProfileUpdate", "successUserProfileUpdate", "errorUserProfileUpdate", "getProfileUserReducer", "userProfile", "loadingUserProfile", "profile", "successUserProfile", "errorUserProfile", "createNewUserReducer", "loadingUserAdd", "successUserAdd", "errorUserAdd", "usersListReducer", "loadingUsers", "errorUsers", "userLoginReducer", "loading", "userInfo", "logoutSavedUserReducer", "loadingUserLogout", "successUserLogout", "errorUserLogout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/userReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport {\n  USER_<PERSON>OGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_<PERSON>OGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_PASSWORD_UPDATE_SUCCESS,\n  USER_PASSWORD_UPDATE_REQUEST,\n  USER_PASSWORD_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATOR_ADD_FAIL,\n  //\n  COORDINATOR_DETAIL_SUCCESS,\n  COORDINATOR_DETAIL_REQUEST,\n  COORDINATOR_DETAIL_FAIL,\n  //\n  COORDINATOR_UPDATE_SUCCESS,\n  COORDINATOR_UPDATE_REQUEST,\n  COORDINATOR_UPDATE_FAIL,\n  //\n  USER_UPDATE_LOGIN_SUCCESS,\n  USER_UPDATE_LOGIN_REQUEST,\n  USER_UPDATE_LOGIN_FAIL,\n  //\n  USER_HISTORY_LOGED_SUCCESS,\n  USER_HISTORY_LOGED_REQUEST,\n  USER_HISTORY_LOGED_FAIL,\n  //\n  USER_HISTORY_SUCCESS,\n  USER_HISTORY_REQUEST,\n  USER_HISTORY_FAIL,\n  //\n  USER_LOGOUT_SAVE_SUCCESS,\n  USER_LOGOUT_SAVE_REQUEST,\n  USER_LOGOUT_SAVE_FAIL,\n  //\n  USER_RESET_SEND_SUCCESS,\n  USER_RESET_SEND_REQUEST,\n  USER_RESET_SEND_FAIL,\n  //\n} from \"../constants/userConstants\";\n\nexport const resetPasswordReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_RESET_SEND_REQUEST:\n      return { loadingResetPassword: true };\n    case USER_RESET_SEND_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingResetPassword: false,\n        successResetPassword: true,\n      };\n    case USER_RESET_SEND_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingResetPassword: false,\n        successResetPassword: false,\n        errorResetPassword: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const historyListCoordinatorReducer = (\n  state = { historyCoordinator: [] },\n  action\n) => {\n  switch (action.type) {\n    case USER_HISTORY_REQUEST:\n      return { loadingHistoryCoordinator: true, historyCoordinator: [] };\n    case USER_HISTORY_SUCCESS:\n      return {\n        loadingHistoryCoordinator: false,\n        historyCoordinator: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_HISTORY_FAIL:\n      return {\n        loadingHistoryCoordinator: false,\n        errorHistoryCoordinator: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const historyListLoggedReducer = (\n  state = { historyLogged: [] },\n  action\n) => {\n  switch (action.type) {\n    case USER_HISTORY_LOGED_REQUEST:\n      return { loadingHistoryLogged: true, historyLogged: [] };\n    case USER_HISTORY_LOGED_SUCCESS:\n      return {\n        loadingHistoryLogged: false,\n        historyLogged: action.payload.historys,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_HISTORY_LOGED_FAIL:\n      return {\n        loadingHistoryLogged: false,\n        errorHistoryLogged: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateLastLoginUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_UPDATE_LOGIN_REQUEST:\n      return { loadingUpdateLastLogin: true };\n    case USER_UPDATE_LOGIN_SUCCESS:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: true,\n      };\n    case USER_UPDATE_LOGIN_FAIL:\n      return {\n        loadingUpdateLastLogin: false,\n        successUpdateLastLogin: false,\n        errorUpdateLastLogin: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_UPDATE_REQUEST:\n      return { loadingCoordinatorUpdate: true };\n    case COORDINATOR_UPDATE_SUCCESS:\n      toast.success(\"This Coordinator has been updated successfully.\");\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: true,\n      };\n    case COORDINATOR_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorUpdate: false,\n        successCoordinatorUpdate: false,\n        errorCoordinatorUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailCoordinatorReducer = (\n  state = { coordinatorInfo: {} },\n  action\n) => {\n  switch (action.type) {\n    case COORDINATOR_DETAIL_REQUEST:\n      return { loadingCoordinatorInfo: true };\n    case COORDINATOR_DETAIL_SUCCESS:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: true,\n        coordinatorInfo: action.payload.coordinator,\n      };\n    case COORDINATOR_DETAIL_FAIL:\n      return {\n        loadingCoordinatorInfo: false,\n        successCoordinatorInfo: false,\n        errorCoordinatorInfo: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updatePasswordUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PASSWORD_UPDATE_REQUEST:\n      return { loadingUserPasswordUpdate: true };\n    case USER_PASSWORD_UPDATE_SUCCESS:\n      toast.success(\"Your password has been successfully updated\");\n      return {\n        loadingUserPasswordUpdate: false,\n        successUserPasswordUpdate: true,\n      };\n    case USER_PASSWORD_UPDATE_FAIL:\n      return {\n        loadingUserPasswordUpdate: false,\n        errorUserPasswordUpdate: action.payload,\n        successUserPasswordUpdate: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createCoordinatorReducer = (state = {}, action) => {\n  switch (action.type) {\n    case COORDINATOR_ADD_REQUEST:\n      return { loadingCoordinatorAdd: true };\n    case COORDINATOR_ADD_SUCCESS:\n      toast.success(\"This Coordinator has been added successfully\");\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: true,\n      };\n    case COORDINATOR_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingCoordinatorAdd: false,\n        successCoordinatorAdd: false,\n        errorCoordinatorAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const coordinatorsListReducer = (\n  state = { coordinators: [] },\n  action\n) => {\n  switch (action.type) {\n    case COORDINATOR_LIST_REQUEST:\n      return { loadingCoordinators: true, coordinators: [] };\n    case COORDINATOR_LIST_SUCCESS:\n      return {\n        loadingCoordinators: false,\n        coordinators: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case COORDINATOR_LIST_FAIL:\n      return { loadingCoordinators: false, errorCoordinators: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const deleteUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_DELETE_REQUEST:\n      return { loadingUserDelete: true };\n    case USER_DELETE_SUCCESS:\n      toast.success(\"This Coordinator has been successfully deleted.\");\n      return {\n        loadingUserDelete: false,\n        successUserDelete: true,\n      };\n    case USER_DELETE_FAIL:\n      return {\n        loadingUserDelete: false,\n        errorUsersDelete: action.payload,\n        successUserDelete: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateProfileUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_PROFILE_UPDATE_REQUEST:\n      return { loadingUserProfileUpdate: true };\n    case USER_PROFILE_UPDATE_SUCCESS:\n      toast.success(\"Your profile has been successfully updated\");\n      return {\n        loadingUserProfileUpdate: false,\n        successUserProfileUpdate: true,\n      };\n    case USER_PROFILE_UPDATE_FAIL:\n      return {\n        loadingUserProfileUpdate: false,\n        errorUserProfileUpdate: action.payload,\n        successUserProfileUpdate: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const getProfileUserReducer = (state = { userProfile: [] }, action) => {\n  switch (action.type) {\n    case USER_PROFILE_REQUEST:\n      return { loadingUserProfile: true };\n    case USER_PROFILE_SUCCESS:\n      return {\n        loadingUserProfile: false,\n        userProfile: action.payload.profile,\n        successUserProfile: true,\n      };\n    case USER_PROFILE_FAIL:\n      return {\n        loadingUserProfile: false,\n        errorUserProfile: action.payload,\n        successUserProfile: false,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_ADD_REQUEST:\n      return { loadingUserAdd: true };\n    case USER_ADD_SUCCESS:\n      toast.success(\"This user has been added successfully\");\n      return {\n        loadingUserAdd: false,\n        successUserAdd: true,\n      };\n    case USER_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserAdd: false,\n        successUserAdd: false,\n        errorUserAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const usersListReducer = (state = { users: [] }, action) => {\n  switch (action.type) {\n    case USER_LIST_REQUEST:\n      return { loadingUsers: true, users: [] };\n    case USER_LIST_SUCCESS:\n      return {\n        loadingUsers: false,\n        users: action.payload.users,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case USER_LIST_FAIL:\n      return { loadingUsers: false, errorUsers: action.payload };\n    default:\n      return state;\n  }\n};\n\nexport const userLoginReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGIN_REQUEST:\n      return { loading: true };\n    case USER_LOGIN_SUCCESS:\n      return { loading: false, userInfo: action.payload };\n    case USER_LOGIN_FAIL:\n      return { loading: false, error: action.payload };\n    case USER_LOGOUT:\n      return {};\n    default:\n      return state;\n  }\n};\n\nexport const logoutSavedUserReducer = (state = {}, action) => {\n  switch (action.type) {\n    case USER_LOGOUT_SAVE_REQUEST:\n      return { loadingUserLogout: true };\n    case USER_LOGOUT_SAVE_SUCCESS:\n      toast.success(\"You are has been logout successfully\");\n      return {\n        loadingUserLogout: false,\n        successUserLogout: true,\n      };\n    case USER_LOGOUT_SAVE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingUserLogout: false,\n        successUserLogout: false,\n        errorUserLogout: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC;AACA;AAAA,OACK,4BAA4B;AAEnC,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKL,uBAAuB;MAC1B,OAAO;QAAEM,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKP,uBAAuB;MAC1B/C,KAAK,CAACuD,OAAO,CAAC,8CAA8C,CAAC;MAC7D,OAAO;QACLD,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE;MACxB,CAAC;IACH,KAAKP,oBAAoB;MACvBjD,KAAK,CAACyD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE,KAAK;QAC3BG,kBAAkB,EAAEP,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,6BAA6B,GAAGA,CAC3CT,KAAK,GAAG;EAAEU,kBAAkB,EAAE;AAAG,CAAC,EAClCT,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKX,oBAAoB;MACvB,OAAO;QAAEoB,yBAAyB,EAAE,IAAI;QAAED,kBAAkB,EAAE;MAAG,CAAC;IACpE,KAAKpB,oBAAoB;MACvB,OAAO;QACLqB,yBAAyB,EAAE,KAAK;QAChCD,kBAAkB,EAAET,MAAM,CAACM,OAAO,CAACK,QAAQ;QAC3CC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKtB,iBAAiB;MACpB,OAAO;QACLmB,yBAAyB,EAAE,KAAK;QAChCI,uBAAuB,EAAEd,MAAM,CAACM;MAClC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgB,wBAAwB,GAAGA,CACtChB,KAAK,GAAG;EAAEiB,aAAa,EAAE;AAAG,CAAC,EAC7BhB,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKd,0BAA0B;MAC7B,OAAO;QAAE8B,oBAAoB,EAAE,IAAI;QAAED,aAAa,EAAE;MAAG,CAAC;IAC1D,KAAK9B,0BAA0B;MAC7B,OAAO;QACL+B,oBAAoB,EAAE,KAAK;QAC3BD,aAAa,EAAEhB,MAAM,CAACM,OAAO,CAACK,QAAQ;QACtCC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKzB,uBAAuB;MAC1B,OAAO;QACL6B,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAElB,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoB,0BAA0B,GAAGA,CAACpB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAChE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKjB,yBAAyB;MAC5B,OAAO;QAAEoC,sBAAsB,EAAE;MAAK,CAAC;IACzC,KAAKrC,yBAAyB;MAC5B,OAAO;QACLqC,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE;MAC1B,CAAC;IACH,KAAKpC,sBAAsB;MACzB,OAAO;QACLmC,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,KAAK;QAC7BC,oBAAoB,EAAEtB,MAAM,CAACM;MAC/B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMwB,wBAAwB,GAAGA,CAACxB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKpB,0BAA0B;MAC7B,OAAO;QAAE2C,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAK5C,0BAA0B;MAC7BhC,KAAK,CAACuD,OAAO,CAAC,iDAAiD,CAAC;MAChE,OAAO;QACLqB,wBAAwB,EAAE,KAAK;QAC/BC,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAK3C,uBAAuB;MAC1BlC,KAAK,CAACyD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLkB,wBAAwB,EAAE,KAAK;QAC/BC,wBAAwB,EAAE,KAAK;QAC/BC,sBAAsB,EAAE1B,MAAM,CAACM;MACjC,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM4B,wBAAwB,GAAGA,CACtC5B,KAAK,GAAG;EAAE6B,eAAe,EAAE,CAAC;AAAE,CAAC,EAC/B5B,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKvB,0BAA0B;MAC7B,OAAO;QAAEmD,sBAAsB,EAAE;MAAK,CAAC;IACzC,KAAKpD,0BAA0B;MAC7B,OAAO;QACLoD,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,IAAI;QAC5BF,eAAe,EAAE5B,MAAM,CAACM,OAAO,CAACyB;MAClC,CAAC;IACH,KAAKpD,uBAAuB;MAC1B,OAAO;QACLkD,sBAAsB,EAAE,KAAK;QAC7BC,sBAAsB,EAAE,KAAK;QAC7BE,oBAAoB,EAAEhC,MAAM,CAACM;MAC/B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMkC,yBAAyB,GAAGA,CAAClC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC/D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKnC,4BAA4B;MAC/B,OAAO;QAAEoE,yBAAyB,EAAE;MAAK,CAAC;IAC5C,KAAKrE,4BAA4B;MAC/BjB,KAAK,CAACuD,OAAO,CAAC,6CAA6C,CAAC;MAC5D,OAAO;QACL+B,yBAAyB,EAAE,KAAK;QAChCC,yBAAyB,EAAE;MAC7B,CAAC;IACH,KAAKpE,yBAAyB;MAC5B,OAAO;QACLmE,yBAAyB,EAAE,KAAK;QAChCE,uBAAuB,EAAEpC,MAAM,CAACM,OAAO;QACvC6B,yBAAyB,EAAE;MAC7B,CAAC;IACH;MACE,OAAOpC,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMsC,wBAAwB,GAAGA,CAACtC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK1B,uBAAuB;MAC1B,OAAO;QAAE+D,qBAAqB,EAAE;MAAK,CAAC;IACxC,KAAKhE,uBAAuB;MAC1B1B,KAAK,CAACuD,OAAO,CAAC,8CAA8C,CAAC;MAC7D,OAAO;QACLmC,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE;MACzB,CAAC;IACH,KAAK/D,oBAAoB;MACvB5B,KAAK,CAACyD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLgC,qBAAqB,EAAE,KAAK;QAC5BC,qBAAqB,EAAE,KAAK;QAC5BC,mBAAmB,EAAExC,MAAM,CAACM;MAC9B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM0C,uBAAuB,GAAGA,CACrC1C,KAAK,GAAG;EAAE2C,YAAY,EAAE;AAAG,CAAC,EAC5B1C,MAAM,KACH;EACH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK7B,wBAAwB;MAC3B,OAAO;QAAEuE,mBAAmB,EAAE,IAAI;QAAED,YAAY,EAAE;MAAG,CAAC;IACxD,KAAKvE,wBAAwB;MAC3B,OAAO;QACLwE,mBAAmB,EAAE,KAAK;QAC1BD,YAAY,EAAE1C,MAAM,CAACM,OAAO,CAACsC,KAAK;QAClChC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKxC,qBAAqB;MACxB,OAAO;QAAEsE,mBAAmB,EAAE,KAAK;QAAEE,iBAAiB,EAAE7C,MAAM,CAACM;MAAQ,CAAC;IAC1E;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM+C,iBAAiB,GAAGA,CAAC/C,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACvD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKhC,mBAAmB;MACtB,OAAO;QAAE8E,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK/E,mBAAmB;MACtBpB,KAAK,CAACuD,OAAO,CAAC,iDAAiD,CAAC;MAChE,OAAO;QACL4C,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK9E,gBAAgB;MACnB,OAAO;QACL6E,iBAAiB,EAAE,KAAK;QACxBE,gBAAgB,EAAEjD,MAAM,CAACM,OAAO;QAChC0C,iBAAiB,EAAE;MACrB,CAAC;IACH;MACE,OAAOjD,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMmD,wBAAwB,GAAGA,CAACnD,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC9D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKtC,2BAA2B;MAC9B,OAAO;QAAEwF,wBAAwB,EAAE;MAAK,CAAC;IAC3C,KAAKzF,2BAA2B;MAC9Bd,KAAK,CAACuD,OAAO,CAAC,4CAA4C,CAAC;MAC3D,OAAO;QACLgD,wBAAwB,EAAE,KAAK;QAC/BC,wBAAwB,EAAE;MAC5B,CAAC;IACH,KAAKxF,wBAAwB;MAC3B,OAAO;QACLuF,wBAAwB,EAAE,KAAK;QAC/BE,sBAAsB,EAAErD,MAAM,CAACM,OAAO;QACtC8C,wBAAwB,EAAE;MAC5B,CAAC;IACH;MACE,OAAOrD,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMuD,qBAAqB,GAAGA,CAACvD,KAAK,GAAG;EAAEwD,WAAW,EAAE;AAAG,CAAC,EAAEvD,MAAM,KAAK;EAC5E,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKzC,oBAAoB;MACvB,OAAO;QAAEgG,kBAAkB,EAAE;MAAK,CAAC;IACrC,KAAKjG,oBAAoB;MACvB,OAAO;QACLiG,kBAAkB,EAAE,KAAK;QACzBD,WAAW,EAAEvD,MAAM,CAACM,OAAO,CAACmD,OAAO;QACnCC,kBAAkB,EAAE;MACtB,CAAC;IACH,KAAKjG,iBAAiB;MACpB,OAAO;QACL+F,kBAAkB,EAAE,KAAK;QACzBG,gBAAgB,EAAE3D,MAAM,CAACM,OAAO;QAChCoD,kBAAkB,EAAE;MACtB,CAAC;IACH;MACE,OAAO3D,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAM6D,oBAAoB,GAAGA,CAAC7D,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK/C,gBAAgB;MACnB,OAAO;QAAE2G,cAAc,EAAE;MAAK,CAAC;IACjC,KAAK5G,gBAAgB;MACnBL,KAAK,CAACuD,OAAO,CAAC,uCAAuC,CAAC;MACtD,OAAO;QACL0D,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE;MAClB,CAAC;IACH,KAAK3G,aAAa;MAChBP,KAAK,CAACyD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLuD,cAAc,EAAE,KAAK;QACrBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE/D,MAAM,CAACM;MACvB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMiE,gBAAgB,GAAGA,CAACjE,KAAK,GAAG;EAAE6C,KAAK,EAAE;AAAG,CAAC,EAAE5C,MAAM,KAAK;EACjE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK5C,iBAAiB;MACpB,OAAO;QAAE4G,YAAY,EAAE,IAAI;QAAErB,KAAK,EAAE;MAAG,CAAC;IAC1C,KAAKxF,iBAAiB;MACpB,OAAO;QACL6G,YAAY,EAAE,KAAK;QACnBrB,KAAK,EAAE5C,MAAM,CAACM,OAAO,CAACsC,KAAK;QAC3BhC,KAAK,EAAEZ,MAAM,CAACM,OAAO,CAACM,KAAK;QAC3BC,IAAI,EAAEb,MAAM,CAACM,OAAO,CAACO;MACvB,CAAC;IACH,KAAKvD,cAAc;MACjB,OAAO;QAAE2G,YAAY,EAAE,KAAK;QAAEC,UAAU,EAAElE,MAAM,CAACM;MAAQ,CAAC;IAC5D;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoE,gBAAgB,GAAGA,CAACpE,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EACtD,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKpD,kBAAkB;MACrB,OAAO;QAAEuH,OAAO,EAAE;MAAK,CAAC;IAC1B,KAAKtH,kBAAkB;MACrB,OAAO;QAAEsH,OAAO,EAAE,KAAK;QAAEC,QAAQ,EAAErE,MAAM,CAACM;MAAQ,CAAC;IACrD,KAAKvD,eAAe;MAClB,OAAO;QAAEqH,OAAO,EAAE,KAAK;QAAE/D,KAAK,EAAEL,MAAM,CAACM;MAAQ,CAAC;IAClD,KAAKtD,WAAW;MACd,OAAO,CAAC,CAAC;IACX;MACE,OAAO+C,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMuE,sBAAsB,GAAGA,CAACvE,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC5D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKR,wBAAwB;MAC3B,OAAO;QAAE8E,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK/E,wBAAwB;MAC3B5C,KAAK,CAACuD,OAAO,CAAC,sCAAsC,CAAC;MACrD,OAAO;QACLoE,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK9E,qBAAqB;MACxB9C,KAAK,CAACyD,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLiE,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAEzE,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}