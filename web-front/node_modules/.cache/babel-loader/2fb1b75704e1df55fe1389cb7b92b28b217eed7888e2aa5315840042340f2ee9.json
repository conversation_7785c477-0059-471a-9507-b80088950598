{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams, useSearchParams } from \"react-router-dom\";\nimport { addNewCommentCase, deleteCommentCase, detailCase, duplicateCase, getCaseHistory, getListCommentCase, updateAssignedCase, updateCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$assurance_n, _caseInfo$created_use, _caseInfo$created_use2, _caseInfo$assurance$a, _caseInfo$assurance, _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$pat, _caseInfo$patient2, _caseInfo$patient3, _caseInfo$case_status, _caseInfo$patient$ful2, _caseInfo$patient4, _caseInfo$patient$bir, _caseInfo$patient5, _caseInfo$patient$pat2, _caseInfo$patient6, _caseInfo$patient$pat3, _caseInfo$patient7, _caseInfo$patient$pat4, _caseInfo$patient8, _caseInfo$patient$pat5, _caseInfo$patient9, _caseInfo$patient$pat6, _caseInfo$patient10, _caseInfo$case_type, _caseInfo$currency_pr, _caseInfo$coordinator3, _caseInfo$case_descri, _caseInfo$assistance_, _caseInfo$medical_rep, _caseInfo$medical_rep2, _caseInfo$invoice_num, _caseInfo$upload_invo, _caseInfo$upload_invo2, _caseInfo$assurance_s, _caseInfo$assurance$a2, _caseInfo$assurance2, _caseInfo$assurance_n2, _caseInfo$policy_numb, _caseInfo$upload_auth, _caseInfo$upload_auth2;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n  const [isDuplicate, setIsDuplicate] = useState(false);\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // Edit Status Modal\n  const [showEditStatusModal, setShowEditStatusModal] = useState(false);\n  const [selectedStatuses, setSelectedStatuses] = useState([]);\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const {\n    getRootProps: getRootComments,\n    getInputProps: getInputComments\n  } = useDropzone({\n    accept: {\n      \"image/*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesComments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesComments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCommentCase = useSelector(state => state.commentCaseList);\n  const {\n    comments,\n    loadingCommentCase,\n    errorCommentCase,\n    pages\n  } = listCommentCase;\n  const commentCaseDelete = useSelector(state => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete\n  } = commentCaseDelete;\n  const createCommentCase = useSelector(state => state.createNewCommentCase);\n  const {\n    loadingCommentCaseAdd,\n    successCommentCaseAdd,\n    errorCommentCaseAdd\n  } = createCommentCase;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseAssignedUpdate = useSelector(state => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate\n  } = caseAssignedUpdate;\n  const caseDuplicat = useSelector(state => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate\n  } = caseDuplicat;\n  const caseHistoryState = useSelector(state => state.caseHistory);\n  const {\n    loadingHistory,\n    errorHistory,\n    history,\n    page: historyCurrentPage,\n    pages: historyTotalPages\n  } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases-list/edit/\" + caseDuplicate);\n      dispatch({\n        type: \"RESET_DUPLICATE_CASE\"\n      });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = tabName => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n  const caseStatus = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n  const caseStatusColor = casestatus => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n  const getIconCountry = country => {\n    const foundCountry = COUNTRIES.find(option => option.title === country);\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = code => {\n    const patientCurrency = code !== null && code !== void 0 ? code : \"\";\n    const foundCurrency = CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.find(option => option.code === patientCurrency);\n    if (foundCurrency) {\n      var _foundCurrency$symbol;\n      return (_foundCurrency$symbol = foundCurrency.symbol) !== null && _foundCurrency$symbol !== void 0 ? _foundCurrency$symbol : code;\n    } else {\n      return code;\n    }\n  };\n  const getSectionIndex = selectItem => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-3 bg-white shadow-sm px-4 py-4 rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap items-center justify-between mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#344054] text-xs font-medium\",\n                    children: \"CIA REF\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#0388A6] text-sm font-medium\",\n                    children: (_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 401,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#667085]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 409,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#344054] text-xs font-medium\",\n                    children: \"Created By\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: (_caseInfo$created_use = (_caseInfo$created_use2 = caseInfo.created_user) === null || _caseInfo$created_use2 === void 0 ? void 0 : _caseInfo$created_use2.full_name) !== null && _caseInfo$created_use !== void 0 ? _caseInfo$created_use : \"---\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var _caseInfo$coordinator, _caseInfo$coordinator2;\n                setSelectCoordinator((_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\");\n                setSelectCoordinatorError(\"\");\n                setOpenDiag(true);\n                setIsLoading(false);\n              },\n              className: \"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md transition-colors duration-300 text-xs mt-2 sm:mt-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4 mr-1\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-medium\",\n                children: \"Assign Coordinator\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-3 gap-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 453,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 21\n                }, this), caseInfo.is_pay ? /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\",\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\",\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 472,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"CIA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#667085] text-sm truncate max-w-[120px]\",\n                  children: (_caseInfo$assurance$a = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.assurance_name) !== null && _caseInfo$assurance$a !== void 0 ? _caseInfo$assurance$a : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 487,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Patient\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#667085] text-sm truncate max-w-[120px]\",\n                  children: (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium\",\n                  children: \"Country\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center\",\n                  children: [getIconCountry((_caseInfo$patient$pat = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.patient_country) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\"), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-[#667085] text-sm ml-1 truncate max-w-[100px]\",\n                    children: caseStatus((_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.patient_country)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 506,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start col-span-2 sm:col-span-3 md:col-span-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] p-1.5 rounded-md mr-2 mt-0.5 flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 517,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-[#344054] text-xs font-medium mb-1\",\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 521,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-wrap gap-1\",\n                  children: (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map((stat, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`,\n                    children: caseStatus(stat.status_coordination)\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 524,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center\",\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\",\n              href: \"/cases-list/edit/\" + caseInfo.id + \"?section=\" + getSectionIndex(selectPage),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 554,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 546,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mx-1\",\n                children: \"Edit Case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6 mx-2 border-b border-[#F1F5F9]\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap -mb-px\",\n              children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\", \"History\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleTabChange(select),\n                className: `inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${selectPage === select ? \"border-[#0388A6] text-[#0388A6]\" : \"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"}`,\n                children: select\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 15\n          }, this), selectPage === \"General Information\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col md:flex-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 646,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 645,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Patient Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Full Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 655,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] font-medium\",\n                      children: (_caseInfo$patient$ful2 = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 654,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Date of Birth\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 660,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$bir = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 661,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 659,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Phone\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$pat2 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_phone) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 670,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: (_caseInfo$patient$pat3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.patient_email) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 671,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 675,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] flex items-center\",\n                      children: [getIconCountry((_caseInfo$patient$pat4 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\"), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-1\",\n                        children: [(_caseInfo$patient$pat5 = (_caseInfo$patient9 = caseInfo.patient) === null || _caseInfo$patient9 === void 0 ? void 0 : _caseInfo$patient9.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"---\", \", \", (_caseInfo$patient$pat6 = (_caseInfo$patient10 = caseInfo.patient) === null || _caseInfo$patient10 === void 0 ? void 0 : _caseInfo$patient10.patient_country) !== null && _caseInfo$patient$pat6 !== void 0 ? _caseInfo$patient$pat6 : \"---\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 678,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 676,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 690,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 689,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Case Details\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 693,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 687,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5 space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Case Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 699,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: (_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 701,\n                        columnNumber: 29\n                      }, this), caseInfo.case_type === \"Medical\" && caseInfo.case_type_item && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"ml-1 text-[#667085]\",\n                        children: [\"| \", caseInfo.case_type_item]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 698,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Price of Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 709,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] font-medium\",\n                      children: parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 710,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Price (EUR)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 716,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 717,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Creation Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054]\",\n                      children: formatDate(caseInfo.case_date)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 724,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 722,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Assigned Coordinator\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 728,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] flex items-center\",\n                      children: (_caseInfo$coordinator3 = caseInfo.coordinator_user) !== null && _caseInfo$coordinator3 !== void 0 && _caseInfo$coordinator3.full_name ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\",\n                          children: /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-[#0388A6] font-medium\",\n                            children: caseInfo.coordinator_user.full_name.charAt(0)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 733,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 732,\n                          columnNumber: 33\n                        }, this), caseInfo.coordinator_user.full_name]\n                      }, void 0, true) : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 729,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 727,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-[#667085] mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-[#344054] whitespace-pre-wrap\",\n                      children: (_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 747,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 745,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 640,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Coordination Details\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"m4.5 12.75 6 6 9-13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 764,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 763,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Coordination Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 767,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-5\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 776,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 775,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 774,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Current Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 780,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: caseInfo.status_coordination ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`,\n                          children: caseStatus(caseInfo.status_coordination)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 783,\n                          columnNumber: 33\n                        }, this) : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 781,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 779,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 773,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 796,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 794,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Last Updated\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 800,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: formatDate(caseInfo.updated_at)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 801,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 799,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 793,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 771,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 814,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 813,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 812,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: \"Assistances Information\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 817,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 811,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-5\",\n                  children: ((_caseInfo$assistance_ = caseInfo.assistance_services) === null || _caseInfo$assistance_ === void 0 ? void 0 : _caseInfo$assistance_.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-6\",\n                    children: caseInfo.assistance_services.map((itemAssistance, index) => {\n                      var _caseInfo$assistance_2, _itemAssistance$creat, _itemAssistance$creat2, _itemAssistance$provi;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center mb-2 sm:mb-0\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-4 h-4 text-[#0388A6]\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 832,\n                                    columnNumber: 41\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 831,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 830,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                                className: \"font-medium text-[#303030]\",\n                                children: [\"Appointment #\", ((_caseInfo$assistance_2 = caseInfo.assistance_services) === null || _caseInfo$assistance_2 === void 0 ? void 0 : _caseInfo$assistance_2.length) - index]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 835,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 829,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex flex-col sm:flex-row sm:items-center\",\n                              children: [itemAssistance.created_user && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-[#F9FAFB] p-1 rounded-full mr-1.5\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-3 h-3 text-[#667085]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 842,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 841,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 840,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: [\"Created by \", ((_itemAssistance$creat = itemAssistance.created_user) === null || _itemAssistance$creat === void 0 ? void 0 : _itemAssistance$creat.full_name) || ((_itemAssistance$creat2 = itemAssistance.created_user) === null || _itemAssistance$creat2 === void 0 ? void 0 : _itemAssistance$creat2.email) || \"User\", itemAssistance.created_at && /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-[#667085] ml-1\",\n                                    children: [\"on \", formatDate(itemAssistance.created_at)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 848,\n                                    columnNumber: 45\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 845,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 839,\n                                columnNumber: 39\n                              }, this), itemAssistance.appointment_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\",\n                                children: formatDate(itemAssistance.appointment_date)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 856,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 837,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 828,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 827,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"px-5 py-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"mb-6\",\n                            children: /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                              children: [itemAssistance.start_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 873,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 872,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 871,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Hospital Starting Date\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 877,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: formatDate(itemAssistance.start_date)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 878,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 876,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 870,\n                                columnNumber: 39\n                              }, this), itemAssistance.end_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 887,\n                                      columnNumber: 45\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 886,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 885,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Hospital Ending Date\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 891,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: formatDate(itemAssistance.end_date)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 892,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 890,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 884,\n                                columnNumber: 39\n                              }, this), itemAssistance.service_location && /*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    strokeWidth: \"1.5\",\n                                    stroke: \"currentColor\",\n                                    className: \"w-4 h-4 text-[#0388A6]\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 901,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                                      strokeLinecap: \"round\",\n                                      strokeLinejoin: \"round\",\n                                      d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 902,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 900,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 899,\n                                  columnNumber: 41\n                                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-xs text-[#667085] block\",\n                                    children: \"Service Location\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 906,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                    className: \"text-sm font-medium text-[#344054]\",\n                                    children: itemAssistance.service_location\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 907,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 905,\n                                  columnNumber: 41\n                                }, this)]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 898,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 868,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 867,\n                            columnNumber: 33\n                          }, this), ((_itemAssistance$provi = itemAssistance.provider_services) === null || _itemAssistance$provi === void 0 ? void 0 : _itemAssistance$provi.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"flex items-center mb-4\",\n                              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                  xmlns: \"http://www.w3.org/2000/svg\",\n                                  fill: \"none\",\n                                  viewBox: \"0 0 24 24\",\n                                  strokeWidth: \"1.5\",\n                                  stroke: \"currentColor\",\n                                  className: \"w-4 h-4 text-[#0388A6]\",\n                                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 920,\n                                    columnNumber: 43\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 919,\n                                  columnNumber: 41\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 918,\n                                columnNumber: 39\n                              }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                                className: \"text-sm font-medium text-[#303030]\",\n                                children: \"Providers\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 923,\n                                columnNumber: 39\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 917,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"space-y-4\",\n                              children: itemAssistance.provider_services.map((providerService, idx) => {\n                                var _providerService$prov, _providerService$prov2;\n                                return /*#__PURE__*/_jsxDEV(\"div\", {\n                                  className: \"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\",\n                                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"flex items-center mb-3\",\n                                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\",\n                                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#0388A6]\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 932,\n                                          columnNumber: 49\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 931,\n                                        columnNumber: 47\n                                      }, this)\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 930,\n                                      columnNumber: 45\n                                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"text-sm font-medium text-[#344054]\",\n                                        children: ((_providerService$prov = providerService.provider) === null || _providerService$prov === void 0 ? void 0 : _providerService$prov.full_name) || \"---\"\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 936,\n                                        columnNumber: 47\n                                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                        className: \"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\",\n                                        children: providerService.service_type || \"---\"\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 937,\n                                        columnNumber: 47\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 935,\n                                      columnNumber: 45\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 929,\n                                    columnNumber: 43\n                                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\",\n                                    children: [providerService.service_specialist && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 945,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 944,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Speciality\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 948,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: providerService.service_specialist\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 949,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 947,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 943,\n                                      columnNumber: 47\n                                    }, this), providerService.provider_date && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 957,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 956,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Visit Date\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 960,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: formatDate(providerService.provider_date)\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 961,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 959,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 955,\n                                      columnNumber: 47\n                                    }, this), ((_providerService$prov2 = providerService.provider) === null || _providerService$prov2 === void 0 ? void 0 : _providerService$prov2.phone) && /*#__PURE__*/_jsxDEV(\"div\", {\n                                      className: \"flex items-center\",\n                                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        strokeWidth: \"1.5\",\n                                        stroke: \"currentColor\",\n                                        className: \"w-4 h-4 text-[#667085] mr-2\",\n                                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                          strokeLinecap: \"round\",\n                                          strokeLinejoin: \"round\",\n                                          d: \"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 969,\n                                          columnNumber: 51\n                                        }, this)\n                                      }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 968,\n                                        columnNumber: 49\n                                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-xs text-[#667085] block\",\n                                          children: \"Contact\"\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 972,\n                                          columnNumber: 51\n                                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                          className: \"text-sm text-[#344054]\",\n                                          children: providerService.provider.phone\n                                        }, void 0, false, {\n                                          fileName: _jsxFileName,\n                                          lineNumber: 973,\n                                          columnNumber: 51\n                                        }, this)]\n                                      }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 971,\n                                        columnNumber: 49\n                                      }, this)]\n                                    }, void 0, true, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 967,\n                                      columnNumber: 47\n                                    }, this)]\n                                  }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 941,\n                                    columnNumber: 43\n                                  }, this)]\n                                }, idx, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 928,\n                                  columnNumber: 41\n                                }, this);\n                              })\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 926,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 916,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 865,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 825,\n                        columnNumber: 29\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 823,\n                    columnNumber: 25\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl p-8 text-center shadow-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-8 h-8 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 991,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 990,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-[#303030] font-medium mb-2\",\n                      children: \"No Assistances Informations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 994,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-sm\",\n                      children: \"No assistances details have been added to this case yet.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 820,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 809,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 808,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 758,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Medical Reports\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Medical Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1006,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: ((_caseInfo$medical_rep = caseInfo.medical_reports) === null || _caseInfo$medical_rep === void 0 ? void 0 : _caseInfo$medical_rep.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: (_caseInfo$medical_rep2 = caseInfo.medical_reports) === null || _caseInfo$medical_rep2 === void 0 ? void 0 : _caseInfo$medical_rep2.map((item, index) => /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: baseURLFile + item.file,\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        viewBox: \"0 0 24 24\",\n                        fill: \"currentColor\",\n                        className: \"w-5 h-5 text-[#0388A6]\",\n                        children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1036,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                          d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1037,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1029,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"overflow-hidden\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                        children: item.file_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1041,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-xs text-[#667085]\",\n                        children: [item.file_size, \" mb\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1044,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1028,\n                    columnNumber: 29\n                  }, this)\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 27\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1019,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center py-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-8 h-8 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1054,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1053,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1052,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-[#344054] font-medium mb-2\",\n                  children: \"No Medical Reports\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#667085] text-sm\",\n                  children: \"No medical reports have been uploaded for this case yet.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1058,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1051,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1017,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1005,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Invoices\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1071,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Invoice Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1074,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1068,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1067,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1084,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1083,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1082,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Invoice Number\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1088,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: (_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1089,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1081,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1096,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1095,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1094,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Date Issued\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1100,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: formatDate(caseInfo.date_issued) || \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1101,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1093,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1108,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1107,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1106,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Amount\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1112,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: [\"$ \", parseFloat(caseInfo.invoice_amount || 0).toFixed(2)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1113,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1111,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1105,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1122,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Due Date\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1126,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1127,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        strokeWidth: \"1.5\",\n                        stroke: \"currentColor\",\n                        className: \"w-4 h-4 text-[#0388A6]\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          strokeLinecap: \"round\",\n                          strokeLinejoin: \"round\",\n                          d: \"m4.5 12.75 6 6 9-13.5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1134,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1133,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1132,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-xs text-[#667085] block\",\n                        children: \"Invoice Status\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1138,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-medium text-[#344054]\",\n                        children: \"---\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1139,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1137,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1131,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1118,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1150,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1149,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1148,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Uploaded Documents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1147,\n                  columnNumber: 23\n                }, this), ((_caseInfo$upload_invo = caseInfo.upload_invoices) === null || _caseInfo$upload_invo === void 0 ? void 0 : _caseInfo$upload_invo.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                  children: (_caseInfo$upload_invo2 = caseInfo.upload_invoices) === null || _caseInfo$upload_invo2 === void 0 ? void 0 : _caseInfo$upload_invo2.map((item, idx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: baseURLFile + item.file,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          className: \"w-5 h-5 text-[#0388A6]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1174,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1175,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1168,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1167,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"overflow-hidden\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                          children: item.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1179,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-[#667085]\",\n                          children: [item.file_size, \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1182,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1178,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 31\n                    }, this)\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1159,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1157,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-6 bg-[#F9FAFB] rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-6 h-6 text-[#667085]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1192,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1191,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1190,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: \"No invoice documents have been uploaded yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1195,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1189,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1146,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1078,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1066,\n            columnNumber: 17\n          }, this) : null, selectPage === \"Insurance Authorization\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1209,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1208,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1207,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-[#344054]\",\n                  children: \"Insurance Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1212,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1206,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-5\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"m4.5 12.75 6 6 9-13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1221,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1220,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1219,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Authorization Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1225,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1226,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1224,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1233,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1232,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Insurance Company\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1237,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance$a2 = (_caseInfo$assurance2 = caseInfo.assurance) === null || _caseInfo$assurance2 === void 0 ? void 0 : _caseInfo$assurance2.assurance_name) !== null && _caseInfo$assurance$a2 !== void 0 ? _caseInfo$assurance$a2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1238,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1236,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1230,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1245,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1244,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1243,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1249,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$assurance_n2 = caseInfo.assurance_number) !== null && _caseInfo$assurance_n2 !== void 0 ? _caseInfo$assurance_n2 : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1250,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1248,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1242,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center bg-[#F9FAFB] rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-full p-2 shadow-sm mr-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1257,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1256,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1255,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-[#667085] block\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1261,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm font-medium text-[#344054]\",\n                      children: (_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1262,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1260,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1254,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1217,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-4 h-4 text-[#0388A6]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1272,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1271,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1270,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-sm font-medium text-[#344054]\",\n                    children: \"Uploaded Documents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1275,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1269,\n                  columnNumber: 23\n                }, this), ((_caseInfo$upload_auth = caseInfo.upload_authorization) === null || _caseInfo$upload_auth === void 0 ? void 0 : _caseInfo$upload_auth.length) > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                  children: (_caseInfo$upload_auth2 = caseInfo.upload_authorization) === null || _caseInfo$upload_auth2 === void 0 ? void 0 : _caseInfo$upload_auth2.map((item, idx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                    href: baseURLFile + item.file,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"block transition-transform duration-200 hover:scale-[1.02]\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          className: \"w-5 h-5 text-[#0388A6]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1296,\n                            columnNumber: 37\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1297,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1290,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1289,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"overflow-hidden\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\",\n                          children: item.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1301,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"text-xs text-[#667085]\",\n                          children: [item.file_size, \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1304,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1300,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1288,\n                      columnNumber: 31\n                    }, this)\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1281,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1279,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-center py-6 bg-[#F9FAFB] rounded-lg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      strokeWidth: \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"w-6 h-6 text-[#667085]\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1314,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1313,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1312,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-[#667085] text-sm\",\n                    children: \"No authorization documents have been uploaded yet\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1317,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1311,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1216,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1204,\n            columnNumber: 17\n          }, this) : null, selectPage === \"History\" ? /*#__PURE__*/_jsxDEV(CaseHistory, {\n            historyData: {\n              history: history,\n              page: historyCurrentPage,\n              pages: historyTotalPages,\n              count: (history === null || history === void 0 ? void 0 : history.length) || 0\n            },\n            loading: loadingHistory,\n            error: errorHistory\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1327,\n            columnNumber: 17\n          }, this) : null]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#E6F4F7] p-1.5 rounded-md mr-2\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4 text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1346,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1345,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1344,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-sm font-medium text-[#344054]\",\n              children: \"Add Comment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1349,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1343,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-[#344054] mb-1.5\",\n                children: \"Comment\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: commentInput,\n                  onChange: v => setCommentInput(v.target.value),\n                  placeholder: \"Type your comment here...\",\n                  className: `w-full min-h-[120px] px-3 py-2 bg-white border ${commentInputError ? \"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\" : \"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"} rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1359,\n                  columnNumber: 21\n                }, this), commentInputError && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mt-1 text-[#D92D20] text-xs\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-3.5 h-3.5 mr-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1372,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 25\n                  }, this), commentInputError]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1358,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1354,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-medium text-[#344054] mb-1.5\",\n                children: \"Images\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                ...getRootComments({\n                  className: \"dropzone\"\n                }),\n                className: \"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  ...getInputComments()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1391,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#E6F4F7] p-2 rounded-full mb-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-5 h-5 text-[#0388A6]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1401,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1393,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1392,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-[#667085] text-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-medium text-[#0388A6]\",\n                    children: \"Click to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1409,\n                    columnNumber: 23\n                  }, this), \" or drag and drop\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1408,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-xs text-[#667085] mt-1\",\n                  children: \"PNG, JPG or JPEG (max. 10MB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1411,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1385,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1381,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1352,\n            columnNumber: 15\n          }, this), (filesComments === null || filesComments === void 0 ? void 0 : filesComments.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-xs font-medium text-[#344054] mb-2\",\n              children: \"Uploaded Files\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1421,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\",\n              children: filesComments.map((file, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-white rounded-md p-2 mr-3 flex-shrink-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: file.preview,\n                    className: \"w-10 h-10 object-cover rounded\",\n                    onError: e => {\n                      e.target.onerror = null;\n                      e.target.src = \"/assets/placeholder.png\";\n                    },\n                    alt: \"Preview\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1429,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1428,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"overflow-hidden flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-[#344054] truncate\",\n                    children: file.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1440,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-xs text-[#667085]\",\n                    children: [(file.size / (1024 * 1024)).toFixed(2), \" MB\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1443,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1439,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setFilesComments(prevFiles => prevFiles.filter((_, indexToRemove) => idx !== indexToRemove));\n                  },\n                  className: \"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\",\n                  title: \"Remove file\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    strokeWidth: \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"w-4 h-4 text-[#D92D20]\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M6 18 18 6M6 6l12 12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1464,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1456,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1447,\n                  columnNumber: 25\n                }, this)]\n              }, file.name + idx, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1424,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1422,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1420,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              disabled: loadingCommentCaseAdd,\n              onClick: () => {\n                var check = true;\n                setCommentInputError(\"\");\n                if (commentInput === \"\" && filesComments.length === 0) {\n                  setCommentInputError(\"Please add a comment or upload an image\");\n                  check = false;\n                }\n                if (check) {\n                  dispatch(addNewCommentCase({\n                    content: commentInput,\n                    files_commet: filesComments\n                  }, id));\n                } else {\n                  toast.error(\"Some fields are empty or invalid. Please try again\");\n                }\n              },\n              className: `inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${loadingCommentCaseAdd ? \"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\" : \"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"}`,\n              children: loadingCommentCaseAdd ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                    className: \"opacity-25\",\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1515,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    className: \"opacity-75\",\n                    fill: \"currentColor\",\n                    d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1516,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1514,\n                  columnNumber: 23\n                }, this), \"Saving...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-4 h-4 mr-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1523,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1522,\n                  columnNumber: 23\n                }, this), \"Add Comment\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1479,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1478,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-8\",\n            children: loadingCommentCase ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center items-center py-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1535,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1534,\n              columnNumber: 19\n            }, this) : errorCommentCase ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                strokeWidth: \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-5 h-5 mr-2\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1540,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1539,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: errorCommentCase\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1542,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 19\n            }, this) : comments && comments.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-6\",\n              children: comments === null || comments === void 0 ? void 0 : comments.map((comment, idx) => {\n                var _comment$coordinator, _comment$coordinator2, _comment$coordinator3, _comment$coordinator4, _comment$coordinator5, _comment$coordinator6, _comment$coordinator7, _comment$coordinator8, _comment$files;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-start\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mr-3 flex-shrink-0\",\n                      children: comment.coordinator ? (_comment$coordinator = comment.coordinator) !== null && _comment$coordinator !== void 0 && _comment$coordinator.photo ? /*#__PURE__*/_jsxDEV(\"img\", {\n                        className: \"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\",\n                        src: baseURLFile + ((_comment$coordinator2 = comment.coordinator) === null || _comment$coordinator2 === void 0 ? void 0 : _comment$coordinator2.photo),\n                        onError: e => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        },\n                        alt: ((_comment$coordinator3 = comment.coordinator) === null || _comment$coordinator3 === void 0 ? void 0 : _comment$coordinator3.full_name) || \"User\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1552,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-lg font-medium\",\n                          children: [(_comment$coordinator4 = comment.coordinator) !== null && _comment$coordinator4 !== void 0 && _comment$coordinator4.first_name ? (_comment$coordinator5 = comment.coordinator) === null || _comment$coordinator5 === void 0 ? void 0 : _comment$coordinator5.first_name[0] : \"\", (_comment$coordinator6 = comment.coordinator) !== null && _comment$coordinator6 !== void 0 && _comment$coordinator6.last_name ? (_comment$coordinator7 = comment.coordinator) === null || _comment$coordinator7 === void 0 ? void 0 : _comment$coordinator7.last_name[0] : \"\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1563,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1562,\n                        columnNumber: 33\n                      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          strokeWidth: \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-6 h-6 text-[#667085]\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1576,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1575,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1574,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1549,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"font-medium text-[#344054]\",\n                          children: ((_comment$coordinator8 = comment.coordinator) === null || _comment$coordinator8 === void 0 ? void 0 : _comment$coordinator8.full_name) || \"System\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1584,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"bg-white p-1 rounded-full mr-1.5\",\n                            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              strokeWidth: \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"w-3.5 h-3.5 text-[#667085]\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1591,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1590,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1589,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: formatDate(comment.created_at)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1594,\n                            columnNumber: 33\n                          }, this), comment.can_delete && /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => {\n                              setSelectComment(comment.id);\n                              setEventType(\"delete\");\n                              setIsDeleteComment(true);\n                            },\n                            className: \"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                              xmlns: \"http://www.w3.org/2000/svg\",\n                              fill: \"none\",\n                              viewBox: \"0 0 24 24\",\n                              strokeWidth: \"1.5\",\n                              stroke: \"currentColor\",\n                              className: \"w-3.5 h-3.5 mr-1\",\n                              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1606,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1605,\n                              columnNumber: 37\n                            }, this), \"Delete\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1597,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1588,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1583,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\",\n                        children: comment.content || \"No content\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1614,\n                        columnNumber: 29\n                      }, this), (comment === null || comment === void 0 ? void 0 : (_comment$files = comment.files) === null || _comment$files === void 0 ? void 0 : _comment$files.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\",\n                        children: comment.files.map((file, fileIdx) => /*#__PURE__*/_jsxDEV(\"a\", {\n                          target: \"_blank\",\n                          rel: \"noopener noreferrer\",\n                          href: baseURLFile + file.file,\n                          className: \"block transition-transform hover:scale-[1.03] duration-200\",\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\",\n                            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                              src: baseURLFile + file.file,\n                              className: \"w-full h-full object-cover\",\n                              onError: e => {\n                                e.target.onerror = null;\n                                e.target.src = \"/assets/placeholder.png\";\n                              },\n                              alt: \"Attachment\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1629,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1638,\n                              columnNumber: 39\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1628,\n                            columnNumber: 37\n                          }, this)\n                        }, fileIdx, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1621,\n                          columnNumber: 35\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1619,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1582,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1548,\n                    columnNumber: 25\n                  }, this)\n                }, idx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1547,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1545,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-10 bg-[#F9FAFB] rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  strokeWidth: \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"w-8 h-8 text-[#667085]\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    d: \"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1653,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1652,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1651,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-[#344054] font-medium mb-2\",\n                children: \"No Comments Yet\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1656,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-[#667085] text-sm\",\n                children: \"Be the first to add a comment to this case\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1657,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1650,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1532,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1342,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: isDeleteComment,\n      message: eventType === \"delete\" ? \"Are you sure you want to delete this Comment?\" : \"Are you sure ?\",\n      title: eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\",\n      icon: \"delete\",\n      confirmText: \"Delete\",\n      cancelText: \"Cancel\",\n      onConfirm: async () => {\n        if (eventType === \"delete\" && selectComment !== \"\") {\n          dispatch(deleteCommentCase(selectComment));\n          setIsDeleteComment(false);\n          setEventType(\"\");\n        } else {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }\n      },\n      onCancel: () => {\n        setIsDeleteComment(false);\n        setEventType(\"\");\n        setSelectComment(\"\");\n      },\n      loadEvent: loadingCommentCaseDelete\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1666,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n      isOpen: openDiag,\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center text-[#0388A6]\",\n        children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n          xmlns: \"http://www.w3.org/2000/svg\",\n          fill: \"none\",\n          viewBox: \"0 0 24 24\",\n          strokeWidth: \"1.5\",\n          stroke: \"currentColor\",\n          className: \"w-5 h-5 mr-2\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1701,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1700,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Assign Case Coordinator\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1703,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1699,\n        columnNumber: 11\n      }, this),\n      message: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full my-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 text-[#0388A6] mr-2\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1710,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1709,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"text-[#0388A6] font-medium text-sm\",\n            children: [\"Assigned Coordinator \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-red-500\",\n              children: \"*\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1713,\n              columnNumber: 38\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1712,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1708,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1719,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1718,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1717,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            className: `bg-white border ${selectCoordinatorError ? \"border-red-500 focus:ring-red-500 focus:border-red-500\" : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"} text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`,\n            value: selectCoordinator,\n            onChange: v => setSelectCoordinator(v.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select a coordinator...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1731,\n              columnNumber: 17\n            }, this), coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: item.id,\n              children: item.full_name\n            }, item.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1733,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1722,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              strokeWidth: \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-5 h-5 text-gray-400\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m19.5 8.25-7.5 7.5-7.5-7.5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1738,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1737,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1736,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1716,\n          columnNumber: 13\n        }, this), selectCoordinatorError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center mt-2 text-red-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            strokeWidth: \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4 mr-1\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1745,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1744,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xs\",\n            children: selectCoordinatorError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1747,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1743,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1707,\n        columnNumber: 11\n      }, this),\n      icon: \"info\",\n      confirmText: \"Assign Coordinator\",\n      cancelText: \"Cancel\",\n      confirmButtonClass: \"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\",\n      cancelButtonClass: \"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\",\n      onConfirm: async () => {\n        setSelectCoordinatorError(\"\");\n        if (selectCoordinator === \"\") {\n          setSelectCoordinatorError(\"This field is required.\");\n        } else {\n          setIsLoading(true);\n          await dispatch(updateAssignedCase(id, {\n            coordinator: selectCoordinator\n          }));\n          setIsLoading(false);\n        }\n      },\n      onCancel: () => {\n        setSelectCoordinator(\"\");\n        setSelectCoordinatorError(\"\");\n        setOpenDiag(false);\n        setIsLoading(false);\n      },\n      loadEvent: isLoading,\n      loadingText: \"Assigning coordinator...\",\n      loadingIcon: /*#__PURE__*/_jsxDEV(\"svg\", {\n        className: \"animate-spin h-5 w-5 text-white\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          className: \"opacity-25\",\n          cx: \"12\",\n          cy: \"12\",\n          r: \"10\",\n          stroke: \"currentColor\",\n          strokeWidth: \"4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1780,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          className: \"opacity-75\",\n          fill: \"currentColor\",\n          d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1781,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1779,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1696,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 319,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"yEkpFU4JMoQc8kIp8ZxuszZ7aAo=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSearchParams, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "deleteCommentCase", "detailCase", "duplicateCase", "getCaseHistory", "getListCommentCase", "updateAssignedCase", "updateCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "CaseHistory", "baseURLFile", "COUNTRIES", "CURRENCYITEMS", "useDropzone", "toast", "getListCoordinators", "CASE_DUPLICATE_REQUEST", "ConfirmationModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_s", "_caseInfo$assurance_n", "_caseInfo$created_use", "_caseInfo$created_use2", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$pat", "_caseInfo$patient2", "_caseInfo$patient3", "_caseInfo$case_status", "_caseInfo$patient$ful2", "_caseInfo$patient4", "_caseInfo$patient$bir", "_caseInfo$patient5", "_caseInfo$patient$pat2", "_caseInfo$patient6", "_caseInfo$patient$pat3", "_caseInfo$patient7", "_caseInfo$patient$pat4", "_caseInfo$patient8", "_caseInfo$patient$pat5", "_caseInfo$patient9", "_caseInfo$patient$pat6", "_caseInfo$patient10", "_caseInfo$case_type", "_caseInfo$currency_pr", "_caseInfo$coordinator3", "_caseInfo$case_descri", "_caseInfo$assistance_", "_caseInfo$medical_rep", "_caseInfo$medical_rep2", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$upload_invo2", "_caseInfo$assurance_s", "_caseInfo$assurance$a2", "_caseInfo$assurance2", "_caseInfo$assurance_n2", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "_caseInfo$upload_auth2", "navigate", "location", "dispatch", "id", "searchParams", "setSearchParams", "page", "get", "tabParam", "historyPageParam", "isLoading", "setIsLoading", "openDiag", "setOpenDiag", "selectCoordinator", "setSelectCoordinator", "selectCoordinatorError", "setSelectCoordinatorError", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "isDuplicate", "setIsDuplicate", "isDeleteComment", "setIsDeleteComment", "selectComment", "setSelectComment", "eventType", "setEventType", "showEditStatusModal", "setShowEditStatusModal", "selectedStatuses", "setSelectedStatuses", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "commentCaseDelete", "loadingCommentCaseDelete", "successCommentCaseDelete", "errorCommentCaseDelete", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseAssignedUpdate", "updateCaseAssigned", "loadingCaseAssignedUpdate", "errorCaseAssignedUpdate", "successCaseAssignedUpdate", "caseDuplicat", "loadingCaseDuplicate", "errorCaseDuplicate", "successCaseDuplicate", "caseDuplicate", "caseHistoryState", "caseHistory", "loadingHistory", "errorHistory", "history", "historyCurrentPage", "historyTotalPages", "redirect", "console", "log", "type", "historyPageFromUrl", "handleTabChange", "tabName", "newParams", "URLSearchParams", "set", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "caseStatusColor", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "getCurrencyCode", "code", "patientCurrency", "foundCurrency", "_foundCurrency$symbol", "symbol", "getSectionIndex", "selectItem", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "strokeWidth", "assurance_number", "created_user", "full_name", "onClick", "_caseInfo$coordinator", "_caseInfo$coordinator2", "coordinator_user", "is_pay", "assurance", "assurance_name", "patient", "patient_country", "case_status", "stat", "index", "status_coordination", "class", "select", "birth_day", "patient_phone", "patient_email", "patient_city", "case_type", "case_type_item", "parseFloat", "price_tatal", "toFixed", "currency_price", "eur_price", "case_date", "char<PERSON>t", "case_description", "updated_at", "assistance_services", "length", "itemAssistance", "_caseInfo$assistance_2", "_itemAssistance$creat", "_itemAssistance$creat2", "_itemAssistance$provi", "email", "created_at", "appointment_date", "start_date", "end_date", "service_location", "provider_services", "providerService", "idx", "_providerService$prov", "_providerService$prov2", "provider", "service_type", "service_specialist", "provider_date", "phone", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance_status", "policy_number", "upload_authorization", "historyData", "count", "value", "onChange", "v", "placeholder", "src", "onError", "e", "onerror", "alt", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "content", "files_commet", "cx", "cy", "r", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator3", "_comment$coordinator4", "_comment$coordinator5", "_comment$coordinator6", "_comment$coordinator7", "_comment$coordinator8", "_comment$files", "coordinator", "photo", "first_name", "last_name", "can_delete", "files", "fileIdx", "isOpen", "confirmText", "cancelText", "onConfirm", "onCancel", "loadEvent", "confirmButtonClass", "cancelButtonClass", "loadingText", "loadingIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  deleteCommentCase,\n  detailCase,\n  duplicateCase,\n  getCaseHistory,\n  getListCommentCase,\n  updateAssignedCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport CaseHistory from \"../../components/CaseHistory\";\nimport { baseURLFile, COUNTRIES, CURRENCYITEMS } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { CASE_DUPLICATE_REQUEST } from \"../../redux/constants/caseConstants\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams, setSearchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const tabParam = searchParams.get(\"tab\") || \"General Information\";\n  const historyPageParam = searchParams.get(\"historyPage\") || \"1\";\n\n  const [isLoading, setIsLoading] = useState(false);\n  const [openDiag, setOpenDiag] = useState(false);\n  const [selectCoordinator, setSelectCoordinator] = useState(\"\");\n  const [selectCoordinatorError, setSelectCoordinatorError] = useState(\"\");\n\n  const [selectPage, setSelectPage] = useState(tabParam);\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  const [isDuplicate, setIsDuplicate] = useState(false);\n\n  const [isDeleteComment, setIsDeleteComment] = useState(false);\n  const [selectComment, setSelectComment] = useState(\"\");\n  const [eventType, setEventType] = useState(\"\");\n\n  // Edit Status Modal\n  const [showEditStatusModal, setShowEditStatusModal] = useState(false);\n  const [selectedStatuses, setSelectedStatuses] = useState([]);\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const commentCaseDelete = useSelector((state) => state.deleteCommentCase);\n  const {\n    loadingCommentCaseDelete,\n    successCommentCaseDelete,\n    errorCommentCaseDelete,\n  } = commentCaseDelete;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseAssignedUpdate = useSelector((state) => state.updateCaseAssigned);\n  const {\n    loadingCaseAssignedUpdate,\n    errorCaseAssignedUpdate,\n    successCaseAssignedUpdate,\n  } = caseAssignedUpdate;\n\n  const caseDuplicat = useSelector((state) => state.duplicateCase);\n  const {\n    loadingCaseDuplicate,\n    errorCaseDuplicate,\n    successCaseDuplicate,\n    caseDuplicate,\n  } = caseDuplicat;\n\n  const caseHistoryState = useSelector((state) => state.caseHistory);\n  const { loadingHistory, errorHistory, history, page: historyCurrentPage, pages: historyTotalPages } = caseHistoryState;\n\n  // We don't need historyPage state anymore as we're using URL parameters directly\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  useEffect(() => {\n    if (successCommentCaseDelete) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseDelete]);\n\n  useEffect(() => {\n    if (successCaseDuplicate && caseDuplicate) {\n      navigate(\"/cases-list/edit/\" + caseDuplicate);\n      dispatch({ type: \"RESET_DUPLICATE_CASE\" });\n    }\n  }, [successCaseDuplicate, caseDuplicate]);\n\n  // Reset flag on navigation back\n  useEffect(() => {\n    return () => setIsDuplicate(false);\n  }, []);\n\n  useEffect(() => {\n    if (successCaseAssignedUpdate) {\n      setSelectCoordinator(\"\");\n      setSelectCoordinatorError(\"\");\n      setOpenDiag(false);\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n      dispatch(getListCoordinators(\"0\"));\n    }\n  }, [successCaseAssignedUpdate]);\n\n  // Fetch history data when the History tab is selected or history page changes\n  useEffect(() => {\n    if (selectPage === \"History\" && id) {\n      // Get the historyPage from URL parameters\n      const historyPageFromUrl = searchParams.get('page') || '1';\n      dispatch(getCaseHistory(id, historyPageFromUrl));\n    }\n  }, [selectPage, id, dispatch, searchParams]);\n\n  // We don't need the handleHistoryPageChange function anymore\n  // since Paginate component handles navigation directly through links\n\n  // Handle tab selection\n  const handleTabChange = (tabName) => {\n    setSelectPage(tabName);\n\n    // Update URL with the new tab\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('tab', tabName);\n    setSearchParams(newParams);\n  };\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      case \"coordination-fee\":\n        return \"Coordination Fee\";\n      case \"coordinated-missing-payment\":\n        return \"Coordinated, Missing Payment\";\n      case \"failed\":\n        return \"Failed\";\n      default:\n        return casestatus;\n    }\n  };\n\n  const caseStatusColor = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"text-danger\";\n      case \"coordinated-missing-m-r\":\n        return \"text-[#FFA500]\";\n      case \"coordinated-missing-invoice\":\n        return \"text-[#FFA500]\";\n      case \"waiting-for-insurance-authorization\":\n        return \"text-primary\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"text-primary\";\n      case \"fully-coordinated\":\n        return \"text-[#008000]\";\n      case \"failed\":\n        return \"text-[#d34053]\";\n      default:\n        return \"\";\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  //\n  const getCurrencyCode = (code) => {\n    const patientCurrency = code ?? \"\";\n\n    const foundCurrency = CURRENCYITEMS?.find(\n      (option) => option.code === patientCurrency\n    );\n\n    if (foundCurrency) {\n      return foundCurrency.symbol ?? code;\n    } else {\n      return code;\n    }\n  };\n\n  const getSectionIndex = (selectItem) => {\n    if (selectItem === \"General Information\") {\n      return 0;\n    } else if (selectItem === \"Coordination Details\") {\n      return 1;\n    } else if (selectItem === \"Medical Reports\") {\n      return 2;\n    } else if (selectItem === \"Invoices\") {\n      return 3;\n    } else if (selectItem === \"Insurance Authorization\") {\n      return 4;\n    } else if (selectItem === \"History\") {\n      return 0;\n    } else {\n      return 0;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-3 bg-white shadow-sm px-4 py-4 rounded-lg\">\n              {/* Top Row with CIA REF, Created By, and Assign Button */}\n              <div className=\"flex flex-wrap items-center justify-between mb-3\">\n                <div className=\"flex items-center space-x-4\">\n                  {/* CIA REF */}\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <div className=\"text-[#344054] text-xs font-medium\">CIA REF</div>\n                      <div className=\"text-[#0388A6] text-sm font-medium\">{caseInfo.assurance_number ?? \"---\"}</div>\n                    </div>\n                  </div>\n\n                  {/* Created By */}\n                  <div className=\"flex items-center\">\n                    <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.042 21.672 13.684 16.6m0 0-2.51 2.225.569-9.47 5.227 7.917-3.286-.672Zm-7.518-.267A8.25 8.25 0 1 1 20.25 10.5M8.288 14.212A5.25 5.25 0 1 1 17.25 10.5\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <div className=\"text-[#344054] text-xs font-medium\">Created By</div>\n                      <div className=\"text-[#667085] text-sm\">{caseInfo.created_user?.full_name ?? \"---\"}</div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Assign Coordinator Button */}\n                <button\n                  onClick={() => {\n                    setSelectCoordinator(caseInfo.coordinator_user?.id ?? \"\");\n                    setSelectCoordinatorError(\"\");\n                    setOpenDiag(true);\n                    setIsLoading(false);\n                  }}\n                  className=\"flex items-center bg-[#0388A6] hover:bg-[#026e84] text-white px-3 py-1.5 rounded-md transition-colors duration-300 text-xs mt-2 sm:mt-0\"\n                >\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    strokeWidth=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-4 h-4 mr-1\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\"\n                    />\n                  </svg>\n                  <span className=\"font-medium\">Assign Coordinator</span>\n                </button>\n              </div>\n\n              {/* Main Info Grid - 3 columns on all screens */}\n              <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-x-3 gap-y-2\">\n                {/* Payment Status */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Payment</div>\n                    {caseInfo.is_pay ? (\n                      <span className=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#E7F9ED] text-[#0C6735]\">\n                        Paid\n                      </span>\n                    ) : (\n                      <span className=\"inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-[#FEECEB] text-[#B42318]\">\n                        Unpaid\n                      </span>\n                    )}\n                  </div>\n                </div>\n\n                {/* CIA */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">CIA</div>\n                    <div className=\"text-[#667085] text-sm truncate max-w-[120px]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Patient Name */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Patient</div>\n                    <div className=\"text-[#667085] text-sm truncate max-w-[120px]\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                  </div>\n                </div>\n\n                {/* Country */}\n                <div className=\"flex items-center\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium\">Country</div>\n                    <div className=\"flex items-center\">\n                      {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                      <span className=\"text-[#667085] text-sm ml-1 truncate max-w-[100px]\">{caseStatus(caseInfo.patient?.patient_country)}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Status */}\n                <div className=\"flex items-start col-span-2 sm:col-span-3 md:col-span-2\">\n                  <div className=\"bg-[#F9FAFB] p-1.5 rounded-md mr-2 mt-0.5 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085]\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <div className=\"text-[#344054] text-xs font-medium mb-1\">Status</div>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {caseInfo.case_status?.map((stat, index) => (\n                        <span key={index} className={`inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(stat.status_coordination)}`}>\n                          {caseStatus(stat.status_coordination)}\n                        </span>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"flex flex-row items-center\">\n                <a\n                  className=\"text-white bg-[#FF9100] px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  href={\n                    \"/cases-list/edit/\" +\n                    caseInfo.id +\n                    \"?section=\" +\n                    getSectionIndex(selectPage)\n                  }\n                >\n                  <span>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4\"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z\"\n                      />\n                    </svg>\n                  </span>\n                  <span className=\"mx-1\">Edit Case</span>\n                </a>\n                {/* <button\n                  disabled={loadingCaseDuplicate}\n                  onClick={() => {\n                    dispatch(duplicateCase(caseInfo.id));\n                  }}\n                  className=\"text-white bg-success px-3 py-2 rounded text-xs font-bold flex flex-row w-max items-center mx-2\"\n                  // href={\"/cases-list/edit/\" + caseInfo.id}\n                >\n                  <span>\n                    {loadingCaseDuplicate ? (\n                      <div role=\"status\">\n                        <svg\n                          aria-hidden=\"true\"\n                          class=\"size-4 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\n                          viewBox=\"0 0 100 101\"\n                          fill=\"none\"\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                        >\n                          <path\n                            d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                            fill=\"currentColor\"\n                          />\n                          <path\n                            d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                            fill=\"currentFill\"\n                          />\n                        </svg>\n                        <span class=\"sr-only\">Loading...</span>\n                      </div>\n                    ) : (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-4\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                        />\n                      </svg>\n                    )}\n                  </span>\n                  <span className=\"mx-1\">Duplicate Case</span>\n                </button> */}\n              </div>\n              <div className=\"mb-6 mx-2 border-b border-[#F1F5F9]\">\n                <div className=\"flex flex-wrap -mb-px\">\n                  {[\n                    \"General Information\",\n                    \"Coordination Details\",\n                    \"Medical Reports\",\n                    \"Invoices\",\n                    \"Insurance Authorization\",\n                    \"History\",\n                  ].map((select, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleTabChange(select)}\n                      className={`inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${\n                        selectPage === select\n                          ? \"border-[#0388A6] text-[#0388A6]\"\n                          : \"border-transparent text-[#667085] hover:text-[#344054] hover:border-[#E6F4F7]\"\n                      }`}\n                    >\n                      {select}\n                    </button>\n                  ))}\n                </div>\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  {/* Patient Details Section */}\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Patient Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Full Name</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">{caseInfo.patient?.full_name ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Date of Birth</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.birth_day ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Phone</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_phone ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Email</div>\n                          <div className=\"text-sm text-[#344054]\">{caseInfo.patient?.patient_email ?? \"---\"}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Location</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {getIconCountry(caseInfo.patient?.patient_country ?? \"\")}\n                            <span className=\"ml-1\">{caseInfo.patient?.patient_city ?? \"---\"}, {caseInfo.patient?.patient_country ?? \"---\"}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Case Details Section */}\n                    <div className=\"md:w-1/2 w-full md:border-l border-t md:border-t-0 border-[#F1F5F9]\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v7.5m2.25-6.466a9.016 9.016 0 0 0-3.461-.203c-.536.072-.974.478-1.021 1.017\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Case Details</h3>\n                        </div>\n                      </div>\n\n                      <div className=\"p-5 space-y-4\">\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Case Type</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            <span className=\"font-medium\">{caseInfo.case_type ?? \"---\"}</span>\n                            {caseInfo.case_type === \"Medical\" && caseInfo.case_type_item &&\n                              <span className=\"ml-1 text-[#667085]\">| {caseInfo.case_type_item}</span>\n                            }\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price of Service</div>\n                          <div className=\"text-sm text-[#344054] font-medium\">\n                            {parseFloat(caseInfo.price_tatal).toFixed(2) + \" \" + getCurrencyCode(caseInfo.currency_price ?? \"\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Price (EUR)</div>\n                          <div className=\"text-sm text-[#344054]\">\n                            {parseFloat(caseInfo.eur_price).toFixed(2) + \" \" + getCurrencyCode(\"EUR\")}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Creation Date</div>\n                          <div className=\"text-sm text-[#344054]\">{formatDate(caseInfo.case_date)}</div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Assigned Coordinator</div>\n                          <div className=\"text-sm text-[#344054] flex items-center\">\n                            {caseInfo.coordinator_user?.full_name ? (\n                              <>\n                                <div className=\"bg-[#E6F4F7] w-5 h-5 rounded-full flex items-center justify-center mr-1.5\">\n                                  <span className=\"text-xs text-[#0388A6] font-medium\">\n                                    {caseInfo.coordinator_user.full_name.charAt(0)}\n                                  </span>\n                                </div>\n                                {caseInfo.coordinator_user.full_name}\n                              </>\n                            ) : (\n                              \"---\"\n                            )}\n                          </div>\n                        </div>\n\n                        <div className=\"flex flex-col\">\n                          <div className=\"text-xs text-[#667085] mb-1\">Description</div>\n                          <div className=\"text-sm text-[#344054] whitespace-pre-wrap\">\n                            {caseInfo.case_description ?? \"---\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                      <div className=\"flex items-center mb-1\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <h3 className=\"text-sm font-medium text-[#344054]\">Coordination Status</h3>\n                      </div>\n                    </div>\n\n                    <div className=\"p-5\">\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Current Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">\n                              {caseInfo.status_coordination ? (\n                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${caseStatusColor(caseInfo.status_coordination)}`}>\n                                  {caseStatus(caseInfo.status_coordination)}\n                                </span>\n                              ) : (\n                                \"---\"\n                              )}\n                            </span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Last Updated</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.updated_at)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                    <div className=\"w-full\">\n                      <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                        <div className=\"flex items-center mb-1\">\n                          <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h3 className=\"text-sm font-medium text-[#344054]\">Assistances Information</h3>\n                        </div>\n                      </div>\n                      <div className=\"p-5\">\n\n                      {caseInfo.assistance_services?.length > 0 ? (\n                        <div className=\"space-y-6\">\n                          {caseInfo.assistance_services.map((itemAssistance, index) => (\n                            <div key={index} className=\"bg-white rounded-xl shadow-sm overflow-hidden  border-[0.00001px] border-[#0388A6]\">\n                              {/* Assistance Header */}\n                              <div className=\"bg-gradient-to-r from-[#F8FAFC] to-white px-5 py-4\">\n                                <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center\">\n                                  <div className=\"flex items-center mb-2 sm:mb-0\">\n                                    <div className=\"bg-[#0388A6] bg-opacity-10 rounded-full p-2 mr-3\">\n                                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                      </svg>\n                                    </div>\n                                    <h4 className=\"font-medium text-[#303030]\">Appointment #{caseInfo.assistance_services?.length-index}</h4>\n                                  </div>\n                                  <div className=\"flex flex-col sm:flex-row sm:items-center\">\n                                    {itemAssistance.created_user && (\n                                      <div className=\"flex items-center text-xs text-[#344054] mb-2 sm:mb-0 sm:mr-3\">\n                                        <div className=\"bg-[#F9FAFB] p-1 rounded-full mr-1.5\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3 h-3 text-[#667085]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                          </svg>\n                                        </div>\n                                        <span>\n                                          Created by {itemAssistance.created_user?.full_name || itemAssistance.created_user?.email || \"User\"}\n                                          {itemAssistance.created_at && (\n                                            <span className=\"text-[#667085] ml-1\">\n                                              on {formatDate(itemAssistance.created_at)}\n                                            </span>\n                                          )}\n                                        </span>\n                                      </div>\n                                    )}\n                                    {itemAssistance.appointment_date && (\n                                      <div className=\"flex items-center text-xs text-[#0388A6] px-3 py-1.5 rounded-full bg-[#E6F4F7]\">\n                                        {formatDate(itemAssistance.appointment_date)}\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n                              </div>\n\n                              {/* Assistance Content */}\n                              <div className=\"px-5 py-4\">\n                                {/* Assistance Details */}\n                                <div className=\"mb-6\">\n                                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                                    {itemAssistance.start_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Starting Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.start_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.end_date && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Hospital Ending Date</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{formatDate(itemAssistance.end_date)}</span>\n                                        </div>\n                                      </div>\n                                    )}\n\n                                    {itemAssistance.service_location && (\n                                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\" />\n                                          </svg>\n                                        </div>\n                                        <div>\n                                          <span className=\"text-xs text-[#667085] block\">Service Location</span>\n                                          <span className=\"text-sm font-medium text-[#344054]\">{itemAssistance.service_location}</span>\n                                        </div>\n                                      </div>\n                                    )}\n                                  </div>\n                                </div>\n\n                                {/* Provider Services */}\n                                {itemAssistance.provider_services?.length > 0 && (\n                                  <div>\n                                    <div className=\"flex items-center mb-4\">\n                                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n                                        </svg>\n                                      </div>\n                                      <h5 className=\"text-sm font-medium text-[#303030]\">Providers</h5>\n                                    </div>\n\n                                    <div className=\"space-y-4\">\n                                      {itemAssistance.provider_services.map((providerService, idx) => (\n                                        <div key={idx} className=\"bg-[#F9FAFB] p-4 rounded-lg hover:shadow-sm transition-shadow duration-200\">\n                                          <div className=\"flex items-center mb-3\">\n                                            <div className=\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm mr-3\">\n                                              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                              </svg>\n                                            </div>\n                                            <div>\n                                              <span className=\"text-sm font-medium text-[#344054]\">{providerService.provider?.full_name || \"---\"}</span>\n                                              <span className=\"text-xs text-[#0388A6] ml-2 bg-[#E6F4F7] px-2 py-0.5 rounded-full\">{providerService.service_type || \"---\"}</span>\n                                            </div>\n                                          </div>\n\n                                          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3 pl-11\">\n                                            {providerService.service_specialist && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Speciality</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.service_specialist}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider_date && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Visit Date</span>\n                                                  <span className=\"text-sm text-[#344054]\">{formatDate(providerService.provider_date)}</span>\n                                                </div>\n                                              </div>\n                                            )}\n\n                                            {providerService.provider?.phone && (\n                                              <div className=\"flex items-center\">\n                                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#667085] mr-2\">\n                                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z\" />\n                                                </svg>\n                                                <div>\n                                                  <span className=\"text-xs text-[#667085] block\">Contact</span>\n                                                  <span className=\"text-sm text-[#344054]\">{providerService.provider.phone}</span>\n                                                </div>\n                                              </div>\n                                            )}\n                                          </div>\n                                        </div>\n                                      ))}\n                                    </div>\n                                  </div>\n                                )}\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"bg-white rounded-xl p-8 text-center shadow-sm\">\n                          <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\" />\n                            </svg>\n                          </div>\n                          <h4 className=\"text-[#303030] font-medium mb-2\">No Assistances Informations</h4>\n                          <p className=\"text-gray-500 text-sm\">No assistances details have been added to this case yet.</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                  </div>\n                  </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Medical Reports</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    {caseInfo.medical_reports?.length > 0 ? (\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        {caseInfo.medical_reports?.map((item, index) => (\n                          <a\n                            key={index}\n                            href={baseURLFile + item.file}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                          >\n                            <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                              <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  className=\"w-5 h-5 text-[#0388A6]\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"overflow-hidden\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                  {item.file_name}\n                                </div>\n                                <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                              </div>\n                            </div>\n                          </a>\n                        ))}\n                      </div>\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <div className=\"bg-[#E6F4F7] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-[#344054] font-medium mb-2\">No Medical Reports</h4>\n                        <p className=\"text-[#667085] text-sm\">No medical reports have been uploaded for this case yet.</p>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Invoice Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Number</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.invoice_number ?? \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Date Issued</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">{formatDate(caseInfo.date_issued) || \"---\"}</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Amount</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">$ {parseFloat(caseInfo.invoice_amount || 0).toFixed(2)}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Due Date</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                          <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                            </svg>\n                          </div>\n                          <div>\n                            <span className=\"text-xs text-[#667085] block\">Invoice Status</span>\n                            <span className=\"text-sm font-medium text-[#344054]\">---</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_invoices?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_invoices?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No invoice documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-4 mx-2 bg-white shadow-sm rounded-xl overflow-hidden\">\n                  <div className=\"px-5 py-4 bg-gradient-to-r from-[#F8FAFC] to-white\">\n                    <div className=\"flex items-center mb-1\">\n                      <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z\" />\n                        </svg>\n                      </div>\n                      <h3 className=\"text-sm font-medium text-[#344054]\">Insurance Details</h3>\n                    </div>\n                  </div>\n\n                  <div className=\"p-5\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8\">\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Authorization Status</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_status ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M2.25 21h19.5m-18-18v18m10.5-18v18m6-13.5V21M6.75 6.75h.75m-.75 3h.75m-.75 3h.75m3-6h.75m-.75 3h.75m-.75 3h.75M6.75 21v-3.375c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21M3 3h12m-.75 4.5H21m-3.75 3.75h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Zm0 3h.008v.008h-.008v-.008Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Insurance Company</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance?.assurance_name ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">CIA Reference</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.assurance_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n\n                      <div className=\"flex items-center bg-[#F9FAFB] rounded-lg p-3\">\n                        <div className=\"bg-white rounded-full p-2 shadow-sm mr-3\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Zm6-10.125a1.875 1.875 0 1 1-3.75 0 1.875 1.875 0 0 1 3.75 0Zm1.294 6.336a6.721 6.721 0 0 1-3.17.789 6.721 6.721 0 0 1-3.168-.789 3.376 3.376 0 0 1 6.338 0Z\" />\n                          </svg>\n                        </div>\n                        <div>\n                          <span className=\"text-xs text-[#667085] block\">Policy Number</span>\n                          <span className=\"text-sm font-medium text-[#344054]\">{caseInfo.policy_number ?? \"---\"}</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Uploaded Documents */}\n                    <div className=\"mt-6\">\n                      <div className=\"flex items-center mb-4\">\n                        <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                          </svg>\n                        </div>\n                        <h4 className=\"text-sm font-medium text-[#344054]\">Uploaded Documents</h4>\n                      </div>\n\n                      {caseInfo.upload_authorization?.length > 0 ? (\n                        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                          {caseInfo.upload_authorization?.map((item, idx) => (\n                            <a\n                              key={idx}\n                              href={baseURLFile + item.file}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"block transition-transform duration-200 hover:scale-[1.02]\"\n                            >\n                              <div className=\"bg-[#F9FAFB] rounded-xl p-4 hover:shadow-sm transition-shadow duration-200 flex items-center\">\n                                <div className=\"bg-white rounded-full p-2.5 shadow-sm mr-3 flex-shrink-0\">\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    viewBox=\"0 0 24 24\"\n                                    fill=\"currentColor\"\n                                    className=\"w-5 h-5 text-[#0388A6]\"\n                                  >\n                                    <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                    <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                  </svg>\n                                </div>\n                                <div className=\"overflow-hidden\">\n                                  <div className=\"whitespace-nowrap overflow-hidden text-ellipsis text-sm font-medium text-[#344054]\">\n                                    {item.file_name}\n                                  </div>\n                                  <div className=\"text-xs text-[#667085]\">{item.file_size} mb</div>\n                                </div>\n                              </div>\n                            </a>\n                          ))}\n                        </div>\n                      ) : (\n                        <div className=\"text-center py-6 bg-[#F9FAFB] rounded-lg\">\n                          <div className=\"bg-white w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 shadow-sm\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\" />\n                            </svg>\n                          </div>\n                          <p className=\"text-[#667085] text-sm\">No authorization documents have been uploaded yet</p>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/* \"History\" */}\n              {selectPage === \"History\" ? (\n                <CaseHistory\n                  historyData={{\n                    history: history,\n                    page: historyCurrentPage,\n                    pages: historyTotalPages,\n                    count: history?.length || 0\n                  }}\n                  loading={loadingHistory}\n                  error={errorHistory}\n                />\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-4 bg-white shadow-sm px-5 py-6 rounded-xl\">\n              <div className=\"flex items-center mb-4\">\n                <div className=\"bg-[#E6F4F7] p-1.5 rounded-md mr-2\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6]\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-sm font-medium text-[#344054]\">Add Comment</h3>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Comment Input */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Comment\n                  </label>\n                  <div className=\"relative\">\n                    <textarea\n                      value={commentInput}\n                      onChange={(v) => setCommentInput(v.target.value)}\n                      placeholder=\"Type your comment here...\"\n                      className={`w-full min-h-[120px] px-3 py-2 bg-white border ${\n                        commentInputError\n                          ? \"border-[#D92D20] focus:border-[#D92D20] focus:ring-[#FEECEB]\"\n                          : \"border-[#E6F4F7] focus:border-[#0388A6] focus:ring-[#E6F4F7]\"\n                      } rounded-lg text-sm text-[#344054] focus:outline-none focus:ring-2 transition-colors duration-200 resize-none`}\n                    ></textarea>\n                    {commentInputError && (\n                      <div className=\"flex items-center mt-1 text-[#D92D20] text-xs\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                        </svg>\n                        {commentInputError}\n                      </div>\n                    )}\n                  </div>\n                </div>\n\n                {/* Image Upload */}\n                <div>\n                  <label className=\"block text-xs font-medium text-[#344054] mb-1.5\">\n                    Images\n                  </label>\n                  <div\n                    {...getRootComments({\n                      className: \"dropzone\",\n                    })}\n                    className=\"bg-[#F9FAFB] border border-dashed border-[#E6F4F7] rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer min-h-[120px] hover:bg-[#F1F5F9] transition-colors duration-200\"\n                  >\n                    <input {...getInputComments()} />\n                    <div className=\"bg-[#E6F4F7] p-2 rounded-full mb-2\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        strokeWidth=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"w-5 h-5 text-[#0388A6]\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                        />\n                      </svg>\n                    </div>\n                    <p className=\"text-sm text-[#667085] text-center\">\n                      <span className=\"font-medium text-[#0388A6]\">Click to upload</span> or drag and drop\n                    </p>\n                    <p className=\"text-xs text-[#667085] mt-1\">\n                      PNG, JPG or JPEG (max. 10MB)\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Uploaded Files Preview */}\n              {filesComments?.length > 0 && (\n                <div className=\"mt-6\">\n                  <h4 className=\"text-xs font-medium text-[#344054] mb-2\">Uploaded Files</h4>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3\">\n                    {filesComments.map((file, idx) => (\n                      <div\n                        className=\"bg-[#F9FAFB] rounded-lg p-3 flex items-center group relative\"\n                        key={file.name + idx}\n                      >\n                        <div className=\"bg-white rounded-md p-2 mr-3 flex-shrink-0\">\n                          <img\n                            src={file.preview}\n                            className=\"w-10 h-10 object-cover rounded\"\n                            onError={(e) => {\n                              e.target.onerror = null;\n                              e.target.src = \"/assets/placeholder.png\";\n                            }}\n                            alt=\"Preview\"\n                          />\n                        </div>\n                        <div className=\"overflow-hidden flex-1\">\n                          <div className=\"text-sm font-medium text-[#344054] truncate\">\n                            {file.name}\n                          </div>\n                          <div className=\"text-xs text-[#667085]\">\n                            {(file.size / (1024 * 1024)).toFixed(2)} MB\n                          </div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter((_, indexToRemove) => idx !== indexToRemove)\n                            );\n                          }}\n                          className=\"absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-[#FEECEB]\"\n                          title=\"Remove file\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            strokeWidth=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"w-4 h-4 text-[#D92D20]\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              )}\n\n              {/* Submit Button */}\n              <div className=\"mt-6\">\n                <button\n                  disabled={loadingCommentCaseAdd}\n                  onClick={() => {\n                    var check = true;\n                    setCommentInputError(\"\");\n\n                    if (commentInput === \"\" && filesComments.length === 0) {\n                      setCommentInputError(\"Please add a comment or upload an image\");\n                      check = false;\n                    }\n\n                    if (check) {\n                      dispatch(\n                        addNewCommentCase(\n                          {\n                            content: commentInput,\n                            files_commet: filesComments,\n                          },\n                          id\n                        )\n                      );\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. Please try again\"\n                      );\n                    }\n                  }}\n                  className={`inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium ${\n                    loadingCommentCaseAdd\n                      ? \"bg-[#E6F4F7] text-[#0388A6] cursor-not-allowed\"\n                      : \"bg-[#0388A6] text-white hover:bg-[#026e84] transition-colors duration-200\"\n                  }`}\n                >\n                  {loadingCommentCaseAdd ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-[#0388A6]\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Saving...\n                    </>\n                  ) : (\n                    <>\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-2\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                      Add Comment\n                    </>\n                  )}\n                </button>\n              </div>\n\n              {/* Comments List */}\n              <div className=\"mt-8\">\n                {loadingCommentCase ? (\n                  <div className=\"flex justify-center items-center py-8\">\n                    <div className=\"animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#0388A6]\"></div>\n                  </div>\n                ) : errorCommentCase ? (\n                  <div className=\"bg-[#FEECEB] text-[#B42318] p-4 rounded-lg flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                    </svg>\n                    <span>{errorCommentCase}</span>\n                  </div>\n                ) : comments && comments.length > 0 ? (\n                  <div className=\"space-y-6\">\n                    {comments?.map((comment, idx) => (\n                      <div key={idx} className=\"bg-[#F9FAFB] rounded-xl p-4 shadow-sm\">\n                        <div className=\"flex items-start\">\n                          <div className=\"mr-3 flex-shrink-0\">\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\"w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                  onError={(e) => {\n                                    e.target.onerror = null;\n                                    e.target.src = \"/assets/placeholder.png\";\n                                  }}\n                                  alt={comment.coordinator?.full_name || \"User\"}\n                                />\n                              ) : (\n                                <div className=\"w-12 h-12 rounded-full bg-[#0388A6] text-white flex items-center justify-center shadow-sm\">\n                                  <span className=\"text-lg font-medium\">\n                                    {comment.coordinator?.first_name\n                                      ? comment.coordinator?.first_name[0]\n                                      : \"\"}\n                                    {comment.coordinator?.last_name\n                                      ? comment.coordinator?.last_name[0]\n                                      : \"\"}\n                                  </span>\n                                </div>\n                              )\n                            ) : (\n                              <div className=\"w-12 h-12 rounded-full bg-[#F1F5F9] flex items-center justify-center shadow-sm\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-6 h-6 text-[#667085]\">\n                                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                                </svg>\n                              </div>\n                            )}\n                          </div>\n\n                          <div className=\"flex-1\">\n                            <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-2\">\n                              <div className=\"font-medium text-[#344054]\">\n                                {comment.coordinator?.full_name || \"System\"}\n                              </div>\n\n                              <div className=\"flex items-center text-xs text-[#667085] mt-1 sm:mt-0\">\n                                <div className=\"bg-white p-1 rounded-full mr-1.5\">\n                                  <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 text-[#667085]\">\n                                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n                                  </svg>\n                                </div>\n                                <span>{formatDate(comment.created_at)}</span>\n\n                                {comment.can_delete && (\n                                  <button\n                                    onClick={() => {\n                                      setSelectComment(comment.id);\n                                      setEventType(\"delete\");\n                                      setIsDeleteComment(true);\n                                    }}\n                                    className=\"ml-3 text-[#D92D20] hover:text-[#B42318] transition-colors flex items-center\"\n                                  >\n                                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-3.5 h-3.5 mr-1\">\n                                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\" />\n                                    </svg>\n                                    Delete\n                                  </button>\n                                )}\n                              </div>\n                            </div>\n\n                            <div className=\"bg-white rounded-lg p-3 text-sm text-[#344054] whitespace-pre-line mb-3\">\n                              {comment.content || \"No content\"}\n                            </div>\n\n                            {comment?.files?.length > 0 && (\n                              <div className=\"grid grid-cols-2 sm:grid-cols-4 gap-2 mt-3\">\n                                {comment.files.map((file, fileIdx) => (\n                                  <a\n                                    key={fileIdx}\n                                    target=\"_blank\"\n                                    rel=\"noopener noreferrer\"\n                                    href={baseURLFile + file.file}\n                                    className=\"block transition-transform hover:scale-[1.03] duration-200\"\n                                  >\n                                    <div className=\"relative rounded-lg overflow-hidden bg-[#F1F5F9] aspect-square\">\n                                      <img\n                                        src={baseURLFile + file.file}\n                                        className=\"w-full h-full object-cover\"\n                                        onError={(e) => {\n                                          e.target.onerror = null;\n                                          e.target.src = \"/assets/placeholder.png\";\n                                        }}\n                                        alt=\"Attachment\"\n                                      />\n                                      <div className=\"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-opacity duration-200\"></div>\n                                    </div>\n                                  </a>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-10 bg-[#F9FAFB] rounded-lg\">\n                    <div className=\"bg-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-8 h-8 text-[#667085]\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 0 1 .865-.501 48.172 48.172 0 0 0 3.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0 0 12 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018Z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"text-[#344054] font-medium mb-2\">No Comments Yet</h4>\n                    <p className=\"text-[#667085] text-sm\">Be the first to add a comment to this case</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n\n      <ConfirmationModal\n        isOpen={isDeleteComment}\n        message={\n          eventType === \"delete\"\n            ? \"Are you sure you want to delete this Comment?\"\n            : \"Are you sure ?\"\n        }\n        title={eventType === \"delete\" ? \"Delete Comment\" : \"Confirmation\"}\n        icon=\"delete\"\n        confirmText=\"Delete\"\n        cancelText=\"Cancel\"\n        onConfirm={async () => {\n          if (eventType === \"delete\" && selectComment !== \"\") {\n            dispatch(deleteCommentCase(selectComment));\n            setIsDeleteComment(false);\n            setEventType(\"\");\n          } else {\n            setIsDeleteComment(false);\n            setEventType(\"\");\n            setSelectComment(\"\");\n          }\n        }}\n        onCancel={() => {\n          setIsDeleteComment(false);\n          setEventType(\"\");\n          setSelectComment(\"\");\n        }}\n        loadEvent={loadingCommentCaseDelete}\n      />\n\n      <ConfirmationModal\n        isOpen={openDiag}\n        title={\n          <div className=\"flex items-center text-[#0388A6]\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 mr-2\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n            </svg>\n            <span>Assign Case Coordinator</span>\n          </div>\n        }\n        message={\n          <div className=\"w-full my-4\">\n            <div className=\"flex items-center mb-2\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 text-[#0388A6] mr-2\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z\" />\n              </svg>\n              <label className=\"text-[#0388A6] font-medium text-sm\">\n                Assigned Coordinator <span className=\"text-red-500\">*</span>\n              </label>\n            </div>\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                </svg>\n              </div>\n              <select\n                className={`bg-white border ${\n                  selectCoordinatorError\n                    ? \"border-red-500 focus:ring-red-500 focus:border-red-500\"\n                    : \"border-gray-200 focus:ring-[#0388A6] focus:border-[#0388A6]\"\n                } text-[#303030] rounded-lg block w-full pl-10 pr-10 py-3 appearance-none focus:outline-none focus:ring-2 transition-colors duration-200 text-sm`}\n                value={selectCoordinator}\n                onChange={(v) => setSelectCoordinator(v.target.value)}\n              >\n                <option value={\"\"}>Select a coordinator...</option>\n                {coordinators?.map((item) => (\n                  <option key={item.id} value={item.id}>{item.full_name}</option>\n                ))}\n              </select>\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-5 h-5 text-gray-400\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"m19.5 8.25-7.5 7.5-7.5-7.5\" />\n                </svg>\n              </div>\n            </div>\n            {selectCoordinatorError && (\n              <div className=\"flex items-center mt-2 text-red-500\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\" className=\"w-4 h-4 mr-1\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n                </svg>\n                <span className=\"text-xs\">{selectCoordinatorError}</span>\n              </div>\n            )}\n          </div>\n        }\n        icon=\"info\"\n        confirmText=\"Assign Coordinator\"\n        cancelText=\"Cancel\"\n        confirmButtonClass=\"bg-[#0388A6] hover:bg-[#026e84] text-white transition-colors duration-300\"\n        cancelButtonClass=\"bg-gray-100 hover:bg-gray-200 text-[#303030] transition-colors duration-300\"\n        onConfirm={async () => {\n          setSelectCoordinatorError(\"\");\n\n          if (selectCoordinator === \"\") {\n            setSelectCoordinatorError(\"This field is required.\");\n          } else {\n            setIsLoading(true);\n            await dispatch(\n              updateAssignedCase(id, { coordinator: selectCoordinator })\n            );\n            setIsLoading(false);\n          }\n        }}\n        onCancel={() => {\n          setSelectCoordinator(\"\");\n          setSelectCoordinatorError(\"\");\n          setOpenDiag(false);\n          setIsLoading(false);\n        }}\n        loadEvent={isLoading}\n        loadingText=\"Assigning coordinator...\"\n        loadingIcon={\n          <svg className=\"animate-spin h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n            <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n            <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n          </svg>\n        }\n      />\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SACEC,WAAW,EACXC,WAAW,EACXC,SAAS,EACTC,eAAe,QACV,kBAAkB;AACzB,SACEC,iBAAiB,EACjBC,iBAAiB,EACjBC,UAAU,EACVC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,kBAAkB,EAClBC,UAAU,QACL,iCAAiC;AACxC,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,WAAW,MAAM,8BAA8B;AACtD,SAASC,WAAW,EAAEC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAEvE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnE,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAMC,QAAQ,GAAG5E,WAAW,CAAC,CAAC;EAC9B,MAAM6E,QAAQ,GAAG9E,WAAW,CAAC,CAAC;EAC9B,MAAM+E,QAAQ,GAAGjF,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEkF;EAAG,CAAC,GAAG9E,SAAS,CAAC,CAAC;EACxB,MAAM,CAAC+E,YAAY,EAAEC,eAAe,CAAC,GAAG/E,eAAe,CAAC,CAAC;EACzD,MAAMgF,IAAI,GAAGF,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGJ,YAAY,CAACG,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB;EACjE,MAAME,gBAAgB,GAAGL,YAAY,CAACG,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG;EAE/D,MAAM,CAACG,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4F,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC8F,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACgG,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACkG,UAAU,EAAEC,aAAa,CAAC,GAAGnG,QAAQ,CAACwF,QAAQ,CAAC;EACtD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACwG,WAAW,EAAEC,cAAc,CAAC,GAAGzG,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM,CAAC0G,eAAe,EAAEC,kBAAkB,CAAC,GAAG3G,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4G,aAAa,EAAEC,gBAAgB,CAAC,GAAG7G,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC8G,SAAS,EAAEC,YAAY,CAAC,GAAG/G,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACgH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjH,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnH,QAAQ,CAAC,EAAE,CAAC;;EAE5D;EACA;EACA,MAAM,CAACoH,aAAa,EAAEC,gBAAgB,CAAC,GAAGrH,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM;IAAEsH,YAAY,EAAEC,eAAe;IAAEC,aAAa,EAAEC;EAAiB,CAAC,GACtEnG,WAAW,CAAC;IACVoG,MAAM,EAAE;MACN,SAAS,EAAE;IACb,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,gBAAgB,CAAEQ,SAAS,IAAK,CAC9B,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEJhI,SAAS,CAAC,MAAM;IACd,OAAO,MACLqH,aAAa,CAACiB,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,SAAS,GAAGrI,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAG1I,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAAC/H,UAAU,CAAC;EAC3D,MAAM;IAAEoI,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,eAAe,GAAG/I,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACU,eAAe,CAAC;EACrE,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAM,CAAC,GAC7DL,eAAe;EAEjB,MAAMM,iBAAiB,GAAGrJ,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAAChI,iBAAiB,CAAC;EACzE,MAAM;IACJgJ,wBAAwB;IACxBC,wBAAwB;IACxBC;EACF,CAAC,GAAGH,iBAAiB;EAErB,MAAMI,iBAAiB,GAAGzJ,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACoB,oBAAoB,CAAC;EAC5E,MAAM;IAAEC,qBAAqB;IAAEC,qBAAqB;IAAEC;EAAoB,CAAC,GACzEJ,iBAAiB;EAEnB,MAAMK,gBAAgB,GAAG9J,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACyB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,kBAAkB,GAAGnK,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAAC8B,kBAAkB,CAAC;EAC3E,MAAM;IACJC,yBAAyB;IACzBC,uBAAuB;IACvBC;EACF,CAAC,GAAGJ,kBAAkB;EAEtB,MAAMK,YAAY,GAAGxK,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAAC9H,aAAa,CAAC;EAChE,MAAM;IACJiK,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,YAAY;EAEhB,MAAMK,gBAAgB,GAAG7K,WAAW,CAAEsI,KAAK,IAAKA,KAAK,CAACwC,WAAW,CAAC;EAClE,MAAM;IAAEC,cAAc;IAAEC,YAAY;IAAEC,OAAO;IAAE7F,IAAI,EAAE8F,kBAAkB;IAAE9B,KAAK,EAAE+B;EAAkB,CAAC,GAAGN,gBAAgB;;EAEtH;EACA;EACA,MAAMO,QAAQ,GAAG,GAAG;EACpBvL,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0I,QAAQ,EAAE;MACbzD,QAAQ,CAACsG,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAAC/C,QAAQ,CAAC;MAErBvD,QAAQ,CAACzE,UAAU,CAAC0E,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,EAAEuE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACwD,QAAQ,EAAEyD,QAAQ,EAAEvD,QAAQ,EAAEC,EAAE,EAAEG,IAAI,CAAC,CAAC;EAE5CvF,SAAS,CAAC,MAAM;IACd,IAAI+J,qBAAqB,EAAE;MACzBzD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBc,gBAAgB,CAAC,EAAE,CAAC;MACpBnC,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,EAAEuE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAAC2E,qBAAqB,CAAC,CAAC;EAE3B/J,SAAS,CAAC,MAAM;IACd,IAAI0J,wBAAwB,EAAE;MAC5BpD,eAAe,CAAC,EAAE,CAAC;MACnBE,oBAAoB,CAAC,EAAE,CAAC;MACxBc,gBAAgB,CAAC,EAAE,CAAC;MACpBnC,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,EAAEuE,EAAE,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACsE,wBAAwB,CAAC,CAAC;EAE9B1J,SAAS,CAAC,MAAM;IACd,IAAI8K,oBAAoB,IAAIC,aAAa,EAAE;MACzC9F,QAAQ,CAAC,mBAAmB,GAAG8F,aAAa,CAAC;MAC7C5F,QAAQ,CAAC;QAAEuG,IAAI,EAAE;MAAuB,CAAC,CAAC;IAC5C;EACF,CAAC,EAAE,CAACZ,oBAAoB,EAAEC,aAAa,CAAC,CAAC;;EAEzC;EACA/K,SAAS,CAAC,MAAM;IACd,OAAO,MAAM0G,cAAc,CAAC,KAAK,CAAC;EACpC,CAAC,EAAE,EAAE,CAAC;EAEN1G,SAAS,CAAC,MAAM;IACd,IAAI0K,yBAAyB,EAAE;MAC7B1E,oBAAoB,CAAC,EAAE,CAAC;MACxBE,yBAAyB,CAAC,EAAE,CAAC;MAC7BJ,WAAW,CAAC,KAAK,CAAC;MAClBX,QAAQ,CAACzE,UAAU,CAAC0E,EAAE,CAAC,CAAC;MACxBD,QAAQ,CAACtE,kBAAkB,CAAC,GAAG,EAAEuE,EAAE,CAAC,CAAC;MACrCD,QAAQ,CAAC1D,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACiJ,yBAAyB,CAAC,CAAC;;EAE/B;EACA1K,SAAS,CAAC,MAAM;IACd,IAAImG,UAAU,KAAK,SAAS,IAAIf,EAAE,EAAE;MAClC;MACA,MAAMuG,kBAAkB,GAAGtG,YAAY,CAACG,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;MAC1DL,QAAQ,CAACvE,cAAc,CAACwE,EAAE,EAAEuG,kBAAkB,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACxF,UAAU,EAAEf,EAAE,EAAED,QAAQ,EAAEE,YAAY,CAAC,CAAC;;EAE5C;EACA;;EAEA;EACA,MAAMuG,eAAe,GAAIC,OAAO,IAAK;IACnCzF,aAAa,CAACyF,OAAO,CAAC;;IAEtB;IACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAAC1G,YAAY,CAAC;IACnDyG,SAAS,CAACE,GAAG,CAAC,KAAK,EAAEH,OAAO,CAAC;IAC7BvG,eAAe,CAACwG,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMG,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAON,UAAU;IACnB;EACF,CAAC;EAED,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,sBAAsB;MAC/B,KAAK,yBAAyB;QAC5B,OAAO,2BAA2B;MACpC,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,qCAAqC;QACxC,OAAO,qCAAqC;MAC9C,KAAK,kCAAkC;QACrC,OAAO,mCAAmC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,mBAAmB;MAC5B,KAAK,kBAAkB;QACrB,OAAO,kBAAkB;MAC3B,KAAK,6BAA6B;QAChC,OAAO,8BAA8B;MACvC,KAAK,QAAQ;QACX,OAAO,QAAQ;MACjB;QACE,OAAOA,UAAU;IACrB;EACF,CAAC;EAED,MAAMC,eAAe,GAAID,UAAU,IAAK;IACtC,QAAQA,UAAU;MAChB,KAAK,sBAAsB;QACzB,OAAO,aAAa;MACtB,KAAK,yBAAyB;QAC5B,OAAO,gBAAgB;MACzB,KAAK,6BAA6B;QAChC,OAAO,gBAAgB;MACzB,KAAK,qCAAqC;QACxC,OAAO,cAAc;MACvB,KAAK,kCAAkC;QACrC,OAAO,cAAc;MACvB,KAAK,mBAAmB;QACtB,OAAO,gBAAgB;MACzB,KAAK,QAAQ;QACX,OAAO,gBAAgB;MACzB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAME,cAAc,GAAIC,OAAO,IAAK;IAClC,MAAMC,YAAY,GAAGzL,SAAS,CAAC0L,IAAI,CAAEC,MAAM,IAAKA,MAAM,CAACC,KAAK,KAAKJ,OAAO,CAAC;IAEzE,IAAIC,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACI,IAAI;IAC1B,CAAC,MAAM;MACL,OAAO,EAAE;IACX;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,IAAI,IAAK;IAChC,MAAMC,eAAe,GAAGD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;IAElC,MAAME,aAAa,GAAGhM,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyL,IAAI,CACtCC,MAAM,IAAKA,MAAM,CAACI,IAAI,KAAKC,eAC9B,CAAC;IAED,IAAIC,aAAa,EAAE;MAAA,IAAAC,qBAAA;MACjB,QAAAA,qBAAA,GAAOD,aAAa,CAACE,MAAM,cAAAD,qBAAA,cAAAA,qBAAA,GAAIH,IAAI;IACrC,CAAC,MAAM;MACL,OAAOA,IAAI;IACb;EACF,CAAC;EAED,MAAMK,eAAe,GAAIC,UAAU,IAAK;IACtC,IAAIA,UAAU,KAAK,qBAAqB,EAAE;MACxC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,sBAAsB,EAAE;MAChD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,iBAAiB,EAAE;MAC3C,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MACpC,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,yBAAyB,EAAE;MACnD,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,UAAU,KAAK,SAAS,EAAE;MACnC,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF,CAAC;;EAED;EACA,oBACE7L,OAAA,CAACb,aAAa;IAAA2M,QAAA,gBACZ9L,OAAA;MAAK+L,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf9L,OAAA;QAAK+L,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD9L,OAAA;UAAGgM,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB9L,OAAA;YAAK+L,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D9L,OAAA;cACEiM,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB9L,OAAA;gBACEqM,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN3M,OAAA;cAAM+L,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ3M,OAAA;UAAA8L,QAAA,eACE9L,OAAA;YACEiM,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB9L,OAAA;cACEqM,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP3M,OAAA;UAAGgM,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnB9L,OAAA;YAAK+L,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJ3M,OAAA;UAAA8L,QAAA,eACE9L,OAAA;YACEiM,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB9L,OAAA;cACEqM,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP3M,OAAA;UAAK+L,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL1F,eAAe,gBACdjH,OAAA,CAACZ,MAAM;QAAAoN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRzF,aAAa,gBACflH,OAAA,CAACX,KAAK;QAACwK,IAAI,EAAE,OAAQ;QAAC+C,OAAO,EAAE1F;MAAc;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9CvF,QAAQ,gBACVpH,OAAA;QAAA8L,QAAA,gBAEE9L,OAAA;UAAK+L,SAAS,EAAC,8CAA8C;UAAAD,QAAA,gBAE3D9L,OAAA;YAAK+L,SAAS,EAAC,kDAAkD;YAAAD,QAAA,gBAC/D9L,OAAA;cAAK+L,SAAS,EAAC,6BAA6B;cAAAD,QAAA,gBAE1C9L,OAAA;gBAAK+L,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA8M;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAO;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACjE3M,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,GAAApL,qBAAA,GAAE0G,QAAQ,CAAC0F,gBAAgB,cAAApM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA8L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3F,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3M,OAAA;gBAAK+L,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,gBAChC9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA2J;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAA8L,QAAA,gBACE9L,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACpE3M,OAAA;oBAAK+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,GAAAnL,qBAAA,IAAAC,sBAAA,GAAEwG,QAAQ,CAAC2F,YAAY,cAAAnM,sBAAA,uBAArBA,sBAAA,CAAuBoM,SAAS,cAAArM,qBAAA,cAAAA,qBAAA,GAAI;kBAAK;oBAAA6L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cACEiN,OAAO,EAAEA,CAAA,KAAM;gBAAA,IAAAC,qBAAA,EAAAC,sBAAA;gBACbhJ,oBAAoB,EAAA+I,qBAAA,IAAAC,sBAAA,GAAC/F,QAAQ,CAACgG,gBAAgB,cAAAD,sBAAA,uBAAzBA,sBAAA,CAA2B5J,EAAE,cAAA2J,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;gBACzD7I,yBAAyB,CAAC,EAAE,CAAC;gBAC7BJ,WAAW,CAAC,IAAI,CAAC;gBACjBF,YAAY,CAAC,KAAK,CAAC;cACrB,CAAE;cACFgI,SAAS,EAAC,yIAAyI;cAAAD,QAAA,gBAEnJ9L,OAAA;gBACEiM,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnBU,WAAW,EAAC,KAAK;gBACjBT,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,cAAc;gBAAAD,QAAA,eAExB9L,OAAA;kBACEqM,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2X;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9X;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN3M,OAAA;gBAAM+L,SAAS,EAAC,aAAa;gBAAAD,QAAA,EAAC;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN3M,OAAA;YAAK+L,SAAS,EAAC,gEAAgE;YAAAD,QAAA,gBAE7E9L,OAAA;cAAK+L,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChC9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/D9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkf;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACviB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAChEvF,QAAQ,CAACiG,MAAM,gBACdrN,OAAA;kBAAM+L,SAAS,EAAC,qGAAqG;kBAAAD,QAAA,EAAC;gBAEtH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,gBAEP3M,OAAA;kBAAM+L,SAAS,EAAC,qGAAqG;kBAAAD,QAAA,EAAC;gBAEtH;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cAAK+L,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChC9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/D9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAoI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAG;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7D3M,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,GAAAjL,qBAAA,IAAAC,mBAAA,GAAEsG,QAAQ,CAACkG,SAAS,cAAAxM,mBAAA,uBAAlBA,mBAAA,CAAoByM,cAAc,cAAA1M,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cAAK+L,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChC9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/D9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9M;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjE3M,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,GAAA/K,qBAAA,IAAAC,iBAAA,GAAEoG,QAAQ,CAACoG,OAAO,cAAAxM,iBAAA,uBAAhBA,iBAAA,CAAkBgM,SAAS,cAAAjM,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAAyL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cAAK+L,SAAS,EAAC,mBAAmB;cAAAD,QAAA,gBAChC9L,OAAA;gBAAK+L,SAAS,EAAC,kDAAkD;gBAAAD,QAAA,eAC/D9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC/F3M,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAgF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjE3M,OAAA;kBAAK+L,SAAS,EAAC,mBAAmB;kBAAAD,QAAA,GAC/Bf,cAAc,EAAA9J,qBAAA,IAAAC,kBAAA,GAACkG,QAAQ,CAACoG,OAAO,cAAAtM,kBAAA,uBAAhBA,kBAAA,CAAkBuM,eAAe,cAAAxM,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC,eACxDjB,OAAA;oBAAM+L,SAAS,EAAC,oDAAoD;oBAAAD,QAAA,EAAElB,UAAU,EAAAzJ,kBAAA,GAACiG,QAAQ,CAACoG,OAAO,cAAArM,kBAAA,uBAAhBA,kBAAA,CAAkBsM,eAAe;kBAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cAAK+L,SAAS,EAAC,yDAAyD;cAAAD,QAAA,gBACtE9L,OAAA;gBAAK+L,SAAS,EAAC,yDAAyD;gBAAAD,QAAA,eACtE9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,yCAAyC;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrE3M,OAAA;kBAAK+L,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,GAAA1K,qBAAA,GAClCgG,QAAQ,CAACsG,WAAW,cAAAtM,qBAAA,uBAApBA,qBAAA,CAAsB8E,GAAG,CAAC,CAACyH,IAAI,EAAEC,KAAK,kBACrC5N,OAAA;oBAAkB+L,SAAS,EAAG,2EAA0EjB,eAAe,CAAC6C,IAAI,CAACE,mBAAmB,CAAE,EAAE;oBAAA/B,QAAA,EACjJlB,UAAU,CAAC+C,IAAI,CAACE,mBAAmB;kBAAC,GAD5BD,KAAK;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3M,OAAA;UAAK+L,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD9L,OAAA;YAAK+L,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzC9L,OAAA;cACE+L,SAAS,EAAC,mGAAmG;cAC7GC,IAAI,EACF,mBAAmB,GACnB5E,QAAQ,CAAC7D,EAAE,GACX,WAAW,GACXqI,eAAe,CAACtH,UAAU,CAC3B;cAAAwH,QAAA,gBAED9L,OAAA;gBAAA8L,QAAA,eACE9L,OAAA;kBACEiM,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrB0B,KAAK,EAAC,QAAQ;kBAAAhC,QAAA,eAEd9L,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBuM,CAAC,EAAC;kBAA2gB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9gB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACP3M,OAAA;gBAAM+L,SAAS,EAAC,MAAM;gBAAAD,QAAA,EAAC;cAAS;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiDD,CAAC,eACN3M,OAAA;YAAK+L,SAAS,EAAC,qCAAqC;YAAAD,QAAA,eAClD9L,OAAA;cAAK+L,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EACnC,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,EACzB,SAAS,CACV,CAAC5F,GAAG,CAAC,CAAC6H,MAAM,EAAEH,KAAK,kBAClB5N,OAAA;gBAEEiN,OAAO,EAAEA,CAAA,KAAMlD,eAAe,CAACgE,MAAM,CAAE;gBACvChC,SAAS,EAAG,oGACVzH,UAAU,KAAKyJ,MAAM,GACjB,iCAAiC,GACjC,+EACL,EAAE;gBAAAjC,QAAA,EAEFiC;cAAM,GARFH,KAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELrI,UAAU,KAAK,qBAAqB,gBACnCtE,OAAA;YAAK+L,SAAS,EAAC,yDAAyD;YAAAD,QAAA,eAEtE9L,OAAA;cAAK+L,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC9L,OAAA;gBAAK+L,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B9L,OAAA;kBAAK+L,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjE9L,OAAA;oBAAK+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrC9L,OAAA;sBAAK+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAyJ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9M;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAI+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAe;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5B9L,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5D3M,OAAA;sBAAK+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAzK,sBAAA,IAAAC,kBAAA,GAAE8F,QAAQ,CAACoG,OAAO,cAAAlM,kBAAA,uBAAhBA,kBAAA,CAAkB0L,SAAS,cAAA3L,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAAmL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChE3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAAvK,qBAAA,IAAAC,kBAAA,GAAE4F,QAAQ,CAACoG,OAAO,cAAAhM,kBAAA,uBAAhBA,kBAAA,CAAkBwM,SAAS,cAAAzM,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAiL,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjF,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAK;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxD3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAArK,sBAAA,IAAAC,kBAAA,GAAE0F,QAAQ,CAACoG,OAAO,cAAA9L,kBAAA,uBAAhBA,kBAAA,CAAkBuM,aAAa,cAAAxM,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAA+K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAK;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACxD3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,GAAAnK,sBAAA,IAAAC,kBAAA,GAAEwF,QAAQ,CAACoG,OAAO,cAAA5L,kBAAA,uBAAhBA,kBAAA,CAAkBsM,aAAa,cAAAvM,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAA6K,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAQ;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC3D3M,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,GACtDf,cAAc,EAAAlJ,sBAAA,IAAAC,kBAAA,GAACsF,QAAQ,CAACoG,OAAO,cAAA1L,kBAAA,uBAAhBA,kBAAA,CAAkB2L,eAAe,cAAA5L,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC,eACxD7B,OAAA;wBAAM+L,SAAS,EAAC,MAAM;wBAAAD,QAAA,IAAA/J,sBAAA,IAAAC,kBAAA,GAAEoF,QAAQ,CAACoG,OAAO,cAAAxL,kBAAA,uBAAhBA,kBAAA,CAAkBmM,YAAY,cAAApM,sBAAA,cAAAA,sBAAA,GAAI,KAAK,EAAC,IAAE,GAAAE,sBAAA,IAAAC,mBAAA,GAACkF,QAAQ,CAACoG,OAAO,cAAAtL,mBAAA,uBAAhBA,mBAAA,CAAkBuL,eAAe,cAAAxL,sBAAA,cAAAA,sBAAA,GAAI,KAAK;sBAAA;wBAAAuK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3M,OAAA;gBAAK+L,SAAS,EAAC,qEAAqE;gBAAAD,QAAA,gBAClF9L,OAAA;kBAAK+L,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjE9L,OAAA;oBAAK+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrC9L,OAAA;sBAAK+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAA8M;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAI+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAY;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAC5B9L,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAS;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC5D3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,gBACrC9L,OAAA;wBAAM+L,SAAS,EAAC,aAAa;wBAAAD,QAAA,GAAA3J,mBAAA,GAAEiF,QAAQ,CAACgH,SAAS,cAAAjM,mBAAA,cAAAA,mBAAA,GAAI;sBAAK;wBAAAqK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EACjEvF,QAAQ,CAACgH,SAAS,KAAK,SAAS,IAAIhH,QAAQ,CAACiH,cAAc,iBAC1DrO,OAAA;wBAAM+L,SAAS,EAAC,qBAAqB;wBAAAD,QAAA,GAAC,IAAE,EAAC1E,QAAQ,CAACiH,cAAc;sBAAA;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAgB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnE3M,OAAA;sBAAK+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAChDwC,UAAU,CAAClH,QAAQ,CAACmH,WAAW,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGlD,eAAe,EAAAlJ,qBAAA,GAACgF,QAAQ,CAACqH,cAAc,cAAArM,qBAAA,cAAAA,qBAAA,GAAI,EAAE;oBAAC;sBAAAoK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9D3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EACpCwC,UAAU,CAAClH,QAAQ,CAACsH,SAAS,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGlD,eAAe,CAAC,KAAK;oBAAC;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChE3M,OAAA;sBAAK+L,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,EAAE1B,UAAU,CAAChD,QAAQ,CAACuH,SAAS;oBAAC;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3E,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAoB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvE3M,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EACtD,CAAAzJ,sBAAA,GAAA+E,QAAQ,CAACgG,gBAAgB,cAAA/K,sBAAA,eAAzBA,sBAAA,CAA2B2K,SAAS,gBACnChN,OAAA,CAAAE,SAAA;wBAAA4L,QAAA,gBACE9L,OAAA;0BAAK+L,SAAS,EAAC,2EAA2E;0BAAAD,QAAA,eACxF9L,OAAA;4BAAM+L,SAAS,EAAC,oCAAoC;4BAAAD,QAAA,EACjD1E,QAAQ,CAACgG,gBAAgB,CAACJ,SAAS,CAAC4B,MAAM,CAAC,CAAC;0BAAC;4BAAApC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1C;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,EACLvF,QAAQ,CAACgG,gBAAgB,CAACJ,SAAS;sBAAA,eACpC,CAAC,GAEH;oBACD;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC5B9L,OAAA;sBAAK+L,SAAS,EAAC,6BAA6B;sBAAAD,QAAA,EAAC;oBAAW;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAC9D3M,OAAA;sBAAK+L,SAAS,EAAC,4CAA4C;sBAAAD,QAAA,GAAAxJ,qBAAA,GACxD8E,QAAQ,CAACyH,gBAAgB,cAAAvM,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAkK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPrI,UAAU,KAAK,sBAAsB,gBACpCtE,OAAA;YAAA8L,QAAA,gBACE9L,OAAA;cAAK+L,SAAS,EAAC,yDAAyD;cAAAD,QAAA,gBACtE9L,OAAA;gBAAK+L,SAAS,EAAC,oDAAoD;gBAAAD,QAAA,eACjE9L,OAAA;kBAAK+L,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrC9L,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAI+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAmB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN3M,OAAA;gBAAK+L,SAAS,EAAC,KAAK;gBAAAD,QAAA,eAClB9L,OAAA;kBAAK+L,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,gBACpD9L,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAgE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpE3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EACjD1E,QAAQ,CAACyG,mBAAmB,gBAC3B7N,OAAA;0BAAM+L,SAAS,EAAG,2EAA0EjB,eAAe,CAAC1D,QAAQ,CAACyG,mBAAmB,CAAE,EAAE;0BAAA/B,QAAA,EACzIlB,UAAU,CAACxD,QAAQ,CAACyG,mBAAmB;wBAAC;0BAAArB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC,GAEP;sBACD;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAkD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAY;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAClE3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAE1B,UAAU,CAAChD,QAAQ,CAAC0H,UAAU;sBAAC;wBAAAtC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3M,OAAA;cAAK+L,SAAS,EAAC,yDAAyD;cAAAD,QAAA,eACtE9L,OAAA;gBAAK+L,SAAS,EAAC,QAAQ;gBAAAD,QAAA,gBACrB9L,OAAA;kBAAK+L,SAAS,EAAC,oDAAoD;kBAAAD,QAAA,eACjE9L,OAAA;oBAAK+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,gBACrC9L,OAAA;sBAAK+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,eACjD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAslB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3oB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAI+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAuB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAK+L,SAAS,EAAC,KAAK;kBAAAD,QAAA,EAEnB,EAAAvJ,qBAAA,GAAA6E,QAAQ,CAAC2H,mBAAmB,cAAAxM,qBAAA,uBAA5BA,qBAAA,CAA8ByM,MAAM,IAAG,CAAC,gBACvChP,OAAA;oBAAK+L,SAAS,EAAC,WAAW;oBAAAD,QAAA,EACvB1E,QAAQ,CAAC2H,mBAAmB,CAAC7I,GAAG,CAAC,CAAC+I,cAAc,EAAErB,KAAK;sBAAA,IAAAsB,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA;sBAAA,oBACtDrP,OAAA;wBAAiB+L,SAAS,EAAC,oFAAoF;wBAAAD,QAAA,gBAE7G9L,OAAA;0BAAK+L,SAAS,EAAC,oDAAoD;0BAAAD,QAAA,eACjE9L,OAAA;4BAAK+L,SAAS,EAAC,8DAA8D;4BAAAD,QAAA,gBAC3E9L,OAAA;8BAAK+L,SAAS,EAAC,gCAAgC;8BAAAD,QAAA,gBAC7C9L,OAAA;gCAAK+L,SAAS,EAAC,kDAAkD;gCAAAD,QAAA,eAC/D9L,OAAA;kCAAKiM,KAAK,EAAC,4BAA4B;kCAACC,IAAI,EAAC,MAAM;kCAACC,OAAO,EAAC,WAAW;kCAACU,WAAW,EAAC,KAAK;kCAACT,MAAM,EAAC,cAAc;kCAACL,SAAS,EAAC,wBAAwB;kCAAAD,QAAA,eAChJ9L,OAAA;oCAAMqM,aAAa,EAAC,OAAO;oCAACC,cAAc,EAAC,OAAO;oCAACC,CAAC,EAAC;kCAAmO;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACxR;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,eACN3M,OAAA;gCAAI+L,SAAS,EAAC,4BAA4B;gCAAAD,QAAA,GAAC,eAAa,EAAC,EAAAoD,sBAAA,GAAA9H,QAAQ,CAAC2H,mBAAmB,cAAAG,sBAAA,uBAA5BA,sBAAA,CAA8BF,MAAM,IAACpB,KAAK;8BAAA;gCAAApB,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAK,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtG,CAAC,eACN3M,OAAA;8BAAK+L,SAAS,EAAC,2CAA2C;8BAAAD,QAAA,GACvDmD,cAAc,CAAClC,YAAY,iBAC1B/M,OAAA;gCAAK+L,SAAS,EAAC,+DAA+D;gCAAAD,QAAA,gBAC5E9L,OAAA;kCAAK+L,SAAS,EAAC,sCAAsC;kCAAAD,QAAA,eACnD9L,OAAA;oCAAKiM,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJ9L,OAAA;sCAAMqM,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAyJ;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAC9M;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACN3M,OAAA;kCAAA8L,QAAA,GAAM,aACO,EAAC,EAAAqD,qBAAA,GAAAF,cAAc,CAAClC,YAAY,cAAAoC,qBAAA,uBAA3BA,qBAAA,CAA6BnC,SAAS,OAAAoC,sBAAA,GAAIH,cAAc,CAAClC,YAAY,cAAAqC,sBAAA,uBAA3BA,sBAAA,CAA6BE,KAAK,KAAI,MAAM,EACjGL,cAAc,CAACM,UAAU,iBACxBvP,OAAA;oCAAM+L,SAAS,EAAC,qBAAqB;oCAAAD,QAAA,GAAC,KACjC,EAAC1B,UAAU,CAAC6E,cAAc,CAACM,UAAU,CAAC;kCAAA;oCAAA/C,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrC,CACP;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACG,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACJ,CACN,EACAsC,cAAc,CAACO,gBAAgB,iBAC9BxP,OAAA;gCAAK+L,SAAS,EAAC,gFAAgF;gCAAAD,QAAA,EAC5F1B,UAAU,CAAC6E,cAAc,CAACO,gBAAgB;8BAAC;gCAAAhD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACzC,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eAGN3M,OAAA;0BAAK+L,SAAS,EAAC,WAAW;0BAAAD,QAAA,gBAExB9L,OAAA;4BAAK+L,SAAS,EAAC,MAAM;4BAAAD,QAAA,eACnB9L,OAAA;8BAAK+L,SAAS,EAAC,uCAAuC;8BAAAD,QAAA,GACnDmD,cAAc,CAACQ,UAAU,iBACxBzP,OAAA;gCAAK+L,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5D9L,OAAA;kCAAK+L,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvD9L,OAAA;oCAAKiM,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJ9L,OAAA;sCAAMqM,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAmG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxJ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACN3M,OAAA;kCAAA8L,QAAA,gBACE9L,OAAA;oCAAM+L,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAsB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC5E3M,OAAA;oCAAM+L,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAE1B,UAAU,CAAC6E,cAAc,CAACQ,UAAU;kCAAC;oCAAAjD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChG,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN,EAEAsC,cAAc,CAACS,QAAQ,iBACtB1P,OAAA;gCAAK+L,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5D9L,OAAA;kCAAK+L,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvD9L,OAAA;oCAAKiM,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,eAChJ9L,OAAA;sCAAMqM,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAmG;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACxJ;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACN3M,OAAA;kCAAA8L,QAAA,gBACE9L,OAAA;oCAAM+L,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAoB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eAC1E3M,OAAA;oCAAM+L,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAE1B,UAAU,CAAC6E,cAAc,CAACS,QAAQ;kCAAC;oCAAAlD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC9F,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN,EAEAsC,cAAc,CAACU,gBAAgB,iBAC9B3P,OAAA;gCAAK+L,SAAS,EAAC,+CAA+C;gCAAAD,QAAA,gBAC5D9L,OAAA;kCAAK+L,SAAS,EAAC,0CAA0C;kCAAAD,QAAA,eACvD9L,OAAA;oCAAKiM,KAAK,EAAC,4BAA4B;oCAACC,IAAI,EAAC,MAAM;oCAACC,OAAO,EAAC,WAAW;oCAACU,WAAW,EAAC,KAAK;oCAACT,MAAM,EAAC,cAAc;oCAACL,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,gBAChJ9L,OAAA;sCAAMqM,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAuC;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,eAC/F3M,OAAA;sCAAMqM,aAAa,EAAC,OAAO;sCAACC,cAAc,EAAC,OAAO;sCAACC,CAAC,EAAC;oCAAgF;sCAAAC,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACrI;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACH,CAAC,eACN3M,OAAA;kCAAA8L,QAAA,gBACE9L,OAAA;oCAAM+L,SAAS,EAAC,8BAA8B;oCAAAD,QAAA,EAAC;kCAAgB;oCAAAU,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAM,CAAC,eACtE3M,OAAA;oCAAM+L,SAAS,EAAC,oCAAoC;oCAAAD,QAAA,EAAEmD,cAAc,CAACU;kCAAgB;oCAAAnD,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAO,CAAC;gCAAA;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1F,CAAC;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CACN;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,EAGL,EAAA0C,qBAAA,GAAAJ,cAAc,CAACW,iBAAiB,cAAAP,qBAAA,uBAAhCA,qBAAA,CAAkCL,MAAM,IAAG,CAAC,iBAC3ChP,OAAA;4BAAA8L,QAAA,gBACE9L,OAAA;8BAAK+L,SAAS,EAAC,wBAAwB;8BAAAD,QAAA,gBACrC9L,OAAA;gCAAK+L,SAAS,EAAC,oCAAoC;gCAAAD,QAAA,eACjD9L,OAAA;kCAAKiM,KAAK,EAAC,4BAA4B;kCAACC,IAAI,EAAC,MAAM;kCAACC,OAAO,EAAC,WAAW;kCAACU,WAAW,EAAC,KAAK;kCAACT,MAAM,EAAC,cAAc;kCAACL,SAAS,EAAC,wBAAwB;kCAAAD,QAAA,eAChJ9L,OAAA;oCAAMqM,aAAa,EAAC,OAAO;oCAACC,cAAc,EAAC,OAAO;oCAACC,CAAC,EAAC;kCAA2X;oCAAAC,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAChb;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACH,CAAC,eACN3M,OAAA;gCAAI+L,SAAS,EAAC,oCAAoC;gCAAAD,QAAA,EAAC;8BAAS;gCAAAU,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAI,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9D,CAAC,eAEN3M,OAAA;8BAAK+L,SAAS,EAAC,WAAW;8BAAAD,QAAA,EACvBmD,cAAc,CAACW,iBAAiB,CAAC1J,GAAG,CAAC,CAAC2J,eAAe,EAAEC,GAAG;gCAAA,IAAAC,qBAAA,EAAAC,sBAAA;gCAAA,oBACzDhQ,OAAA;kCAAe+L,SAAS,EAAC,4EAA4E;kCAAAD,QAAA,gBACnG9L,OAAA;oCAAK+L,SAAS,EAAC,wBAAwB;oCAAAD,QAAA,gBACrC9L,OAAA;sCAAK+L,SAAS,EAAC,+EAA+E;sCAAAD,QAAA,eAC5F9L,OAAA;wCAAKiM,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,wBAAwB;wCAAAD,QAAA,eAChJ9L,OAAA;0CAAMqM,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAyJ;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAC9M;oCAAC;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CAAC,eACN3M,OAAA;sCAAA8L,QAAA,gBACE9L,OAAA;wCAAM+L,SAAS,EAAC,oCAAoC;wCAAAD,QAAA,EAAE,EAAAiE,qBAAA,GAAAF,eAAe,CAACI,QAAQ,cAAAF,qBAAA,uBAAxBA,qBAAA,CAA0B/C,SAAS,KAAI;sCAAK;wCAAAR,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC,eAC1G3M,OAAA;wCAAM+L,SAAS,EAAC,mEAAmE;wCAAAD,QAAA,EAAE+D,eAAe,CAACK,YAAY,IAAI;sCAAK;wCAAA1D,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAAO,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAC/H,CAAC;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACH,CAAC,eAEN3M,OAAA;oCAAK+L,SAAS,EAAC,6CAA6C;oCAAAD,QAAA,GACzD+D,eAAe,CAACM,kBAAkB,iBACjCnQ,OAAA;sCAAK+L,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChC9L,OAAA;wCAAKiM,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJ9L,OAAA;0CAAMqM,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAkd;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACvgB,CAAC,eACN3M,OAAA;wCAAA8L,QAAA,gBACE9L,OAAA;0CAAM+L,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAU;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAChE3M,OAAA;0CAAM+L,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAE+D,eAAe,CAACM;wCAAkB;0CAAA3D,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACjF,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN,EAEAkD,eAAe,CAACO,aAAa,iBAC5BpQ,OAAA;sCAAK+L,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChC9L,OAAA;wCAAKiM,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJ9L,OAAA;0CAAMqM,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAmO;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxR,CAAC,eACN3M,OAAA;wCAAA8L,QAAA,gBACE9L,OAAA;0CAAM+L,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAU;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAChE3M,OAAA;0CAAM+L,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAE1B,UAAU,CAACyF,eAAe,CAACO,aAAa;wCAAC;0CAAA5D,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxF,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN,EAEA,EAAAqD,sBAAA,GAAAH,eAAe,CAACI,QAAQ,cAAAD,sBAAA,uBAAxBA,sBAAA,CAA0BK,KAAK,kBAC9BrQ,OAAA;sCAAK+L,SAAS,EAAC,mBAAmB;sCAAAD,QAAA,gBAChC9L,OAAA;wCAAKiM,KAAK,EAAC,4BAA4B;wCAACC,IAAI,EAAC,MAAM;wCAACC,OAAO,EAAC,WAAW;wCAACU,WAAW,EAAC,KAAK;wCAACT,MAAM,EAAC,cAAc;wCAACL,SAAS,EAAC,6BAA6B;wCAAAD,QAAA,eACrJ9L,OAAA;0CAAMqM,aAAa,EAAC,OAAO;0CAACC,cAAc,EAAC,OAAO;0CAACC,CAAC,EAAC;wCAAmW;0CAAAC,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAE;sCAAC;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OACxZ,CAAC,eACN3M,OAAA;wCAAA8L,QAAA,gBACE9L,OAAA;0CAAM+L,SAAS,EAAC,8BAA8B;0CAAAD,QAAA,EAAC;wCAAO;0CAAAU,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAM,CAAC,eAC7D3M,OAAA;0CAAM+L,SAAS,EAAC,wBAAwB;0CAAAD,QAAA,EAAE+D,eAAe,CAACI,QAAQ,CAACI;wCAAK;0CAAA7D,QAAA,EAAAC,YAAA;0CAAAC,UAAA;0CAAAC,YAAA;wCAAA,OAAO,CAAC;sCAAA;wCAAAH,QAAA,EAAAC,YAAA;wCAAAC,UAAA;wCAAAC,YAAA;sCAAA,OAC7E,CAAC;oCAAA;sCAAAH,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OACH,CACN;kCAAA;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACE,CAAC;gCAAA,GAjDEmD,GAAG;kCAAAtD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAkDR,CAAC;8BAAA,CACP;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CACN;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA,GA9JEiB,KAAK;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA+JV,CAAC;oBAAA,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN3M,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,mFAAmF;sBAAAD,QAAA,eAChG9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAslB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3oB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAI+L,SAAS,EAAC,iCAAiC;sBAAAD,QAAA,EAAC;oBAA2B;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChF3M,OAAA;sBAAG+L,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,EAAC;oBAAwD;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9F;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,GACN,IAAI,EAEPrI,UAAU,KAAK,iBAAiB,gBAC/BtE,OAAA;YAAK+L,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtE9L,OAAA;cAAK+L,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjE9L,OAAA;gBAAK+L,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrC9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAmQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAI+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAe;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3M,OAAA;cAAK+L,SAAS,EAAC,KAAK;cAAAD,QAAA,EACjB,EAAAtJ,qBAAA,GAAA4E,QAAQ,CAACkJ,eAAe,cAAA9N,qBAAA,uBAAxBA,qBAAA,CAA0BwM,MAAM,IAAG,CAAC,gBACnChP,OAAA;gBAAK+L,SAAS,EAAC,uCAAuC;gBAAAD,QAAA,GAAArJ,sBAAA,GACnD2E,QAAQ,CAACkJ,eAAe,cAAA7N,sBAAA,uBAAxBA,sBAAA,CAA0ByD,GAAG,CAAC,CAACqK,IAAI,EAAE3C,KAAK,kBACzC5N,OAAA;kBAEEgM,IAAI,EAAEzM,WAAW,GAAGgR,IAAI,CAACpK,IAAK;kBAC9BqK,MAAM,EAAC,QAAQ;kBACfC,GAAG,EAAC,qBAAqB;kBACzB1E,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,eAEtE9L,OAAA;oBAAK+L,SAAS,EAAC,8FAA8F;oBAAAD,QAAA,gBAC3G9L,OAAA;sBAAK+L,SAAS,EAAC,0DAA0D;sBAAAD,QAAA,eACvE9L,OAAA;wBACEiM,KAAK,EAAC,4BAA4B;wBAClCE,OAAO,EAAC,WAAW;wBACnBD,IAAI,EAAC,cAAc;wBACnBH,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,gBAElC9L,OAAA;0BAAMuM,CAAC,EAAC;wBAAqN;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAChO3M,OAAA;0BAAMuM,CAAC,EAAC;wBAAuI;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/I;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAK+L,SAAS,EAAC,iBAAiB;sBAAAD,QAAA,gBAC9B9L,OAAA;wBAAK+L,SAAS,EAAC,oFAAoF;wBAAAD,QAAA,EAChGyE,IAAI,CAACG;sBAAS;wBAAAlE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ,CAAC,eACN3M,OAAA;wBAAK+L,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,GAAEyE,IAAI,CAACI,SAAS,EAAC,KAAG;sBAAA;wBAAAnE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAxBDiB,KAAK;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyBT,CACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAEN3M,OAAA;gBAAK+L,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,gBAC/B9L,OAAA;kBAAK+L,SAAS,EAAC,mFAAmF;kBAAAD,QAAA,eAChG9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAmQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAI+L,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE3M,OAAA;kBAAG+L,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,EAAC;gBAAwD;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPrI,UAAU,KAAK,UAAU,gBACxBtE,OAAA;YAAK+L,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtE9L,OAAA;cAAK+L,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjE9L,OAAA;gBAAK+L,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrC9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAkf;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACviB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAI+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAe;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3M,OAAA;cAAK+L,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClB9L,OAAA;gBAAK+L,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzD9L,OAAA;kBAAK+L,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxB9L,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAmQ;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpE3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,GAAApJ,qBAAA,GAAE0E,QAAQ,CAACwJ,cAAc,cAAAlO,qBAAA,cAAAA,qBAAA,GAAI;sBAAK;wBAAA8J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAmG;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAW;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjE3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAE1B,UAAU,CAAChD,QAAQ,CAACyJ,WAAW,CAAC,IAAI;sBAAK;wBAAArE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAA4O;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjS;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAM;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC5D3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,GAAC,IAAE,EAACwC,UAAU,CAAClH,QAAQ,CAAC0J,cAAc,IAAI,CAAC,CAAC,CAACtC,OAAO,CAAC,CAAC,CAAC;sBAAA;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,WAAW;kBAAAD,QAAA,gBACxB9L,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAkD;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eAC9D3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAG;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN3M,OAAA;oBAAK+L,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAC5D9L,OAAA;sBAAK+L,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,eACvD9L,OAAA;wBAAKiM,KAAK,EAAC,4BAA4B;wBAACC,IAAI,EAAC,MAAM;wBAACC,OAAO,EAAC,WAAW;wBAACU,WAAW,EAAC,KAAK;wBAACT,MAAM,EAAC,cAAc;wBAACL,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,eAChJ9L,OAAA;0BAAMqM,aAAa,EAAC,OAAO;0BAACC,cAAc,EAAC,OAAO;0BAACC,CAAC,EAAC;wBAAuB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACN3M,OAAA;sBAAA8L,QAAA,gBACE9L,OAAA;wBAAM+L,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,EAAC;sBAAc;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACpE3M,OAAA;wBAAM+L,SAAS,EAAC,oCAAoC;wBAAAD,QAAA,EAAC;sBAAG;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3M,OAAA;gBAAK+L,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB9L,OAAA;kBAAK+L,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrC9L,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAI+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,EAEL,EAAAhK,qBAAA,GAAAyE,QAAQ,CAAC2J,eAAe,cAAApO,qBAAA,uBAAxBA,qBAAA,CAA0BqM,MAAM,IAAG,CAAC,gBACnChP,OAAA;kBAAK+L,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,GAAAlJ,sBAAA,GACnDwE,QAAQ,CAAC2J,eAAe,cAAAnO,sBAAA,uBAAxBA,sBAAA,CAA0BsD,GAAG,CAAC,CAACqK,IAAI,EAAET,GAAG,kBACvC9P,OAAA;oBAEEgM,IAAI,EAAEzM,WAAW,GAAGgR,IAAI,CAACpK,IAAK;oBAC9BqK,MAAM,EAAC,QAAQ;oBACfC,GAAG,EAAC,qBAAqB;oBACzB1E,SAAS,EAAC,4DAA4D;oBAAAD,QAAA,eAEtE9L,OAAA;sBAAK+L,SAAS,EAAC,8FAA8F;sBAAAD,QAAA,gBAC3G9L,OAAA;wBAAK+L,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,eACvE9L,OAAA;0BACEiM,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBH,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,gBAElC9L,OAAA;4BAAMuM,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO3M,OAAA;4BAAMuM,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN3M,OAAA;wBAAK+L,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC9B9L,OAAA;0BAAK+L,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAChGyE,IAAI,CAACG;wBAAS;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN3M,OAAA;0BAAK+L,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,GAAEyE,IAAI,CAACI,SAAS,EAAC,KAAG;wBAAA;0BAAAnE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAxBDmD,GAAG;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBP,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAEN3M,OAAA;kBAAK+L,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,gBACvD9L,OAAA;oBAAK+L,SAAS,EAAC,yFAAyF;oBAAAD,QAAA,eACtG9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAG+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EAAC;kBAA2C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAEPrI,UAAU,KAAK,yBAAyB,gBACvCtE,OAAA;YAAK+L,SAAS,EAAC,yDAAyD;YAAAD,QAAA,gBACtE9L,OAAA;cAAK+L,SAAS,EAAC,oDAAoD;cAAAD,QAAA,eACjE9L,OAAA;gBAAK+L,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,gBACrC9L,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAChJ9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAAme;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxhB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAI+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN3M,OAAA;cAAK+L,SAAS,EAAC,KAAK;cAAAD,QAAA,gBAClB9L,OAAA;gBAAK+L,SAAS,EAAC,4CAA4C;gBAAAD,QAAA,gBACzD9L,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5D9L,OAAA;oBAAK+L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5E;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAM+L,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAoB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1E3M,OAAA;sBAAM+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAjJ,qBAAA,GAAEuE,QAAQ,CAAC4J,gBAAgB,cAAAnO,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAA2J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5D9L,OAAA;oBAAK+L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAA4S;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjW;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAM+L,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACvE3M,OAAA;sBAAM+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAAhJ,sBAAA,IAAAC,oBAAA,GAAEqE,QAAQ,CAACkG,SAAS,cAAAvK,oBAAA,uBAAlBA,oBAAA,CAAoBwK,cAAc,cAAAzK,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5D9L,OAAA;oBAAK+L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAM+L,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnE3M,OAAA;sBAAM+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAA9I,sBAAA,GAAEoE,QAAQ,CAAC0F,gBAAgB,cAAA9J,sBAAA,cAAAA,sBAAA,GAAI;oBAAK;sBAAAwJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN3M,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5D9L,OAAA;oBAAK+L,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,eACvD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAA8T;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAA8L,QAAA,gBACE9L,OAAA;sBAAM+L,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnE3M,OAAA;sBAAM+L,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,GAAA7I,qBAAA,GAAEmE,QAAQ,CAAC6J,aAAa,cAAAhO,qBAAA,cAAAA,qBAAA,GAAI;oBAAK;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1F,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN3M,OAAA;gBAAK+L,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB9L,OAAA;kBAAK+L,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrC9L,OAAA;oBAAK+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,eACjD9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAI+L,SAAS,EAAC,oCAAoC;oBAAAD,QAAA,EAAC;kBAAkB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,EAEL,EAAAzJ,qBAAA,GAAAkE,QAAQ,CAAC8J,oBAAoB,cAAAhO,qBAAA,uBAA7BA,qBAAA,CAA+B8L,MAAM,IAAG,CAAC,gBACxChP,OAAA;kBAAK+L,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,GAAA3I,sBAAA,GACnDiE,QAAQ,CAAC8J,oBAAoB,cAAA/N,sBAAA,uBAA7BA,sBAAA,CAA+B+C,GAAG,CAAC,CAACqK,IAAI,EAAET,GAAG,kBAC5C9P,OAAA;oBAEEgM,IAAI,EAAEzM,WAAW,GAAGgR,IAAI,CAACpK,IAAK;oBAC9BqK,MAAM,EAAC,QAAQ;oBACfC,GAAG,EAAC,qBAAqB;oBACzB1E,SAAS,EAAC,4DAA4D;oBAAAD,QAAA,eAEtE9L,OAAA;sBAAK+L,SAAS,EAAC,8FAA8F;sBAAAD,QAAA,gBAC3G9L,OAAA;wBAAK+L,SAAS,EAAC,0DAA0D;wBAAAD,QAAA,eACvE9L,OAAA;0BACEiM,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnBH,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,gBAElC9L,OAAA;4BAAMuM,CAAC,EAAC;0BAAqN;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChO3M,OAAA;4BAAMuM,CAAC,EAAC;0BAAuI;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN3M,OAAA;wBAAK+L,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC9B9L,OAAA;0BAAK+L,SAAS,EAAC,oFAAoF;0BAAAD,QAAA,EAChGyE,IAAI,CAACG;wBAAS;0BAAAlE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACN3M,OAAA;0BAAK+L,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,GAAEyE,IAAI,CAACI,SAAS,EAAC,KAAG;wBAAA;0BAAAnE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAxBDmD,GAAG;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyBP,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAEN3M,OAAA;kBAAK+L,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,gBACvD9L,OAAA;oBAAK+L,SAAS,EAAC,yFAAyF;oBAAAD,QAAA,eACtG9L,OAAA;sBAAKiM,KAAK,EAAC,4BAA4B;sBAACC,IAAI,EAAC,MAAM;sBAACC,OAAO,EAAC,WAAW;sBAACU,WAAW,EAAC,KAAK;sBAACT,MAAM,EAAC,cAAc;sBAACL,SAAS,EAAC,wBAAwB;sBAAAD,QAAA,eAChJ9L,OAAA;wBAAMqM,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC,OAAO;wBAACC,CAAC,EAAC;sBAAmQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxT;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN3M,OAAA;oBAAG+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,EAAC;kBAAiD;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxF,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACJ,IAAI,EAGPrI,UAAU,KAAK,SAAS,gBACvBtE,OAAA,CAACV,WAAW;YACV6R,WAAW,EAAE;cACX5H,OAAO,EAAEA,OAAO;cAChB7F,IAAI,EAAE8F,kBAAkB;cACxB9B,KAAK,EAAE+B,iBAAiB;cACxB2H,KAAK,EAAE,CAAA7H,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyF,MAAM,KAAI;YAC5B,CAAE;YACFlI,OAAO,EAAEuC,cAAe;YACxBtC,KAAK,EAAEuC;UAAa;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,GACA,IAAI;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGL,CAAC,eAEN3M,OAAA;UAAK+L,SAAS,EAAC,8CAA8C;UAAAD,QAAA,gBAC3D9L,OAAA;YAAK+L,SAAS,EAAC,wBAAwB;YAAAD,QAAA,gBACrC9L,OAAA;cAAK+L,SAAS,EAAC,oCAAoC;cAAAD,QAAA,eACjD9L,OAAA;gBAAKiM,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACU,WAAW,EAAC,KAAK;gBAACT,MAAM,EAAC,cAAc;gBAACL,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,eAChJ9L,OAAA;kBAAMqM,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAAkW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN3M,OAAA;cAAI+L,SAAS,EAAC,oCAAoC;cAAAD,QAAA,EAAC;YAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eAEN3M,OAAA;YAAK+L,SAAS,EAAC,uCAAuC;YAAAD,QAAA,gBAEpD9L,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO+L,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,EAAC;cAEnE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3M,OAAA;gBAAK+L,SAAS,EAAC,UAAU;gBAAAD,QAAA,gBACvB9L,OAAA;kBACEqR,KAAK,EAAE7M,YAAa;kBACpB8M,QAAQ,EAAGC,CAAC,IAAK9M,eAAe,CAAC8M,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE;kBACjDG,WAAW,EAAC,2BAA2B;kBACvCzF,SAAS,EAAG,kDACVrH,iBAAiB,GACb,8DAA8D,GAC9D,8DACL;gBAA+G;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACXjI,iBAAiB,iBAChB1E,OAAA;kBAAK+L,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,gBAC5D9L,OAAA;oBAAKiM,KAAK,EAAC,4BAA4B;oBAACC,IAAI,EAAC,MAAM;oBAACC,OAAO,EAAC,WAAW;oBAACU,WAAW,EAAC,KAAK;oBAACT,MAAM,EAAC,cAAc;oBAACL,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,eAC1I9L,OAAA;sBAAMqM,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,CAAC,EAAC;oBAA6E;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClI,CAAC,EACLjI,iBAAiB;gBAAA;kBAAA8H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN3M,OAAA;cAAA8L,QAAA,gBACE9L,OAAA;gBAAO+L,SAAS,EAAC,iDAAiD;gBAAAD,QAAA,EAAC;cAEnE;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR3M,OAAA;gBAAA,GACM2F,eAAe,CAAC;kBAClBoG,SAAS,EAAE;gBACb,CAAC,CAAC;gBACFA,SAAS,EAAC,4LAA4L;gBAAAD,QAAA,gBAEtM9L,OAAA;kBAAA,GAAW6F,gBAAgB,CAAC;gBAAC;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACjC3M,OAAA;kBAAK+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,eACjD9L,OAAA;oBACEiM,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBU,WAAW,EAAC,KAAK;oBACjBT,MAAM,EAAC,cAAc;oBACrBL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAElC9L,OAAA;sBACEqM,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAA4G;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/G;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBAAG+L,SAAS,EAAC,oCAAoC;kBAAAD,QAAA,gBAC/C9L,OAAA;oBAAM+L,SAAS,EAAC,4BAA4B;oBAAAD,QAAA,EAAC;kBAAe;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,qBACrE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ3M,OAAA;kBAAG+L,SAAS,EAAC,6BAA6B;kBAAAD,QAAA,EAAC;gBAE3C;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGL,CAAAnH,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwJ,MAAM,IAAG,CAAC,iBACxBhP,OAAA;YAAK+L,SAAS,EAAC,MAAM;YAAAD,QAAA,gBACnB9L,OAAA;cAAI+L,SAAS,EAAC,yCAAyC;cAAAD,QAAA,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3E3M,OAAA;cAAK+L,SAAS,EAAC,sDAAsD;cAAAD,QAAA,EAClEtG,aAAa,CAACU,GAAG,CAAC,CAACC,IAAI,EAAE2J,GAAG,kBAC3B9P,OAAA;gBACE+L,SAAS,EAAC,8DAA8D;gBAAAD,QAAA,gBAGxE9L,OAAA;kBAAK+L,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,eACzD9L,OAAA;oBACEyR,GAAG,EAAEtL,IAAI,CAACG,OAAQ;oBAClByF,SAAS,EAAC,gCAAgC;oBAC1C2F,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,GAAG,IAAI;sBACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,GAAG,yBAAyB;oBAC1C,CAAE;oBACFI,GAAG,EAAC;kBAAS;oBAAArF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACN3M,OAAA;kBAAK+L,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,gBACrC9L,OAAA;oBAAK+L,SAAS,EAAC,6CAA6C;oBAAAD,QAAA,EACzD3F,IAAI,CAAC2L;kBAAI;oBAAAtF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACN3M,OAAA;oBAAK+L,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,GACpC,CAAC3F,IAAI,CAAC4L,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEvD,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;kBAAA;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN3M,OAAA;kBACEiN,OAAO,EAAEA,CAAA,KAAM;oBACbxH,gBAAgB,CAAEQ,SAAS,IACzBA,SAAS,CAAC+L,MAAM,CAAC,CAACC,CAAC,EAAEC,aAAa,KAAKpC,GAAG,KAAKoC,aAAa,CAC9D,CAAC;kBACH,CAAE;kBACFnG,SAAS,EAAC,iJAAiJ;kBAC3JX,KAAK,EAAC,aAAa;kBAAAU,QAAA,eAEnB9L,OAAA;oBACEiM,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnBU,WAAW,EAAC,KAAK;oBACjBT,MAAM,EAAC,cAAc;oBACrBL,SAAS,EAAC,wBAAwB;oBAAAD,QAAA,eAElC9L,OAAA;sBACEqM,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA,GA5CJxG,IAAI,CAAC2L,IAAI,GAAGhC,GAAG;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA6CjB,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGD3M,OAAA;YAAK+L,SAAS,EAAC,MAAM;YAAAD,QAAA,eACnB9L,OAAA;cACEmS,QAAQ,EAAElK,qBAAsB;cAChCgF,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAImF,KAAK,GAAG,IAAI;gBAChBzN,oBAAoB,CAAC,EAAE,CAAC;gBAExB,IAAIH,YAAY,KAAK,EAAE,IAAIgB,aAAa,CAACwJ,MAAM,KAAK,CAAC,EAAE;kBACrDrK,oBAAoB,CAAC,yCAAyC,CAAC;kBAC/DyN,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAIA,KAAK,EAAE;kBACT9O,QAAQ,CACN3E,iBAAiB,CACf;oBACE0T,OAAO,EAAE7N,YAAY;oBACrB8N,YAAY,EAAE9M;kBAChB,CAAC,EACDjC,EACF,CACF,CAAC;gBACH,CAAC,MAAM;kBACL5D,KAAK,CAACoH,KAAK,CACT,oDACF,CAAC;gBACH;cACF,CAAE;cACFgF,SAAS,EAAG,qEACV9D,qBAAqB,GACjB,gDAAgD,GAChD,2EACL,EAAE;cAAA6D,QAAA,EAEF7D,qBAAqB,gBACpBjI,OAAA,CAAAE,SAAA;gBAAA4L,QAAA,gBACE9L,OAAA;kBAAK+L,SAAS,EAAC,gDAAgD;kBAACE,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAAAL,QAAA,gBAChI9L,OAAA;oBAAQ+L,SAAS,EAAC,YAAY;oBAACwG,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,IAAI;oBAACrG,MAAM,EAAC,cAAc;oBAACS,WAAW,EAAC;kBAAG;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACrG3M,OAAA;oBAAM+L,SAAS,EAAC,YAAY;oBAACG,IAAI,EAAC,cAAc;oBAACK,CAAC,EAAC;kBAAiH;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzK,CAAC,aAER;cAAA,eAAE,CAAC,gBAEH3M,OAAA,CAAAE,SAAA;gBAAA4L,QAAA,gBACE9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,cAAc;kBAAAD,QAAA,eACtI9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvZ,CAAC,eAER;cAAA,eAAE;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN3M,OAAA;YAAK+L,SAAS,EAAC,MAAM;YAAAD,QAAA,EAClBtE,kBAAkB,gBACjBxH,OAAA;cAAK+L,SAAS,EAAC,uCAAuC;cAAAD,QAAA,eACpD9L,OAAA;gBAAK+L,SAAS,EAAC;cAA4E;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,GACJlF,gBAAgB,gBAClBzH,OAAA;cAAK+L,SAAS,EAAC,8DAA8D;cAAAD,QAAA,gBAC3E9L,OAAA;gBAAKiM,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAACU,WAAW,EAAC,KAAK;gBAACT,MAAM,EAAC,cAAc;gBAACL,SAAS,EAAC,cAAc;gBAAAD,QAAA,eACtI9L,OAAA;kBAAMqM,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,CAAC,EAAC;gBAA6E;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClI,CAAC,eACN3M,OAAA;gBAAA8L,QAAA,EAAOrE;cAAgB;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,GACJpF,QAAQ,IAAIA,QAAQ,CAACyH,MAAM,GAAG,CAAC,gBACjChP,OAAA;cAAK+L,SAAS,EAAC,WAAW;cAAAD,QAAA,EACvBvE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAErB,GAAG,CAAC,CAACwM,OAAO,EAAE5C,GAAG;gBAAA,IAAA6C,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,cAAA;gBAAA,oBAC1BnT,OAAA;kBAAe+L,SAAS,EAAC,uCAAuC;kBAAAD,QAAA,eAC9D9L,OAAA;oBAAK+L,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,gBAC/B9L,OAAA;sBAAK+L,SAAS,EAAC,oBAAoB;sBAAAD,QAAA,EAChC4G,OAAO,CAACU,WAAW,GAClB,CAAAT,oBAAA,GAAAD,OAAO,CAACU,WAAW,cAAAT,oBAAA,eAAnBA,oBAAA,CAAqBU,KAAK,gBACxBrT,OAAA;wBACE+L,SAAS,EAAC,qEAAqE;wBAC/E0F,GAAG,EAAElS,WAAW,KAAAqT,qBAAA,GAAGF,OAAO,CAACU,WAAW,cAAAR,qBAAA,uBAAnBA,qBAAA,CAAqBS,KAAK,CAAC;wBAC9C3B,OAAO,EAAGC,CAAC,IAAK;0BACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,GAAG,IAAI;0BACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,GAAG,yBAAyB;wBAC1C,CAAE;wBACFI,GAAG,EAAE,EAAAgB,qBAAA,GAAAH,OAAO,CAACU,WAAW,cAAAP,qBAAA,uBAAnBA,qBAAA,CAAqB7F,SAAS,KAAI;sBAAO;wBAAAR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,gBAEF3M,OAAA;wBAAK+L,SAAS,EAAC,2FAA2F;wBAAAD,QAAA,eACxG9L,OAAA;0BAAM+L,SAAS,EAAC,qBAAqB;0BAAAD,QAAA,GAClC,CAAAgH,qBAAA,GAAAJ,OAAO,CAACU,WAAW,cAAAN,qBAAA,eAAnBA,qBAAA,CAAqBQ,UAAU,IAAAP,qBAAA,GAC5BL,OAAO,CAACU,WAAW,cAAAL,qBAAA,uBAAnBA,qBAAA,CAAqBO,UAAU,CAAC,CAAC,CAAC,GAClC,EAAE,EACL,CAAAN,qBAAA,GAAAN,OAAO,CAACU,WAAW,cAAAJ,qBAAA,eAAnBA,qBAAA,CAAqBO,SAAS,IAAAN,qBAAA,GAC3BP,OAAO,CAACU,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBM,SAAS,CAAC,CAAC,CAAC,GACjC,EAAE;wBAAA;0BAAA/G,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CACN,gBAED3M,OAAA;wBAAK+L,SAAS,EAAC,gFAAgF;wBAAAD,QAAA,eAC7F9L,OAAA;0BAAKiM,KAAK,EAAC,4BAA4B;0BAACC,IAAI,EAAC,MAAM;0BAACC,OAAO,EAAC,WAAW;0BAACU,WAAW,EAAC,KAAK;0BAACT,MAAM,EAAC,cAAc;0BAACL,SAAS,EAAC,wBAAwB;0BAAAD,QAAA,eAChJ9L,OAAA;4BAAMqM,aAAa,EAAC,OAAO;4BAACC,cAAc,EAAC,OAAO;4BAACC,CAAC,EAAC;0BAAyJ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9M;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAEN3M,OAAA;sBAAK+L,SAAS,EAAC,QAAQ;sBAAAD,QAAA,gBACrB9L,OAAA;wBAAK+L,SAAS,EAAC,gEAAgE;wBAAAD,QAAA,gBAC7E9L,OAAA;0BAAK+L,SAAS,EAAC,4BAA4B;0BAAAD,QAAA,EACxC,EAAAoH,qBAAA,GAAAR,OAAO,CAACU,WAAW,cAAAF,qBAAA,uBAAnBA,qBAAA,CAAqBlG,SAAS,KAAI;wBAAQ;0BAAAR,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eAEN3M,OAAA;0BAAK+L,SAAS,EAAC,uDAAuD;0BAAAD,QAAA,gBACpE9L,OAAA;4BAAK+L,SAAS,EAAC,kCAAkC;4BAAAD,QAAA,eAC/C9L,OAAA;8BAAKiM,KAAK,EAAC,4BAA4B;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAACU,WAAW,EAAC,KAAK;8BAACT,MAAM,EAAC,cAAc;8BAACL,SAAS,EAAC,4BAA4B;8BAAAD,QAAA,eACpJ9L,OAAA;gCAAMqM,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,CAAC,EAAC;8BAAkD;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvG;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACN3M,OAAA;4BAAA8L,QAAA,EAAO1B,UAAU,CAACsI,OAAO,CAACnD,UAAU;0BAAC;4BAAA/C,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,EAE5C+F,OAAO,CAACc,UAAU,iBACjBxT,OAAA;4BACEiN,OAAO,EAAEA,CAAA,KAAM;8BACbhI,gBAAgB,CAACyN,OAAO,CAACnP,EAAE,CAAC;8BAC5B4B,YAAY,CAAC,QAAQ,CAAC;8BACtBJ,kBAAkB,CAAC,IAAI,CAAC;4BAC1B,CAAE;4BACFgH,SAAS,EAAC,8EAA8E;4BAAAD,QAAA,gBAExF9L,OAAA;8BAAKiM,KAAK,EAAC,4BAA4B;8BAACC,IAAI,EAAC,MAAM;8BAACC,OAAO,EAAC,WAAW;8BAACU,WAAW,EAAC,KAAK;8BAACT,MAAM,EAAC,cAAc;8BAACL,SAAS,EAAC,kBAAkB;8BAAAD,QAAA,eAC1I9L,OAAA;gCAAMqM,aAAa,EAAC,OAAO;gCAACC,cAAc,EAAC,OAAO;gCAACC,CAAC,EAAC;8BAA+Z;gCAAAC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACpd,CAAC,UAER;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CACT;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAEN3M,OAAA;wBAAK+L,SAAS,EAAC,yEAAyE;wBAAAD,QAAA,EACrF4G,OAAO,CAACL,OAAO,IAAI;sBAAY;wBAAA7F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC,EAEL,CAAA+F,OAAO,aAAPA,OAAO,wBAAAS,cAAA,GAAPT,OAAO,CAAEe,KAAK,cAAAN,cAAA,uBAAdA,cAAA,CAAgBnE,MAAM,IAAG,CAAC,iBACzBhP,OAAA;wBAAK+L,SAAS,EAAC,4CAA4C;wBAAAD,QAAA,EACxD4G,OAAO,CAACe,KAAK,CAACvN,GAAG,CAAC,CAACC,IAAI,EAAEuN,OAAO,kBAC/B1T,OAAA;0BAEEwQ,MAAM,EAAC,QAAQ;0BACfC,GAAG,EAAC,qBAAqB;0BACzBzE,IAAI,EAAEzM,WAAW,GAAG4G,IAAI,CAACA,IAAK;0BAC9B4F,SAAS,EAAC,4DAA4D;0BAAAD,QAAA,eAEtE9L,OAAA;4BAAK+L,SAAS,EAAC,gEAAgE;4BAAAD,QAAA,gBAC7E9L,OAAA;8BACEyR,GAAG,EAAElS,WAAW,GAAG4G,IAAI,CAACA,IAAK;8BAC7B4F,SAAS,EAAC,4BAA4B;8BACtC2F,OAAO,EAAGC,CAAC,IAAK;gCACdA,CAAC,CAACnB,MAAM,CAACoB,OAAO,GAAG,IAAI;gCACvBD,CAAC,CAACnB,MAAM,CAACiB,GAAG,GAAG,yBAAyB;8BAC1C,CAAE;8BACFI,GAAG,EAAC;4BAAY;8BAAArF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,eACF3M,OAAA;8BAAK+L,SAAS,EAAC;4BAA4F;8BAAAS,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAM,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC/G;wBAAC,GAjBD+G,OAAO;0BAAAlH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAkBX,CACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC,GAlGEmD,GAAG;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmGR,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,gBAEN3M,OAAA;cAAK+L,SAAS,EAAC,2CAA2C;cAAAD,QAAA,gBACxD9L,OAAA;gBAAK+L,SAAS,EAAC,yFAAyF;gBAAAD,QAAA,eACtG9L,OAAA;kBAAKiM,KAAK,EAAC,4BAA4B;kBAACC,IAAI,EAAC,MAAM;kBAACC,OAAO,EAAC,WAAW;kBAACU,WAAW,EAAC,KAAK;kBAACT,MAAM,EAAC,cAAc;kBAACL,SAAS,EAAC,wBAAwB;kBAAAD,QAAA,eAChJ9L,OAAA;oBAAMqM,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,CAAC,EAAC;kBAAkW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvZ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3M,OAAA;gBAAI+L,SAAS,EAAC,iCAAiC;gBAAAD,QAAA,EAAC;cAAe;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpE3M,OAAA;gBAAG+L,SAAS,EAAC,wBAAwB;gBAAAD,QAAA,EAAC;cAA0C;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN3M,OAAA,CAACF,iBAAiB;MAChB6T,MAAM,EAAE7O,eAAgB;MACxB8H,OAAO,EACL1H,SAAS,KAAK,QAAQ,GAClB,+CAA+C,GAC/C,gBACL;MACDkG,KAAK,EAAElG,SAAS,KAAK,QAAQ,GAAG,gBAAgB,GAAG,cAAe;MAClEmG,IAAI,EAAC,QAAQ;MACbuI,WAAW,EAAC,QAAQ;MACpBC,UAAU,EAAC,QAAQ;MACnBC,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrB,IAAI5O,SAAS,KAAK,QAAQ,IAAIF,aAAa,KAAK,EAAE,EAAE;UAClD1B,QAAQ,CAAC1E,iBAAiB,CAACoG,aAAa,CAAC,CAAC;UAC1CD,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;QAClB,CAAC,MAAM;UACLJ,kBAAkB,CAAC,KAAK,CAAC;UACzBI,YAAY,CAAC,EAAE,CAAC;UAChBF,gBAAgB,CAAC,EAAE,CAAC;QACtB;MACF,CAAE;MACF8O,QAAQ,EAAEA,CAAA,KAAM;QACdhP,kBAAkB,CAAC,KAAK,CAAC;QACzBI,YAAY,CAAC,EAAE,CAAC;QAChBF,gBAAgB,CAAC,EAAE,CAAC;MACtB,CAAE;MACF+O,SAAS,EAAEpM;IAAyB;MAAA4E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEF3M,OAAA,CAACF,iBAAiB;MAChB6T,MAAM,EAAE3P,QAAS;MACjBoH,KAAK,eACHpL,OAAA;QAAK+L,SAAS,EAAC,kCAAkC;QAAAD,QAAA,gBAC/C9L,OAAA;UAAKiM,KAAK,EAAC,4BAA4B;UAACC,IAAI,EAAC,MAAM;UAACC,OAAO,EAAC,WAAW;UAACU,WAAW,EAAC,KAAK;UAACT,MAAM,EAAC,cAAc;UAACL,SAAS,EAAC,cAAc;UAAAD,QAAA,eACtI9L,OAAA;YAAMqM,aAAa,EAAC,OAAO;YAACC,cAAc,EAAC,OAAO;YAACC,CAAC,EAAC;UAA2X;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChb,CAAC,eACN3M,OAAA;UAAA8L,QAAA,EAAM;QAAuB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CACN;MACDC,OAAO,eACL5M,OAAA;QAAK+L,SAAS,EAAC,aAAa;QAAAD,QAAA,gBAC1B9L,OAAA;UAAK+L,SAAS,EAAC,wBAAwB;UAAAD,QAAA,gBACrC9L,OAAA;YAAKiM,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACU,WAAW,EAAC,KAAK;YAACT,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eACrJ9L,OAAA;cAAMqM,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA2X;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChb,CAAC,eACN3M,OAAA;YAAO+L,SAAS,EAAC,oCAAoC;YAAAD,QAAA,GAAC,uBAC/B,eAAA9L,OAAA;cAAM+L,SAAS,EAAC,cAAc;cAAAD,QAAA,EAAC;YAAC;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN3M,OAAA;UAAK+L,SAAS,EAAC,UAAU;UAAAD,QAAA,gBACvB9L,OAAA;YAAK+L,SAAS,EAAC,sEAAsE;YAAAD,QAAA,eACnF9L,OAAA;cAAKiM,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACU,WAAW,EAAC,KAAK;cAACT,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/I9L,OAAA;gBAAMqM,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAAyJ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9M;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN3M,OAAA;YACE+L,SAAS,EAAG,mBACV3H,sBAAsB,GAClB,wDAAwD,GACxD,6DACL,iJAAiJ;YAClJiN,KAAK,EAAEnN,iBAAkB;YACzBoN,QAAQ,EAAGC,CAAC,IAAKpN,oBAAoB,CAACoN,CAAC,CAACf,MAAM,CAACa,KAAK,CAAE;YAAAvF,QAAA,gBAEtD9L,OAAA;cAAQqR,KAAK,EAAE,EAAG;cAAAvF,QAAA,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClDrE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEpC,GAAG,CAAEqK,IAAI,iBACtBvQ,OAAA;cAAsBqR,KAAK,EAAEd,IAAI,CAAChN,EAAG;cAAAuI,QAAA,EAAEyE,IAAI,CAACvD;YAAS,GAAxCuD,IAAI,CAAChN,EAAE;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0C,CAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eACT3M,OAAA;YAAK+L,SAAS,EAAC,uEAAuE;YAAAD,QAAA,eACpF9L,OAAA;cAAKiM,KAAK,EAAC,4BAA4B;cAACC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACU,WAAW,EAAC,KAAK;cAACT,MAAM,EAAC,cAAc;cAACL,SAAS,EAAC,uBAAuB;cAAAD,QAAA,eAC/I9L,OAAA;gBAAMqM,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,CAAC,EAAC;cAA4B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLvI,sBAAsB,iBACrBpE,OAAA;UAAK+L,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClD9L,OAAA;YAAKiM,KAAK,EAAC,4BAA4B;YAACC,IAAI,EAAC,MAAM;YAACC,OAAO,EAAC,WAAW;YAACU,WAAW,EAAC,KAAK;YAACT,MAAM,EAAC,cAAc;YAACL,SAAS,EAAC,cAAc;YAAAD,QAAA,eACtI9L,OAAA;cAAMqM,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,CAAC,EAAC;YAA6E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClI,CAAC,eACN3M,OAAA;YAAM+L,SAAS,EAAC,SAAS;YAAAD,QAAA,EAAE1H;UAAsB;YAAAoI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDtB,IAAI,EAAC,MAAM;MACXuI,WAAW,EAAC,oBAAoB;MAChCC,UAAU,EAAC,QAAQ;MACnBI,kBAAkB,EAAC,2EAA2E;MAC9FC,iBAAiB,EAAC,6EAA6E;MAC/FJ,SAAS,EAAE,MAAAA,CAAA,KAAY;QACrBzP,yBAAyB,CAAC,EAAE,CAAC;QAE7B,IAAIH,iBAAiB,KAAK,EAAE,EAAE;UAC5BG,yBAAyB,CAAC,yBAAyB,CAAC;QACtD,CAAC,MAAM;UACLN,YAAY,CAAC,IAAI,CAAC;UAClB,MAAMT,QAAQ,CACZrE,kBAAkB,CAACsE,EAAE,EAAE;YAAE6P,WAAW,EAAElP;UAAkB,CAAC,CAC3D,CAAC;UACDH,YAAY,CAAC,KAAK,CAAC;QACrB;MACF,CAAE;MACFgQ,QAAQ,EAAEA,CAAA,KAAM;QACd5P,oBAAoB,CAAC,EAAE,CAAC;QACxBE,yBAAyB,CAAC,EAAE,CAAC;QAC7BJ,WAAW,CAAC,KAAK,CAAC;QAClBF,YAAY,CAAC,KAAK,CAAC;MACrB,CAAE;MACFiQ,SAAS,EAAElQ,SAAU;MACrBqQ,WAAW,EAAC,0BAA0B;MACtCC,WAAW,eACTpU,OAAA;QAAK+L,SAAS,EAAC,iCAAiC;QAACE,KAAK,EAAC,4BAA4B;QAACC,IAAI,EAAC,MAAM;QAACC,OAAO,EAAC,WAAW;QAAAL,QAAA,gBACjH9L,OAAA;UAAQ+L,SAAS,EAAC,YAAY;UAACwG,EAAE,EAAC,IAAI;UAACC,EAAE,EAAC,IAAI;UAACC,CAAC,EAAC,IAAI;UAACrG,MAAM,EAAC,cAAc;UAACS,WAAW,EAAC;QAAG;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eACrG3M,OAAA;UAAM+L,SAAS,EAAC,YAAY;UAACG,IAAI,EAAC,cAAc;UAACK,CAAC,EAAC;QAAiH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzK;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEpB;AAAClM,EAAA,CArtDQD,gBAAgB;EAAA,QACNhC,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EACkBC,eAAe,EA4BrDgB,WAAW,EAsBKpB,WAAW,EAGVA,WAAW,EAINA,WAAW,EAITA,WAAW,EAOXA,WAAW,EAIZA,WAAW,EAITA,WAAW,EAOjBA,WAAW,EAQPA,WAAW;AAAA;AAAA+V,EAAA,GAhG7B7T,gBAAgB;AAutDzB,eAAeA,gBAAgB;AAAC,IAAA6T,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}