{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/LayoutSection.js\";\nimport React from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LayoutSection(props) {\n  var _props$styles;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \" border border-gray rounded-md rounded-t-xl shadow-2 my-2 \",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-2 rounded-t-xl bg-gray  ${(_props$styles = props.styles) !== null && _props$styles !== void 0 ? _props$styles : \"bg-gray\"} `,\n      children: props.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 bg-white rounded-b-xl\",\n      children: props.children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = LayoutSection;\nexport default LayoutSection;\nvar _c;\n$RefreshReg$(_c, \"LayoutSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "LayoutSection", "props", "_props$styles", "className", "children", "styles", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/components/LayoutSection.js"], "sourcesContent": ["import React from \"react\";\n\nfunction LayoutSection(props) {\n  return (\n    <div className=\" border border-gray rounded-md rounded-t-xl shadow-2 my-2 \">\n      <div\n        className={`p-2 rounded-t-xl bg-gray  ${props.styles ?? \"bg-gray\"} `}\n      >\n        {props.title}\n      </div>\n      <div className=\"p-4 bg-white rounded-b-xl\">{props.children}</div>\n    </div>\n  );\n}\n\nexport default LayoutSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,aAAaA,CAACC,KAAK,EAAE;EAAA,IAAAC,aAAA;EAC5B,oBACEH,OAAA;IAAKI,SAAS,EAAC,4DAA4D;IAAAC,QAAA,gBACzEL,OAAA;MACEI,SAAS,EAAG,6BAA0B,CAAAD,aAAA,GAAED,KAAK,CAACI,MAAM,cAAAH,aAAA,cAAAA,aAAA,GAAI,SAAU,GAAG;MAAAE,QAAA,EAEpEH,KAAK,CAACK;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACNX,OAAA;MAAKI,SAAS,EAAC,2BAA2B;MAAAC,QAAA,EAAEH,KAAK,CAACG;IAAQ;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9D,CAAC;AAEV;AAACC,EAAA,GAXQX,aAAa;AAatB,eAAeA,aAAa;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}