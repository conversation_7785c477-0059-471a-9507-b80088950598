{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction LoginScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n  const [isCheck, setIsCheck] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n  const submitHandle = async e => {\n    console.log(\"jj\");\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-screen min-h-screen flex md:flex-row flex-col bg-white\",\n    children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 3000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:w-1/2 w-full  bg-cover bg-no-repeat\",\n      style: {\n        backgroundImage: \"url(\" + bgLogin + \")\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \" flex flex-col items-left justify-center px-12 py-5  md:h-screen\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoProjet,\n          className: \"size-20\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-[#025163] text-3xl my-5\",\n          children: \"Access your healthcare management tools easily and securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imgLogin,\n          className: \" md:w-[70%] w-max md:block hidden\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"md:w-1/2 w-full \",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        className: \" w-full h-full flex flex-col\",\n        onSubmit: submitHandle,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-[#303030] font-bold text-2xl\",\n            children: \"Log in\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n            type: \"error\",\n            message: error\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 23\n          }, this), loading && /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl mb-6\",\n            children: [loading, \" loading\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-20 text-sm\",\n            children: \"Adresse e-mail\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            className: \" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\",\n            id: \"username\",\n            required: true,\n            type: \"email\",\n            placeholder: \"Adresse e-mail\",\n            value: username,\n            onChange: e => setUsername(e.target.value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 text-sm\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              className: \" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\",\n              id: \"password\",\n              required: true,\n              type: !showPass ? \"password\" : \"text\",\n              placeholder: \"********\",\n              value: password,\n              onChange: e => setPassword(e.target.value)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => setShowPass(!showPass),\n              className: \" cursor-pointer \",\n              children: showPass ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"size-5 text-[#666666]\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"size-5  text-[#666666]\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 flex flex-row justify-between items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                checked: isCheck,\n                onChange: v => {\n                  setIsCheck(!isCheck);\n                },\n                id: \"check-save\",\n                type: \"checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                for: \"check-save\",\n                className: \"mx-2 text-sm text-[#030229] cursor-pointer\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              className: \" text-sm text-[#0388A6]\",\n              href: \"/reset-password\",\n              children: \"Reset Password?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-5\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\",\n              children: \"Log in\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 my-2 text-[#878787] text-center\",\n          children: [\"Copyright \\xA9 2024 Atlas Assistance |\", \" \", /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"\",\n            className: \"font-bold\",\n            children: \"Privacy Policy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this)\n  // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n  //   <div className=\"flex justify-center items-center h-screen\">\n  //     <form\n  //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n  //       onSubmit={submitHandle}\n  //     >\n  //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n  //       {error && <Alert type=\"error\" message={error} />}\n\n  //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n  //       <div className=\"mb-4\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"username\"\n  //         >\n  //           Adresse e-mail\n  //         </label>\n  //         <input\n  //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n  //           id=\"username\"\n  //           type=\"text\"\n  //           placeholder=\"\"\n  //           value={username}\n  //           onChange={(e) => setUsername(e.target.value)}\n  //         />\n  //       </div>\n  //       <div className=\"mb-6\">\n  //         <label\n  //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n  //           htmlFor=\"password\"\n  //         >\n  //           Mot de passe\n  //         </label>\n  //         <div className=\"flex flex-row items-center\">\n  //           <input\n  //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n  //             id=\"password\"\n  //             type={!showPass ? \"password\" : \"text\"}\n  //             placeholder=\"\"\n  //             value={password}\n  //             onChange={(e) => setPassword(e.target.value)}\n  //           />\n  //           <div\n  //             onClick={() => setShowPass(!showPass)}\n  //             className=\" cursor-pointer py-2 px-2 \"\n  //           >\n  //             {showPass ? (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n  //                 />\n  //               </svg>\n  //             ) : (\n  //               <svg\n  //                 xmlns=\"http://www.w3.org/2000/svg\"\n  //                 fill=\"none\"\n  //                 viewBox=\"0 0 24 24\"\n  //                 stroke-width=\"1.5\"\n  //                 stroke=\"currentColor\"\n  //                 className=\"w-6 h-6\"\n  //               >\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n  //                 />\n  //                 <path\n  //                   strokeLinecap=\"round\"\n  //                   strokeLinejoin=\"round\"\n  //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  //                 />\n  //               </svg>\n  //             )}\n  //           </div>\n  //         </div>\n  //       </div>\n  //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n  //         <button\n  //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n  //           type=\"submit\"\n  //         >\n  //           Connexion\n  //         </button>\n  //         <a\n  //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n  //           href=\"#!\"\n  //         >\n  //           Mot de passe oublié?\n  //         </a>\n  //       </div>\n  //     </form>\n  //   </div>\n  // </div>\n  ;\n}\n_s(LoginScreen, \"QbcWtgWncOJRxzwaoDw36F9LtKE=\", false, function () {\n  return [useNavigate, useDispatch, useSelector];\n});\n_c = LoginScreen;\nexport default LoginScreen;\nvar _c;\n$RefreshReg$(_c, \"LoginScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useDispatch", "useSelector", "login", "<PERSON><PERSON>", "useNavigate", "bgLogin", "logoProjet", "imgLogin", "ToastContainer", "jsxDEV", "_jsxDEV", "LoginScreen", "_s", "navigate", "username", "setUsername", "password", "setPassword", "dispatch", "showPass", "setShowPass", "is<PERSON><PERSON><PERSON>", "setIsCheck", "userLogin", "state", "userInfo", "error", "loading", "<PERSON><PERSON><PERSON><PERSON>", "e", "console", "log", "preventDefault", "className", "children", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "backgroundImage", "src", "onSubmit", "type", "message", "id", "required", "placeholder", "value", "onChange", "target", "onClick", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "checked", "v", "for", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/LoginScreen.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\n\nimport { useDispatch, useSelector } from \"react-redux\";\n\nimport { login } from \"../../redux/actions/userActions\";\nimport Alert from \"../../components/Alert\";\nimport { useNavigate } from \"react-router-dom\";\n\nimport bgLogin from \"../../images/bg-login.png\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport imgLogin from \"../../images/image-login.png\";\n\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nfunction LoginScreen() {\n  const navigate = useNavigate();\n  const [username, setUsername] = useState(\"\");\n  const [password, setPassword] = useState(\"\");\n\n  const dispatch = useDispatch();\n  const [showPass, setShowPass] = useState(false);\n  const [isCheck, setIsCheck] = useState(false);\n\n  // const redirect = '/dashboard'\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  useEffect(() => {\n    if (userInfo) {\n      navigate(\"/dashboard\");\n    }\n  }, [navigate, userInfo]);\n\n  const submitHandle = async (e) => {\n    console.log(\"jj\");\n\n    e.preventDefault();\n    dispatch(login(username, password));\n  };\n\n  // return (\n  //   <div>\n  //     <div className=\"h-screen w-screen\">\n  //       <iframe\n  //         title=\"Om Nom Run Game\"\n  //         src=\"https://play.famobi.com/wrapper/om-nom-run/A1000-10\"\n  //         className=\"w-full h-full\"\n  //         frameBorder=\"0\"\n  //         scrolling=\"no\"\n  //         allowFullScreen\n  //       ></iframe>\n  //     </div>\n  //   </div>\n  // );\n\n  return (\n    <div className=\"w-screen min-h-screen flex md:flex-row flex-col bg-white\">\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n      <div\n        className=\"md:w-1/2 w-full  bg-cover bg-no-repeat\"\n        style={{ backgroundImage: \"url(\" + bgLogin + \")\" }}\n      >\n        <div className=\" flex flex-col items-left justify-center px-12 py-5  md:h-screen\">\n          <img src={logoProjet} className=\"size-20\" />\n          <div className=\"text-[#025163] text-3xl my-5\">\n            Access your healthcare management tools easily and securely\n          </div>\n          <img src={imgLogin} className=\" md:w-[70%] w-max md:block hidden\" />\n        </div>\n      </div>\n      {/*  */}\n      <div className=\"md:w-1/2 w-full \">\n        <form className=\" w-full h-full flex flex-col\" onSubmit={submitHandle}>\n          <div className=\"md:w-[80%] w-full mx-auto flex flex-col items-left  px-12 py-16 md:flex-1 \">\n            <div className=\"text-[#303030] font-bold text-2xl\">Log in</div>\n            {error && <Alert type=\"error\" message={error} />}\n\n            {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n            <div className=\"mt-20 text-sm\">Adresse e-mail</div>\n            <input\n              className=\" border border-[#666666] text-sm mt-2 rounded-full  w-full py-3 px-3 text-gray-700  focus:outline-none focus:shadow-outline\"\n              id=\"username\"\n              required\n              type=\"email\"\n              placeholder=\"Adresse e-mail\"\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n            />\n            <div className=\"mt-3 text-sm\">Password</div>\n            <div className=\" border border-[#666666] mt-2 rounded-full  w-full  py-3 px-3 flex flex-row items-center\">\n              <input\n                className=\" text-gray-700 text-sm focus:outline-none focus:shadow-outline w-full flex-1\"\n                id=\"password\"\n                required\n                type={!showPass ? \"password\" : \"text\"}\n                placeholder=\"********\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n              <div\n                onClick={() => setShowPass(!showPass)}\n                className=\" cursor-pointer \"\n              >\n                {showPass ? (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5 text-[#666666]\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n                    />\n                  </svg>\n                ) : (\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"size-5  text-[#666666]\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                    />\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                    />\n                  </svg>\n                )}\n              </div>\n            </div>\n            <div className=\"mt-4 flex flex-row justify-between items-center\">\n              <div className=\"flex flex-row items-center\">\n                <input\n                  checked={isCheck}\n                  onChange={(v) => {\n                    setIsCheck(!isCheck);\n                  }}\n                  id=\"check-save\"\n                  type={\"checkbox\"}\n                />\n                <label\n                  for=\"check-save\"\n                  className=\"mx-2 text-sm text-[#030229] cursor-pointer\"\n                >\n                  Remember me\n                </label>\n              </div>\n              <a className=\" text-sm text-[#0388A6]\" href=\"/reset-password\">\n                Reset Password?\n              </a>\n            </div>\n            <div className=\"mt-5\">\n              <button\n                type=\"submit\"\n                className=\"bg-[#0388A6] text-white w-full rounded-full py-4 px-3 text-center\"\n              >\n                Log in\n              </button>\n            </div>\n          </div>\n          <div className=\"mt-4 my-2 text-[#878787] text-center\">\n            Copyright © 2024 Atlas Assistance |{\" \"}\n            <a href=\"\" className=\"font-bold\">\n              Privacy Policy\n            </a>\n          </div>\n        </form>\n      </div>\n    </div>\n    // <div className=\"w-screen h-screen bg-cover bg-center bg-no-repeat bg-opacity-25 \">\n    //   <div className=\"flex justify-center items-center h-screen\">\n    //     <form\n    //       className=\"bg-white shadow-lg rounded mx-3 px-8 pt-6 pb-8 mb-4 md:w-1/3 w-screen\"\n    //       onSubmit={submitHandle}\n    //     >\n    //       <h2 className=\"text-2xl mb-6\">Connectez-vous à l'administrateur</h2>\n    //       {error && <Alert type=\"error\" message={error} />}\n\n    //       {loading && <h2 className=\"text-2xl mb-6\">{loading} loading</h2>}\n    //       <div className=\"mb-4\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"username\"\n    //         >\n    //           Adresse e-mail\n    //         </label>\n    //         <input\n    //           className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\"\n    //           id=\"username\"\n    //           type=\"text\"\n    //           placeholder=\"\"\n    //           value={username}\n    //           onChange={(e) => setUsername(e.target.value)}\n    //         />\n    //       </div>\n    //       <div className=\"mb-6\">\n    //         <label\n    //           className=\"block text-gray-700 text-sm font-bold mb-2\"\n    //           htmlFor=\"password\"\n    //         >\n    //           Mot de passe\n    //         </label>\n    //         <div className=\"flex flex-row items-center\">\n    //           <input\n    //             className=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 mb-3 leading-tight focus:outline-none focus:shadow-outline\"\n    //             id=\"password\"\n    //             type={!showPass ? \"password\" : \"text\"}\n    //             placeholder=\"\"\n    //             value={password}\n    //             onChange={(e) => setPassword(e.target.value)}\n    //           />\n    //           <div\n    //             onClick={() => setShowPass(!showPass)}\n    //             className=\" cursor-pointer py-2 px-2 \"\n    //           >\n    //             {showPass ? (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88\"\n    //                 />\n    //               </svg>\n    //             ) : (\n    //               <svg\n    //                 xmlns=\"http://www.w3.org/2000/svg\"\n    //                 fill=\"none\"\n    //                 viewBox=\"0 0 24 24\"\n    //                 stroke-width=\"1.5\"\n    //                 stroke=\"currentColor\"\n    //                 className=\"w-6 h-6\"\n    //               >\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n    //                 />\n    //                 <path\n    //                   strokeLinecap=\"round\"\n    //                   strokeLinejoin=\"round\"\n    //                   d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n    //                 />\n    //               </svg>\n    //             )}\n    //           </div>\n    //         </div>\n    //       </div>\n    //       <div className=\"flex md:flex-row flex-col items-center justify-between\">\n    //         <button\n    //           className=\"border border-primary bg-primary text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\"\n    //           type=\"submit\"\n    //         >\n    //           Connexion\n    //         </button>\n    //         <a\n    //           className=\"inline-block align-baseline font-bold text-sm text-blue-500 hover:text-blue-800\"\n    //           href=\"#!\"\n    //         >\n    //           Mot de passe oublié?\n    //         </a>\n    //       </div>\n    //     </form>\n    //   </div>\n    // </div>\n  );\n}\n\nexport default LoginScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAElD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,KAAK,QAAQ,iCAAiC;AACvD,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,WAAW,QAAQ,kBAAkB;AAE9C,OAAOC,OAAO,MAAM,2BAA2B;AAC/C,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,QAAQ,MAAM,8BAA8B;AAEnD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMoB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAE7C;;EAEA,MAAMyB,SAAS,GAAGtB,WAAW,CAAEuB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9CxB,SAAS,CAAC,MAAM;IACd,IAAI0B,QAAQ,EAAE;MACZZ,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEY,QAAQ,CAAC,CAAC;EAExB,MAAMG,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;IAEjBF,CAAC,CAACG,cAAc,CAAC,CAAC;IAClBd,QAAQ,CAAChB,KAAK,CAACY,QAAQ,EAAEE,QAAQ,CAAC,CAAC;EACrC,CAAC;;EAED;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,oBACEN,OAAA;IAAKuB,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBACvExB,OAAA,CAACF,cAAc;MACb2B,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACFtC,OAAA;MACEuB,SAAS,EAAC,wCAAwC;MAClDgB,KAAK,EAAE;QAAEC,eAAe,EAAE,MAAM,GAAG7C,OAAO,GAAG;MAAI,CAAE;MAAA6B,QAAA,eAEnDxB,OAAA;QAAKuB,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC/ExB,OAAA;UAAKyC,GAAG,EAAE7C,UAAW;UAAC2B,SAAS,EAAC;QAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5CtC,OAAA;UAAKuB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAE9C;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNtC,OAAA;UAAKyC,GAAG,EAAE5C,QAAS;UAAC0B,SAAS,EAAC;QAAmC;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENtC,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BxB,OAAA;QAAMuB,SAAS,EAAC,8BAA8B;QAACmB,QAAQ,EAAExB,YAAa;QAAAM,QAAA,gBACpExB,OAAA;UAAKuB,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBACzFxB,OAAA;YAAKuB,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAM;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC9DtB,KAAK,iBAAIhB,OAAA,CAACP,KAAK;YAACkD,IAAI,EAAC,OAAO;YAACC,OAAO,EAAE5B;UAAM;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EAE/CrB,OAAO,iBAAIjB,OAAA;YAAIuB,SAAS,EAAC,eAAe;YAAAC,QAAA,GAAEP,OAAO,EAAC,UAAQ;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChEtC,OAAA;YAAKuB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAc;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDtC,OAAA;YACEuB,SAAS,EAAC,6HAA6H;YACvIsB,EAAE,EAAC,UAAU;YACbC,QAAQ;YACRH,IAAI,EAAC,OAAO;YACZI,WAAW,EAAC,gBAAgB;YAC5BC,KAAK,EAAE5C,QAAS;YAChB6C,QAAQ,EAAG9B,CAAC,IAAKd,WAAW,CAACc,CAAC,CAAC+B,MAAM,CAACF,KAAK;UAAE;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACFtC,OAAA;YAAKuB,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAQ;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5CtC,OAAA;YAAKuB,SAAS,EAAC,0FAA0F;YAAAC,QAAA,gBACvGxB,OAAA;cACEuB,SAAS,EAAC,8EAA8E;cACxFsB,EAAE,EAAC,UAAU;cACbC,QAAQ;cACRH,IAAI,EAAE,CAAClC,QAAQ,GAAG,UAAU,GAAG,MAAO;cACtCsC,WAAW,EAAC,UAAU;cACtBC,KAAK,EAAE1C,QAAS;cAChB2C,QAAQ,EAAG9B,CAAC,IAAKZ,WAAW,CAACY,CAAC,CAAC+B,MAAM,CAACF,KAAK;YAAE;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACFtC,OAAA;cACEmD,OAAO,EAAEA,CAAA,KAAMzC,WAAW,CAAC,CAACD,QAAQ,CAAE;cACtCc,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAE3Bf,QAAQ,gBACPT,OAAA;gBACEoD,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBhC,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eAEjCxB,OAAA;kBACEwD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA8U;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENtC,OAAA;gBACEoD,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBhC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBAElCxB,OAAA;kBACEwD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA0L;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7L,CAAC,eACFtC,OAAA;kBACEwD,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAAqC;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtC,OAAA;YAAKuB,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DxB,OAAA;cAAKuB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxB,OAAA;gBACE2D,OAAO,EAAEhD,OAAQ;gBACjBsC,QAAQ,EAAGW,CAAC,IAAK;kBACfhD,UAAU,CAAC,CAACD,OAAO,CAAC;gBACtB,CAAE;gBACFkC,EAAE,EAAC,YAAY;gBACfF,IAAI,EAAE;cAAW;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACFtC,OAAA;gBACE6D,GAAG,EAAC,YAAY;gBAChBtC,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACvD;cAED;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNtC,OAAA;cAAGuB,SAAS,EAAC,yBAAyB;cAACuC,IAAI,EAAC,iBAAiB;cAAAtC,QAAA,EAAC;YAE9D;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNtC,OAAA;YAAKuB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBxB,OAAA;cACE2C,IAAI,EAAC,QAAQ;cACbpB,SAAS,EAAC,mEAAmE;cAAAC,QAAA,EAC9E;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtC,OAAA;UAAKuB,SAAS,EAAC,sCAAsC;UAAAC,QAAA,GAAC,wCACjB,EAAC,GAAG,eACvCxB,OAAA;YAAG8D,IAAI,EAAC,EAAE;YAACvC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAEjC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH;EACL;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAAA;AAEJ;AAACpC,EAAA,CAzRQD,WAAW;EAAA,QACDP,WAAW,EAIXJ,WAAW,EAMVC,WAAW;AAAA;AAAAwE,EAAA,GAXtB9D,WAAW;AA2RpB,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}