{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{detailInsurance}from\"../../redux/actions/insuranceActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile,COUNTRIES}from\"../../constants\";import{casesListInsurance}from\"../../redux/actions/caseActions\";import Paginate from\"../../components/Paginate\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";function InsuranceProfileScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const insuranceDetail=useSelector(state=>state.detailInsurance);const{loadingInsuranceInfo,errorInsuranceInfo,successInsuranceInfo,insuranceInfo}=insuranceDetail;const listCases=useSelector(state=>state.caseListInsurance);const{casesInsurance,loadingCasesInsurance,errorCasesInsurance,pages}=listCases;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailInsurance(id));dispatch(casesListInsurance(page,\"\",id));}},[navigate,userInfo,dispatch,id,page]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const getIconCountry=country=>{const foundCountry=COUNTRIES.find(option=>option.title===country);if(foundCountry){return foundCountry.icon;}else{return\"\";}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinated\":return\"Fully Coordinated\";default:return casestatus;}};return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"a\",{href:\"/insurances-company\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Insurance Company\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Profile\"})]}),loadingInsuranceInfo?/*#__PURE__*/_jsx(Loader,{}):errorInsuranceInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorInsuranceInfo}):insuranceInfo?/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-5 text-[#303030] text-opacity-60\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\",children:[/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_logo?/*#__PURE__*/_jsx(\"img\",{className:\"size-20 rounded-2xl shadow-1\",alt:insuranceInfo.assurance_name,src:baseURLFile+insuranceInfo.assurance_logo,onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}}):/*#__PURE__*/_jsx(\"img\",{className:\"size-20 rounded-2xl shadow-1\",alt:insuranceInfo.assurance_name,src:\"/assets/placeholder.png\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-bold\",children:\"Insurance Name: \"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-1\",children:/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_name})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm flex flex-row items-center my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-bold\",children:\"Country: \"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-1\",children:/*#__PURE__*/_jsxs(\"div\",{children:[getIconCountry(insuranceInfo.assurance_country),\" \",insuranceInfo.assurance_country]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm flex flex-row items-start my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-bold\",children:\"Emails: \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_email}),/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_email_two}),/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_email_three})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm flex flex-row items-start my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-bold\",children:\"Phones: \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1  px-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_phone}),/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_phone_two}),/*#__PURE__*/_jsx(\"div\",{children:insuranceInfo.assurance_phone_three})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col md:items-center my-1\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 px-2 flex flex-row items-center my-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5 text-[#32475C] text-opacity-55 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1\",children:[\"Joined \",formatDate(insuranceInfo.created_at)]})]})})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\" w-full  px-1 py-3 \",children:/*#__PURE__*/_jsx(\"div\",{className:\"py-4 px-2 shadow-1 bg-white\",children:loadingCasesInsurance?/*#__PURE__*/_jsx(Loader,{}):errorCasesInsurance?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCasesInsurance}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto \",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-[#F3F5FB] text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"ID\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \",children:\"Patient Name\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Type\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Assigned Provider\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Status\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\",children:\"Date Created\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[casesInsurance===null||casesInsurance===void 0?void 0:casesInsurance.map((item,index)=>{var _item$assurance$assur,_item$assurance,_item$patient$full_na,_item$patient,_item$case_type,_item$provider$full_n,_item$provider;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[\"#\",item.id]})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$assurance$assur=(_item$assurance=item.assurance)===null||_item$assurance===void 0?void 0:_item$assurance.assurance_name)!==null&&_item$assurance$assur!==void 0?_item$assurance$assur:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$patient$full_na=(_item$patient=item.patient)===null||_item$patient===void 0?void 0:_item$patient.full_name)!==null&&_item$patient$full_na!==void 0?_item$patient$full_na:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$case_type=item.case_type)!==null&&_item$case_type!==void 0?_item$case_type:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$provider$full_n=(_item$provider=item.provider)===null||_item$provider===void 0?void 0:_item$provider.full_name)!==null&&_item$provider$full_n!==void 0?_item$provider$full_n:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:caseStatus(item.status_coordination)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:formatDate(item.case_date)})}),/*#__PURE__*/_jsx(\"td\",{className:\" py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"/cases-list/detail/\"+item.id,children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})})})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-5\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/insurances-company/profile/\".concat(id,\"?\"),search:\"\",page:page,pages:pages})})]})})})]})}):null]})});}export default InsuranceProfileScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useParams", "useSearchParams", "detailInsurance", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "COUNTRIES", "casesListInsurance", "Paginate", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "InsuranceProfileScreen", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "userLogin", "state", "userInfo", "insuranceDetail", "loadingInsuranceInfo", "errorInsuranceInfo", "successInsuranceInfo", "insuranceInfo", "listCases", "caseListInsurance", "casesInsurance", "loadingCasesInsurance", "errorCasesInsurance", "pages", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "getIconCountry", "country", "foundCountry", "find", "option", "title", "icon", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "assurance_logo", "alt", "assurance_name", "src", "onError", "e", "target", "onerror", "assurance_country", "assurance_email", "assurance_email_two", "assurance_email_three", "assurance_phone", "assurance_phone_two", "assurance_phone_three", "created_at", "map", "item", "index", "_item$assurance$assur", "_item$assurance", "_item$patient$full_na", "_item$patient", "_item$case_type", "_item$provider$full_n", "_item$provider", "assurance", "patient", "full_name", "case_type", "provider", "status_coordination", "case_date", "to", "route", "concat", "search"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsuranceProfileScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { detailInsurance } from \"../../redux/actions/insuranceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile, COUNTRIES } from \"../../constants\";\nimport { casesListInsurance } from \"../../redux/actions/caseActions\";\nimport Paginate from \"../../components/Paginate\";\n\nfunction InsuranceProfileScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const insuranceDetail = useSelector((state) => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo,\n  } = insuranceDetail;\n\n  const listCases = useSelector((state) => state.caseListInsurance);\n  const { casesInsurance, loadingCasesInsurance, errorCasesInsurance, pages } =\n    listCases;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n      dispatch(casesListInsurance(page, \"\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const getIconCountry = (country) => {\n    const foundCountry = COUNTRIES.find((option) => option.title === country);\n\n    if (foundCountry) {\n      return foundCountry.icon;\n    } else {\n      return \"\";\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinated\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/insurances-company\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Insurance Company</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Profile</div>\n        </div>\n        {/*  */}\n        {loadingInsuranceInfo ? (\n          <Loader />\n        ) : errorInsuranceInfo ? (\n          <Alert type={\"error\"} message={errorInsuranceInfo} />\n        ) : insuranceInfo ? (\n          <>\n            <div className=\"my-5 text-[#303030] text-opacity-60\">\n              {/* profile */}\n              <div className=\"bg-white rounded shadow-1 my-1 w-full px-3 py-4 flex flex-row items-start text-xs\">\n                <div>\n                  {insuranceInfo.assurance_logo ? (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={insuranceInfo.assurance_name}\n                      src={baseURLFile + insuranceInfo.assurance_logo}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  ) : (\n                    <img\n                      className=\"size-20 rounded-2xl shadow-1\"\n                      alt={insuranceInfo.assurance_name}\n                      src={\"/assets/placeholder.png\"}\n                      onError={(e) => {\n                        e.target.onerror = null;\n                        e.target.src = \"/assets/placeholder.png\";\n                      }}\n                    />\n                  )}\n                </div>\n                {/*  */}\n                <div className=\"flex-1 px-5\">\n                  <div className=\"text-sm flex flex-row items-center my-1\">\n                    <div className=\"font-bold\">Insurance Name: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>{insuranceInfo.assurance_name}</div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm flex flex-row items-center my-1\">\n                    <div className=\"font-bold\">Country: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>\n                        {getIconCountry(insuranceInfo.assurance_country)}{\" \"}\n                        {insuranceInfo.assurance_country}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"text-sm flex flex-row items-start my-1\">\n                    <div className=\"font-bold\">Emails: </div>\n                    <div className=\"flex-1 px-1\">\n                      <div>{insuranceInfo.assurance_email}</div>\n                      <div>{insuranceInfo.assurance_email_two}</div>\n                      <div>{insuranceInfo.assurance_email_three}</div>\n                    </div>\n                  </div>\n                  <div className=\"text-sm flex flex-row items-start my-1\">\n                    <div className=\"font-bold\">Phones: </div>\n                    <div className=\"flex-1  px-1\">\n                      <div>{insuranceInfo.assurance_phone}</div>\n                      <div>{insuranceInfo.assurance_phone_two}</div>\n                      <div>{insuranceInfo.assurance_phone_three}</div>\n                    </div>\n                  </div>\n                  <div className=\"flex md:flex-row flex-col md:items-center my-1\">\n                    <div className=\"flex-1 px-2 flex flex-row items-center my-1\">\n                      <div className=\"flex flex-row items-center \">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-5 text-[#32475C] text-opacity-55 mx-1\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5\"\n                          />\n                        </svg>\n\n                        <div className=\"mx-1\">\n                          Joined {formatDate(insuranceInfo.created_at)}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              {/* cases */}\n              <div className=\" w-full  px-1 py-3 \">\n                <div className=\"py-4 px-2 shadow-1 bg-white\">\n                  {loadingCasesInsurance ? (\n                    <Loader />\n                  ) : errorCasesInsurance ? (\n                    <Alert type=\"error\" message={errorCasesInsurance} />\n                  ) : (\n                    <div className=\"max-w-full overflow-x-auto \">\n                      <table className=\"w-full table-auto\">\n                        <thead>\n                          <tr className=\" bg-[#F3F5FB] text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              ID\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Client\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max \">\n                              Patient Name\n                            </th>\n                            <th className=\"min-w-[30px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Type\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Assigned Provider\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Status\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\">\n                              Date Created\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-[#8E8E8E] text-xs w-max\"></th>\n                          </tr>\n                        </thead>\n                        {/*  */}\n                        <tbody>\n                          {casesInsurance?.map((item, index) => (\n                            //  <a href={`/cases/detail/${item.id}`}></a>\n                            <tr key={index}>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  #{item.id}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.assurance?.assurance_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.patient?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.case_type ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {item.provider?.full_name ?? \"---\"}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {caseStatus(item.status_coordination)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max  \">\n                                  {formatDate(item.case_date)}\n                                </p>\n                              </td>\n                              <td className=\" py-3 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  <Link\n                                    className=\"mx-1 detail-class\"\n                                    to={\"/cases-list/detail/\" + item.id}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                                      />\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                                      />\n                                    </svg>\n                                  </Link>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                          <tr className=\"h-5\"></tr>\n                        </tbody>\n                      </table>\n                      <div className=\"\">\n                        <Paginate\n                          route={`/insurances-company/profile/${id}?`}\n                          search={\"\"}\n                          page={page}\n                          pages={pages}\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          </>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default InsuranceProfileScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OAASC,eAAe,KAAQ,sCAAsC,CACtE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,CAAEC,SAAS,KAAQ,iBAAiB,CACxD,OAASC,kBAAkB,KAAQ,iCAAiC,CACpE,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEjD,QAAS,CAAAC,sBAAsBA,CAAA,CAAG,CAChC,KAAM,CAAAC,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAmB,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAqB,QAAQ,CAAGxB,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEyB,EAAG,CAAC,CAAGpB,SAAS,CAAC,CAAC,CACxB,KAAM,CAACqB,YAAY,CAAC,CAAGpB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAqB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAAAC,SAAS,CAAG5B,WAAW,CAAE6B,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,eAAe,CAAG/B,WAAW,CAAE6B,KAAK,EAAKA,KAAK,CAACvB,eAAe,CAAC,CACrE,KAAM,CACJ0B,oBAAoB,CACpBC,kBAAkB,CAClBC,oBAAoB,CACpBC,aACF,CAAC,CAAGJ,eAAe,CAEnB,KAAM,CAAAK,SAAS,CAAGpC,WAAW,CAAE6B,KAAK,EAAKA,KAAK,CAACQ,iBAAiB,CAAC,CACjE,KAAM,CAAEC,cAAc,CAAEC,qBAAqB,CAAEC,mBAAmB,CAAEC,KAAM,CAAC,CACzEL,SAAS,CAEX,KAAM,CAAAM,QAAQ,CAAG,GAAG,CACpB5C,SAAS,CAAC,IAAM,CACd,GAAI,CAACgC,QAAQ,CAAE,CACbT,QAAQ,CAACqB,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLnB,QAAQ,CAACjB,eAAe,CAACkB,EAAE,CAAC,CAAC,CAC7BD,QAAQ,CAACX,kBAAkB,CAACc,IAAI,CAAE,EAAE,CAAEF,EAAE,CAAC,CAAC,CAC5C,CACF,CAAC,CAAE,CAACH,QAAQ,CAAES,QAAQ,CAAEP,QAAQ,CAAEC,EAAE,CAAEE,IAAI,CAAC,CAAC,CAE5C,KAAM,CAAAiB,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,cAAc,CAAIC,OAAO,EAAK,CAClC,KAAM,CAAAC,YAAY,CAAG1C,SAAS,CAAC2C,IAAI,CAAEC,MAAM,EAAKA,MAAM,CAACC,KAAK,GAAKJ,OAAO,CAAC,CAEzE,GAAIC,YAAY,CAAE,CAChB,MAAO,CAAAA,YAAY,CAACI,IAAI,CAC1B,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,mBAAmB,CACtB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED,mBACE5C,IAAA,CAACR,aAAa,EAAAqD,QAAA,cACZ3C,KAAA,QAAA2C,QAAA,eACE3C,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD7C,IAAA,MAAG+C,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB3C,KAAA,QAAK4C,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACEoD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNtD,IAAA,SAAM8C,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ7C,IAAA,MAAG+C,IAAI,CAAC,qBAAqB,CAAAF,QAAA,cAC3B3C,KAAA,QAAK4C,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D7C,IAAA,SAAA6C,QAAA,cACE7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACEoD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPtD,IAAA,QAAK8C,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACtC,CAAC,CACL,CAAC,cACJ7C,IAAA,SAAA6C,QAAA,cACE7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACEoD,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPtD,IAAA,QAAK8C,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,EAC5B,CAAC,CAEL5B,oBAAoB,cACnBjB,IAAA,CAACP,MAAM,GAAE,CAAC,CACRyB,kBAAkB,cACpBlB,IAAA,CAACN,KAAK,EAAC6D,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEtC,kBAAmB,CAAE,CAAC,CACnDE,aAAa,cACfpB,IAAA,CAAAI,SAAA,EAAAyC,QAAA,cACE3C,KAAA,QAAK4C,SAAS,CAAC,qCAAqC,CAAAD,QAAA,eAElD3C,KAAA,QAAK4C,SAAS,CAAC,mFAAmF,CAAAD,QAAA,eAChG7C,IAAA,QAAA6C,QAAA,CACGzB,aAAa,CAACqC,cAAc,cAC3BzD,IAAA,QACE8C,SAAS,CAAC,8BAA8B,CACxCY,GAAG,CAAEtC,aAAa,CAACuC,cAAe,CAClCC,GAAG,CAAEjE,WAAW,CAAGyB,aAAa,CAACqC,cAAe,CAChDI,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,cAEF5D,IAAA,QACE8C,SAAS,CAAC,8BAA8B,CACxCY,GAAG,CAAEtC,aAAa,CAACuC,cAAe,CAClCC,GAAG,CAAE,yBAA0B,CAC/BC,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CACF,CACE,CAAC,cAEN1D,KAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B3C,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD7C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,kBAAgB,CAAK,CAAC,cACjD7C,IAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B7C,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAACuC,cAAc,CAAM,CAAC,CACtC,CAAC,EACH,CAAC,cACNzD,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD7C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC1C7C,IAAA,QAAK8C,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1B3C,KAAA,QAAA2C,QAAA,EACGT,cAAc,CAAChB,aAAa,CAAC6C,iBAAiB,CAAC,CAAE,GAAG,CACpD7C,aAAa,CAAC6C,iBAAiB,EAC7B,CAAC,CACH,CAAC,EACH,CAAC,cAEN/D,KAAA,QAAK4C,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrD7C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cACzC3C,KAAA,QAAK4C,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1B7C,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAAC8C,eAAe,CAAM,CAAC,cAC1ClE,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAAC+C,mBAAmB,CAAM,CAAC,cAC9CnE,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAACgD,qBAAqB,CAAM,CAAC,EAC7C,CAAC,EACH,CAAC,cACNlE,KAAA,QAAK4C,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrD7C,IAAA,QAAK8C,SAAS,CAAC,WAAW,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cACzC3C,KAAA,QAAK4C,SAAS,CAAC,cAAc,CAAAD,QAAA,eAC3B7C,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAACiD,eAAe,CAAM,CAAC,cAC1CrE,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAACkD,mBAAmB,CAAM,CAAC,cAC9CtE,IAAA,QAAA6C,QAAA,CAAMzB,aAAa,CAACmD,qBAAqB,CAAM,CAAC,EAC7C,CAAC,EACH,CAAC,cACNvE,IAAA,QAAK8C,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC7D7C,IAAA,QAAK8C,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1D3C,KAAA,QAAK4C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,4CAA4C,CAAAD,QAAA,cAEtD7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsD,CAAC,CAAC,mOAAmO,CACtO,CAAC,CACC,CAAC,cAENpD,KAAA,QAAK4C,SAAS,CAAC,MAAM,CAAAD,QAAA,EAAC,SACb,CAACjB,UAAU,CAACR,aAAa,CAACoD,UAAU,CAAC,EACzC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxE,IAAA,QAAK8C,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClC7C,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACzCrB,qBAAqB,cACpBxB,IAAA,CAACP,MAAM,GAAE,CAAC,CACRgC,mBAAmB,cACrBzB,IAAA,CAACN,KAAK,EAAC6D,IAAI,CAAC,OAAO,CAACC,OAAO,CAAE/B,mBAAoB,CAAE,CAAC,cAEpDvB,KAAA,QAAK4C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3C,KAAA,UAAO4C,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClC7C,IAAA,UAAA6C,QAAA,cACE3C,KAAA,OAAI4C,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACtC7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,IAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,+DAA+D,CAAAD,QAAA,CAAC,MAE9E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,mBAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,QAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAAD,QAAA,CAAC,cAE/E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,gEAAgE,CAAK,CAAC,EAClF,CAAC,CACA,CAAC,cAER5C,KAAA,UAAA2C,QAAA,EACGtB,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEkD,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,cAAA,qBAC/B;AACAhF,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC3C,KAAA,MAAG4C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,GACxC,CAAC6B,IAAI,CAACjE,EAAE,EACR,CAAC,CACF,CAAC,cACLT,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA+B,qBAAA,EAAAC,eAAA,CACvCH,IAAI,CAACS,SAAS,UAAAN,eAAA,iBAAdA,eAAA,CAAgBlB,cAAc,UAAAiB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACvC,CAAC,CACF,CAAC,cACL5E,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAiC,qBAAA,EAAAC,aAAA,CACvCL,IAAI,CAACU,OAAO,UAAAL,aAAA,iBAAZA,aAAA,CAAcM,SAAS,UAAAP,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,CACF,CAAC,cACL9E,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAmC,eAAA,CACvCN,IAAI,CAACY,SAAS,UAAAN,eAAA,UAAAA,eAAA,CAAI,KAAK,CACvB,CAAC,CACF,CAAC,cACLhF,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAoC,qBAAA,EAAAC,cAAA,CACvCR,IAAI,CAACa,QAAQ,UAAAL,cAAA,iBAAbA,cAAA,CAAeG,SAAS,UAAAJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,CACF,CAAC,cACLjF,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCF,UAAU,CAAC+B,IAAI,CAACc,mBAAmB,CAAC,CACpC,CAAC,CACF,CAAC,cACLxF,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCjB,UAAU,CAAC8C,IAAI,CAACe,SAAS,CAAC,CAC1B,CAAC,CACF,CAAC,cACLzF,IAAA,OAAI8C,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACxC7C,IAAA,MAAG8C,SAAS,CAAC,2CAA2C,CAAAD,QAAA,cACtD7C,IAAA,CAACd,IAAI,EACH4D,SAAS,CAAC,mBAAmB,CAC7B4C,EAAE,CAAE,qBAAqB,CAAGhB,IAAI,CAACjE,EAAG,CAAAoC,QAAA,cAEpC3C,KAAA,QACE8C,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzE7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsD,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFtD,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsD,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,CACN,CAAC,CACF,CAAC,GA/DEqB,KAgEL,CAAC,GACN,CAAC,cACF3E,IAAA,OAAI8C,SAAS,CAAC,KAAK,CAAK,CAAC,EACpB,CAAC,EACH,CAAC,cACR9C,IAAA,QAAK8C,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf7C,IAAA,CAACF,QAAQ,EACP6F,KAAK,gCAAAC,MAAA,CAAiCnF,EAAE,KAAI,CAC5CoF,MAAM,CAAE,EAAG,CACXlF,IAAI,CAAEA,IAAK,CACXe,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,CACE,CAAC,CACH,CAAC,EACH,CAAC,CACN,CAAC,CACD,IAAI,EACL,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAArB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}