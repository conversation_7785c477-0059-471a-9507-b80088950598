{"ast": null, "code": "import { useMemo } from 'react';\nexport function useConnectDragSource(connector) {\n  return useMemo(function () {\n    return connector.hooks.dragSource();\n  }, [connector]);\n}\nexport function useConnectDragPreview(connector) {\n  return useMemo(function () {\n    return connector.hooks.dragPreview();\n  }, [connector]);\n}", "map": {"version": 3, "names": ["useMemo", "useConnectDragSource", "connector", "hooks", "dragSource", "useConnectDragPreview", "dragPreview"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd/dist/esm/hooks/useDrag/connectors.js"], "sourcesContent": ["import { useMemo } from 'react';\nexport function useConnectDragSource(connector) {\n  return useMemo(function () {\n    return connector.hooks.dragSource();\n  }, [connector]);\n}\nexport function useConnectDragPreview(connector) {\n  return useMemo(function () {\n    return connector.hooks.dragPreview();\n  }, [connector]);\n}"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,OAAO,SAASC,oBAAoBA,CAACC,SAAS,EAAE;EAC9C,OAAOF,OAAO,CAAC,YAAY;IACzB,OAAOE,SAAS,CAACC,KAAK,CAACC,UAAU,CAAC,CAAC;EACrC,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;AACjB;AACA,OAAO,SAASG,qBAAqBA,CAACH,SAAS,EAAE;EAC/C,OAAOF,OAAO,CAAC,YAAY;IACzB,OAAOE,SAAS,CAACC,KAAK,CAACG,WAAW,CAAC,CAAC;EACtC,CAAC,EAAE,CAACJ,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}