{"ast": null, "code": "import axios from \"../../axios\";\nimport { USER_LOGIN_REQUEST, USER_LOGIN_SUCCESS, USER_LOGIN_FAIL, USER_LOGOUT,\n//\nUSER_ADD_SUCCESS, USER_ADD_REQUEST, USER_ADD_FAIL,\n//\nUSER_LIST_SUCCESS, USER_LIST_REQUEST, USER_LIST_FAIL,\n//\nUSER_PROFILE_SUCCESS, USER_PROFILE_REQUEST, USER_PROFILE_FAIL,\n//\nUSER_PROFILE_UPDATE_SUCCESS, USER_PROFILE_UPDATE_REQUEST, USER_PROFILE_UPDATE_FAIL,\n//\nUSER_PASSWORD_UPDATE_SUCCESS, USER_PASSWORD_UPDATE_REQUEST, USER_PASSWORD_UPDATE_FAIL,\n//\nUSER_DELETE_SUCCESS, USER_DELETE_REQUEST, USER_DELETE_FAIL,\n//\nCOORDINATOR_LIST_SUCCESS, COORDINATOR_LIST_REQUEST, COORDINATOR_LIST_FAIL,\n//\nCOORDINATOR_ADD_SUCCESS, COORDINATOR_ADD_REQUEST, COORDINATOR_ADD_FAIL,\n//\nCOORDINATOR_DETAIL_SUCCESS, COORDINATOR_DETAIL_REQUEST, COORDINATOR_DETAIL_FAIL,\n//\nCOORDINATOR_UPDATE_SUCCESS, COORDINATOR_UPDATE_REQUEST, COORDINATOR_UPDATE_FAIL,\n//\nUSER_UPDATE_LOGIN_SUCCESS, USER_UPDATE_LOGIN_REQUEST, USER_UPDATE_LOGIN_FAIL,\n//\nUSER_HISTORY_LOGED_SUCCESS, USER_HISTORY_LOGED_REQUEST, USER_HISTORY_LOGED_FAIL,\n//\nUSER_HISTORY_SUCCESS, USER_HISTORY_REQUEST, USER_HISTORY_FAIL,\n//\nUSER_LOGOUT_SAVE_SUCCESS, USER_LOGOUT_SAVE_REQUEST, USER_LOGOUT_SAVE_FAIL\n//\n} from \"../constants/userConstants\";\nimport { UAParser } from \"ua-parser-js\";\nexport const getHistoryListCoordinator = (page, coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/get-history-coordinator/${coordinator}/?page=${page}`, config);\n    dispatch({\n      type: USER_HISTORY_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getHistoryListLogged = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_LOGED_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/get-history-byloged/?page=${page}`, config);\n    dispatch({\n      type: USER_HISTORY_LOGED_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_LOGED_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateLastLogin = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_UPDATE_LOGIN_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-login-time/`, {}, config);\n    dispatch({\n      type: USER_UPDATE_LOGIN_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_UPDATE_LOGIN_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateCoordinator = (id, coordinator) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/coordinator-update/${id}/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const getCoordinatorDetail = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinator/` + id, config);\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const updateUserPassword = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-password/`, user, config);\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const createNewCoordinator = coordinator => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/create-coordinator/`, coordinator, config);\n    dispatch({\n      type: COORDINATOR_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This Coordinator has not been added, please try again.\"\n    });\n  }\n};\nexport const getListCoordinators = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/coordinators/?page=${page}`, config);\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const deleteUser = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/users/delete/${id}/`, config);\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const updateUserProfile = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.put(`/users/update-profile/`, user, config);\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"Votre profile n'a pas été modifié, réessayez\"\n    });\n  }\n};\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/profile/`, config);\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const addNewUser = user => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/add/`, user, config);\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This user has not been added, please try again.\"\n    });\n  }\n};\nexport const getListUsers = page => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/users/?page=${page}`, config);\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const login = (username, password) => async dispatch => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n    dispatch({\n      type: USER_LOGIN_REQUEST\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\"\n      }\n    };\n    const {\n      data\n    } = await axios.post(\"/users/login/\", {\n      username: username,\n      password: password,\n      device: device,\n      browser: browser\n    }, config);\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    console.log(error);\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\nexport const logout = () => dispatch => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({\n    type: USER_LOGOUT\n  });\n  document.location.href = \"/\";\n};\nexport const LogoutSaved = () => async (dispatch, getState) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n    dispatch({\n      type: USER_LOGOUT_SAVE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/users/add/`, {\n      device: device,\n      browser: browser\n    }, config);\n    dispatch({\n      type: USER_LOGOUT_SAVE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    var err = error.response && error.response.data.detail ? error.response.data.detail : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGOUT_SAVE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : \"This user has not been added, please try again.\"\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_SUCCESS", "USER_ADD_REQUEST", "USER_ADD_FAIL", "USER_LIST_SUCCESS", "USER_LIST_REQUEST", "USER_LIST_FAIL", "USER_PROFILE_SUCCESS", "USER_PROFILE_REQUEST", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_FAIL", "USER_DELETE_SUCCESS", "USER_DELETE_REQUEST", "USER_DELETE_FAIL", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_SUCCESS", "USER_UPDATE_LOGIN_REQUEST", "USER_UPDATE_LOGIN_FAIL", "USER_HISTORY_LOGED_SUCCESS", "USER_HISTORY_LOGED_REQUEST", "USER_HISTORY_LOGED_FAIL", "USER_HISTORY_SUCCESS", "USER_HISTORY_REQUEST", "USER_HISTORY_FAIL", "USER_LOGOUT_SAVE_SUCCESS", "USER_LOGOUT_SAVE_REQUEST", "USER_LOGOUT_SAVE_FAIL", "<PERSON><PERSON><PERSON><PERSON>", "getHistoryListCoordinator", "page", "coordinator", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "get", "payload", "error", "err", "response", "detail", "localStorage", "removeItem", "document", "location", "href", "getHistoryListLogged", "updateLastLogin", "put", "updateCoordinator", "id", "getCoordinatorDetail", "updateUserPassword", "user", "createNewCoordinator", "post", "getListCoordinators", "deleteUser", "delete", "updateUserProfile", "getUserProfile", "addNewUser", "getListUsers", "login", "username", "password", "parser", "result", "getResult", "browser", "name", "device", "vendor", "model", "setItem", "JSON", "stringify", "console", "log", "logout", "LogoutSaved"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/actions/userActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  USER_LOGIN_REQUEST,\n  USER_LOGIN_SUCCESS,\n  USER_LOGIN_FAIL,\n  USER_LOGOUT,\n  //\n  USER_ADD_SUCCESS,\n  USER_ADD_REQUEST,\n  USER_ADD_FAIL,\n  //\n  USER_LIST_SUCCESS,\n  USER_LIST_REQUEST,\n  USER_LIST_FAIL,\n  //\n  USER_PROFILE_SUCCESS,\n  USER_PROFILE_REQUEST,\n  USER_PROFILE_FAIL,\n  //\n  USER_PROFILE_UPDATE_SUCCESS,\n  USER_PROFILE_UPDATE_REQUEST,\n  USER_PROFILE_UPDATE_FAIL,\n  //\n  USER_PASSWORD_UPDATE_SUCCESS,\n  USER_PASSWORD_UPDATE_REQUEST,\n  USER_PASSWORD_UPDATE_FAIL,\n  //\n  USER_DELETE_SUCCESS,\n  USER_DELETE_REQUEST,\n  USER_DELETE_FAIL,\n  //\n  COORDINATOR_LIST_SUCCESS,\n  COORDINATOR_LIST_REQUEST,\n  COORDINATOR_LIST_FAIL,\n  //\n  COORDINATOR_ADD_SUCCESS,\n  COORDINATOR_ADD_REQUEST,\n  COORDINATOR_ADD_FAIL,\n  //\n  COORDINATOR_DETAIL_SUCCESS,\n  COORDINATOR_DETAIL_REQUEST,\n  COORDINATOR_DETAIL_FAIL,\n  //\n  COORDINATOR_UPDATE_SUCCESS,\n  COORDINATOR_UPDATE_REQUEST,\n  COORDINATOR_UPDATE_FAIL,\n  //\n  USER_UPDATE_LOGIN_SUCCESS,\n  USER_UPDATE_LOGIN_REQUEST,\n  USER_UPDATE_LOGIN_FAIL,\n  //\n  USER_HISTORY_LOGED_SUCCESS,\n  USER_HISTORY_LOGED_REQUEST,\n  USER_HISTORY_LOGED_FAIL,\n  //\n  USER_HISTORY_SUCCESS,\n  USER_HISTORY_REQUEST,\n  USER_HISTORY_FAIL,\n\n  //\n  USER_LOGOUT_SAVE_SUCCESS,\n  USER_LOGOUT_SAVE_REQUEST,\n  USER_LOGOUT_SAVE_FAIL,\n  //\n} from \"../constants/userConstants\";\nimport { UAParser } from \"ua-parser-js\";\n\nexport const getHistoryListCoordinator =\n  (page, coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: USER_HISTORY_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.get(\n        `/users/get-history-coordinator/${coordinator}/?page=${page}`,\n        config\n      );\n\n      dispatch({\n        type: USER_HISTORY_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: USER_HISTORY_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const getHistoryListLogged = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_HISTORY_LOGED_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/users/get-history-byloged/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: USER_HISTORY_LOGED_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_HISTORY_LOGED_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateLastLogin = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_UPDATE_LOGIN_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-login-time/`, {}, config);\n\n    dispatch({\n      type: USER_UPDATE_LOGIN_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_UPDATE_LOGIN_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateCoordinator =\n  (id, coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_UPDATE_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"multipart/form-data\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.put(\n        `/users/coordinator-update/${id}/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_UPDATE_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_UPDATE_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : error.detail,\n      });\n    }\n  };\n\nexport const getCoordinatorDetail = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/coordinator/` + id, config);\n\n    dispatch({\n      type: COORDINATOR_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const updateUserPassword = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PASSWORD_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-password/`, user, config);\n\n    dispatch({\n      type: USER_PASSWORD_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PASSWORD_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const createNewCoordinator =\n  (coordinator) => async (dispatch, getState) => {\n    try {\n      dispatch({\n        type: COORDINATOR_ADD_REQUEST,\n      });\n      var {\n        userLogin: { userInfo },\n      } = getState();\n      const config = {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${userInfo.access}`,\n        },\n      };\n      const { data } = await axios.post(\n        `/users/create-coordinator/`,\n        coordinator,\n        config\n      );\n\n      dispatch({\n        type: COORDINATOR_ADD_SUCCESS,\n        payload: data,\n      });\n    } catch (error) {\n      var err =\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail;\n      if (err) {\n        if (err === \"Given token not valid for any token type\") {\n          localStorage.removeItem(\"userInfoUnimedCare\");\n          document.location.href = \"/\";\n        }\n      }\n      dispatch({\n        type: COORDINATOR_ADD_FAIL,\n        payload:\n          error.response && error.response.data.detail\n            ? error.response.data.detail\n            : \"This Coordinator has not been added, please try again.\",\n      });\n    }\n  };\n\nexport const getListCoordinators = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: COORDINATOR_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/users/coordinators/?page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: COORDINATOR_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: COORDINATOR_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const deleteUser = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/users/delete/${id}/`, config);\n\n    dispatch({\n      type: USER_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const updateUserProfile = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(`/users/update-profile/`, user, config);\n\n    dispatch({\n      type: USER_PROFILE_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"Votre profile n'a pas été modifié, réessayez\",\n    });\n  }\n};\n\nexport const getUserProfile = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_PROFILE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/profile/`, config);\n\n    dispatch({\n      type: USER_PROFILE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_PROFILE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const addNewUser = (user) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/users/add/`, user, config);\n\n    dispatch({\n      type: USER_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"This user has not been added, please try again.\",\n    });\n  }\n};\n\nexport const getListUsers = (page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: USER_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/users/?page=${page}`, config);\n\n    dispatch({\n      type: USER_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const login = (username, password) => async (dispatch) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n\n    dispatch({\n      type: USER_LOGIN_REQUEST,\n    });\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n      },\n    };\n    const { data } = await axios.post(\n      \"/users/login/\",\n      {\n        username: username,\n        password: password,\n        device: device,\n        browser: browser,\n      },\n      config\n    );\n\n    dispatch({\n      type: USER_LOGIN_SUCCESS,\n      payload: data,\n    });\n    localStorage.setItem(\"userInfoUnimedCare\", JSON.stringify(data));\n  } catch (error) {\n    console.log(error);\n\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGIN_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\nexport const logout = () => (dispatch) => {\n  localStorage.removeItem(\"userInfoUnimedCare\");\n  dispatch({ type: USER_LOGOUT });\n  document.location.href = \"/\";\n};\n\nexport const LogoutSaved = () => async (dispatch, getState) => {\n  try {\n    const parser = new UAParser();\n    const result = parser.getResult();\n\n    const browser = result.browser.name || \"Unknown browser\";\n    let device = \"\";\n    if (result.device.vendor) {\n      device = result.device.vendor + \" - \";\n    }\n    device += result.device.model || result.device.type || \"Unknown device\";\n\n    dispatch({\n      type: USER_LOGOUT_SAVE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(\n      `/users/add/`,\n      { device: device, browser: browser },\n      config\n    );\n\n    dispatch({\n      type: USER_LOGOUT_SAVE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    var err =\n      error.response && error.response.data.detail\n        ? error.response.data.detail\n        : error.detail;\n    if (err) {\n      if (err === \"Given token not valid for any token type\") {\n        localStorage.removeItem(\"userInfoUnimedCare\");\n        document.location.href = \"/\";\n      }\n    }\n    dispatch({\n      type: USER_LOGOUT_SAVE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : \"This user has not been added, please try again.\",\n    });\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe,EACfC,WAAW;AACX;AACAC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa;AACb;AACAC,iBAAiB,EACjBC,iBAAiB,EACjBC,cAAc;AACd;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,2BAA2B,EAC3BC,2BAA2B,EAC3BC,wBAAwB;AACxB;AACAC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,yBAAyB;AACzB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC,qBAAqB;AACrB;AACAC,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB;AACpB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,yBAAyB,EACzBC,yBAAyB,EACzBC,sBAAsB;AACtB;AACAC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,uBAAuB;AACvB;AACAC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AAEjB;AACAC,wBAAwB,EACxBC,wBAAwB,EACxBC;AACA;AAAA,OACK,4BAA4B;AACnC,SAASC,QAAQ,QAAQ,cAAc;AAEvC,OAAO,MAAMC,yBAAyB,GACpCA,CAACC,IAAI,EAAEC,WAAW,KAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACnD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEX;IACR,CAAC,CAAC;IACF,IAAI;MACFY,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAC7B,kCAAiCX,WAAY,UAASD,IAAK,EAAC,EAC7DO,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEZ,oBAAoB;MAC1BqB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEV,iBAAiB;MACvBmB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMM,oBAAoB,GAAIvB,IAAI,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EAC1E,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEd;IACR,CAAC,CAAC;IACF,IAAI;MACFe,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAC7B,oCAAmCZ,IAAK,EAAC,EAC1CO,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEf,0BAA0B;MAChCwB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEb,uBAAuB;MAC7BsB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMO,eAAe,GAAGA,CAAA,KAAM,OAAOtB,QAAQ,EAAEC,QAAQ,KAAK;EACjE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEjB;IACR,CAAC,CAAC;IACF,IAAI;MACFkB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC0E,GAAG,CAAE,2BAA0B,EAAE,CAAC,CAAC,EAAElB,MAAM,CAAC;IAEzEL,QAAQ,CAAC;MACPE,IAAI,EAAElB,yBAAyB;MAC/B2B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEhB,sBAAsB;MAC5ByB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMS,iBAAiB,GAC5BA,CAACC,EAAE,EAAE1B,WAAW,KAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EACjD,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEpB;IACR,CAAC,CAAC;IACF,IAAI;MACFqB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC0E,GAAG,CAC7B,6BAA4BE,EAAG,GAAE,EAClC1B,WAAW,EACXM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAErB,0BAA0B;MAChC8B,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEnB,uBAAuB;MAC7B4B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMW,oBAAoB,GAAID,EAAE,IAAK,OAAOzB,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEvB;IACR,CAAC,CAAC;IACF,IAAI;MACFwB,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAAE,qBAAoB,GAAGe,EAAE,EAAEpB,MAAM,CAAC;IAEpEL,QAAQ,CAAC;MACPE,IAAI,EAAExB,0BAA0B;MAChCiC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEtB,uBAAuB;MAC7B+B,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMY,kBAAkB,GAAIC,IAAI,IAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EACxE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEnC;IACR,CAAC,CAAC;IACF,IAAI;MACFoC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC0E,GAAG,CAAE,yBAAwB,EAAEK,IAAI,EAAEvB,MAAM,CAAC;IAEzEL,QAAQ,CAAC;MACPE,IAAI,EAAEpC,4BAA4B;MAClC6C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElC,yBAAyB;MAC/B2C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMc,oBAAoB,GAC9B9B,WAAW,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAC7C,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE1B;IACR,CAAC,CAAC;IACF,IAAI;MACF2B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAACiF,IAAI,CAC9B,4BAA2B,EAC5B/B,WAAW,EACXM,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAE3B,uBAAuB;MAC7BoC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEzB,oBAAoB;MAC1BkC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAEH,OAAO,MAAMgB,mBAAmB,GAAIjC,IAAI,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EACzE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE7B;IACR,CAAC,CAAC;IACF,IAAI;MACF8B,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAC7B,6BAA4BZ,IAAK,EAAC,EACnCO,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAE9B,wBAAwB;MAC9BuC,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE5B,qBAAqB;MAC3BqC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMiB,UAAU,GAAIP,EAAE,IAAK,OAAOzB,QAAQ,EAAEC,QAAQ,KAAK;EAC9D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEhC;IACR,CAAC,CAAC;IACF,IAAI;MACFiC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAACoF,MAAM,CAAE,iBAAgBR,EAAG,GAAE,EAAEpB,MAAM,CAAC;IAEnEL,QAAQ,CAAC;MACPE,IAAI,EAAEjC,mBAAmB;MACzB0C,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE/B,gBAAgB;MACtBwC,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMmB,iBAAiB,GAAIN,IAAI,IAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EACvE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEtC;IACR,CAAC,CAAC;IACF,IAAI;MACFuC,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,qBAAqB;QACrCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC0E,GAAG,CAAE,wBAAuB,EAAEK,IAAI,EAAEvB,MAAM,CAAC;IAExEL,QAAQ,CAAC;MACPE,IAAI,EAAEvC,2BAA2B;MACjCgD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAErC,wBAAwB;MAC9B8C,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMoB,cAAc,GAAGA,CAAA,KAAM,OAAOnC,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEzC;IACR,CAAC,CAAC;IACF,IAAI;MACF0C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAAE,iBAAgB,EAAEL,MAAM,CAAC;IAE3DL,QAAQ,CAAC;MACPE,IAAI,EAAE1C,oBAAoB;MAC1BmD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAExC,iBAAiB;MACvBiD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMqB,UAAU,GAAIR,IAAI,IAAK,OAAO5B,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE/C;IACR,CAAC,CAAC;IACF,IAAI;MACFgD,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAACiF,IAAI,CAAE,aAAY,EAAEF,IAAI,EAAEvB,MAAM,CAAC;IAE9DL,QAAQ,CAAC;MACPE,IAAI,EAAEhD,gBAAgB;MACtByD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE9C,aAAa;MACnBuD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMsB,YAAY,GAAIvC,IAAI,IAAK,OAAOE,QAAQ,EAAEC,QAAQ,KAAK;EAClE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAE5C;IACR,CAAC,CAAC;IACF,IAAI;MACF6C,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAAC6D,GAAG,CAAE,gBAAeZ,IAAK,EAAC,EAAEO,MAAM,CAAC;IAEhEL,QAAQ,CAAC;MACPE,IAAI,EAAE7C,iBAAiB;MACvBsD,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAE3C,cAAc;MACpBoD,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMuB,KAAK,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,KAAK,MAAOxC,QAAQ,IAAK;EAC/D,IAAI;IACF,MAAMyC,MAAM,GAAG,IAAI7C,QAAQ,CAAC,CAAC;IAC7B,MAAM8C,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;IAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;IACxD,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIJ,MAAM,CAACI,MAAM,CAACC,MAAM,EAAE;MACxBD,MAAM,GAAGJ,MAAM,CAACI,MAAM,CAACC,MAAM,GAAG,KAAK;IACvC;IACAD,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACE,KAAK,IAAIN,MAAM,CAACI,MAAM,CAAC5C,IAAI,IAAI,gBAAgB;IAEvEF,QAAQ,CAAC;MACPE,IAAI,EAAEpD;IACR,CAAC,CAAC;IACF,MAAMuD,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC;IACD,MAAM;MAAEG;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAACiF,IAAI,CAC/B,eAAe,EACf;MACES,QAAQ,EAAEA,QAAQ;MAClBC,QAAQ,EAAEA,QAAQ;MAClBM,MAAM,EAAEA,MAAM;MACdF,OAAO,EAAEA;IACX,CAAC,EACDvC,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAEnD,kBAAkB;MACxB4D,OAAO,EAAEF;IACX,CAAC,CAAC;IACFO,YAAY,CAACiC,OAAO,CAAC,oBAAoB,EAAEC,IAAI,CAACC,SAAS,CAAC1C,IAAI,CAAC,CAAC;EAClE,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdwC,OAAO,CAACC,GAAG,CAACzC,KAAK,CAAC;IAElB,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAElD,eAAe;MACrB2D,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG;IACd,CAAC,CAAC;EACJ;AACF,CAAC;AAED,OAAO,MAAMuC,MAAM,GAAGA,CAAA,KAAOtD,QAAQ,IAAK;EACxCgB,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;EAC7CjB,QAAQ,CAAC;IAAEE,IAAI,EAAEjD;EAAY,CAAC,CAAC;EAC/BiE,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;AAC9B,CAAC;AAED,OAAO,MAAMmC,WAAW,GAAGA,CAAA,KAAM,OAAOvD,QAAQ,EAAEC,QAAQ,KAAK;EAC7D,IAAI;IACF,MAAMwC,MAAM,GAAG,IAAI7C,QAAQ,CAAC,CAAC;IAC7B,MAAM8C,MAAM,GAAGD,MAAM,CAACE,SAAS,CAAC,CAAC;IAEjC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO,CAACC,IAAI,IAAI,iBAAiB;IACxD,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIJ,MAAM,CAACI,MAAM,CAACC,MAAM,EAAE;MACxBD,MAAM,GAAGJ,MAAM,CAACI,MAAM,CAACC,MAAM,GAAG,KAAK;IACvC;IACAD,MAAM,IAAIJ,MAAM,CAACI,MAAM,CAACE,KAAK,IAAIN,MAAM,CAACI,MAAM,CAAC5C,IAAI,IAAI,gBAAgB;IAEvEF,QAAQ,CAAC;MACPE,IAAI,EAAER;IACR,CAAC,CAAC;IACF,IAAI;MACFS,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAM5D,KAAK,CAACiF,IAAI,CAC9B,aAAY,EACb;MAAEgB,MAAM,EAAEA,MAAM;MAAEF,OAAO,EAAEA;IAAQ,CAAC,EACpCvC,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAET,wBAAwB;MAC9BkB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACd,IAAIC,GAAG,GACLD,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1BH,KAAK,CAACG,MAAM;IAClB,IAAIF,GAAG,EAAE;MACP,IAAIA,GAAG,KAAK,0CAA0C,EAAE;QACtDG,YAAY,CAACC,UAAU,CAAC,oBAAoB,CAAC;QAC7CC,QAAQ,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAG;MAC9B;IACF;IACApB,QAAQ,CAAC;MACPE,IAAI,EAAEP,qBAAqB;MAC3BgB,OAAO,EACLC,KAAK,CAACE,QAAQ,IAAIF,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GACxCH,KAAK,CAACE,QAAQ,CAACL,IAAI,CAACM,MAAM,GAC1B;IACR,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}