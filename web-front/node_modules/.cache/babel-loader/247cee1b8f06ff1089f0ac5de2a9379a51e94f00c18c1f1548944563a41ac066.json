{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js\",\n  _s = $RefreshSig$();\nimport { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport dashboardIcon from \"./../images/icon/dashboard-icon.png\";\nimport contactSupportIcon from \"./../images/icon/contactsupport-icon.png\";\nimport faqtIcon from \"./../images/icon/faqIcon.png\";\nimport helpIcon from \"./../images/icon/helpIcon.png\";\nimport casesIcon from \"./../images/icon/cases-icon.png\";\nimport logoProjet from \"./../images/logo-project.png\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  props,\n  sidebarOpen,\n  setSidebarOpen\n}) => {\n  _s();\n  const location = useLocation();\n  const {\n    pathname\n  } = location;\n  const navigate = useNavigate();\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    error,\n    loading\n  } = userLogin;\n  const [codeSearch, setCodeSearch] = useState(\"\");\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    className: `absolute left-0 top-0 z-999999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/dashboard\",\n        className: \"w-full\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: logoProjet,\n          cl: true,\n          alt: \"Logo\",\n          className: \"text-white mx-auto max-h-25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        // ref={trigger}\n        onClick: () => {\n          setSidebarOpen(!sidebarOpen);\n        },\n        \"aria-controls\": \"sidebar\",\n        \"aria-expanded\": sidebarOpen,\n        className: \"block lg:hidden text-black\",\n        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"fill-current\",\n          width: \"20\",\n          height: \"18\",\n          viewBox: \"0 0 20 18\",\n          fill: \"none\",\n          xmlns: \"http://www.w3.org/2000/svg\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            d: \"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\",\n            fill: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\",\n      children: /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"mt-3 py-4 px-4 lg:mt-9 lg:px-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-6 flex flex-col gap-1.5\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/dashboard\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"dashboard\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: dashboardIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 19\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/cases-list\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"cases-list\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: casesIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 19\n                }, this), \"Cases List\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/providers-map\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"providers-map\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: dashboardIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), \"Providers Map\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), userInfo && (userInfo.role === \"1\" || userInfo.role === 1 || userInfo.role === \"2\" || userInfo.role === 2) ? /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/insurances-company\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"insurances-company\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: dashboardIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 152,\n                  columnNumber: 21\n                }, this), \"Insurance Company\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this) : null, userInfo && (userInfo.role === \"1\" || userInfo.role === 1 || userInfo.role === \"2\" || userInfo.role === 2) ? /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/coordinator-space\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"coordinator-space\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: dashboardIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this), \"Coordinator Space\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this) : null, /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink\n              // to=\"/settings\"\n              , {\n                to: \"/settings\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"settings\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: dashboardIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), \"Settings\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/contact-support\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"contact-support\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  className: \"size-6 text-danger\",\n                  src: contactSupportIcon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), \"Contact Support\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(NavLink, {\n                to: \"/logout\",\n                className: `group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${pathname.includes(\"logout\") && \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"25\",\n                  height: \"24\",\n                  viewBox: \"0 0 25 24\",\n                  fill: \"none\",\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12.5376 4V12\",\n                    stroke: \"#DB3C3F\",\n                    \"stroke-width\": \"1.5\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), \"Log Out\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"Z3LsERCkBCFGXiD6ISFswwB1TzU=\", false, function () {\n  return [useLocation, useNavigate, useDispatch, useSelector];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["NavLink", "useLocation", "useNavigate", "useEffect", "useState", "logoMini", "dashboardIcon", "contactSupportIcon", "faqtIcon", "helpIcon", "casesIcon", "logoProjet", "useDispatch", "useSelector", "toast", "jsxDEV", "_jsxDEV", "Sidebar", "props", "sidebarOpen", "setSidebarOpen", "_s", "location", "pathname", "navigate", "openParametrs", "setOpenParametrs", "openDepenses", "setOpenDepenses", "dispatch", "userLogin", "state", "userInfo", "error", "loading", "codeSearch", "setCodeSearch", "redirect", "console", "log", "includes", "className", "children", "to", "src", "cl", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "width", "height", "viewBox", "fill", "xmlns", "d", "role", "stroke", "strokeLinecap", "strokeLinejoin", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/Sidebar.js"], "sourcesContent": ["import { NavLink, useLocation, useNavigate } from \"react-router-dom\";\nimport { useEffect, useState } from \"react\";\n\nimport logoMini from \"./../images/icon/tassyer-logo-min.png\";\nimport dashboardIcon from \"./../images/icon/dashboard-icon.png\";\nimport contactSupportIcon from \"./../images/icon/contactsupport-icon.png\";\nimport faqtIcon from \"./../images/icon/faqIcon.png\";\nimport helpIcon from \"./../images/icon/helpIcon.png\";\nimport casesIcon from \"./../images/icon/cases-icon.png\";\nimport logoProjet from \"./../images/logo-project.png\";\n\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { toast } from \"react-toastify\";\n\nconst Sidebar = ({ props, sidebarOpen, setSidebarOpen }) => {\n  const location = useLocation();\n  const { pathname } = location;\n  const navigate = useNavigate();\n\n  const [openParametrs, setOpenParametrs] = useState(false);\n  const [openDepenses, setOpenDepenses] = useState(false);\n\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, error, loading } = userLogin;\n\n  const [codeSearch, setCodeSearch] = useState(\"\");\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      console.log(userInfo);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (pathname.includes(\"/settings\")) {\n      setOpenParametrs(true);\n    }\n    if (pathname.includes(\"/depenses\")) {\n      setOpenDepenses(true);\n    }\n  }, [pathname]);\n\n  return (\n    <aside\n      className={`absolute left-0 top-0 z-999999 flex h-screen w-72.5 flex-col overflow-y-hidden bg-[#f9fafa] shadow duration-300 ease-linear dark:bg-boxdark lg:static lg:translate-x-0 ${\n        sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\"\n      }`}\n    >\n      {/* <!-- SIDEBAR HEADER --> */}\n      <div className=\"flex items-center justify-between gap-2 px-6 py-5.5 lg:py-6.5\">\n        <NavLink to=\"/dashboard\" className=\"w-full\">\n          <img\n            src={logoProjet}\n            cl\n            alt=\"Logo\"\n            className=\"text-white mx-auto max-h-25\"\n          />\n        </NavLink>\n\n        <button\n          // ref={trigger}\n          onClick={() => {\n            setSidebarOpen(!sidebarOpen);\n          }}\n          aria-controls=\"sidebar\"\n          aria-expanded={sidebarOpen}\n          className=\"block lg:hidden text-black\"\n        >\n          <svg\n            className=\"fill-current\"\n            width=\"20\"\n            height=\"18\"\n            viewBox=\"0 0 20 18\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <path\n              d=\"M19 8.175H2.98748L9.36248 1.6875C9.69998 1.35 9.69998 0.825 9.36248 0.4875C9.02498 0.15 8.49998 0.15 8.16248 0.4875L0.399976 8.3625C0.0624756 8.7 0.0624756 9.225 0.399976 9.5625L8.16248 17.4375C8.31248 17.5875 8.53748 17.7 8.76248 17.7C8.98748 17.7 9.17498 17.625 9.36248 17.475C9.69998 17.1375 9.69998 16.6125 9.36248 16.275L3.02498 9.8625H19C19.45 9.8625 19.825 9.4875 19.825 9.0375C19.825 8.55 19.45 8.175 19 8.175Z\"\n              fill=\"\"\n            />\n          </svg>\n        </button>\n      </div>\n      {/* <!-- SIDEBAR HEADER --> */}\n\n      <div className=\"no-scrollbar flex flex-col overflow-y-auto duration-300 ease-linear\">\n        {/* <!-- Sidebar Menu --> */}\n        <nav className=\"mt-3 py-4 px-4 lg:mt-9 lg:px-6\">\n          {/* <!-- Menu Group --> */}\n          <div>\n            {/*  */}\n            <ul className=\"mb-6 flex flex-col gap-1.5\">\n              {/* Tableau de bord */}\n\n              <li>\n                <NavLink\n                  to=\"/dashboard\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"dashboard\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={dashboardIcon} />\n                  Dashboard\n                </NavLink>\n              </li>\n              {/* Cases List */}\n              <li>\n                <NavLink\n                  to=\"/cases-list\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"cases-list\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={casesIcon} />\n                  Cases List\n                </NavLink>\n              </li>\n              {/* Providers Map */}\n              <li>\n                <NavLink\n                  to=\"/providers-map\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"providers-map\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={dashboardIcon} />\n                  Providers Map\n                </NavLink>\n              </li>\n              {/* Insurance Company */}\n              {userInfo &&\n              (userInfo.role === \"1\" ||\n                userInfo.role === 1 ||\n                userInfo.role === \"2\" ||\n                userInfo.role === 2) ? (\n                <li>\n                  <NavLink\n                    to=\"/insurances-company\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"insurances-company\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <img className=\"size-6 text-danger\" src={dashboardIcon} />\n                    Insurance Company\n                  </NavLink>\n                </li>\n              ) : null}\n              {/* Coordinator Space */}\n              {userInfo &&\n              (userInfo.role === \"1\" ||\n                userInfo.role === 1 ||\n                userInfo.role === \"2\" ||\n                userInfo.role === 2) ? (\n                <li>\n                  <NavLink\n                    to=\"/coordinator-space\"\n                    className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                      pathname.includes(\"coordinator-space\") &&\n                      \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                    }`}\n                  >\n                    <img className=\"size-6 text-danger\" src={dashboardIcon} />\n                    Coordinator Space\n                  </NavLink>\n                </li>\n              ) : null}\n\n              {/* Settings */}\n              <li>\n                <NavLink\n                  // to=\"/settings\"\n                  to=\"/settings\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"settings\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={dashboardIcon} />\n                  Settings\n                </NavLink>\n              </li>\n              {/* Help */}\n              {/* <li>\n                <NavLink\n                  to=\"/help\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"help\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={helpIcon} />\n                  Help\n                </NavLink>\n              </li> */}\n              {/* FAQ */}\n              {/* <li>\n                <NavLink\n                  to=\"/faq\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"faq\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img className=\"size-6 text-danger\" src={faqtIcon} />\n                  FAQ\n                </NavLink>\n              </li> */}\n              {/* Contact Support */}\n              <li>\n                <NavLink\n                  to=\"/contact-support\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"contact-support\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <img\n                    className=\"size-6 text-danger\"\n                    src={contactSupportIcon}\n                  />\n                  Contact Support\n                </NavLink>\n              </li>\n\n              <hr />\n              {/* Déconnexion */}\n              <li>\n                <NavLink\n                  to=\"/logout\"\n                  className={`group relative flex items-center gap-2.5 text-sm py-3 px-4 font-bold text-black h-12  hover:bg-[#f7f9fc] hover:border hover:border-[#7FD082] hover:rounded-md  ${\n                    pathname.includes(\"logout\") &&\n                    \"bg-[#f7f9fc] border border-[#7FD082] rounded-md \"\n                  }`}\n                >\n                  <svg\n                    width=\"25\"\n                    height=\"24\"\n                    viewBox=\"0 0 25 24\"\n                    fill=\"none\"\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                  >\n                    <path\n                      d=\"M18.1946 6.34277C21.3186 9.46677 21.3186 14.5328 18.1946 17.6568C15.0706 20.7808 10.0046 20.7808 6.8806 17.6568C3.7566 14.5328 3.7566 9.46677 6.8806 6.34277\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    />\n                    <path\n                      d=\"M12.5376 4V12\"\n                      stroke=\"#DB3C3F\"\n                      stroke-width=\"1.5\"\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                    />\n                  </svg>\n                  Log Out\n                </NavLink>\n              </li>\n            </ul>\n          </div>\n        </nav>\n      </div>\n    </aside>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,SAASA,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACpE,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE3C,OAAOC,QAAQ,MAAM,uCAAuC;AAC5D,OAAOC,aAAa,MAAM,qCAAqC;AAC/D,OAAOC,kBAAkB,MAAM,0CAA0C;AACzE,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,QAAQ,MAAM,+BAA+B;AACpD,OAAOC,SAAS,MAAM,iCAAiC;AACvD,OAAOC,UAAU,MAAM,8BAA8B;AAErD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,OAAO,GAAGA,CAAC;EAAEC,KAAK;EAAEC,WAAW;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAS,CAAC,GAAGD,QAAQ;EAC7B,MAAME,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACuB,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAEvD,MAAMyB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAE9B,MAAMkB,SAAS,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGJ,SAAS;EAE9C,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAMiC,QAAQ,GAAG,GAAG;EACpBlC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC6B,QAAQ,EAAE;MACbR,QAAQ,CAACa,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLC,OAAO,CAACC,GAAG,CAACP,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACR,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,CAAC,CAAC;EAElC1B,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCd,gBAAgB,CAAC,IAAI,CAAC;IACxB;IACA,IAAIH,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClCZ,eAAe,CAAC,IAAI,CAAC;IACvB;EACF,CAAC,EAAE,CAACL,QAAQ,CAAC,CAAC;EAEd,oBACEP,OAAA;IACEyB,SAAS,EAAG,0KACVtB,WAAW,GAAG,eAAe,GAAG,mBACjC,EAAE;IAAAuB,QAAA,gBAGH1B,OAAA;MAAKyB,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5E1B,OAAA,CAAChB,OAAO;QAAC2C,EAAE,EAAC,YAAY;QAACF,SAAS,EAAC,QAAQ;QAAAC,QAAA,eACzC1B,OAAA;UACE4B,GAAG,EAAEjC,UAAW;UAChBkC,EAAE;UACFC,GAAG,EAAC,MAAM;UACVL,SAAS,EAAC;QAA6B;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEVlC,OAAA;QACE;QACAmC,OAAO,EAAEA,CAAA,KAAM;UACb/B,cAAc,CAAC,CAACD,WAAW,CAAC;QAC9B,CAAE;QACF,iBAAc,SAAS;QACvB,iBAAeA,WAAY;QAC3BsB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eAEtC1B,OAAA;UACEyB,SAAS,EAAC,cAAc;UACxBW,KAAK,EAAC,IAAI;UACVC,MAAM,EAAC,IAAI;UACXC,OAAO,EAAC,WAAW;UACnBC,IAAI,EAAC,MAAM;UACXC,KAAK,EAAC,4BAA4B;UAAAd,QAAA,eAElC1B,OAAA;YACEyC,CAAC,EAAC,oaAAoa;YACtaF,IAAI,EAAC;UAAE;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNlC,OAAA;MAAKyB,SAAS,EAAC,qEAAqE;MAAAC,QAAA,eAElF1B,OAAA;QAAKyB,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE7C1B,OAAA;UAAA0B,QAAA,eAEE1B,OAAA;YAAIyB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBAGxC1B,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,YAAY;gBACfF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,WAAW,CAAC,IAC9B,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAEtC;gBAAc;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,aAAa;gBAChBF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,YAAY,CAAC,IAC/B,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAElC;gBAAU;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,cAExD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,gBAAgB;gBACnBF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,eAAe,CAAC,IAClC,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAEtC;gBAAc;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAEJlB,QAAQ,KACRA,QAAQ,CAAC0B,IAAI,KAAK,GAAG,IACpB1B,QAAQ,CAAC0B,IAAI,KAAK,CAAC,IACnB1B,QAAQ,CAAC0B,IAAI,KAAK,GAAG,IACrB1B,QAAQ,CAAC0B,IAAI,KAAK,CAAC,CAAC,gBACpB1C,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,qBAAqB;gBACxBF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,oBAAoB,CAAC,IACvC,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAEtC;gBAAc;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,GACH,IAAI,EAEPlB,QAAQ,KACRA,QAAQ,CAAC0B,IAAI,KAAK,GAAG,IACpB1B,QAAQ,CAAC0B,IAAI,KAAK,CAAC,IACnB1B,QAAQ,CAAC0B,IAAI,KAAK,GAAG,IACrB1B,QAAQ,CAAC0B,IAAI,KAAK,CAAC,CAAC,gBACpB1C,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,oBAAoB;gBACvBF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,mBAAmB,CAAC,IACtC,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAEtC;gBAAc;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,GACH,IAAI,eAGRlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB;cACC;cAAA;gBACA2C,EAAE,EAAC,WAAW;gBACdF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,UAAU,CAAC,IAC7B,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBAAKyB,SAAS,EAAC,oBAAoB;kBAACG,GAAG,EAAEtC;gBAAc;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAE5D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eA4BLlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,kBAAkB;gBACrBF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,iBAAiB,CAAC,IACpC,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBACEyB,SAAS,EAAC,oBAAoB;kBAC9BG,GAAG,EAAErC;gBAAmB;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,mBAEJ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAELlC,OAAA;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENlC,OAAA;cAAA0B,QAAA,eACE1B,OAAA,CAAChB,OAAO;gBACN2C,EAAE,EAAC,SAAS;gBACZF,SAAS,EAAG,kKACVlB,QAAQ,CAACiB,QAAQ,CAAC,QAAQ,CAAC,IAC3B,kDACD,EAAE;gBAAAE,QAAA,gBAEH1B,OAAA;kBACEoC,KAAK,EAAC,IAAI;kBACVC,MAAM,EAAC,IAAI;kBACXC,OAAO,EAAC,WAAW;kBACnBC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,4BAA4B;kBAAAd,QAAA,gBAElC1B,OAAA;oBACEyC,CAAC,EAAC,8JAA8J;oBAChKE,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClBC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFlC,OAAA;oBACEyC,CAAC,EAAC,eAAe;oBACjBE,MAAM,EAAC,SAAS;oBAChB,gBAAa,KAAK;oBAClBC,aAAa,EAAC,OAAO;oBACrBC,cAAc,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,WAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAAC7B,EAAA,CApQIJ,OAAO;EAAA,QACMhB,WAAW,EAEXC,WAAW,EAKXU,WAAW,EAEVC,WAAW;AAAA;AAAAiD,EAAA,GAVzB7C,OAAO;AAsQb,eAAeA,OAAO;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}