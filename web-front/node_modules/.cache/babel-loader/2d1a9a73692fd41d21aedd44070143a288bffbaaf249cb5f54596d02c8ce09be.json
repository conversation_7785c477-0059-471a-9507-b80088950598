{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/Project Location/web-location/src/screens/agences/AgenceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { deleteAgence, getListAgences } from \"../../redux/actions/agenceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AgenceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1] ? location.search.split(\"&\")[1].split(\"=\")[1] : 1;\n  const dispatch = useDispatch();\n  const [agenceId, setAgenceId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listAgence = useSelector(state => state.agenceList);\n  const {\n    agences,\n    loading,\n    error,\n    pages\n  } = listAgence;\n  const agenceDelete = useSelector(state => state.deleteAgence);\n  const {\n    loadingAgenceDelete,\n    errorAgenceDelete,\n    successAgenceDelete\n  } = agenceDelete;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListAgences(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  useEffect(() => {\n    if (successAgenceDelete) {\n      dispatch(getListAgences(\"1\"));\n      setAgenceId(\"\");\n      setIsDelete(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successAgenceDelete]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Agences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black \",\n            children: \"Gestion des agences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/agences/add\",\n            className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), \"Ajouter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left dark:bg-meta-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"N\\xB0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Nom\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"R\\xE9sponsable\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"GSM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Adresse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"T\\xE9l\\xE9phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [agences === null || agences === void 0 ? void 0 : agences.map((agence, id) => {\n                var _agence$name, _agence$responsable, _agence$gsm_phone, _agence$address, _agence$phone, _agence$email;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: agence.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$name = agence.name) !== null && _agence$name !== void 0 ? _agence$name : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 169,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 168,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$responsable = agence.responsable) !== null && _agence$responsable !== void 0 ? _agence$responsable : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$gsm_phone = agence.gsm_phone) !== null && _agence$gsm_phone !== void 0 ? _agence$gsm_phone : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 179,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$address = agence.address) !== null && _agence$address !== void 0 ? _agence$address : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$phone = agence.phone) !== null && _agence$phone !== void 0 ? _agence$phone : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 189,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max\",\n                      children: (_agence$email = agence.email) !== null && _agence$email !== void 0 ? _agence$email : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 194,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4 \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/agences/edit/\" + agence.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 214,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 206,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 202,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"mx-1 delete-class\",\n                        onClick: () => {\n                          setEventType(\"delete\");\n                          setAgenceId(agence.id);\n                          setIsDelete(true);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 230,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 222,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/agences?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ConfirmationModal, {\n        isOpen: isDelete,\n        message: eventType === \"delete\" ? \"Êtes-vous sûr de vouloir supprimer cette agence?\" : \"Êtes-vous sûr de vouloir ?\",\n        onConfirm: async () => {\n          if (eventType === \"delete\" && agenceId !== \"\") {\n            setLoadEvent(true);\n            dispatch(deleteAgence(agenceId));\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          } else {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }\n        },\n        onCancel: () => {\n          setIsDelete(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        },\n        loadEvent: loadEvent\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid md:grid-cols-2 w-full container mt-5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n}\n_s(AgenceScreen, \"emCmQZts62wAxyN0R08n8XjaFFY=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = AgenceScreen;\nexport default AgenceScreen;\nvar _c;\n$RefreshReg$(_c, \"AgenceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "deleteAgence", "getListAgences", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "ConfirmationModal", "jsxDEV", "_jsxDEV", "AgenceScreen", "_s", "navigate", "location", "page", "search", "split", "dispatch", "agenceId", "setAgenceId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "listAgence", "agenceList", "agences", "loading", "error", "pages", "agenceDelete", "loadingAgenceDelete", "errorAgenceDelete", "successAgenceDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "type", "message", "map", "agence", "id", "_agence$name", "_agence$responsable", "_agence$gsm_phone", "_agence$address", "_agence$phone", "_agence$email", "name", "responsable", "gsm_phone", "address", "phone", "email", "onClick", "route", "isOpen", "onConfirm", "onCancel", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/agences/AgenceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport {\n  deleteAgence,\n  getListAgences,\n} from \"../../redux/actions/agenceActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction AgenceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n  const dispatch = useDispatch();\n\n  const [agenceId, setAgenceId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listAgence = useSelector((state) => state.agenceList);\n  const { agences, loading, error, pages } = listAgence;\n\n  const agenceDelete = useSelector((state) => state.deleteAgence);\n  const { loadingAgenceDelete, errorAgenceDelete, successAgenceDelete } =\n    agenceDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListAgences(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successAgenceDelete) {\n      dispatch(getListAgences(\"1\"));\n      setAgenceId(\"\");\n      setIsDelete(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successAgenceDelete]);\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Agences</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">\n              Gestion des agences\n            </h4>\n            <Link\n              to={\"/agences/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      N°\n                    </th>\n                    <th className=\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Nom\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Résponsable\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      GSM\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Adresse\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Téléphone\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Email\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {agences?.map((agence, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">{agence.id}</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.responsable ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.gsm_phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.address ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max\">\n                          {agence.email ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/agences/edit/\" + agence.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setAgenceId(agence.id);\n                              setIsDelete(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/agences?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer cette agence?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"delete\" && agenceId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteAgence(agenceId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AgenceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SACEC,YAAY,EACZC,cAAc,QACT,mCAAmC;AAC1C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,iBAAiB,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnE,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,IAAI,GAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAC3C,CAAC;EACL,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM+B,SAAS,GAAG7B,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,UAAU,GAAGhC,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAACG,UAAU,CAAC;EAC3D,MAAM;IAAEC,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGL,UAAU;EAErD,MAAMM,YAAY,GAAGtC,WAAW,CAAE8B,KAAK,IAAKA,KAAK,CAAC1B,YAAY,CAAC;EAC/D,MAAM;IAAEmC,mBAAmB;IAAEC,iBAAiB;IAAEC;EAAoB,CAAC,GACnEH,YAAY;EAEd,MAAMI,QAAQ,GAAG,GAAG;EAEpB7C,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,QAAQ,EAAE;MACbhB,QAAQ,CAAC2B,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLtB,QAAQ,CAACf,cAAc,CAACY,IAAI,CAAC,CAAC;IAChC;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEgB,QAAQ,EAAEX,QAAQ,EAAEH,IAAI,CAAC,CAAC;EAExCpB,SAAS,CAAC,MAAM;IACd,IAAI4C,mBAAmB,EAAE;MACvBrB,QAAQ,CAACf,cAAc,CAAC,GAAG,CAAC,CAAC;MAC7BiB,WAAW,CAAC,EAAE,CAAC;MACfE,WAAW,CAAC,KAAK,CAAC;MAClBI,YAAY,CAAC,EAAE,CAAC;MAChBF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACe,mBAAmB,CAAC,CAAC;EACzB,oBACE7B,OAAA,CAACN,aAAa;IAAAqC,QAAA,eACZ/B,OAAA;MAAA+B,QAAA,gBACE/B,OAAA;QAAKgC,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/B,OAAA;UAAGiC,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/B,OAAA;YAAKgC,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/B,OAAA;cACEkC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/B,OAAA;gBACEsC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5C,OAAA;cAAMgC,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ5C,OAAA;UAAA+B,QAAA,eACE/B,OAAA;YACEkC,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/B,OAAA;cACEsC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP5C,OAAA;UAAKgC,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eACN5C,OAAA;QAAKgC,SAAS,EAAC,6GAA6G;QAAAD,QAAA,gBAC1H/B,OAAA;UAAKgC,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/D/B,OAAA;YAAIgC,SAAS,EAAC,sCAAsC;YAAAD,QAAA,EAAC;UAErD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL5C,OAAA,CAACX,IAAI;YACHwD,EAAE,EAAE,cAAe;YACnBb,SAAS,EAAC,+DAA+D;YAAAD,QAAA,gBAEzE/B,OAAA;cACEkC,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/B,OAAA;gBACEsC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,EAGLrB,OAAO,gBACNvB,OAAA,CAACL,MAAM;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACRpB,KAAK,gBACPxB,OAAA,CAACJ,KAAK;UAACkD,IAAI,EAAC,OAAO;UAACC,OAAO,EAAEvB;QAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtC5C,OAAA;UAAKgC,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9C/B,OAAA;YAAOgC,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClC/B,OAAA;cAAA+B,QAAA,eACE/B,OAAA;gBAAIgC,SAAS,EAAC,oCAAoC;gBAAAD,QAAA,gBAChD/B,OAAA;kBAAIgC,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,EAAC;gBAE3E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,6DAA6D;kBAAAD,QAAA,EAAC;gBAE5E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL5C,OAAA;kBAAIgC,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAER5C,OAAA;cAAA+B,QAAA,GACGT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0B,GAAG,CAAC,CAACC,MAAM,EAAEC,EAAE;gBAAA,IAAAC,YAAA,EAAAC,mBAAA,EAAAC,iBAAA,EAAAC,eAAA,EAAAC,aAAA,EAAAC,aAAA;gBAAA,oBACvBxD,OAAA;kBAAA+B,QAAA,gBACE/B,OAAA;oBAAIgC,SAAS,EAAC,gDAAgD;oBAAAD,QAAA,eAC5D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,EAAEkB,MAAM,CAACC;oBAAE;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAoB,YAAA,GACpCF,MAAM,CAACQ,IAAI,cAAAN,YAAA,cAAAA,YAAA,GAAI;oBAAK;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAqB,mBAAA,GACpCH,MAAM,CAACS,WAAW,cAAAN,mBAAA,cAAAA,mBAAA,GAAI;oBAAK;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAsB,iBAAA,GACpCJ,MAAM,CAACU,SAAS,cAAAN,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAuB,eAAA,GACpCL,MAAM,CAACW,OAAO,cAAAN,eAAA,cAAAA,eAAA,GAAI;oBAAK;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAwB,aAAA,GACpCN,MAAM,CAACY,KAAK,cAAAN,aAAA,cAAAA,aAAA,GAAI;oBAAK;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,0BAA0B;sBAAAD,QAAA,GAAAyB,aAAA,GACpCP,MAAM,CAACa,KAAK,cAAAN,aAAA,cAAAA,aAAA,GAAI;oBAAK;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAEL5C,OAAA;oBAAIgC,SAAS,EAAC,iDAAiD;oBAAAD,QAAA,eAC7D/B,OAAA;sBAAGgC,SAAS,EAAC,wCAAwC;sBAAAD,QAAA,gBAEnD/B,OAAA,CAACX,IAAI;wBACH2C,SAAS,EAAC,mBAAmB;wBAC7Ba,EAAE,EAAE,gBAAgB,GAAGI,MAAM,CAACC,EAAG;wBAAAnB,QAAA,eAEjC/B,OAAA;0BACEkC,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzE/B,OAAA;4BACEsC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEP5C,OAAA;wBACEgC,SAAS,EAAC,mBAAmB;wBAC7B+B,OAAO,EAAEA,CAAA,KAAM;0BACb/C,YAAY,CAAC,QAAQ,CAAC;0BACtBN,WAAW,CAACuC,MAAM,CAACC,EAAE,CAAC;0BACtBtC,WAAW,CAAC,IAAI,CAAC;wBACnB,CAAE;wBAAAmB,QAAA,eAEF/B,OAAA;0BACEkC,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExE/B,OAAA;4BACEsC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAA+Z;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACla;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACF5C,OAAA;gBAAIgC,SAAS,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACR5C,OAAA;YAAKgC,SAAS,EAAC,EAAE;YAAAD,QAAA,eACf/B,OAAA,CAACH,QAAQ;cACPmE,KAAK,EAAE,WAAY;cACnB1D,MAAM,EAAE,EAAG;cACXD,IAAI,EAAEA,IAAK;cACXoB,KAAK,EAAEA;YAAM;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN5C,OAAA,CAACF,iBAAiB;QAChBmE,MAAM,EAAEtD,QAAS;QACjBoC,OAAO,EACLhC,SAAS,KAAK,QAAQ,GAClB,kDAAkD,GAClD,4BACL;QACDmD,SAAS,EAAE,MAAAA,CAAA,KAAY;UACrB,IAAInD,SAAS,KAAK,QAAQ,IAAIN,QAAQ,KAAK,EAAE,EAAE;YAC7CK,YAAY,CAAC,IAAI,CAAC;YAClBN,QAAQ,CAAChB,YAAY,CAACiB,QAAQ,CAAC,CAAC;YAChCG,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB,CAAC,MAAM;YACLF,WAAW,CAAC,KAAK,CAAC;YAClBI,YAAY,CAAC,EAAE,CAAC;YAChBF,YAAY,CAAC,KAAK,CAAC;UACrB;QACF,CAAE;QACFqD,QAAQ,EAAEA,CAAA,KAAM;UACdvD,WAAW,CAAC,KAAK,CAAC;UAClBI,YAAY,CAAC,EAAE,CAAC;UAChBF,YAAY,CAAC,KAAK,CAAC;QACrB,CAAE;QACFD,SAAS,EAAEA;MAAU;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACF5C,OAAA;QAAKgC,SAAS,EAAC;MAA2C;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC1C,EAAA,CAzRQD,YAAY;EAAA,QACFV,WAAW,EACXD,WAAW,EAIXH,WAAW,EAOVC,WAAW,EAGVA,WAAW,EAGTA,WAAW;AAAA;AAAAgF,EAAA,GAnBzBnE,YAAY;AA2RrB,eAAeA,YAAY;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}