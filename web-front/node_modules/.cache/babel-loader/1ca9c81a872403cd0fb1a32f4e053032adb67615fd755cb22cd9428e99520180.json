{"ast": null, "code": "import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\nfunction isWaapiSupportedEasing(easing) {\n  return Boolean(!easing || typeof easing === \"string\" && easing in supportedWaapiEasing || isBezierDefinition(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nfunction mapEasingToNativeEasingWithDefault(easing) {\n  return mapEasingToNativeEasing(easing) || supportedWaapiEasing.easeOut;\n}\nfunction mapEasingToNativeEasing(easing) {\n  if (!easing) {\n    return undefined;\n  } else if (isBezierDefinition(easing)) {\n    return cubicBezierAsString(easing);\n  } else if (Array.isArray(easing)) {\n    return easing.map(mapEasingToNativeEasingWithDefault);\n  } else {\n    return supportedWaapiEasing[easing];\n  }\n}\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "isWaapiSupportedEasing", "easing", "Boolean", "supportedWaapiEasing", "Array", "isArray", "every", "cubicBezierAsString", "a", "b", "c", "d", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut", "mapEasingToNativeEasingWithDefault", "mapEasingToNativeEasing", "undefined", "map"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && easing in supportedWaapiEasing) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasingWithDefault(easing) {\n    return (mapEasingToNativeEasing(easing) ||\n        supportedWaapiEasing.easeOut);\n}\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing) {\n        return undefined;\n    }\n    else if (isBezierDefinition(easing)) {\n        return cubicBezierAsString(easing);\n    }\n    else if (Array.isArray(easing)) {\n        return easing.map(mapEasingToNativeEasingWithDefault);\n    }\n    else {\n        return supportedWaapiEasing[easing];\n    }\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gDAAgD;AAEnF,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAOC,OAAO,CAAC,CAACD,MAAM,IACjB,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAIE,oBAAqB,IAC9DJ,kBAAkB,CAACE,MAAM,CAAC,IACzBG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,IAAIA,MAAM,CAACK,KAAK,CAACN,sBAAsB,CAAE,CAAC;AACxE;AACA,MAAMO,mBAAmB,GAAGA,CAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,KAAM,gBAAeH,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAIC,CAAE,GAAE;AACpF,MAAMR,oBAAoB,GAAG;EACzBS,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAEV,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/CW,OAAO,EAAEX,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChDY,MAAM,EAAEZ,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACtDa,OAAO,EAAEb,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,SAASc,kCAAkCA,CAACpB,MAAM,EAAE;EAChD,OAAQqB,uBAAuB,CAACrB,MAAM,CAAC,IACnCE,oBAAoB,CAACY,OAAO;AACpC;AACA,SAASO,uBAAuBA,CAACrB,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EAAE;IACT,OAAOsB,SAAS;EACpB,CAAC,MACI,IAAIxB,kBAAkB,CAACE,MAAM,CAAC,EAAE;IACjC,OAAOM,mBAAmB,CAACN,MAAM,CAAC;EACtC,CAAC,MACI,IAAIG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;IAC5B,OAAOA,MAAM,CAACuB,GAAG,CAACH,kCAAkC,CAAC;EACzD,CAAC,MACI;IACD,OAAOlB,oBAAoB,CAACF,MAAM,CAAC;EACvC;AACJ;AAEA,SAASM,mBAAmB,EAAEP,sBAAsB,EAAEsB,uBAAuB,EAAEnB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}