{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Circle from\"react-circle\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function KpisInformationScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;// const listProviders = useSelector((state) => state.providerList);\n// const { providers, loadingProviders, errorProviders, pages } = listProviders;\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{// dispatch(providersList(page));\n}},[navigate,userInfo,dispatch]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"KPI\\xB4S / INFORMES\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"KPI\\xB4S / INFORMES\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% asistencias sin coordinar\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% asistencias / Sin coste\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% asistencias / Sin IM\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% casos / cliente\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% casos / tipo de asistencia\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/3 w-full p-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2 text-center\",children:\"% casos/ pais\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-1/2\",children:/*#__PURE__*/_jsx(Circle,{animate:true// Boolean: Animated/Static progress\n,animationDuration:\"1s\"//String: Length of animation\n,responsive:true// Boolean: Make SVG adapt to parent size\n,size:150// Number: Defines the size of the circle.\n,lineWidth:14// Number: Defines the thickness of the circle's stroke.\n,progress:69// Number: Update to change the progress and percentage.\n,progressColor:\"cornflowerblue\"// String: Color of \"progress\" portion of circle.\n,bgColor:\"whitesmoke\"// String: Color of \"empty\" portion of circle.\n,textColor:\"hotpink\"// String: Color of percentage text color.\n,textStyle:{font:\"bold 5rem Helvetica, Arial, sans-serif\"// CSSProperties: Custom styling for percentage.\n},percentSpacing:10// Number: Adjust spacing of \"%\" symbol and number.\n,roundedStroke:true// Boolean: Rounded/Flat line ends\n,showPercentage:true// Boolean: Show/hide percentage.\n,showPercentageSymbol:true// Boolean: Show/hide only the \"%\" symbol.\n})})]})})]})]})]})]})});}export default KpisInformationScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "Circle", "jsx", "_jsx", "jsxs", "_jsxs", "KpisInformationScreen", "navigate", "location", "searchParams", "page", "get", "dispatch", "userLogin", "state", "userInfo", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "animate", "animationDuration", "responsive", "size", "lineWidth", "progress", "progressColor", "bgColor", "textColor", "textStyle", "font", "percentSpacing", "roundedStroke", "showPercentage", "showPercentageSymbol"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/kpiinformations/KpisInformationScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Circle from \"react-circle\";\n\nfunction KpisInformationScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  // const listProviders = useSelector((state) => state.providerList);\n  // const { providers, loadingProviders, errorProviders, pages } = listProviders;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      // dispatch(providersList(page));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">KPI´S / INFORMES</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              KPI´S / INFORMES\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"my-5\">\n            <div className=\"flex md:flex-row flex-col\">\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">\n                    % asistencias sin coordinar\n                  </div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">\n                    % asistencias / Sin coste\n                  </div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">\n                    % asistencias / Sin IM\n                  </div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex md:flex-row flex-col\">\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">% casos / cliente</div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">\n                    % casos / tipo de asistencia\n                  </div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/3 w-full p-3\">\n                <div className=\"flex flex-row items-center\">\n                  <div className=\"w-1/2 text-center\">% casos/ pais</div>\n                  <div className=\"w-1/2\">\n                    <Circle\n                      animate={true} // Boolean: Animated/Static progress\n                      animationDuration=\"1s\" //String: Length of animation\n                      responsive={true} // Boolean: Make SVG adapt to parent size\n                      size={150} // Number: Defines the size of the circle.\n                      lineWidth={14} // Number: Defines the thickness of the circle's stroke.\n                      progress={69} // Number: Update to change the progress and percentage.\n                      progressColor=\"cornflowerblue\" // String: Color of \"progress\" portion of circle.\n                      bgColor=\"whitesmoke\" // String: Color of \"empty\" portion of circle.\n                      textColor=\"hotpink\" // String: Color of percentage text color.\n                      textStyle={{\n                        font: \"bold 5rem Helvetica, Arial, sans-serif\", // CSSProperties: Custom styling for percentage.\n                      }}\n                      percentSpacing={10} // Number: Adjust spacing of \"%\" symbol and number.\n                      roundedStroke={true} // Boolean: Rounded/Flat line ends\n                      showPercentage={true} // Boolean: Show/hide percentage.\n                      showPercentageSymbol={true} // Boolean: Show/hide only the \"%\" symbol.\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default KpisInformationScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,eAAe,KAAQ,kBAAkB,CAC5E,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElC,QAAS,CAAAC,qBAAqBA,CAAA,CAAG,CAC/B,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAU,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACY,YAAY,CAAC,CAAGV,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAW,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGjB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAkB,SAAS,CAAGjB,WAAW,CAAEkB,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B;AACA;AAEA,KAAM,CAAAG,QAAQ,CAAG,GAAG,CAEpBtB,SAAS,CAAC,IAAM,CACd,GAAI,CAACqB,QAAQ,CAAE,CACbR,QAAQ,CAACS,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL;AAAA,CAEJ,CAAC,CAAE,CAACT,QAAQ,CAAEQ,QAAQ,CAAEH,QAAQ,CAAC,CAAC,CAElC,mBACET,IAAA,CAACH,aAAa,EAAAiB,QAAA,cACZZ,KAAA,QAAAY,QAAA,eACEZ,KAAA,QAAKa,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDd,IAAA,MAAGgB,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBZ,KAAA,QAAKa,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5Dd,IAAA,QACEiB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBd,IAAA,SACEqB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNvB,IAAA,SAAMe,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJd,IAAA,SAAAc,QAAA,cACEd,IAAA,QACEiB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBd,IAAA,SACEqB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPvB,IAAA,QAAKe,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,qBAAgB,CAAK,CAAC,EACrC,CAAC,cACNZ,KAAA,QAAKa,SAAS,CAAC,8GAA8G,CAAAD,QAAA,eAC3Hd,IAAA,QAAKe,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/Dd,IAAA,OAAIe,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,qBAEnE,CAAI,CAAC,CACF,CAAC,cAENZ,KAAA,QAAKa,SAAS,CAAC,MAAM,CAAAD,QAAA,eACnBZ,KAAA,QAAKa,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCd,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,6BAEnC,CAAK,CAAC,cACNd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNtC,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,2BAEnC,CAAK,CAAC,cACNd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNtC,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,wBAEnC,CAAK,CAAC,cACNd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENpC,KAAA,QAAKa,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCd,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cAC1Dd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNtC,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,8BAEnC,CAAK,CAAC,cACNd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cACNtC,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAD,QAAA,cAClCZ,KAAA,QAAKa,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzCd,IAAA,QAAKe,SAAS,CAAC,mBAAmB,CAAAD,QAAA,CAAC,eAAa,CAAK,CAAC,cACtDd,IAAA,QAAKe,SAAS,CAAC,OAAO,CAAAD,QAAA,cACpBd,IAAA,CAACF,MAAM,EACL0B,OAAO,CAAE,IAAM;AAAA,CACfC,iBAAiB,CAAC,IAAK;AAAA,CACvBC,UAAU,CAAE,IAAM;AAAA,CAClBC,IAAI,CAAE,GAAK;AAAA,CACXC,SAAS,CAAE,EAAI;AAAA,CACfC,QAAQ,CAAE,EAAI;AAAA,CACdC,aAAa,CAAC,gBAAiB;AAAA,CAC/BC,OAAO,CAAC,YAAa;AAAA,CACrBC,SAAS,CAAC,SAAU;AAAA,CACpBC,SAAS,CAAE,CACTC,IAAI,CAAE,wCAA0C;AAClD,CAAE,CACFC,cAAc,CAAE,EAAI;AAAA,CACpBC,aAAa,CAAE,IAAM;AAAA,CACrBC,cAAc,CAAE,IAAM;AAAA,CACtBC,oBAAoB,CAAE,IAAM;AAAA,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAnC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}