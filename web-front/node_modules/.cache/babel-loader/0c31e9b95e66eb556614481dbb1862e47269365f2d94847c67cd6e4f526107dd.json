{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js\",\n  _s = $RefreshSig$();\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RaportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n  const [startDateRapport, setStartDateRapport] = useState(\"\");\n  const [startDateRapportError, setStartDateRapportError] = useState(\"\");\n  const [endDateRapport, setEndDateRapport] = useState(\"\");\n  const [endDateRapportError, setEndDateRapportError] = useState(\"\");\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n  const [isMensuelNet, setIsMensuelNet] = useState(false);\n  const [monthNet, setMonthNet] = useState(\"\");\n  const [monthNetError, setMonthNetError] = useState(\"\");\n  const [isMensuelRapport, setIsMensuelRapport] = useState(false);\n  const [monthRapport, setMonthRapport] = useState(\"\");\n  const [monthRapportError, setMonthRapportError] = useState(\"\");\n  const [isMensuelImpay, setIsMensuelImpay] = useState(false);\n  const [monthImpay, setMonthImpay] = useState(\"\");\n  const [monthImpayError, setMonthImpayError] = useState(\"\");\n  const [isMensuelReglement, setIsMensuelReglement] = useState(false);\n  const [monthReglement, setMonthReglement] = useState(\"\");\n  const [monthReglementError, setMonthReglementError] = useState(\"\");\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 95,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Rapport\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Gestion du Rapport\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row md:items-stretch h-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1 \",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"B\\xE9n\\xE9fice net\",\n              styles: \"bg-primary text-white font-bold px-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (!isMensuelNet) {\n                      setMonthNet(\"\");\n                      setStartDateNet(\"\");\n                      setEndDateNet(\"\");\n                    }\n                    setIsMensuelNet(true);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer \",\n                  children: [isMensuelNet ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 157,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${isMensuelNet ? \"font-bold\" : \"\"}`,\n                    children: \"Mensuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 166,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (isMensuelNet) {\n                      setMonthNet(\"\");\n                      setStartDateNet(\"\");\n                      setEndDateNet(\"\");\n                    }\n                    setIsMensuelNet(false);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer\",\n                  children: [!isMensuelNet ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 192,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${!isMensuelNet ? \"font-bold\" : \"\"}`,\n                    children: \"Entre deux dates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this), isMensuelNet ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"month\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: monthNet,\n                  onChange: v => {\n                    console.log(v.target.value);\n                    setMonthNet(v.target.value);\n                    if (v.target.value) {\n                      console.log(\"kkk\");\n                      const [year, month] = v.target.value.split(\"-\").map(Number);\n\n                      // Calculate the start date as the first day of the selected month\n                      const start = new Date(year, month - 1, 1);\n\n                      // Calculate the end date as the last day of the selected month\n                      const end = new Date(year, month, 1);\n\n                      // Format dates to YYYY-MM-DD\n                      const formattedStartDate = start.toISOString().slice(0, 10);\n                      const formattedEndDate = end.toISOString().slice(0, 10);\n                      setStartDateNet(formattedStartDate);\n                      setEndDateNet(formattedEndDate);\n                    }\n                  },\n                  error: monthNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this) : null, !isMensuelNet ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex  md:mt-0 mt-5\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateNet,\n                  onChange: v => setStartDateNet(v.target.value),\n                  error: startDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateNet === \"\",\n                  value: endDateNet,\n                  onChange: v => setEndDateNet(v.target.value),\n                  error: endDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateNetError(\"\");\n                    setEndDateNetError(\"\");\n                    setMonthNetError(\"\");\n                    var check = true;\n                    if (startDateNet === \"\" && !isMensuelNet) {\n                      check = false;\n                      setStartDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (endDateNet === \"\" && !isMensuelNet) {\n                      check = false;\n                      setEndDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (monthNet === \"\" && isMensuelNet) {\n                      check = false;\n                      setMonthNetError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateNet);\n                      const endDate = new Date(endDateNet);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary  text-white px-5 py-1.5 text-center  font-bold rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Rapport en voiture\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (!isMensuelRapport) {\n                      setMonthRapport(\"\");\n                      setStartDateRapport(\"\");\n                      setEndDateRapport(\"\");\n                    }\n                    setIsMensuelRapport(true);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer \",\n                  children: [isMensuelRapport ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 345,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${isMensuelRapport ? \"font-bold\" : \"\"}`,\n                    children: \"Mensuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (isMensuelRapport) {\n                      setMonthRapport(\"\");\n                      setStartDateRapport(\"\");\n                      setEndDateRapport(\"\");\n                    }\n                    setIsMensuelRapport(false);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer\",\n                  children: [!isMensuelRapport ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 389,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${!isMensuelRapport ? \"font-bold\" : \"\"}`,\n                    children: \"Entre deux dates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 362,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Voiture\",\n                  type: \"select\",\n                  placeholder: \"\",\n                  value: selectCar,\n                  onChange: v => setSelectCar(v.target.value),\n                  error: selectCarError,\n                  options: cars === null || cars === void 0 ? void 0 : cars.map(car => {\n                    var _car$marque$marque_ca, _car$marque, _car$model$model_car, _car$model, _car$agence;\n                    return {\n                      value: car.id,\n                      label: ((_car$marque$marque_ca = (_car$marque = car.marque) === null || _car$marque === void 0 ? void 0 : _car$marque.marque_car) !== null && _car$marque$marque_ca !== void 0 ? _car$marque$marque_ca : \"---\") + \" \" + ((_car$model$model_car = (_car$model = car.model) === null || _car$model === void 0 ? void 0 : _car$model.model_car) !== null && _car$model$model_car !== void 0 ? _car$model$model_car : \"\") + (car.agence ? \" (\" + ((_car$agence = car.agence) === null || _car$agence === void 0 ? void 0 : _car$agence.name) + \") \" : \"\")\n                    };\n                  })\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), isMensuelRapport ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"month\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: monthRapport,\n                  onChange: v => {\n                    console.log(v.target.value);\n                    setMonthRapport(v.target.value);\n                    if (v.target.value) {\n                      console.log(\"kkk\");\n                      const [year, month] = v.target.value.split(\"-\").map(Number);\n\n                      // Calculate the start date as the first day of the selected month\n                      const start = new Date(year, month - 1, 1);\n\n                      // Calculate the end date as the last day of the selected month\n                      const end = new Date(year, month, 1);\n\n                      // Format dates to YYYY-MM-DD\n                      const formattedStartDate = start.toISOString().slice(0, 10);\n                      const formattedEndDate = end.toISOString().slice(0, 10);\n                      setStartDateRapport(formattedStartDate);\n                      setEndDateRapport(formattedEndDate);\n                    }\n                  },\n                  error: monthRapportError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 418,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 19\n              }, this) : null, !isMensuelRapport ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateRapport,\n                  onChange: v => setStartDateRapport(v.target.value),\n                  error: startDateRapportError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateRapport === \"\",\n                  max: today,\n                  value: endDateRapport,\n                  onChange: v => setEndDateRapport(v.target.value),\n                  error: endDateRapportError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 19\n              }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSelectCarError(\"\");\n                    setStartDateRapportError(\"\");\n                    setEndDateRapportError(\"\");\n                    setMonthRapportError(\"\");\n                    var check = true;\n                    if (selectCar === \"\") {\n                      check = false;\n                      setSelectCarError(\"Ce champ est requis.\");\n                    }\n                    if (startDateRapport === \"\" && !isMensuelRapport) {\n                      check = false;\n                      setStartDateRapportError(\"Ce champ est requis.\");\n                    }\n                    if (endDateRapport === \"\" && !isMensuelRapport) {\n                      check = false;\n                      setEndDateRapportError(\"Ce champ est requis.\");\n                    }\n                    if (monthRapport === \"\" && isMensuelRapport) {\n                      check = false;\n                      setMonthRapportError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateRapport);\n                      const endDate = new Date(endDateRapport);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${endDateRapport}&end_date=${startDateRapport}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/cars/raport/${selectCar}/?start_date=${startDateRapport}&end_date=${endDateRapport}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Contrats impay\\xE9es\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (!isMensuelImpay) {\n                      setMonthImpay(\"\");\n                      setStartDateImp(\"\");\n                      setEndDateImp(\"\");\n                    }\n                    setIsMensuelImpay(true);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer \",\n                  children: [isMensuelImpay ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${isMensuelImpay ? \"font-bold\" : \"\"}`,\n                    children: \"Mensuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 567,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (isMensuelImpay) {\n                      setMonthImpay(\"\");\n                      setStartDateImp(\"\");\n                      setEndDateImp(\"\");\n                    }\n                    setIsMensuelImpay(false);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer\",\n                  children: [!isMensuelImpay ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 595,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${!isMensuelImpay ? \"font-bold\" : \"\"}`,\n                    children: \"Entre deux dates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 604,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 17\n              }, this), isMensuelImpay ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"month\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: monthImpay,\n                  onChange: v => {\n                    console.log(v.target.value);\n                    setMonthImpay(v.target.value);\n                    if (v.target.value) {\n                      console.log(\"kkk\");\n                      const [year, month] = v.target.value.split(\"-\").map(Number);\n\n                      // Calculate the start date as the first day of the selected month\n                      const start = new Date(year, month - 1, 1);\n\n                      // Calculate the end date as the last day of the selected month\n                      const end = new Date(year, month, 1);\n\n                      // Format dates to YYYY-MM-DD\n                      const formattedStartDate = start.toISOString().slice(0, 10);\n                      const formattedEndDate = end.toISOString().slice(0, 10);\n                      setStartDateImp(formattedStartDate);\n                      setEndDateImp(formattedEndDate);\n                    }\n                  },\n                  error: monthImpayError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this) : null, !isMensuelImpay ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  placeholder: \"\",\n                  value: startDateImp,\n                  onChange: v => setStartDateImp(v.target.value),\n                  error: startDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 653,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateImp === \"\",\n                  placeholder: \"\",\n                  value: endDateImp,\n                  onChange: v => setEndDateImp(v.target.value),\n                  error: endDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 663,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateImpError(\"\");\n                    setEndDateImpError(\"\");\n                    setMonthImpayError(\"\");\n                    var check = true;\n                    if (startDateImp === \"\" && !isMensuelImpay) {\n                      check = false;\n                      setStartDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (endDateImp === \"\" && !isMensuelImpay) {\n                      check = false;\n                      setEndDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (monthImpay === \"\" && isMensuelImpay) {\n                      check = false;\n                      setMonthImpayError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateImp);\n                      const endDate = new Date(endDateImp);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-danger  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 676,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 536,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (!isMensuelReglement) {\n                      setMonthReglement(\"\");\n                      setStartDateReg(\"\");\n                      setEndDateReg(\"\");\n                    }\n                    setIsMensuelReglement(true);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer \",\n                  children: [isMensuelReglement ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 746,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 738,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${isMensuelReglement ? \"font-bold\" : \"\"}`,\n                    children: \"Mensuel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 755,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  onClick: () => {\n                    if (isMensuelReglement) {\n                      setMonthReglement(\"\");\n                      setStartDateReg(\"\");\n                      setEndDateReg(\"\");\n                    }\n                    setIsMensuelReglement(false);\n                  },\n                  className: \"flex flex-row items-center cursor-pointer\",\n                  children: [!isMensuelReglement ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    class: \"size-6\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      d: \"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 785,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 777,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-5 border rounded-full\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 792,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `mx-2 ${!isMensuelReglement ? \"font-bold\" : \"\"}`,\n                    children: \"Entre deux dates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 17\n              }, this), isMensuelReglement ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex md:mt-0 mt-5\",\n                children: /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"month\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: monthReglement,\n                  onChange: v => {\n                    console.log(v.target.value);\n                    setMonthReglement(v.target.value);\n                    if (v.target.value) {\n                      console.log(\"kkk\");\n                      const [year, month] = v.target.value.split(\"-\").map(Number);\n\n                      // Calculate the start date as the first day of the selected month\n                      const start = new Date(year, month - 1, 1);\n\n                      // Calculate the end date as the last day of the selected month\n                      const end = new Date(year, month, 1);\n\n                      // Format dates to YYYY-MM-DD\n                      const formattedStartDate = start.toISOString().slice(0, 10);\n                      const formattedEndDate = end.toISOString().slice(0, 10);\n                      setStartDateReg(formattedStartDate);\n                      setEndDateReg(formattedEndDate);\n                    }\n                  },\n                  error: monthReglementError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 805,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 19\n              }, this) : null, !isMensuelReglement ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex  md:mt-0 mt-5\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 845,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    setMonthReglementError(\"\");\n                    var check = true;\n                    if (startDateReg === \"\" && !isMensuelReglement) {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\" && !isMensuelReglement) {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (monthReglement === \"\" && isMensuelReglement) {\n                      check = false;\n                      setMonthReglementError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 533,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n}\n_s(RaportScreen, \"21mXrT4ygyqC/cUlgqdoMKeauFc=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = RaportScreen;\nexport default RaportScreen;\nvar _c;\n$RefreshReg$(_c, \"RaportScreen\");", "map": {"version": 3, "names": ["toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "useEffect", "useState", "DefaultLayout", "baseURL", "baseURLFile", "jsxDEV", "_jsxDEV", "RaportScreen", "_s", "navigate", "location", "dispatch", "today", "Date", "toISOString", "split", "startDateNet", "setStartDateNet", "startDateNetError", "setStartDateNetError", "endDateNet", "setEndDateNet", "endDateNetError", "setEndDateNetError", "startDateRapport", "setStartDateRapport", "startDateRapportError", "setStartDateRapportError", "endDateRapport", "setEndDateRapport", "endDateRapportError", "setEndDateRapportError", "startDateReg", "setStartDateReg", "startDateRegError", "setStartDateRegError", "endDateReg", "setEndDateReg", "endDateRegError", "setEndDateRegError", "startDateImp", "setStartDateImp", "startDateImpError", "setStartDateImpError", "endDateImp", "setEndDateImp", "endDateImpError", "setEndDateImpError", "selectCar", "setSelectCar", "selectCarError", "setSelectCarError", "isMensuelNet", "setIsMensuelNet", "monthNet", "setMonthNet", "monthNetError", "setMonthNetError", "isMensuelRapport", "setIsMensuelRapport", "monthRapport", "setMonthRapport", "monthRapportError", "setMonthRapportError", "isMensuelImpay", "setIsMensuelImpay", "monthImpay", "setMonthImpay", "monthImpayError", "setMonthImpayError", "isMensuelReglement", "setIsMensuelReglement", "monthReglement", "setMonthReglement", "monthReglementError", "setMonthReglementError", "userLogin", "state", "userInfo", "loading", "error", "listCar", "carList", "cars", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "styles", "onClick", "class", "label", "type", "placeholder", "isMax", "max", "value", "onChange", "v", "console", "log", "target", "year", "month", "map", "Number", "start", "end", "formattedStartDate", "slice", "formattedEndDate", "disabled", "check", "startDate", "endDate", "window", "open", "options", "car", "_car$marque$marque_ca", "_car$marque", "_car$model$model_car", "_car$model", "_car$agence", "id", "marque", "marque_car", "model", "model_car", "agence", "name", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\n\nfunction RaportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n\n  const [startDateRapport, setStartDateRapport] = useState(\"\");\n  const [startDateRapportError, setStartDateRapportError] = useState(\"\");\n  const [endDateRapport, setEndDateRapport] = useState(\"\");\n  const [endDateRapportError, setEndDateRapportError] = useState(\"\");\n\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  const [selectCar, setSelectCar] = useState(\"\");\n  const [selectCarError, setSelectCarError] = useState(\"\");\n\n  const [isMensuelNet, setIsMensuelNet] = useState(false);\n  const [monthNet, setMonthNet] = useState(\"\");\n  const [monthNetError, setMonthNetError] = useState(\"\");\n\n  const [isMensuelRapport, setIsMensuelRapport] = useState(false);\n  const [monthRapport, setMonthRapport] = useState(\"\");\n  const [monthRapportError, setMonthRapportError] = useState(\"\");\n\n  const [isMensuelImpay, setIsMensuelImpay] = useState(false);\n  const [monthImpay, setMonthImpay] = useState(\"\");\n  const [monthImpayError, setMonthImpayError] = useState(\"\");\n\n  const [isMensuelReglement, setIsMensuelReglement] = useState(false);\n  const [monthReglement, setMonthReglement] = useState(\"\");\n  const [monthReglementError, setMonthReglementError] = useState(\"\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Rapport</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Gestion du Rapport\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex flex-col md:flex-row md:items-stretch h-full\">\n            <div className=\"md:w-1/2 w-full px-1 py-1 \">\n              <LayoutSection\n                title=\"Bénéfice net\"\n                styles={\"bg-primary text-white font-bold px-3\"}\n              >\n                <div className=\"md:py-2 md:flex\">\n                  <div\n                    onClick={() => {\n                      if (!isMensuelNet) {\n                        setMonthNet(\"\");\n                        setStartDateNet(\"\");\n                        setEndDateNet(\"\");\n                      }\n                      setIsMensuelNet(true);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer \"\n                  >\n                    {isMensuelNet ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span className={`mx-2 ${isMensuelNet ? \"font-bold\" : \"\"}`}>\n                      Mensuel\n                    </span>\n                  </div>\n                  <div className=\"w-3\"></div>\n                  {/*  */}\n                  <div\n                    onClick={() => {\n                      if (isMensuelNet) {\n                        setMonthNet(\"\");\n                        setStartDateNet(\"\");\n                        setEndDateNet(\"\");\n                      }\n                      setIsMensuelNet(false);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer\"\n                  >\n                    {!isMensuelNet ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${!isMensuelNet ? \"font-bold\" : \"\"}`}\n                    >\n                      Entre deux dates\n                    </span>\n                  </div>\n                </div>\n                {isMensuelNet ? (\n                  <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"month\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={monthNet}\n                      onChange={(v) => {\n                        console.log(v.target.value);\n                        setMonthNet(v.target.value);\n                        if (v.target.value) {\n                          console.log(\"kkk\");\n                          const [year, month] = v.target.value\n                            .split(\"-\")\n                            .map(Number);\n\n                          // Calculate the start date as the first day of the selected month\n                          const start = new Date(year, month - 1, 1);\n\n                          // Calculate the end date as the last day of the selected month\n                          const end = new Date(year, month, 1);\n\n                          // Format dates to YYYY-MM-DD\n                          const formattedStartDate = start\n                            .toISOString()\n                            .slice(0, 10);\n                          const formattedEndDate = end\n                            .toISOString()\n                            .slice(0, 10);\n\n                          setStartDateNet(formattedStartDate);\n                          setEndDateNet(formattedEndDate);\n                        }\n                      }}\n                      error={monthNetError}\n                    />\n                  </div>\n                ) : null}\n\n                {!isMensuelNet ? (\n                  <div className=\"md:py-2 md:flex  md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={startDateNet}\n                      onChange={(v) => setStartDateNet(v.target.value)}\n                      error={startDateNetError}\n                    />\n                    <InputModel\n                      label=\"Date fin\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      disabled={startDateNet === \"\"}\n                      value={endDateNet}\n                      onChange={(v) => setEndDateNet(v.target.value)}\n                      error={endDateNetError}\n                    />\n                  </div>\n                ) : null}\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateNetError(\"\");\n                      setEndDateNetError(\"\");\n                      setMonthNetError(\"\");\n                      var check = true;\n\n                      if (startDateNet === \"\" && !isMensuelNet) {\n                        check = false;\n                        setStartDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (endDateNet === \"\" && !isMensuelNet) {\n                        check = false;\n                        setEndDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (monthNet === \"\" && isMensuelNet) {\n                        check = false;\n                        setMonthNetError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateNet);\n                        const endDate = new Date(endDateNet);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary  text-white px-5 py-1.5 text-center  font-bold rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Rapport en voiture\">\n                <div className=\"md:py-2 md:flex\">\n                  <div\n                    onClick={() => {\n                      if (!isMensuelRapport) {\n                        setMonthRapport(\"\");\n                        setStartDateRapport(\"\");\n                        setEndDateRapport(\"\");\n                      }\n                      setIsMensuelRapport(true);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer \"\n                  >\n                    {isMensuelRapport ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${isMensuelRapport ? \"font-bold\" : \"\"}`}\n                    >\n                      Mensuel\n                    </span>\n                  </div>\n                  <div className=\"w-3\"></div>\n                  {/*  */}\n                  <div\n                    onClick={() => {\n                      if (isMensuelRapport) {\n                        setMonthRapport(\"\");\n                        setStartDateRapport(\"\");\n                        setEndDateRapport(\"\");\n                      }\n                      setIsMensuelRapport(false);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer\"\n                  >\n                    {!isMensuelRapport ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${!isMensuelRapport ? \"font-bold\" : \"\"}`}\n                    >\n                      Entre deux dates\n                    </span>\n                  </div>\n                </div>\n                <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                  <InputModel\n                    label=\"Voiture\"\n                    type=\"select\"\n                    placeholder=\"\"\n                    value={selectCar}\n                    onChange={(v) => setSelectCar(v.target.value)}\n                    error={selectCarError}\n                    options={cars?.map((car) => ({\n                      value: car.id,\n                      label:\n                        (car.marque?.marque_car ?? \"---\") +\n                        \" \" +\n                        (car.model?.model_car ?? \"\") +\n                        (car.agence ? \" (\" + car.agence?.name + \") \" : \"\"),\n                    }))}\n                  />\n                </div>\n                {isMensuelRapport ? (\n                  <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"month\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={monthRapport}\n                      onChange={(v) => {\n                        console.log(v.target.value);\n                        setMonthRapport(v.target.value);\n                        if (v.target.value) {\n                          console.log(\"kkk\");\n                          const [year, month] = v.target.value\n                            .split(\"-\")\n                            .map(Number);\n\n                          // Calculate the start date as the first day of the selected month\n                          const start = new Date(year, month - 1, 1);\n\n                          // Calculate the end date as the last day of the selected month\n                          const end = new Date(year, month, 1);\n\n                          // Format dates to YYYY-MM-DD\n                          const formattedStartDate = start\n                            .toISOString()\n                            .slice(0, 10);\n                          const formattedEndDate = end\n                            .toISOString()\n                            .slice(0, 10);\n\n                          setStartDateRapport(formattedStartDate);\n                          setEndDateRapport(formattedEndDate);\n                        }\n                      }}\n                      error={monthRapportError}\n                    />\n                  </div>\n                ) : null}\n                {!isMensuelRapport ? (\n                  <div className=\"md:py-2 md:flex\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={startDateRapport}\n                      onChange={(v) => setStartDateRapport(v.target.value)}\n                      error={startDateRapportError}\n                    />\n                    <InputModel\n                      label=\"Date fin\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      disabled={startDateRapport === \"\"}\n                      max={today}\n                      value={endDateRapport}\n                      onChange={(v) => setEndDateRapport(v.target.value)}\n                      error={endDateRapportError}\n                    />\n                  </div>\n                ) : null}\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setSelectCarError(\"\");\n                      setStartDateRapportError(\"\");\n                      setEndDateRapportError(\"\");\n                      setMonthRapportError(\"\");\n                      var check = true;\n                      if (selectCar === \"\") {\n                        check = false;\n                        setSelectCarError(\"Ce champ est requis.\");\n                      }\n                      if (startDateRapport === \"\" && !isMensuelRapport) {\n                        check = false;\n                        setStartDateRapportError(\"Ce champ est requis.\");\n                      }\n                      if (endDateRapport === \"\" && !isMensuelRapport) {\n                        check = false;\n                        setEndDateRapportError(\"Ce champ est requis.\");\n                      }\n                      if (monthRapport === \"\" && isMensuelRapport) {\n                        check = false;\n                        setMonthRapportError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateRapport);\n                        const endDate = new Date(endDateRapport);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${endDateRapport}&end_date=${startDateRapport}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/cars/raport/${selectCar}/?start_date=${startDateRapport}&end_date=${endDateRapport}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Contrats impayées\">\n                <div className=\"md:py-2 md:flex\">\n                  <div\n                    onClick={() => {\n                      if (!isMensuelImpay) {\n                        setMonthImpay(\"\");\n                        setStartDateImp(\"\");\n                        setEndDateImp(\"\");\n                      }\n                      setIsMensuelImpay(true);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer \"\n                  >\n                    {isMensuelImpay ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${isMensuelImpay ? \"font-bold\" : \"\"}`}\n                    >\n                      Mensuel\n                    </span>\n                  </div>\n                  <div className=\"w-3\"></div>\n                  {/*  */}\n                  <div\n                    onClick={() => {\n                      if (isMensuelImpay) {\n                        setMonthImpay(\"\");\n                        setStartDateImp(\"\");\n                        setEndDateImp(\"\");\n                      }\n                      setIsMensuelImpay(false);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer\"\n                  >\n                    {!isMensuelImpay ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${!isMensuelImpay ? \"font-bold\" : \"\"}`}\n                    >\n                      Entre deux dates\n                    </span>\n                  </div>\n                </div>\n                {isMensuelImpay ? (\n                  <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"month\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={monthImpay}\n                      onChange={(v) => {\n                        console.log(v.target.value);\n                        setMonthImpay(v.target.value);\n                        if (v.target.value) {\n                          console.log(\"kkk\");\n                          const [year, month] = v.target.value\n                            .split(\"-\")\n                            .map(Number);\n\n                          // Calculate the start date as the first day of the selected month\n                          const start = new Date(year, month - 1, 1);\n\n                          // Calculate the end date as the last day of the selected month\n                          const end = new Date(year, month, 1);\n\n                          // Format dates to YYYY-MM-DD\n                          const formattedStartDate = start\n                            .toISOString()\n                            .slice(0, 10);\n                          const formattedEndDate = end\n                            .toISOString()\n                            .slice(0, 10);\n\n                          setStartDateImp(formattedStartDate);\n                          setEndDateImp(formattedEndDate);\n                        }\n                      }}\n                      error={monthImpayError}\n                    />\n                  </div>\n                ) : null}\n                {!isMensuelImpay ? (\n                  <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"date\"\n                      isMax={true}\n                      max={today}\n                      placeholder=\"\"\n                      value={startDateImp}\n                      onChange={(v) => setStartDateImp(v.target.value)}\n                      error={startDateImpError}\n                    />\n                    <InputModel\n                      label=\"Date fin\"\n                      type=\"date\"\n                      isMax={true}\n                      max={today}\n                      disabled={startDateImp === \"\"}\n                      placeholder=\"\"\n                      value={endDateImp}\n                      onChange={(v) => setEndDateImp(v.target.value)}\n                      error={endDateImpError}\n                    />\n                  </div>\n                ) : null}\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateImpError(\"\");\n                      setEndDateImpError(\"\");\n                      setMonthImpayError(\"\");\n                      var check = true;\n                      if (startDateImp === \"\" && !isMensuelImpay) {\n                        check = false;\n                        setStartDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (endDateImp === \"\" && !isMensuelImpay) {\n                        check = false;\n                        setEndDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (monthImpay === \"\" && isMensuelImpay) {\n                        check = false;\n                        setMonthImpayError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateImp);\n                        const endDate = new Date(endDateImp);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-danger  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement\">\n                <div className=\"md:py-2 md:flex\">\n                  <div\n                    onClick={() => {\n                      if (!isMensuelReglement) {\n                        setMonthReglement(\"\");\n                        setStartDateReg(\"\");\n                        setEndDateReg(\"\");\n                      }\n                      setIsMensuelReglement(true);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer \"\n                  >\n                    {isMensuelReglement ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${\n                        isMensuelReglement ? \"font-bold\" : \"\"\n                      }`}\n                    >\n                      Mensuel\n                    </span>\n                  </div>\n                  <div className=\"w-3\"></div>\n                  {/*  */}\n                  <div\n                    onClick={() => {\n                      if (isMensuelReglement) {\n                        setMonthReglement(\"\");\n                        setStartDateReg(\"\");\n                        setEndDateReg(\"\");\n                      }\n                      setIsMensuelReglement(false);\n                    }}\n                    className=\"flex flex-row items-center cursor-pointer\"\n                  >\n                    {!isMensuelReglement ? (\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        class=\"size-6\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        />\n                      </svg>\n                    ) : (\n                      <div className=\"size-5 border rounded-full\"></div>\n                    )}\n                    <span\n                      className={`mx-2 ${\n                        !isMensuelReglement ? \"font-bold\" : \"\"\n                      }`}\n                    >\n                      Entre deux dates\n                    </span>\n                  </div>\n                </div>\n                {isMensuelReglement ? (\n                  <div className=\"md:py-2 md:flex md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"month\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={monthReglement}\n                      onChange={(v) => {\n                        console.log(v.target.value);\n                        setMonthReglement(v.target.value);\n                        if (v.target.value) {\n                          console.log(\"kkk\");\n                          const [year, month] = v.target.value\n                            .split(\"-\")\n                            .map(Number);\n\n                          // Calculate the start date as the first day of the selected month\n                          const start = new Date(year, month - 1, 1);\n\n                          // Calculate the end date as the last day of the selected month\n                          const end = new Date(year, month, 1);\n\n                          // Format dates to YYYY-MM-DD\n                          const formattedStartDate = start\n                            .toISOString()\n                            .slice(0, 10);\n                          const formattedEndDate = end\n                            .toISOString()\n                            .slice(0, 10);\n\n                          setStartDateReg(formattedStartDate);\n                          setEndDateReg(formattedEndDate);\n                        }\n                      }}\n                      error={monthReglementError}\n                    />\n                  </div>\n                ) : null}\n                {!isMensuelReglement ? (\n                  <div className=\"md:py-2 md:flex  md:mt-0 mt-5\">\n                    <InputModel\n                      label=\"Date début\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      max={today}\n                      value={startDateReg}\n                      onChange={(v) => setStartDateReg(v.target.value)}\n                      error={startDateRegError}\n                    />\n                    <InputModel\n                      label=\"Date fin\"\n                      type=\"date\"\n                      placeholder=\"\"\n                      isMax={true}\n                      disabled={startDateReg === \"\"}\n                      max={today}\n                      value={endDateReg}\n                      onChange={(v) => setEndDateReg(v.target.value)}\n                      error={endDateRegError}\n                    />\n                  </div>\n                ) : null}\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      setMonthReglementError(\"\");\n                      var check = true;\n                      if (startDateReg === \"\" && !isMensuelReglement) {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\" && !isMensuelReglement) {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (monthReglement === \"\" && isMensuelReglement) {\n                        check = false;\n                        setMonthReglementError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default RaportScreen;\n"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACtE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuC,YAAY,EAAEC,eAAe,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+C,SAAS,EAAEC,YAAY,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2D,YAAY,EAAEC,eAAe,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC6D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACqE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM2E,SAAS,GAAG1F,WAAW,CAAE2F,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,OAAO,GAAG/F,WAAW,CAAE2F,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,QAAQ,GAAG,GAAG;EACpBpF,SAAS,CAAC,MAAM;IACd,IAAI,CAAC8E,QAAQ,EAAE;MACbrE,QAAQ,CAAC2E,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLzE,QAAQ,CAACd,WAAW,CAAC,GAAG,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACY,QAAQ,EAAEqE,QAAQ,CAAC,CAAC;EAExB,oBACExE,OAAA,CAACJ,aAAa;IAAAmF,QAAA,eACZ/E,OAAA;MAAA+E,QAAA,gBAEE/E,OAAA;QAAKgF,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/E,OAAA;UAAGiF,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/E,OAAA;YAAKgF,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/E,OAAA;cACEkF,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/E,OAAA;gBACEsF,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5F,OAAA;cAAMgF,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ5F,OAAA;UAAA+E,QAAA,eACE/E,OAAA;YACEkF,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/E,OAAA;cACEsF,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP5F,OAAA;UAAKgF,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAEN5F,OAAA;QAAKgF,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ/E,OAAA;UAAKgF,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D/E,OAAA;YAAIgF,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEN5F,OAAA;UAAKgF,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChE/E,OAAA;YAAKgF,SAAS,EAAC,4BAA4B;YAAAD,QAAA,eACzC/E,OAAA,CAACf,aAAa;cACZ4G,KAAK,EAAC,oBAAc;cACpBC,MAAM,EAAE,sCAAuC;cAAAf,QAAA,gBAE/C/E,OAAA;gBAAKgF,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACjD,YAAY,EAAE;sBACjBG,WAAW,CAAC,EAAE,CAAC;sBACftC,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAgC,eAAe,CAAC,IAAI,CAAC;kBACvB,CAAE;kBACFiC,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,GAErDjC,YAAY,gBACX9C,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBAAMgF,SAAS,EAAG,QAAOlC,YAAY,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAAiC,QAAA,EAAC;kBAE5D;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5F,OAAA;kBAAKgF,SAAS,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE3B5F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIjD,YAAY,EAAE;sBAChBG,WAAW,CAAC,EAAE,CAAC;sBACftC,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAgC,eAAe,CAAC,KAAK,CAAC;kBACxB,CAAE;kBACFiC,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,GAEpD,CAACjC,YAAY,gBACZ9C,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QAAO,CAAClC,YAAY,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAAiC,QAAA,EACvD;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL9C,YAAY,gBACX9C,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAEtD,QAAS;kBAChBuD,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3BrD,WAAW,CAACuD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3B,IAAIE,CAAC,CAACG,MAAM,CAACL,KAAK,EAAE;sBAClBG,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;sBAClB,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAACG,MAAM,CAACL,KAAK,CACjC7F,KAAK,CAAC,GAAG,CAAC,CACVqG,GAAG,CAACC,MAAM,CAAC;;sBAEd;sBACA,MAAMC,KAAK,GAAG,IAAIzG,IAAI,CAACqG,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;;sBAE1C;sBACA,MAAMI,GAAG,GAAG,IAAI1G,IAAI,CAACqG,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;;sBAEpC;sBACA,MAAMK,kBAAkB,GAAGF,KAAK,CAC7BxG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBACf,MAAMC,gBAAgB,GAAGH,GAAG,CACzBzG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBAEfxG,eAAe,CAACuG,kBAAkB,CAAC;sBACnCnG,aAAa,CAACqG,gBAAgB,CAAC;oBACjC;kBACF,CAAE;kBACF1C,KAAK,EAAExB;gBAAc;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,EAEP,CAAC9C,YAAY,gBACZ9C,OAAA;gBAAKgF,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,gBAC5C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAE5F,YAAa;kBACpB6F,QAAQ,EAAGC,CAAC,IAAK7F,eAAe,CAAC6F,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD5B,KAAK,EAAE9D;gBAAkB;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF5F,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACX+G,QAAQ,EAAE3G,YAAY,KAAK,EAAG;kBAC9B4F,KAAK,EAAExF,UAAW;kBAClByF,QAAQ,EAAGC,CAAC,IAAKzF,aAAa,CAACyF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C5B,KAAK,EAAE1D;gBAAgB;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,eACR5F,OAAA;gBAAKgF,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACblF,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtBkC,gBAAgB,CAAC,EAAE,CAAC;oBACpB,IAAImE,KAAK,GAAG,IAAI;oBAEhB,IAAI5G,YAAY,KAAK,EAAE,IAAI,CAACoC,YAAY,EAAE;sBACxCwE,KAAK,GAAG,KAAK;sBACbzG,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,IAAI,CAACgC,YAAY,EAAE;sBACtCwE,KAAK,GAAG,KAAK;sBACbrG,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI+B,QAAQ,KAAK,EAAE,IAAIF,YAAY,EAAE;sBACnCwE,KAAK,GAAG,KAAK;sBACbnE,gBAAgB,CAAC,sBAAsB,CAAC;oBAC1C;oBACA,IAAImE,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIhH,IAAI,CAACG,YAAY,CAAC;sBACxC,MAAM8G,OAAO,GAAG,IAAIjH,IAAI,CAACO,UAAU,CAAC;;sBAEpC;sBACA,IAAIyG,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,qCAAoCiB,UAAW,aAAYJ,YAAa,EAAC,EAC5E,QACF,CAAC;sBACH,CAAC,MAAM;wBACL+G,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,qCAAoCa,YAAa,aAAYI,UAAW,EAAC,EAC5E,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkE,SAAS,EAAC,mEAAmE;kBAAAD,QAAA,EAC9E;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN5F,OAAA;YAAKgF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/E,OAAA,CAACf,aAAa;cAAC4G,KAAK,EAAC,oBAAoB;cAAAd,QAAA,gBACvC/E,OAAA;gBAAKgF,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAAC3C,gBAAgB,EAAE;sBACrBG,eAAe,CAAC,EAAE,CAAC;sBACnBpC,mBAAmB,CAAC,EAAE,CAAC;sBACvBI,iBAAiB,CAAC,EAAE,CAAC;oBACvB;oBACA8B,mBAAmB,CAAC,IAAI,CAAC;kBAC3B,CAAE;kBACF2B,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,GAErD3B,gBAAgB,gBACfpD,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QAAO5B,gBAAgB,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAA2B,QAAA,EAC1D;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5F,OAAA;kBAAKgF,SAAS,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE3B5F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI3C,gBAAgB,EAAE;sBACpBG,eAAe,CAAC,EAAE,CAAC;sBACnBpC,mBAAmB,CAAC,EAAE,CAAC;sBACvBI,iBAAiB,CAAC,EAAE,CAAC;oBACvB;oBACA8B,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACF2B,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,GAEpD,CAAC3B,gBAAgB,gBAChBpD,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QAAO,CAAC5B,gBAAgB,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAA2B,QAAA,EAC3D;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5F,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,SAAS;kBACfC,IAAI,EAAC,QAAQ;kBACbC,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAE5D,SAAU;kBACjB6D,QAAQ,EAAGC,CAAC,IAAK7D,YAAY,CAAC6D,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC9C5B,KAAK,EAAE9B,cAAe;kBACtB+E,OAAO,EAAE9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiC,GAAG,CAAEc,GAAG;oBAAA,IAAAC,qBAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,UAAA,EAAAC,WAAA;oBAAA,OAAM;sBAC3B3B,KAAK,EAAEsB,GAAG,CAACM,EAAE;sBACbjC,KAAK,EACH,EAAA4B,qBAAA,IAAAC,WAAA,GAACF,GAAG,CAACO,MAAM,cAAAL,WAAA,uBAAVA,WAAA,CAAYM,UAAU,cAAAP,qBAAA,cAAAA,qBAAA,GAAI,KAAK,IAChC,GAAG,KAAAE,oBAAA,IAAAC,UAAA,GACFJ,GAAG,CAACS,KAAK,cAAAL,UAAA,uBAATA,UAAA,CAAWM,SAAS,cAAAP,oBAAA,cAAAA,oBAAA,GAAI,EAAE,CAAC,IAC3BH,GAAG,CAACW,MAAM,GAAG,IAAI,KAAAN,WAAA,GAAGL,GAAG,CAACW,MAAM,cAAAN,WAAA,uBAAVA,WAAA,CAAYO,IAAI,IAAG,IAAI,GAAG,EAAE;oBACrD,CAAC;kBAAA,CAAC;gBAAE;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EACLxC,gBAAgB,gBACfpD,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAEhD,YAAa;kBACpBiD,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3B/C,eAAe,CAACiD,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC/B,IAAIE,CAAC,CAACG,MAAM,CAACL,KAAK,EAAE;sBAClBG,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;sBAClB,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAACG,MAAM,CAACL,KAAK,CACjC7F,KAAK,CAAC,GAAG,CAAC,CACVqG,GAAG,CAACC,MAAM,CAAC;;sBAEd;sBACA,MAAMC,KAAK,GAAG,IAAIzG,IAAI,CAACqG,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;;sBAE1C;sBACA,MAAMI,GAAG,GAAG,IAAI1G,IAAI,CAACqG,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;;sBAEpC;sBACA,MAAMK,kBAAkB,GAAGF,KAAK,CAC7BxG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBACf,MAAMC,gBAAgB,GAAGH,GAAG,CACzBzG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBAEfhG,mBAAmB,CAAC+F,kBAAkB,CAAC;sBACvC3F,iBAAiB,CAAC6F,gBAAgB,CAAC;oBACrC;kBACF,CAAE;kBACF1C,KAAK,EAAElB;gBAAkB;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,EACP,CAACxC,gBAAgB,gBAChBpD,OAAA;gBAAKgF,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAEpF,gBAAiB;kBACxBqF,QAAQ,EAAGC,CAAC,IAAKrF,mBAAmB,CAACqF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACrD5B,KAAK,EAAEtD;gBAAsB;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC,eACF5F,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZiB,QAAQ,EAAEnG,gBAAgB,KAAK,EAAG;kBAClCmF,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAEhF,cAAe;kBACtBiF,QAAQ,EAAGC,CAAC,IAAKjF,iBAAiB,CAACiF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACnD5B,KAAK,EAAElD;gBAAoB;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,eACR5F,OAAA;gBAAKgF,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACblD,iBAAiB,CAAC,EAAE,CAAC;oBACrBxB,wBAAwB,CAAC,EAAE,CAAC;oBAC5BI,sBAAsB,CAAC,EAAE,CAAC;oBAC1BgC,oBAAoB,CAAC,EAAE,CAAC;oBACxB,IAAI6D,KAAK,GAAG,IAAI;oBAChB,IAAI5E,SAAS,KAAK,EAAE,EAAE;sBACpB4E,KAAK,GAAG,KAAK;sBACbzE,iBAAiB,CAAC,sBAAsB,CAAC;oBAC3C;oBACA,IAAI3B,gBAAgB,KAAK,EAAE,IAAI,CAACkC,gBAAgB,EAAE;sBAChDkE,KAAK,GAAG,KAAK;sBACbjG,wBAAwB,CAAC,sBAAsB,CAAC;oBAClD;oBACA,IAAIC,cAAc,KAAK,EAAE,IAAI,CAAC8B,gBAAgB,EAAE;sBAC9CkE,KAAK,GAAG,KAAK;sBACb7F,sBAAsB,CAAC,sBAAsB,CAAC;oBAChD;oBACA,IAAI6B,YAAY,KAAK,EAAE,IAAIF,gBAAgB,EAAE;sBAC3CkE,KAAK,GAAG,KAAK;sBACb7D,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAI6D,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIhH,IAAI,CAACW,gBAAgB,CAAC;sBAC5C,MAAMsG,OAAO,GAAG,IAAIjH,IAAI,CAACe,cAAc,CAAC;;sBAExC;sBACA,IAAIiG,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,gBAAe6C,SAAU,gBAAepB,cAAe,aAAYJ,gBAAiB,EAAC,EACxF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLuG,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,gBAAe6C,SAAU,gBAAexB,gBAAiB,aAAYI,cAAe,EAAC,EACxF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF0D,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5F,OAAA;UAAKgF,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBAEzC/E,OAAA;YAAKgF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/E,OAAA,CAACf,aAAa;cAAC4G,KAAK,EAAC,sBAAmB;cAAAd,QAAA,gBACtC/E,OAAA;gBAAKgF,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAACrC,cAAc,EAAE;sBACnBG,aAAa,CAAC,EAAE,CAAC;sBACjB1B,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAoB,iBAAiB,CAAC,IAAI,CAAC;kBACzB,CAAE;kBACFqB,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,GAErDrB,cAAc,gBACb1D,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QAAOtB,cAAc,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAAqB,QAAA,EACxD;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5F,OAAA;kBAAKgF,SAAS,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE3B5F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIrC,cAAc,EAAE;sBAClBG,aAAa,CAAC,EAAE,CAAC;sBACjB1B,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAoB,iBAAiB,CAAC,KAAK,CAAC;kBAC1B,CAAE;kBACFqB,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,GAEpD,CAACrB,cAAc,gBACd1D,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QAAO,CAACtB,cAAc,GAAG,WAAW,GAAG,EAAG,EAAE;oBAAAqB,QAAA,EACzD;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACLlC,cAAc,gBACb1D,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAE1C,UAAW;kBAClB2C,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3BzC,aAAa,CAAC2C,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC7B,IAAIE,CAAC,CAACG,MAAM,CAACL,KAAK,EAAE;sBAClBG,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;sBAClB,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAACG,MAAM,CAACL,KAAK,CACjC7F,KAAK,CAAC,GAAG,CAAC,CACVqG,GAAG,CAACC,MAAM,CAAC;;sBAEd;sBACA,MAAMC,KAAK,GAAG,IAAIzG,IAAI,CAACqG,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;;sBAE1C;sBACA,MAAMI,GAAG,GAAG,IAAI1G,IAAI,CAACqG,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;;sBAEpC;sBACA,MAAMK,kBAAkB,GAAGF,KAAK,CAC7BxG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBACf,MAAMC,gBAAgB,GAAGH,GAAG,CACzBzG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBAEfhF,eAAe,CAAC+E,kBAAkB,CAAC;sBACnC3E,aAAa,CAAC6E,gBAAgB,CAAC;oBACjC;kBACF,CAAE;kBACF1C,KAAK,EAAEZ;gBAAgB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,EACP,CAAClC,cAAc,gBACd1D,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,gBAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACX6F,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEpE,YAAa;kBACpBqE,QAAQ,EAAGC,CAAC,IAAKrE,eAAe,CAACqE,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD5B,KAAK,EAAEtC;gBAAkB;kBAAAqD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF5F,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACX+G,QAAQ,EAAEnF,YAAY,KAAK,EAAG;kBAC9BiE,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEhE,UAAW;kBAClBiE,QAAQ,EAAGC,CAAC,IAAKjE,aAAa,CAACiE,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C5B,KAAK,EAAElC;gBAAgB;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,eACR5F,OAAA;gBAAKgF,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb1D,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtBsB,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIuD,KAAK,GAAG,IAAI;oBAChB,IAAIpF,YAAY,KAAK,EAAE,IAAI,CAACwB,cAAc,EAAE;sBAC1C4D,KAAK,GAAG,KAAK;sBACbjF,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,IAAI,CAACoB,cAAc,EAAE;sBACxC4D,KAAK,GAAG,KAAK;sBACb7E,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAImB,UAAU,KAAK,EAAE,IAAIF,cAAc,EAAE;sBACvC4D,KAAK,GAAG,KAAK;sBACbvD,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIuD,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIhH,IAAI,CAAC2B,YAAY,CAAC;sBACxC,MAAMsF,OAAO,GAAG,IAAIjH,IAAI,CAAC+B,UAAU,CAAC;;sBAEpC;sBACA,IAAIiF,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,yCAAwCyC,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLuF,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,yCAAwCqC,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF0C,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAEN5F,OAAA;YAAKgF,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC/E,OAAA,CAACf,aAAa;cAAC4G,KAAK,EAAC,cAAW;cAAAd,QAAA,gBAC9B/E,OAAA;gBAAKgF,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI,CAAC/B,kBAAkB,EAAE;sBACvBG,iBAAiB,CAAC,EAAE,CAAC;sBACrBxC,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAkC,qBAAqB,CAAC,IAAI,CAAC;kBAC7B,CAAE;kBACFe,SAAS,EAAC,4CAA4C;kBAAAD,QAAA,GAErDf,kBAAkB,gBACjBhE,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QACVhB,kBAAkB,GAAG,WAAW,GAAG,EACpC,EAAE;oBAAAe,QAAA,EACJ;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACN5F,OAAA;kBAAKgF,SAAS,EAAC;gBAAK;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAE3B5F,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAI/B,kBAAkB,EAAE;sBACtBG,iBAAiB,CAAC,EAAE,CAAC;sBACrBxC,eAAe,CAAC,EAAE,CAAC;sBACnBI,aAAa,CAAC,EAAE,CAAC;oBACnB;oBACAkC,qBAAqB,CAAC,KAAK,CAAC;kBAC9B,CAAE;kBACFe,SAAS,EAAC,2CAA2C;kBAAAD,QAAA,GAEpD,CAACf,kBAAkB,gBAClBhE,OAAA;oBACEkF,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBW,KAAK,EAAC,QAAQ;oBAAAjB,QAAA,eAEd/E,OAAA;sBACEsF,aAAa,EAAC,OAAO;sBACrBC,cAAc,EAAC,OAAO;sBACtBC,CAAC,EAAC;oBAAgE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAEN5F,OAAA;oBAAKgF,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAClD,eACD5F,OAAA;oBACEgF,SAAS,EAAG,QACV,CAAChB,kBAAkB,GAAG,WAAW,GAAG,EACrC,EAAE;oBAAAe,QAAA,EACJ;kBAED;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL5B,kBAAkB,gBACjBhE,OAAA;gBAAKgF,SAAS,EAAC,8BAA8B;gBAAAD,QAAA,eAC3C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,OAAO;kBACZC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAEpC,cAAe;kBACtBqC,QAAQ,EAAGC,CAAC,IAAK;oBACfC,OAAO,CAACC,GAAG,CAACF,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBAC3BnC,iBAAiB,CAACqC,CAAC,CAACG,MAAM,CAACL,KAAK,CAAC;oBACjC,IAAIE,CAAC,CAACG,MAAM,CAACL,KAAK,EAAE;sBAClBG,OAAO,CAACC,GAAG,CAAC,KAAK,CAAC;sBAClB,MAAM,CAACE,IAAI,EAAEC,KAAK,CAAC,GAAGL,CAAC,CAACG,MAAM,CAACL,KAAK,CACjC7F,KAAK,CAAC,GAAG,CAAC,CACVqG,GAAG,CAACC,MAAM,CAAC;;sBAEd;sBACA,MAAMC,KAAK,GAAG,IAAIzG,IAAI,CAACqG,IAAI,EAAEC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;;sBAE1C;sBACA,MAAMI,GAAG,GAAG,IAAI1G,IAAI,CAACqG,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC;;sBAEpC;sBACA,MAAMK,kBAAkB,GAAGF,KAAK,CAC7BxG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBACf,MAAMC,gBAAgB,GAAGH,GAAG,CACzBzG,WAAW,CAAC,CAAC,CACb2G,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;sBAEfxF,eAAe,CAACuF,kBAAkB,CAAC;sBACnCnF,aAAa,CAACqF,gBAAgB,CAAC;oBACjC;kBACF,CAAE;kBACF1C,KAAK,EAAEN;gBAAoB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,EACP,CAAC5B,kBAAkB,gBAClBhE,OAAA;gBAAKgF,SAAS,EAAC,+BAA+B;gBAAAD,QAAA,gBAC5C/E,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAE5E,YAAa;kBACpB6E,QAAQ,EAAGC,CAAC,IAAK7E,eAAe,CAAC6E,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBACjD5B,KAAK,EAAE9C;gBAAkB;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACF5F,OAAA,CAACX,UAAU;kBACT4G,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZiB,QAAQ,EAAE3F,YAAY,KAAK,EAAG;kBAC9B2E,GAAG,EAAE/F,KAAM;kBACXgG,KAAK,EAAExE,UAAW;kBAClByE,QAAQ,EAAGC,CAAC,IAAKzE,aAAa,CAACyE,CAAC,CAACG,MAAM,CAACL,KAAK,CAAE;kBAC/C5B,KAAK,EAAE1C;gBAAgB;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ,IAAI,eACR5F,OAAA;gBAAKgF,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD/E,OAAA;kBACE+F,OAAO,EAAEA,CAAA,KAAM;oBACblE,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtBoC,sBAAsB,CAAC,EAAE,CAAC;oBAC1B,IAAIiD,KAAK,GAAG,IAAI;oBAChB,IAAI5F,YAAY,KAAK,EAAE,IAAI,CAACsC,kBAAkB,EAAE;sBAC9CsD,KAAK,GAAG,KAAK;sBACbzF,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,IAAI,CAACkC,kBAAkB,EAAE;sBAC5CsD,KAAK,GAAG,KAAK;sBACbrF,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIiC,cAAc,KAAK,EAAE,IAAIF,kBAAkB,EAAE;sBAC/CsD,KAAK,GAAG,KAAK;sBACbjD,sBAAsB,CAAC,sBAAsB,CAAC;oBAChD;oBACA,IAAIiD,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAIhH,IAAI,CAACmB,YAAY,CAAC;sBACxC,MAAM8F,OAAO,GAAG,IAAIjH,IAAI,CAACuB,UAAU,CAAC;;sBAEpC;sBACA,IAAIyF,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,2CAA0CiC,UAAW,aAAYJ,YAAa,EAAC,EAClF,QACF,CAAC;sBACH,CAAC,MAAM;wBACL+F,MAAM,CAACC,IAAI,CACT7H,OAAO,GACJ,2CAA0C6B,YAAa,aAAYI,UAAW,EAAC,EAClF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFkD,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC1F,EAAA,CAt4BQD,YAAY;EAAA,QACFnB,WAAW,EACXD,WAAW,EACXF,WAAW,EA4CVC,WAAW,EAGbA,WAAW;AAAA;AAAA6J,EAAA,GAlDpBxI,YAAY;AAw4BrB,eAAeA,YAAY;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}