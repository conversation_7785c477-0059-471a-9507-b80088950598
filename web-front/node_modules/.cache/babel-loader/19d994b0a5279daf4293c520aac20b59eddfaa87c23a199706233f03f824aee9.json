{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{casesList,deleteCase}from\"../../redux/actions/caseActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import DefaultLayout from\"../../layouts/DefaultLayout\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CaseScreen(){const navigate=useNavigate();const location=useLocation();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[caseId,setCaseId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCases=useSelector(state=>state.caseList);const{cases,loadingCases,errorCases,pages}=listCases;const caseDelete=useSelector(state=>state.deleteCase);const{loadingCaseDelete,errorCaseDelete,successCaseDelete}=caseDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(casesList(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successCaseDelete){dispatch(casesList(\"1\"));}},[successCaseDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"BUSQUEDA DE CASOS\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"BUSQUEDA DE CASOS\"}),/*#__PURE__*/_jsxs(Link,{to:\"/cases/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Add\"]})]}),loadingCases?/*#__PURE__*/_jsx(Loader,{}):errorCases?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCases}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\" bg-black text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-white text-xs w-max \",children:\"Fecha entrada\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-white text-xs w-max \",children:\"Cliente\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-white text-xs w-max\",children:\"No caso\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\",children:\"Pax\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\",children:\"Phone\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\",children:\"Ciudad\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-white text-xs w-max\",children:\"Pa\\xEDs\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-white text-xs w-max\",children:\"Operaci\\xF3n\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[cases===null||cases===void 0?void 0:cases.map((item,index)=>{var _item$client;return/*#__PURE__*/ (//  <a href={`/cases/detail/${item.id}`}></a>\n_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.case_date})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.case_pax})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_item$client=item.client)===null||_item$client===void 0?void 0:_item$client.full_name})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.case_phone})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.case_email})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.city})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:item.country})}),/*#__PURE__*/_jsx(\"td\",{className:\"border border-black py-3 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 detail-class\",to:\"#\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:[/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"}),/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"})]})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cases/edit/\"+item.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",strokeWidth:\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEventType(\"delete\");setCaseId(item.id);setIsDelete(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"})})})]})})]},index));}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/cases?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer ce case?\":\"Êtes-vous sûr de vouloir ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&caseId!==\"\"){setLoadEvent(true);dispatch(deleteCase(caseId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default CaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "casesList", "deleteCase", "Loader", "<PERSON><PERSON>", "Paginate", "DefaultLayout", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "CaseScreen", "navigate", "location", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "caseId", "setCaseId", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "caseDelete", "loadingCaseDelete", "errorCaseDelete", "successCaseDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "to", "type", "message", "map", "item", "index", "_item$client", "case_date", "case_pax", "id", "client", "full_name", "case_phone", "case_email", "city", "country", "strokeWidth", "onClick", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/CaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { casesList, deleteCase } from \"../../redux/actions/caseActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction CaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [caseId, setCaseId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const caseDelete = useSelector((state) => state.deleteCase);\n  const { loadingCaseDelete, errorCaseDelete, successCaseDelete } = caseDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successCaseDelete) {\n      dispatch(casesList(\"1\"));\n    }\n  }, [successCaseDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">BUSQUEDA DE CASOS</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              BUSQUEDA DE CASOS\n            </h4>\n            <Link\n              to={\"/cases/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Add\n            </Link>\n          </div>\n\n          {loadingCases ? (\n            <Loader />\n          ) : errorCases ? (\n            <Alert type=\"error\" message={errorCases} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\" bg-black text-left \">\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-white text-xs w-max \">\n                      Fecha entrada\n                    </th>\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-white text-xs w-max \">\n                      Cliente\n                    </th>\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-white text-xs w-max\">\n                      No caso\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\">\n                      Pax\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\">\n                      Phone\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\">\n                      Email\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-white text-xs w-max\">\n                      Ciudad\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-white text-xs w-max\">\n                      País\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-white text-xs w-max\">\n                      Operación\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {cases?.map((item, index) => (\n                    //  <a href={`/cases/detail/${item.id}`}></a>\n                    <tr key={index}>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.case_date}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.case_pax}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">{item.id}</p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.client?.full_name}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.case_phone}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.case_email}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.city}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {item.country}\n                        </p>\n                      </td>\n                      <td className=\"border border-black py-3 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max flex flex-row  \">\n                          <Link className=\"mx-1 detail-class\" to={\"#\"}>\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z\"\n                              />\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n                              />\n                            </svg>\n                          </Link>\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/cases/edit/\" + item.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              strokeWidth=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          <div\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setCaseId(item.id);\n                              setIsDelete(true);\n                            }}\n                            className=\"mx-1 delete-class cursor-pointer\"\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m20.25 7.5-.625 10.632a2.25 2.25 0 0 1-2.247 2.118H6.622a2.25 2.25 0 0 1-2.247-2.118L3.75 7.5m6 4.125 2.25 2.25m0 0 2.25 2.25M12 13.875l2.25-2.25M12 13.875l-2.25 2.25M3.375 7.5h17.25c.621 0 1.125-.504 1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125Z\"\n                              />\n                            </svg>\n                          </div>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/cases?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer ce case?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && caseId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteCase(caseId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default CaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,SAAS,CAAEC,UAAU,KAAQ,iCAAiC,CACvE,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,QAAS,CAAAC,UAAUA,CAAA,CAAG,CACpB,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAe,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACiB,YAAY,CAAC,CAAGf,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAgB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGvB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACwB,QAAQ,CAAEC,WAAW,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC2B,SAAS,CAAEC,YAAY,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+B,MAAM,CAAEC,SAAS,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAExC,KAAM,CAAAiC,SAAS,CAAG/B,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,SAAS,CAAGlC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAACG,QAAQ,CAAC,CACxD,KAAM,CAAEC,KAAK,CAAEC,YAAY,CAAEC,UAAU,CAAEC,KAAM,CAAC,CAAGL,SAAS,CAE5D,KAAM,CAAAM,UAAU,CAAGxC,WAAW,CAAEgC,KAAK,EAAKA,KAAK,CAAC1B,UAAU,CAAC,CAC3D,KAAM,CAAEmC,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB/C,SAAS,CAAC,IAAM,CACd,GAAI,CAACoC,QAAQ,CAAE,CACbhB,QAAQ,CAAC2B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtB,QAAQ,CAACjB,SAAS,CAACe,IAAI,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEgB,QAAQ,CAAEX,QAAQ,CAAEF,IAAI,CAAC,CAAC,CAExCvB,SAAS,CAAC,IAAM,CACd,GAAI8C,iBAAiB,CAAE,CACrBrB,QAAQ,CAACjB,SAAS,CAAC,GAAG,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACsC,iBAAiB,CAAC,CAAC,CAEvB,mBACE9B,IAAA,CAACH,aAAa,EAAAmC,QAAA,cACZ9B,KAAA,QAAA8B,QAAA,eACE9B,KAAA,QAAK+B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDhC,IAAA,MAAGkC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB9B,KAAA,QAAK+B,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzC,IAAA,SAAMiC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJhC,IAAA,SAAAgC,QAAA,cACEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,EACtC,CAAC,cAEN9B,KAAA,QAAK+B,SAAS,CAAC,8GAA8G,CAAAD,QAAA,eAC3H9B,KAAA,QAAK+B,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DhC,IAAA,OAAIiC,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,mBAEnE,CAAI,CAAC,cACL9B,KAAA,CAACd,IAAI,EACHsD,EAAE,CAAE,YAAa,CACjBT,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,MAER,EAAM,CAAC,EACJ,CAAC,CAELjB,YAAY,cACXxB,IAAA,CAACN,MAAM,GAAE,CAAC,CACR+B,UAAU,cACZzB,IAAA,CAACL,KAAK,EAACgD,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEnB,UAAW,CAAE,CAAC,cAE3CvB,KAAA,QAAK+B,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C9B,KAAA,UAAO+B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClChC,IAAA,UAAAgC,QAAA,cACE9B,KAAA,OAAI+B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClChC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,eAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,SAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,2DAA2D,CAAAD,QAAA,CAAC,SAE1E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,KAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,QAE3E,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,SAE7D,CAAI,CAAC,cACLhC,IAAA,OAAIiC,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,cAE7D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAER9B,KAAA,UAAA8B,QAAA,EACGT,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEsB,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,QAAAC,YAAA,qBACtB;AACA9C,KAAA,OAAA8B,QAAA,eACEhC,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACG,SAAS,CACd,CAAC,CACF,CAAC,cACLjD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACI,QAAQ,CACb,CAAC,CACF,CAAC,cACLlD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAEc,IAAI,CAACK,EAAE,CAAI,CAAC,CACtD,CAAC,cACLnD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAgB,YAAA,CACvCF,IAAI,CAACM,MAAM,UAAAJ,YAAA,iBAAXA,YAAA,CAAaK,SAAS,CACtB,CAAC,CACF,CAAC,cACLrD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACQ,UAAU,CACf,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACS,UAAU,CACf,CAAC,CACF,CAAC,cACLvD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACU,IAAI,CACT,CAAC,CACF,CAAC,cACLxD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DhC,IAAA,MAAGiC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCc,IAAI,CAACW,OAAO,CACZ,CAAC,CACF,CAAC,cACLzD,IAAA,OAAIiC,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3D9B,KAAA,MAAG+B,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eACtDhC,IAAA,CAACZ,IAAI,EAAC6C,SAAS,CAAC,mBAAmB,CAACS,EAAE,CAAE,GAAI,CAAAV,QAAA,cAC1C9B,KAAA,QACEiC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzEhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByC,CAAC,CAAC,0LAA0L,CAC7L,CAAC,cACFzC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByC,CAAC,CAAC,qCAAqC,CACxC,CAAC,EACC,CAAC,CACF,CAAC,cACPzC,IAAA,CAACZ,IAAI,EACH6C,SAAS,CAAC,mBAAmB,CAC7BS,EAAE,CAAE,cAAc,CAAGI,IAAI,CAACK,EAAG,CAAAnB,QAAA,cAE7BhC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnBqB,WAAW,CAAC,KAAK,CACjBpB,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEhC,IAAA,SACEuC,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cACPzC,IAAA,QACE2D,OAAO,CAAEA,CAAA,GAAM,CACb5C,YAAY,CAAC,QAAQ,CAAC,CACtBE,SAAS,CAAC6B,IAAI,CAACK,EAAE,CAAC,CAClBxC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CACFsB,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5ChC,IAAA,QACEmC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEhC,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByC,CAAC,CAAC,+TAA+T,CAClU,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,GAzGEM,KA0GL,CAAC,GACN,CAAC,cACF/C,IAAA,OAAIiC,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACRjC,IAAA,QAAKiC,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfhC,IAAA,CAACJ,QAAQ,EACPgE,KAAK,CAAE,SAAU,CACjBC,MAAM,CAAE,EAAG,CACXtD,IAAI,CAAEA,IAAK,CACXmB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cACN1B,IAAA,CAACF,iBAAiB,EAChBgE,MAAM,CAAEpD,QAAS,CACjBkC,OAAO,CACL9B,SAAS,GAAK,QAAQ,CAClB,6CAA6C,CAC7C,4BACL,CACDiD,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIjD,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,MAAM,GAAK,EAAE,CAAE,CAClDH,YAAY,CAAC,IAAI,CAAC,CAClBJ,QAAQ,CAAChB,UAAU,CAACuB,MAAM,CAAC,CAAC,CAC5BL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFmD,QAAQ,CAAEA,CAAA,GAAM,CACdrD,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFZ,IAAA,QAAKiC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA9B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}