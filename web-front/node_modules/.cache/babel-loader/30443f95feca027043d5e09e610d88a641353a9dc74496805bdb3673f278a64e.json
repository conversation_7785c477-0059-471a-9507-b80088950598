{"ast": null, "code": "export const CLIENT_LIST_REQUEST=\"CLIENT_LIST_REQUEST\";export const CLIENT_LIST_SUCCESS=\"CLIENT_LIST_SUCCESS\";export const CLIENT_LIST_FAIL=\"CLIENT_LIST_FAIL\";export const CLIENT_ADD_REQUEST=\"CLIENT_ADD_REQUEST\";export const CLIENT_ADD_SUCCESS=\"CLIENT_ADD_SUCCESS\";export const CLIENT_ADD_FAIL=\"CLIENT_ADD_FAIL\";export const CLIENT_DETAIL_REQUEST=\"CLIENT_DETAIL_REQUEST\";export const CLIENT_DETAIL_SUCCESS=\"CLIENT_DETAIL_SUCCESS\";export const CLIENT_DETAIL_FAIL=\"CLIENT_DETAIL_FAIL\";export const CLIENT_UPDATE_REQUEST=\"CLIENT_UPDATE_REQUEST\";export const CLIENT_UPDATE_SUCCESS=\"CLIENT_UPDATE_SUCCESS\";export const CLIENT_UPDATE_FAIL=\"CLIENT_UPDATE_FAIL\";export const CLIENT_DELETE_REQUEST=\"CLIENT_DELETE_REQUEST\";export const CLIENT_DELETE_SUCCESS=\"CLIENT_DELETE_SUCCESS\";export const CLIENT_DELETE_FAIL=\"CLIENT_DELETE_FAIL\";", "map": {"version": 3, "names": ["CLIENT_LIST_REQUEST", "CLIENT_LIST_SUCCESS", "CLIENT_LIST_FAIL", "CLIENT_ADD_REQUEST", "CLIENT_ADD_SUCCESS", "CLIENT_ADD_FAIL", "CLIENT_DETAIL_REQUEST", "CLIENT_DETAIL_SUCCESS", "CLIENT_DETAIL_FAIL", "CLIENT_UPDATE_REQUEST", "CLIENT_UPDATE_SUCCESS", "CLIENT_UPDATE_FAIL", "CLIENT_DELETE_REQUEST", "CLIENT_DELETE_SUCCESS", "CLIENT_DELETE_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/constants/clientConstants.js"], "sourcesContent": ["export const CLIENT_LIST_REQUEST = \"CLIENT_LIST_REQUEST\";\nexport const CLIENT_LIST_SUCCESS = \"CLIENT_LIST_SUCCESS\";\nexport const CLIENT_LIST_FAIL = \"CLIENT_LIST_FAIL\";\n\nexport const CLIENT_ADD_REQUEST = \"CLIENT_ADD_REQUEST\";\nexport const CLIENT_ADD_SUCCESS = \"CLIENT_ADD_SUCCESS\";\nexport const CLIENT_ADD_FAIL = \"CLIENT_ADD_FAIL\";\n\nexport const CLIENT_DETAIL_REQUEST = \"CLIENT_DETAIL_REQUEST\";\nexport const CLIENT_DETAIL_SUCCESS = \"CLIENT_DETAIL_SUCCESS\";\nexport const CLIENT_DETAIL_FAIL = \"CLIENT_DETAIL_FAIL\";\n\nexport const CLIENT_UPDATE_REQUEST = \"CLIENT_UPDATE_REQUEST\";\nexport const CLIENT_UPDATE_SUCCESS = \"CLIENT_UPDATE_SUCCESS\";\nexport const CLIENT_UPDATE_FAIL = \"CLIENT_UPDATE_FAIL\";\n\nexport const CLIENT_DELETE_REQUEST = \"CLIENT_DELETE_REQUEST\";\nexport const CLIENT_DELETE_SUCCESS = \"CLIENT_DELETE_SUCCESS\";\nexport const CLIENT_DELETE_FAIL = \"CLIENT_DELETE_FAIL\";\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,mBAAmB,CAAG,qBAAqB,CACxD,MAAO,MAAM,CAAAC,mBAAmB,CAAG,qBAAqB,CACxD,MAAO,MAAM,CAAAC,gBAAgB,CAAG,kBAAkB,CAElD,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CACtD,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CACtD,MAAO,MAAM,CAAAC,eAAe,CAAG,iBAAiB,CAEhD,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CAEtD,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB,CAEtD,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,qBAAqB,CAAG,uBAAuB,CAC5D,MAAO,MAAM,CAAAC,kBAAkB,CAAG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}