{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/DefaultLayout.js\",\n  _s = $RefreshSig$();\nimport { useState } from \"react\";\nimport Sidebar from \"./Sidebar\";\nimport Header from \"./Header\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n// import axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DefaultLayout = props => {\n  _s();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // const check = async()=>{\n  //     const userInfoFromStorage = localStorage.getItem('userInfoUnimedCare')\n  //       ? JSON.parse(localStorage.getItem('userInfoUnimedCare'))\n  //       : null\n  //     if (userInfoFromStorage != null) {\n  //       const config = {\n  //         headers: {\n  //           'Content-Type': 'application/json',\n  //           Authorization: `Bearer ${userInfoFromStorage.access}`,\n  //         },\n  //       }\n  //       try {\n  //         axios.get(`/api/users/check-login-admin/`, config).then((data) => {\n\n  //         }).catch((er) => {\n  //           console.log(er);\n  //           window.location.replace(\"/\");\n  //         })\n  //       } catch (error) {\n\n  //       }\n\n  //     } else {\n  //       window.location.replace(\"/\");\n  //     }\n  // }\n\n  // useEffect( () => {\n\n  //   }, [])\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dark:bg-boxdark-2 dark:text-bodydark\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex h-screen overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n        sidebarOpen: sidebarOpen,\n        setSidebarOpen: () => setSidebarOpen(!sidebarOpen)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {\n          sidebarOpen: sidebarOpen,\n          setSidebarOpen: () => setSidebarOpen(!sidebarOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n          position: \"top-right\",\n          autoClose: 3000,\n          hideProgressBar: false,\n          newestOnTop: false,\n          closeOnClick: true,\n          rtl: false,\n          pauseOnFocusLoss: true,\n          draggable: true,\n          pauseOnHover: true,\n          theme: \"light\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mx-auto max-w-screen-2xl p-2 md:p-4 2xl:p-8\",\n            children: props.children\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 5\n  }, this);\n};\n_s(DefaultLayout, \"5rGDkYpGQ8fHM9RkMWnKOwsxadk=\");\n_c = DefaultLayout;\nexport default DefaultLayout;\nvar _c;\n$RefreshReg$(_c, \"DefaultLayout\");", "map": {"version": 3, "names": ["useState", "Sidebar", "Header", "ToastContainer", "jsxDEV", "_jsxDEV", "DefaultLayout", "props", "_s", "sidebarOpen", "setSidebarOpen", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/layouts/DefaultLayout.js"], "sourcesContent": ["import { useState } from \"react\";\nimport Sidebar from \"./Sidebar\";\nimport Header from \"./Header\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n// import axios from \"axios\";\n\nconst DefaultLayout = (props) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // const check = async()=>{\n  //     const userInfoFromStorage = localStorage.getItem('userInfoUnimedCare')\n  //       ? JSON.parse(localStorage.getItem('userInfoUnimedCare'))\n  //       : null\n  //     if (userInfoFromStorage != null) {\n  //       const config = {\n  //         headers: {\n  //           'Content-Type': 'application/json',\n  //           Authorization: `Bearer ${userInfoFromStorage.access}`,\n  //         },\n  //       }\n  //       try {\n  //         axios.get(`/api/users/check-login-admin/`, config).then((data) => {\n\n  //         }).catch((er) => {\n  //           console.log(er);\n  //           window.location.replace(\"/\");\n  //         })\n  //       } catch (error) {\n\n  //       }\n\n  //     } else {\n  //       window.location.replace(\"/\");\n  //     }\n  // }\n\n  // useEffect( () => {\n\n  //   }, [])\n\n  return (\n    <div className=\"dark:bg-boxdark-2 dark:text-bodydark\">\n      {/* <!-- ===== Page Wrapper Start ===== --> */}\n      <div className=\"flex h-screen overflow-hidden\">\n        {/* <!-- ===== Sidebar Start ===== --> */}\n        <Sidebar\n          sidebarOpen={sidebarOpen}\n          setSidebarOpen={() => setSidebarOpen(!sidebarOpen)}\n        />\n        {/* <!-- ===== Sidebar End ===== --> */}\n\n        {/* <!-- ===== Content Area Start ===== --> */}\n        <div className=\"relative flex flex-1 flex-col overflow-y-auto overflow-x-hidden\">\n          {/* <!-- ===== Header Start ===== --> */}\n          <Header\n            sidebarOpen={sidebarOpen}\n            setSidebarOpen={() => setSidebarOpen(!sidebarOpen)}\n          />\n          <ToastContainer\n            position=\"top-right\"\n            autoClose={3000}\n            hideProgressBar={false}\n            newestOnTop={false}\n            closeOnClick\n            rtl={false}\n            pauseOnFocusLoss\n            draggable\n            pauseOnHover\n            theme=\"light\"\n          />\n          {/* <!-- ===== Header End ===== --> */}\n\n          {/* <!-- ===== Main Content Start ===== --> */}\n          <main>\n            <div className=\"mx-auto max-w-screen-2xl p-2 md:p-4 2xl:p-8\">\n              {props.children}\n            </div>\n          </main>\n          {/* <!-- ===== Main Content End ===== --> */}\n        </div>\n        {/* <!-- ===== Content Area End ===== --> */}\n      </div>\n      {/* <!-- ===== Page Wrapper End ===== --> */}\n    </div>\n  );\n};\n\nexport default DefaultLayout;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAC9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,aAAa,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;;EAEA,oBACEK,OAAA;IAAKM,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eAEnDP,OAAA;MAAKM,SAAS,EAAC,+BAA+B;MAAAC,QAAA,gBAE5CP,OAAA,CAACJ,OAAO;QACNQ,WAAW,EAAEA,WAAY;QACzBC,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAAC,CAACD,WAAW;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eAIFX,OAAA;QAAKM,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAE9EP,OAAA,CAACH,MAAM;UACLO,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,CAAA,KAAMA,cAAc,CAAC,CAACD,WAAW;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACFX,OAAA,CAACF,cAAc;UACbc,QAAQ,EAAC,WAAW;UACpBC,SAAS,EAAE,IAAK;UAChBC,eAAe,EAAE,KAAM;UACvBC,WAAW,EAAE,KAAM;UACnBC,YAAY;UACZC,GAAG,EAAE,KAAM;UACXC,gBAAgB;UAChBC,SAAS;UACTC,YAAY;UACZC,KAAK,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAIFX,OAAA;UAAAO,QAAA,eACEP,OAAA;YAAKM,SAAS,EAAC,6CAA6C;YAAAC,QAAA,EACzDL,KAAK,CAACK;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV,CAAC;AAACR,EAAA,CA/EIF,aAAa;AAAAqB,EAAA,GAAbrB,aAAa;AAiFnB,eAAeA,aAAa;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}