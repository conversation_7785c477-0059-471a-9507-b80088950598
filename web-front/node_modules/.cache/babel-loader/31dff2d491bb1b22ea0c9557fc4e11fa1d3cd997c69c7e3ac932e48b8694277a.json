{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction EditCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [cliente, setCliente] = useState(\"\");\n  const [errorCliente, setErrorCliente] = useState(\"\");\n  const [date, setDate] = useState(\"\");\n  const [errorDate, setErrorDate] = useState(\"\");\n  const [pax, setPax] = useState(\"\");\n  const [errorPax, setErrorPax] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listClient = useSelector(state => state.clientList);\n  const {\n    clients\n  } = listClient;\n  const clientDetail = useSelector(state => state.detailClient);\n  const {\n    loading,\n    error,\n    success,\n    client\n  } = clientDetail;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"EditCaseScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 10\n  }, this);\n}\n_s(EditCaseScreen, \"ixw1vCPOziNxlp+zCyXQw4VZlHk=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "jsxDEV", "_jsxDEV", "EditCaseScreen", "_s", "navigate", "location", "dispatch", "id", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "cliente", "setCliente", "errorCliente", "setErrorCliente", "date", "setDate", "errorDate", "setErrorDate", "pax", "setPax", "errorPax", "setErrorPax", "email", "setEmail", "errorEmail", "setErrorEmail", "phone", "setPhone", "errorPhone", "setErrorPhone", "country", "setCountry", "errorCountry", "setErrorCountry", "city", "setCity", "errorCity", "setErrorCity", "userLogin", "state", "userInfo", "listClient", "clientList", "clients", "clientDetail", "detailClient", "loading", "error", "success", "client", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [cliente, setCliente] = useState(\"\");\n  const [errorCliente, setErrorCliente] = useState(\"\");\n\n  const [date, setDate] = useState(\"\");\n  const [errorDate, setErrorDate] = useState(\"\");\n\n  const [pax, setPax] = useState(\"\");\n  const [errorPax, setErrorPax] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [errorEmail, setErrorEmail] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [errorPhone, setErrorPhone] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [errorCountry, setErrorCountry] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [errorCity, setErrorCity] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients } = listClient;\n\n  const clientDetail = useSelector((state) => state.detailClient);\n  const { loading, error, success, client } = clientDetail;\n\n  return <div>EditCaseScreen</div>;\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEY;EAAG,CAAC,GAAGR,SAAS,CAAC,CAAC;EAExB,MAAM,CAACS,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgB,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoB,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACsB,IAAI,EAAEC,OAAO,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC0B,GAAG,EAAEC,MAAM,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACkC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC0C,IAAI,EAAEC,OAAO,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC4C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM8C,SAAS,GAAG5C,WAAW,CAAE6C,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,UAAU,GAAG/C,WAAW,CAAE6C,KAAK,IAAKA,KAAK,CAACG,UAAU,CAAC;EAC3D,MAAM;IAAEC;EAAQ,CAAC,GAAGF,UAAU;EAE9B,MAAMG,YAAY,GAAGlD,WAAW,CAAE6C,KAAK,IAAKA,KAAK,CAACM,YAAY,CAAC;EAC/D,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC,OAAO;IAAEC;EAAO,CAAC,GAAGL,YAAY;EAExD,oBAAO7C,OAAA;IAAAmD,QAAA,EAAK;EAAc;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAClC;AAACrD,EAAA,CAxCQD,cAAc;EAAA,QACJJ,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EA0BJH,WAAW,EAGVA,WAAW,EAGTA,WAAW;AAAA;AAAA6D,EAAA,GApCzBvD,cAAc;AA0CvB,eAAeA,cAAc;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}