{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import{deleteReservation,getListReservations}from\"../../redux/actions/reservationActions\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ReservationScreen(){const navigate=useNavigate();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[reservationId,setReservationId]=useState(\"\");const[isDelete,setIsDelete]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listReservation=useSelector(state=>state.reservationList);const{reservations,loading,error,pages}=listReservation;const reservationDelete=useSelector(state=>state.deleteReservation);const{loadingReservationDelete,errorReservationDelete,successReservationDelete}=reservationDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListReservations(page));}},[navigate,userInfo,dispatch,page]);useEffect(()=>{if(successReservationDelete){dispatch(getListReservations(\"1\"));setReservationId(\"\");setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},[successReservationDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"R\\xE9servation\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black \",children:\"Gestion des R\\xE9servation\"}),/*#__PURE__*/_jsxs(Link,{to:\"/reservations/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Ajouter\"]})]}),loading?/*#__PURE__*/_jsx(Loader,{}):error?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"NR\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Voiture\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"D\\xE9but\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Fin\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"NJ\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Prix/jour\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Montant\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Avance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\",children:\"Reste\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[reservations===null||reservations===void 0?void 0:reservations.map((reservation,id)=>{var _reservation$client$f,_reservation$client,_reservation$client$l,_reservation$client2,_ref,_reservation$car$marq,_reservation$car,_reservation$car$marq2,_reservation$car2,_reservation$car2$mod,_reservation$model_ca,_reservation$start_da,_reservation$end_date,_reservation$nbr_day,_parseFloat$toFixed,_parseFloat$toFixed2,_parseFloat$toFixed3,_parseFloat$toFixed4;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:reservation.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  \",children:[(_reservation$client$f=(_reservation$client=reservation.client)===null||_reservation$client===void 0?void 0:_reservation$client.first_name)!==null&&_reservation$client$f!==void 0?_reservation$client$f:\"---\",\" \",(_reservation$client$l=(_reservation$client2=reservation.client)===null||_reservation$client2===void 0?void 0:_reservation$client2.last_name)!==null&&_reservation$client$l!==void 0?_reservation$client$l:\"\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:reservation.is_withcar?(_ref=((_reservation$car$marq=(_reservation$car=reservation.car)===null||_reservation$car===void 0?void 0:(_reservation$car$marq2=_reservation$car.marque)===null||_reservation$car$marq2===void 0?void 0:_reservation$car$marq2.marque_car)!==null&&_reservation$car$marq!==void 0?_reservation$car$marq:\"---\")+\" \"+((_reservation$car2=reservation.car)===null||_reservation$car2===void 0?void 0:(_reservation$car2$mod=_reservation$car2.model)===null||_reservation$car2$mod===void 0?void 0:_reservation$car2$mod.model_car))!==null&&_ref!==void 0?_ref:\"\":((_reservation$model_ca=reservation.model_car)!==null&&_reservation$model_ca!==void 0?_reservation$model_ca:\"---\")+\" (Sans voiture)\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_reservation$start_da=reservation.start_date)!==null&&_reservation$start_da!==void 0?_reservation$start_da:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_reservation$end_date=reservation.end_date)!==null&&_reservation$end_date!==void 0?_reservation$end_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_reservation$nbr_day=reservation.nbr_day)!==null&&_reservation$nbr_day!==void 0?_reservation$nbr_day:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_parseFloat$toFixed=parseFloat(reservation.price_day).toFixed(2))!==null&&_parseFloat$toFixed!==void 0?_parseFloat$toFixed:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_parseFloat$toFixed2=parseFloat(reservation.price_total).toFixed(2))!==null&&_parseFloat$toFixed2!==void 0?_parseFloat$toFixed2:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_parseFloat$toFixed3=parseFloat(reservation.price_avance).toFixed(2))!==null&&_parseFloat$toFixed3!==void 0?_parseFloat$toFixed3:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  \",children:(_parseFloat$toFixed4=parseFloat(reservation.price_rest).toFixed(2))!==null&&_parseFloat$toFixed4!==void 0?_parseFloat$toFixed4:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/reservations/edit/\"+reservation.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"mx-1 delete-class\",onClick:()=>{setEventType(\"delete\");setReservationId(reservation.id);setIsDelete(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/reservations?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isDelete,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer cette réservation ?\":\"Êtes-vous sûr de vouloir ?\",onConfirm:async()=>{if(eventType===\"delete\"&&reservationId!==\"\"){setLoadEvent(true);dispatch(deleteReservation(reservationId));setIsDelete(false);setEventType(\"\");setLoadEvent(false);}else{setIsDelete(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsDelete(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default ReservationScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "useSearchParams", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "deleteReservation", "getListReservations", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "ReservationScreen", "navigate", "searchParams", "page", "get", "dispatch", "reservationId", "setReservationId", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "userLogin", "state", "userInfo", "listReservation", "reservationList", "reservations", "loading", "error", "pages", "reservationDelete", "loadingReservationDelete", "errorReservationDelete", "successReservationDelete", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "to", "type", "message", "map", "reservation", "id", "_reservation$client$f", "_reservation$client", "_reservation$client$l", "_reservation$client2", "_ref", "_reservation$car$marq", "_reservation$car", "_reservation$car$marq2", "_reservation$car2", "_reservation$car2$mod", "_reservation$model_ca", "_reservation$start_da", "_reservation$end_date", "_reservation$nbr_day", "_parseFloat$toFixed", "_parseFloat$toFixed2", "_parseFloat$toFixed3", "_parseFloat$toFixed4", "client", "first_name", "last_name", "is_withcar", "car", "marque", "marque_car", "model", "model_car", "start_date", "end_date", "nbr_day", "parseFloat", "price_day", "toFixed", "price_total", "price_avance", "price_rest", "onClick", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/reservation/ReservationScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport {\n  deleteReservation,\n  getListReservations,\n} from \"../../redux/actions/reservationActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\n\nfunction ReservationScreen() {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [reservationId, setReservationId] = useState(\"\");\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listReservation = useSelector((state) => state.reservationList);\n  const { reservations, loading, error, pages } = listReservation;\n\n  const reservationDelete = useSelector((state) => state.deleteReservation);\n  const {\n    loadingReservationDelete,\n    errorReservationDelete,\n    successReservationDelete,\n  } = reservationDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListReservations(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  useEffect(() => {\n    if (successReservationDelete) {\n      dispatch(getListReservations(\"1\"));\n      setReservationId(\"\");\n      setIsDelete(false);\n      setEventType(\"\");\n      setLoadEvent(false);\n    }\n  }, [successReservationDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Réservation</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">\n              Gestion des Réservation\n            </h4>\n            <Link\n              to={\"/reservations/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                      NR\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {reservations?.map((reservation, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {reservation.id}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {reservation.client?.first_name ?? \"---\"}{\" \"}\n                          {reservation.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {reservation.is_withcar\n                            ? (reservation.car?.marque?.marque_car ?? \"---\") +\n                                \" \" +\n                                reservation.car?.model?.model_car ?? \"\"\n                            : (reservation.model_car ?? \"---\") +\n                              \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {reservation.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {reservation.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {reservation.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {parseFloat(reservation.price_day).toFixed(2) ??\n                            \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {parseFloat(reservation.price_total).toFixed(2) ??\n                            \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {parseFloat(reservation.price_avance).toFixed(2) ??\n                            \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max  \">\n                          {parseFloat(reservation.price_rest).toFixed(2) ??\n                            \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                        <p className=\"text-black  text-xs w-max flex flex-row  \">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/reservations/edit/\" + reservation.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setReservationId(reservation.id);\n                              setIsDelete(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/reservations?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isDelete}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer cette réservation ?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"delete\" && reservationId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteReservation(reservationId));\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsDelete(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsDelete(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ReservationScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,OACEC,iBAAiB,CACjBC,mBAAmB,KACd,wCAAwC,CAC/C,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,QAAS,CAAAC,iBAAiBA,CAAA,CAAG,CAC3B,KAAM,CAAAC,QAAQ,CAAGd,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACe,YAAY,CAAC,CAAGd,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAe,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACuB,aAAa,CAAEC,gBAAgB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0B,QAAQ,CAAEC,WAAW,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC8B,SAAS,CAAEC,YAAY,CAAC,CAAG/B,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAAgC,SAAS,CAAG9B,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,eAAe,CAAGjC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACG,eAAe,CAAC,CACrE,KAAM,CAAEC,YAAY,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAGL,eAAe,CAE/D,KAAM,CAAAM,iBAAiB,CAAGvC,WAAW,CAAE+B,KAAK,EAAKA,KAAK,CAACtB,iBAAiB,CAAC,CACzE,KAAM,CACJ+B,wBAAwB,CACxBC,sBAAsB,CACtBC,wBACF,CAAC,CAAGH,iBAAiB,CAErB,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB9C,SAAS,CAAC,IAAM,CACd,GAAI,CAACmC,QAAQ,CAAE,CACbf,QAAQ,CAAC0B,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLtB,QAAQ,CAACX,mBAAmB,CAACS,IAAI,CAAC,CAAC,CACrC,CACF,CAAC,CAAE,CAACF,QAAQ,CAAEe,QAAQ,CAAEX,QAAQ,CAAEF,IAAI,CAAC,CAAC,CAExCtB,SAAS,CAAC,IAAM,CACd,GAAI6C,wBAAwB,CAAE,CAC5BrB,QAAQ,CAACX,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCa,gBAAgB,CAAC,EAAE,CAAC,CACpBE,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACe,wBAAwB,CAAC,CAAC,CAE9B,mBACE7B,IAAA,CAACR,aAAa,EAAAuC,QAAA,cACZ7B,KAAA,QAAA6B,QAAA,eACE7B,KAAA,QAAK8B,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD/B,IAAA,MAAGiC,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB7B,KAAA,QAAK8B,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D/B,IAAA,QACEkC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/B,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNtC,IAAA,SAAMgC,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJ/B,IAAA,SAAA+B,QAAA,cACE/B,IAAA,QACEkC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/B,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPtC,IAAA,QAAKgC,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,EAChC,CAAC,cACN7B,KAAA,QAAK8B,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1H7B,KAAA,QAAK8B,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/D/B,IAAA,OAAIgC,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,4BAErD,CAAI,CAAC,cACL7B,KAAA,CAACd,IAAI,EACHmD,EAAE,CAAE,mBAAoB,CACxBP,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzE/B,IAAA,QACEkC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/B,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsC,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,EACJ,CAAC,CAGLf,OAAO,cACNvB,IAAA,CAACP,MAAM,GAAE,CAAC,CACR+B,KAAK,cACPxB,IAAA,CAACN,KAAK,EAAC8C,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEjB,KAAM,CAAE,CAAC,cAEtCtB,KAAA,QAAK8B,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C7B,KAAA,UAAO8B,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClC/B,IAAA,UAAA+B,QAAA,cACE7B,KAAA,OAAI8B,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClC/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,IAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,QAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,SAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,UAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,KAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,IAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,WAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,SAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,QAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACL/B,IAAA,OAAIgC,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,SAE7D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAER7B,KAAA,UAAA6B,QAAA,EACGT,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEoB,GAAG,CAAC,CAACC,WAAW,CAAEC,EAAE,QAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,IAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,sBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,oBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,oBACjC5D,KAAA,OAAA6B,QAAA,eACE/B,IAAA,OAAIgC,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC5D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CACvCY,WAAW,CAACC,EAAE,CACd,CAAC,CACF,CAAC,cACL5C,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7B,KAAA,MAAG8B,SAAS,CAAC,6BAA6B,CAAAD,QAAA,GAAAc,qBAAA,EAAAC,mBAAA,CACvCH,WAAW,CAACoB,MAAM,UAAAjB,mBAAA,iBAAlBA,mBAAA,CAAoBkB,UAAU,UAAAnB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAE,GAAG,EAAAE,qBAAA,EAAAC,oBAAA,CAC5CL,WAAW,CAACoB,MAAM,UAAAf,oBAAA,iBAAlBA,oBAAA,CAAoBiB,SAAS,UAAAlB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,EACnC,CAAC,CACF,CAAC,cACL/C,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCY,WAAW,CAACuB,UAAU,EAAAjB,IAAA,CACnB,EAAAC,qBAAA,EAAAC,gBAAA,CAACR,WAAW,CAACwB,GAAG,UAAAhB,gBAAA,kBAAAC,sBAAA,CAAfD,gBAAA,CAAiBiB,MAAM,UAAAhB,sBAAA,iBAAvBA,sBAAA,CAAyBiB,UAAU,UAAAnB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAC3C,GAAG,GAAAG,iBAAA,CACHV,WAAW,CAACwB,GAAG,UAAAd,iBAAA,kBAAAC,qBAAA,CAAfD,iBAAA,CAAiBiB,KAAK,UAAAhB,qBAAA,iBAAtBA,qBAAA,CAAwBiB,SAAS,WAAAtB,IAAA,UAAAA,IAAA,CAAI,EAAE,CACzC,EAAAM,qBAAA,CAACZ,WAAW,CAAC4B,SAAS,UAAAhB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAC/B,iBAAiB,CACpB,CAAC,CACF,CAAC,cACLvD,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAAyB,qBAAA,CACvCb,WAAW,CAAC6B,UAAU,UAAAhB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,CACF,CAAC,cACLxD,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA0B,qBAAA,CACvCd,WAAW,CAAC8B,QAAQ,UAAAhB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,CACF,CAAC,cACLzD,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA2B,oBAAA,CACvCf,WAAW,CAAC+B,OAAO,UAAAhB,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CAC5B,CAAC,CACF,CAAC,cACL1D,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA4B,mBAAA,CACvCgB,UAAU,CAAChC,WAAW,CAACiC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,UAAAlB,mBAAA,UAAAA,mBAAA,CAC3C,KAAK,CACN,CAAC,CACF,CAAC,cACL3D,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA6B,oBAAA,CACvCe,UAAU,CAAChC,WAAW,CAACmC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,UAAAjB,oBAAA,UAAAA,oBAAA,CAC7C,KAAK,CACN,CAAC,CACF,CAAC,cACL5D,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA8B,oBAAA,CACvCc,UAAU,CAAChC,WAAW,CAACoC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,UAAAhB,oBAAA,UAAAA,oBAAA,CAC9C,KAAK,CACN,CAAC,CACF,CAAC,cACL7D,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D/B,IAAA,MAAGgC,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAA+B,oBAAA,CACvCa,UAAU,CAAChC,WAAW,CAACqC,UAAU,CAAC,CAACH,OAAO,CAAC,CAAC,CAAC,UAAAf,oBAAA,UAAAA,oBAAA,CAC5C,KAAK,CACN,CAAC,CACF,CAAC,cAEL9D,IAAA,OAAIgC,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7B,KAAA,MAAG8B,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eAEtD/B,IAAA,CAACZ,IAAI,EACH4C,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,qBAAqB,CAAGI,WAAW,CAACC,EAAG,CAAAb,QAAA,cAE3C/B,IAAA,QACEkC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzE/B,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsC,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPtC,IAAA,WACEgC,SAAS,CAAC,mBAAmB,CAC7BiD,OAAO,CAAEA,CAAA,GAAM,CACbjE,YAAY,CAAC,QAAQ,CAAC,CACtBN,gBAAgB,CAACiC,WAAW,CAACC,EAAE,CAAC,CAChChC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAmB,QAAA,cAEF/B,IAAA,QACEkC,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExE/B,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsC,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,EACR,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACFtC,IAAA,OAAIgC,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACRhC,IAAA,QAAKgC,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf/B,IAAA,CAACL,QAAQ,EACPuF,KAAK,CAAE,gBAAiB,CACxBC,MAAM,CAAE,EAAG,CACX7E,IAAI,CAAEA,IAAK,CACXmB,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAENzB,IAAA,CAACF,iBAAiB,EAChBsF,MAAM,CAAEzE,QAAS,CACjB8B,OAAO,CACL1B,SAAS,GAAK,QAAQ,CAClB,wDAAwD,CACxD,4BACL,CACDsE,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAItE,SAAS,GAAK,QAAQ,EAAIN,aAAa,GAAK,EAAE,CAAE,CAClDK,YAAY,CAAC,IAAI,CAAC,CAClBN,QAAQ,CAACZ,iBAAiB,CAACa,aAAa,CAAC,CAAC,CAC1CG,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACFwE,QAAQ,CAAEA,CAAA,GAAM,CACd1E,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFb,IAAA,QAAKgC,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA7B,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}