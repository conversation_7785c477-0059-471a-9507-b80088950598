{"ast": null, "code": "/*! DataTables styling integration\n * © SpryMedia Ltd - datatables.net/license\n */\n\nimport jQuery from 'jquery';\nimport DataTable from 'datatables.net';\n\n// Allow reassignment of the $ variable\nlet $ = jQuery;\nexport default DataTable;", "map": {"version": 3, "names": ["j<PERSON><PERSON><PERSON>", "DataTable", "$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/datatables.net-dt/js/dataTables.dataTables.mjs"], "sourcesContent": ["/*! DataTables styling integration\n * © SpryMedia Ltd - datatables.net/license\n */\n\nimport jQuery from 'jquery';\nimport DataTable from 'datatables.net';\n\n// Allow reassignment of the $ variable\nlet $ = jQuery;\n\n\n\n\nexport default DataTable;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAOA,MAAM,MAAM,QAAQ;AAC3B,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,CAAC,GAAGF,MAAM;AAKd,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}