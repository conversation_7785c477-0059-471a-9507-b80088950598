{"ast": null, "code": "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n}();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : 'Reducers may not dispatch actions.');\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    var listeners = currentListeners = nextListeners;\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n  function observable() {\n    var _ref;\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n}\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n  var shapeAssertionError;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    var hasChanged = false;\n    var nextState = {};\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n  var boundActionCreators = {};\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n      var _dispatch = function dispatch() {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, legacy_createStore };", "map": {"version": 3, "names": ["_objectSpread", "formatProdErrorMessage", "code", "$$observable", "Symbol", "observable", "randomString", "Math", "random", "toString", "substring", "split", "join", "ActionTypes", "INIT", "REPLACE", "PROBE_UNKNOWN_ACTION", "isPlainObject", "obj", "proto", "Object", "getPrototypeOf", "miniKindOf", "val", "type", "Array", "isArray", "isDate", "isError", "constructorName", "ctorName", "slice", "toLowerCase", "replace", "constructor", "name", "Error", "message", "stackTraceLimit", "Date", "toDateString", "getDate", "setDate", "kindOf", "typeOfVal", "process", "env", "NODE_ENV", "createStore", "reducer", "preloadedState", "enhancer", "_ref2", "arguments", "undefined", "currentReducer", "currentState", "currentListeners", "nextListeners", "isDispatching", "ensureCanMutateNextListeners", "getState", "subscribe", "listener", "isSubscribed", "push", "unsubscribe", "index", "indexOf", "splice", "dispatch", "action", "listeners", "i", "length", "replaceReducer", "nextReducer", "_ref", "outerSubscribe", "observer", "observeState", "next", "legacy_createStore", "warning", "console", "error", "e", "getUnexpectedStateShapeWarningMessage", "inputState", "reducers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reducerKeys", "keys", "argumentName", "<PERSON><PERSON><PERSON><PERSON>", "filter", "key", "hasOwnProperty", "for<PERSON>ach", "assertReducerShape", "initialState", "combineReducers", "finalReducers", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shapeAssertionError", "combination", "state", "warningMessage", "has<PERSON><PERSON>ed", "nextState", "_i", "_key", "previousStateForKey", "nextStateForKey", "actionType", "String", "bindActionCreator", "actionCreator", "apply", "bindActionCreators", "actionCreators", "boundActionCreators", "compose", "_len", "funcs", "arg", "reduce", "a", "b", "applyMiddleware", "middlewares", "store", "_dispatch", "middlewareAPI", "chain", "map", "middleware", "__DO_NOT_USE__ActionTypes"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/redux/es/redux.js"], "sourcesContent": ["import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = (function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n})();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\n\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : 'Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, legacy_createStore };\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EACpC,OAAO,wBAAwB,GAAGA,IAAI,GAAG,2CAA2C,GAAGA,IAAI,GAAG,2BAA2B,GAAG,wDAAwD;AACtL;;AAEA;AACA,IAAIC,YAAY,GAAI,YAAY;EAC9B,OAAO,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,UAAU,IAAI,cAAc;AAC5E,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAAA,EAAG;EACzC,OAAOC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACpE,CAAC;AAED,IAAIC,WAAW,GAAG;EAChBC,IAAI,EAAE,cAAc,GAAGR,YAAY,CAAC,CAAC;EACrCS,OAAO,EAAE,iBAAiB,GAAGT,YAAY,CAAC,CAAC;EAC3CU,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;IACpD,OAAO,8BAA8B,GAAGV,YAAY,CAAC,CAAC;EACxD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASW,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;EACzD,IAAIC,KAAK,GAAGD,GAAG;EAEf,OAAOE,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC,KAAK,IAAI,EAAE;IAC5CA,KAAK,GAAGC,MAAM,CAACC,cAAc,CAACF,KAAK,CAAC;EACtC;EAEA,OAAOC,MAAM,CAACC,cAAc,CAACH,GAAG,CAAC,KAAKC,KAAK;AAC7C;;AAEA;AACA,SAASG,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE,OAAO,WAAW;EACtC,IAAIA,GAAG,KAAK,IAAI,EAAE,OAAO,MAAM;EAC/B,IAAIC,IAAI,GAAG,OAAOD,GAAG;EAErB,QAAQC,IAAI;IACV,KAAK,SAAS;IACd,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,QAAQ;IACb,KAAK,UAAU;MACb;QACE,OAAOA,IAAI;MACb;EACJ;EAEA,IAAIC,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,EAAE,OAAO,OAAO;EACtC,IAAII,MAAM,CAACJ,GAAG,CAAC,EAAE,OAAO,MAAM;EAC9B,IAAIK,OAAO,CAACL,GAAG,CAAC,EAAE,OAAO,OAAO;EAChC,IAAIM,eAAe,GAAGC,QAAQ,CAACP,GAAG,CAAC;EAEnC,QAAQM,eAAe;IACrB,KAAK,QAAQ;IACb,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,SAAS;IACd,KAAK,KAAK;IACV,KAAK,KAAK;MACR,OAAOA,eAAe;EAC1B,CAAC,CAAC;;EAGF,OAAOL,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC3D;AAEA,SAASH,QAAQA,CAACP,GAAG,EAAE;EACrB,OAAO,OAAOA,GAAG,CAACW,WAAW,KAAK,UAAU,GAAGX,GAAG,CAACW,WAAW,CAACC,IAAI,GAAG,IAAI;AAC5E;AAEA,SAASP,OAAOA,CAACL,GAAG,EAAE;EACpB,OAAOA,GAAG,YAAYa,KAAK,IAAI,OAAOb,GAAG,CAACc,OAAO,KAAK,QAAQ,IAAId,GAAG,CAACW,WAAW,IAAI,OAAOX,GAAG,CAACW,WAAW,CAACI,eAAe,KAAK,QAAQ;AAC1I;AAEA,SAASX,MAAMA,CAACJ,GAAG,EAAE;EACnB,IAAIA,GAAG,YAAYgB,IAAI,EAAE,OAAO,IAAI;EACpC,OAAO,OAAOhB,GAAG,CAACiB,YAAY,KAAK,UAAU,IAAI,OAAOjB,GAAG,CAACkB,OAAO,KAAK,UAAU,IAAI,OAAOlB,GAAG,CAACmB,OAAO,KAAK,UAAU;AACzH;AAEA,SAASC,MAAMA,CAACpB,GAAG,EAAE;EACnB,IAAIqB,SAAS,GAAG,OAAOrB,GAAG;EAE1B,IAAIsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCH,SAAS,GAAGtB,UAAU,CAACC,GAAG,CAAC;EAC7B;EAEA,OAAOqB,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,WAAWA,CAACC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAE;EACtD,IAAIC,KAAK;EAET,IAAI,OAAOF,cAAc,KAAK,UAAU,IAAI,OAAOC,QAAQ,KAAK,UAAU,IAAI,OAAOA,QAAQ,KAAK,UAAU,IAAI,OAAOE,SAAS,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;IAClJ,MAAM,IAAIjB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,2DAA2D,GAAG,8DAA8D,GAAG,6IAA6I,CAAC;EACnW;EAEA,IAAI,OAAOiD,cAAc,KAAK,UAAU,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IAC3EA,QAAQ,GAAGD,cAAc;IACzBA,cAAc,GAAGI,SAAS;EAC5B;EAEA,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;IACnC,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAIf,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,8DAA8D,GAAG0C,MAAM,CAACQ,QAAQ,CAAC,GAAG,GAAG,CAAC;IAC9K;IAEA,OAAOA,QAAQ,CAACH,WAAW,CAAC,CAACC,OAAO,EAAEC,cAAc,CAAC;EACvD;EAEA,IAAI,OAAOD,OAAO,KAAK,UAAU,EAAE;IACjC,MAAM,IAAIb,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,kEAAkE,GAAG0C,MAAM,CAACM,OAAO,CAAC,GAAG,GAAG,CAAC;EACjL;EAEA,IAAIM,cAAc,GAAGN,OAAO;EAC5B,IAAIO,YAAY,GAAGN,cAAc;EACjC,IAAIO,gBAAgB,GAAG,EAAE;EACzB,IAAIC,aAAa,GAAGD,gBAAgB;EACpC,IAAIE,aAAa,GAAG,KAAK;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;;EAEE,SAASC,4BAA4BA,CAAA,EAAG;IACtC,IAAIF,aAAa,KAAKD,gBAAgB,EAAE;MACtCC,aAAa,GAAGD,gBAAgB,CAAC1B,KAAK,CAAC,CAAC;IAC1C;EACF;EACA;AACF;AACA;AACA;AACA;;EAGE,SAAS8B,QAAQA,CAAA,EAAG;IAClB,IAAIF,aAAa,EAAE;MACjB,MAAM,IAAIvB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,oEAAoE,GAAG,6DAA6D,GAAG,yEAAyE,CAAC;IACvS;IAEA,OAAOuD,YAAY;EACrB;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASM,SAASA,CAACC,QAAQ,EAAE;IAC3B,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;MAClC,MAAM,IAAI3B,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,8DAA8D,GAAG0C,MAAM,CAACoB,QAAQ,CAAC,GAAG,GAAG,CAAC;IAC9K;IAEA,IAAIJ,aAAa,EAAE;MACjB,MAAM,IAAIvB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,qEAAqE,GAAG,sFAAsF,GAAG,oFAAoF,GAAG,wEAAwE,CAAC;IACvZ;IAEA,IAAI+D,YAAY,GAAG,IAAI;IACvBJ,4BAA4B,CAAC,CAAC;IAC9BF,aAAa,CAACO,IAAI,CAACF,QAAQ,CAAC;IAC5B,OAAO,SAASG,WAAWA,CAAA,EAAG;MAC5B,IAAI,CAACF,YAAY,EAAE;QACjB;MACF;MAEA,IAAIL,aAAa,EAAE;QACjB,MAAM,IAAIvB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,gFAAgF,GAAG,wEAAwE,CAAC;MAClP;MAEA+D,YAAY,GAAG,KAAK;MACpBJ,4BAA4B,CAAC,CAAC;MAC9B,IAAIO,KAAK,GAAGT,aAAa,CAACU,OAAO,CAACL,QAAQ,CAAC;MAC3CL,aAAa,CAACW,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC9BV,gBAAgB,GAAG,IAAI;IACzB,CAAC;EACH;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASa,QAAQA,CAACC,MAAM,EAAE;IACxB,IAAI,CAACtD,aAAa,CAACsD,MAAM,CAAC,EAAE;MAC1B,MAAM,IAAInC,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,gEAAgE,GAAG0C,MAAM,CAAC4B,MAAM,CAAC,GAAG,4UAA4U,CAAC;IACvf;IAEA,IAAI,OAAOA,MAAM,CAAC/C,IAAI,KAAK,WAAW,EAAE;MACtC,MAAM,IAAIY,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,4GAA4G,CAAC;IACnM;IAEA,IAAI0D,aAAa,EAAE;MACjB,MAAM,IAAIvB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,CAAC,CAAC,GAAG,oCAAoC,CAAC;IAC3H;IAEA,IAAI;MACF0D,aAAa,GAAG,IAAI;MACpBH,YAAY,GAAGD,cAAc,CAACC,YAAY,EAAEe,MAAM,CAAC;IACrD,CAAC,SAAS;MACRZ,aAAa,GAAG,KAAK;IACvB;IAEA,IAAIa,SAAS,GAAGf,gBAAgB,GAAGC,aAAa;IAEhD,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAIV,QAAQ,GAAGS,SAAS,CAACC,CAAC,CAAC;MAC3BV,QAAQ,CAAC,CAAC;IACZ;IAEA,OAAOQ,MAAM;EACf;EACA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,SAASI,cAAcA,CAACC,WAAW,EAAE;IACnC,IAAI,OAAOA,WAAW,KAAK,UAAU,EAAE;MACrC,MAAM,IAAIxC,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,iEAAiE,GAAG0C,MAAM,CAACiC,WAAW,CAAC,CAAC;IAC/K;IAEArB,cAAc,GAAGqB,WAAW,CAAC,CAAC;IAC9B;IACA;IACA;;IAEAN,QAAQ,CAAC;MACP9C,IAAI,EAAEX,WAAW,CAACE;IACpB,CAAC,CAAC;EACJ;EACA;AACF;AACA;AACA;AACA;AACA;;EAGE,SAASV,UAAUA,CAAA,EAAG;IACpB,IAAIwE,IAAI;IAER,IAAIC,cAAc,GAAGhB,SAAS;IAC9B,OAAOe,IAAI,GAAG;MACZ;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACMf,SAAS,EAAE,SAASA,SAASA,CAACiB,QAAQ,EAAE;QACtC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,EAAE;UACrD,MAAM,IAAI3C,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,6DAA6D,GAAG0C,MAAM,CAACoC,QAAQ,CAAC,GAAG,GAAG,CAAC;QAC9K;QAEA,SAASC,YAAYA,CAAA,EAAG;UACtB,IAAID,QAAQ,CAACE,IAAI,EAAE;YACjBF,QAAQ,CAACE,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC;UAC3B;QACF;QAEAmB,YAAY,CAAC,CAAC;QACd,IAAId,WAAW,GAAGY,cAAc,CAACE,YAAY,CAAC;QAC9C,OAAO;UACLd,WAAW,EAAEA;QACf,CAAC;MACH;IACF,CAAC,EAAEW,IAAI,CAAC1E,YAAY,CAAC,GAAG,YAAY;MAClC,OAAO,IAAI;IACb,CAAC,EAAE0E,IAAI;EACT,CAAC,CAAC;EACF;EACA;;EAGAP,QAAQ,CAAC;IACP9C,IAAI,EAAEX,WAAW,CAACC;EACpB,CAAC,CAAC;EACF,OAAOsC,KAAK,GAAG;IACbkB,QAAQ,EAAEA,QAAQ;IAClBR,SAAS,EAAEA,SAAS;IACpBD,QAAQ,EAAEA,QAAQ;IAClBc,cAAc,EAAEA;EAClB,CAAC,EAAEvB,KAAK,CAACjD,YAAY,CAAC,GAAGE,UAAU,EAAE+C,KAAK;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI8B,kBAAkB,GAAGlC,WAAW;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,OAAOA,CAAC9C,OAAO,EAAE;EACxB;EACA,IAAI,OAAO+C,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;IACzED,OAAO,CAACC,KAAK,CAAChD,OAAO,CAAC;EACxB;EACA;;EAGA,IAAI;IACF;IACA;IACA;IACA,MAAM,IAAID,KAAK,CAACC,OAAO,CAAC;EAC1B,CAAC,CAAC,OAAOiD,CAAC,EAAE,CAAC,CAAC,CAAC;AAEjB;AAEA,SAASC,qCAAqCA,CAACC,UAAU,EAAEC,QAAQ,EAAElB,MAAM,EAAEmB,kBAAkB,EAAE;EAC/F,IAAIC,WAAW,GAAGvE,MAAM,CAACwE,IAAI,CAACH,QAAQ,CAAC;EACvC,IAAII,YAAY,GAAGtB,MAAM,IAAIA,MAAM,CAAC/C,IAAI,KAAKX,WAAW,CAACC,IAAI,GAAG,+CAA+C,GAAG,wCAAwC;EAE1J,IAAI6E,WAAW,CAACjB,MAAM,KAAK,CAAC,EAAE;IAC5B,OAAO,qEAAqE,GAAG,4DAA4D;EAC7I;EAEA,IAAI,CAACzD,aAAa,CAACuE,UAAU,CAAC,EAAE;IAC9B,OAAO,MAAM,GAAGK,YAAY,GAAG,4BAA4B,GAAGlD,MAAM,CAAC6C,UAAU,CAAC,GAAG,2DAA2D,IAAI,UAAU,GAAGG,WAAW,CAAC/E,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;EACjM;EAEA,IAAIkF,cAAc,GAAG1E,MAAM,CAACwE,IAAI,CAACJ,UAAU,CAAC,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAE;IACjE,OAAO,CAACP,QAAQ,CAACQ,cAAc,CAACD,GAAG,CAAC,IAAI,CAACN,kBAAkB,CAACM,GAAG,CAAC;EAClE,CAAC,CAAC;EACFF,cAAc,CAACI,OAAO,CAAC,UAAUF,GAAG,EAAE;IACpCN,kBAAkB,CAACM,GAAG,CAAC,GAAG,IAAI;EAChC,CAAC,CAAC;EACF,IAAIzB,MAAM,IAAIA,MAAM,CAAC/C,IAAI,KAAKX,WAAW,CAACE,OAAO,EAAE;EAEnD,IAAI+E,cAAc,CAACpB,MAAM,GAAG,CAAC,EAAE;IAC7B,OAAO,aAAa,IAAIoB,cAAc,CAACpB,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI,GAAGoB,cAAc,CAAClF,IAAI,CAAC,MAAM,CAAC,GAAG,cAAc,GAAGiF,YAAY,GAAG,IAAI,CAAC,GAAG,0DAA0D,IAAI,IAAI,GAAGF,WAAW,CAAC/E,IAAI,CAAC,MAAM,CAAC,GAAG,sCAAsC,CAAC;EACnS;AACF;AAEA,SAASuF,kBAAkBA,CAACV,QAAQ,EAAE;EACpCrE,MAAM,CAACwE,IAAI,CAACH,QAAQ,CAAC,CAACS,OAAO,CAAC,UAAUF,GAAG,EAAE;IAC3C,IAAI/C,OAAO,GAAGwC,QAAQ,CAACO,GAAG,CAAC;IAC3B,IAAII,YAAY,GAAGnD,OAAO,CAACK,SAAS,EAAE;MACpC9B,IAAI,EAAEX,WAAW,CAACC;IACpB,CAAC,CAAC;IAEF,IAAI,OAAOsF,YAAY,KAAK,WAAW,EAAE;MACvC,MAAM,IAAIhE,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,8BAA8B,GAAG+F,GAAG,GAAG,+CAA+C,GAAG,4DAA4D,GAAG,6DAA6D,GAAG,uEAAuE,GAAG,wCAAwC,CAAC;IACla;IAEA,IAAI,OAAO/C,OAAO,CAACK,SAAS,EAAE;MAC5B9B,IAAI,EAAEX,WAAW,CAACG,oBAAoB,CAAC;IACzC,CAAC,CAAC,KAAK,WAAW,EAAE;MAClB,MAAM,IAAIoB,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,8BAA8B,GAAG+F,GAAG,GAAG,wDAAwD,IAAI,uBAAuB,GAAGnF,WAAW,CAACC,IAAI,GAAG,oCAAoC,CAAC,GAAG,uEAAuE,GAAG,iEAAiE,GAAG,qEAAqE,GAAG,uEAAuE,CAAC;IAC7iB;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASuF,eAAeA,CAACZ,QAAQ,EAAE;EACjC,IAAIE,WAAW,GAAGvE,MAAM,CAACwE,IAAI,CAACH,QAAQ,CAAC;EACvC,IAAIa,aAAa,GAAG,CAAC,CAAC;EAEtB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkB,WAAW,CAACjB,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,IAAIuB,GAAG,GAAGL,WAAW,CAAClB,CAAC,CAAC;IAExB,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,OAAO0C,QAAQ,CAACO,GAAG,CAAC,KAAK,WAAW,EAAE;QACxCb,OAAO,CAAC,gCAAgC,GAAGa,GAAG,GAAG,IAAI,CAAC;MACxD;IACF;IAEA,IAAI,OAAOP,QAAQ,CAACO,GAAG,CAAC,KAAK,UAAU,EAAE;MACvCM,aAAa,CAACN,GAAG,CAAC,GAAGP,QAAQ,CAACO,GAAG,CAAC;IACpC;EACF;EAEA,IAAIO,gBAAgB,GAAGnF,MAAM,CAACwE,IAAI,CAACU,aAAa,CAAC,CAAC,CAAC;EACnD;;EAEA,IAAIZ,kBAAkB;EAEtB,IAAI7C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC2C,kBAAkB,GAAG,CAAC,CAAC;EACzB;EAEA,IAAIc,mBAAmB;EAEvB,IAAI;IACFL,kBAAkB,CAACG,aAAa,CAAC;EACnC,CAAC,CAAC,OAAOhB,CAAC,EAAE;IACVkB,mBAAmB,GAAGlB,CAAC;EACzB;EAEA,OAAO,SAASmB,WAAWA,CAACC,KAAK,EAAEnC,MAAM,EAAE;IACzC,IAAImC,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,CAAC,CAAC;IACZ;IAEA,IAAIF,mBAAmB,EAAE;MACvB,MAAMA,mBAAmB;IAC3B;IAEA,IAAI3D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI4D,cAAc,GAAGpB,qCAAqC,CAACmB,KAAK,EAAEJ,aAAa,EAAE/B,MAAM,EAAEmB,kBAAkB,CAAC;MAE5G,IAAIiB,cAAc,EAAE;QAClBxB,OAAO,CAACwB,cAAc,CAAC;MACzB;IACF;IAEA,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAElB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,gBAAgB,CAAC7B,MAAM,EAAEoC,EAAE,EAAE,EAAE;MACnD,IAAIC,IAAI,GAAGR,gBAAgB,CAACO,EAAE,CAAC;MAC/B,IAAI7D,OAAO,GAAGqD,aAAa,CAACS,IAAI,CAAC;MACjC,IAAIC,mBAAmB,GAAGN,KAAK,CAACK,IAAI,CAAC;MACrC,IAAIE,eAAe,GAAGhE,OAAO,CAAC+D,mBAAmB,EAAEzC,MAAM,CAAC;MAE1D,IAAI,OAAO0C,eAAe,KAAK,WAAW,EAAE;QAC1C,IAAIC,UAAU,GAAG3C,MAAM,IAAIA,MAAM,CAAC/C,IAAI;QACtC,MAAM,IAAIY,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,qCAAqC,IAAIiH,UAAU,GAAG,IAAI,GAAGC,MAAM,CAACD,UAAU,CAAC,GAAG,IAAI,GAAG,gBAAgB,CAAC,GAAG,gCAAgC,GAAGH,IAAI,GAAG,yBAAyB,GAAG,sEAAsE,GAAG,sFAAsF,CAAC;MAC1a;MAEAF,SAAS,CAACE,IAAI,CAAC,GAAGE,eAAe;MACjCL,UAAU,GAAGA,UAAU,IAAIK,eAAe,KAAKD,mBAAmB;IACpE;IAEAJ,UAAU,GAAGA,UAAU,IAAIL,gBAAgB,CAAC7B,MAAM,KAAKtD,MAAM,CAACwE,IAAI,CAACc,KAAK,CAAC,CAAChC,MAAM;IAChF,OAAOkC,UAAU,GAAGC,SAAS,GAAGH,KAAK;EACvC,CAAC;AACH;AAEA,SAASU,iBAAiBA,CAACC,aAAa,EAAE/C,QAAQ,EAAE;EAClD,OAAO,YAAY;IACjB,OAAOA,QAAQ,CAAC+C,aAAa,CAACC,KAAK,CAAC,IAAI,EAAEjE,SAAS,CAAC,CAAC;EACvD,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASkE,kBAAkBA,CAACC,cAAc,EAAElD,QAAQ,EAAE;EACpD,IAAI,OAAOkD,cAAc,KAAK,UAAU,EAAE;IACxC,OAAOJ,iBAAiB,CAACI,cAAc,EAAElD,QAAQ,CAAC;EACpD;EAEA,IAAI,OAAOkD,cAAc,KAAK,QAAQ,IAAIA,cAAc,KAAK,IAAI,EAAE;IACjE,MAAM,IAAIpF,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,8EAA8E,GAAG0C,MAAM,CAAC6E,cAAc,CAAC,GAAG,KAAK,GAAG,8FAA8F,CAAC;EACxS;EAEA,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAE5B,KAAK,IAAIzB,GAAG,IAAIwB,cAAc,EAAE;IAC9B,IAAIH,aAAa,GAAGG,cAAc,CAACxB,GAAG,CAAC;IAEvC,IAAI,OAAOqB,aAAa,KAAK,UAAU,EAAE;MACvCI,mBAAmB,CAACzB,GAAG,CAAC,GAAGoB,iBAAiB,CAACC,aAAa,EAAE/C,QAAQ,CAAC;IACvE;EACF;EAEA,OAAOmD,mBAAmB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAA,EAAG;EACjB,KAAK,IAAIC,IAAI,GAAGtE,SAAS,CAACqB,MAAM,EAAEkD,KAAK,GAAG,IAAInG,KAAK,CAACkG,IAAI,CAAC,EAAEZ,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGY,IAAI,EAAEZ,IAAI,EAAE,EAAE;IACxFa,KAAK,CAACb,IAAI,CAAC,GAAG1D,SAAS,CAAC0D,IAAI,CAAC;EAC/B;EAEA,IAAIa,KAAK,CAAClD,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,UAAUmD,GAAG,EAAE;MACpB,OAAOA,GAAG;IACZ,CAAC;EACH;EAEA,IAAID,KAAK,CAAClD,MAAM,KAAK,CAAC,EAAE;IACtB,OAAOkD,KAAK,CAAC,CAAC,CAAC;EACjB;EAEA,OAAOA,KAAK,CAACE,MAAM,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IAClC,OAAO,YAAY;MACjB,OAAOD,CAAC,CAACC,CAAC,CAACV,KAAK,CAAC,KAAK,CAAC,EAAEjE,SAAS,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAAS4E,eAAeA,CAAA,EAAG;EACzB,KAAK,IAAIN,IAAI,GAAGtE,SAAS,CAACqB,MAAM,EAAEwD,WAAW,GAAG,IAAIzG,KAAK,CAACkG,IAAI,CAAC,EAAEZ,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGY,IAAI,EAAEZ,IAAI,EAAE,EAAE;IAC9FmB,WAAW,CAACnB,IAAI,CAAC,GAAG1D,SAAS,CAAC0D,IAAI,CAAC;EACrC;EAEA,OAAO,UAAU/D,WAAW,EAAE;IAC5B,OAAO,YAAY;MACjB,IAAImF,KAAK,GAAGnF,WAAW,CAACsE,KAAK,CAAC,KAAK,CAAC,EAAEjE,SAAS,CAAC;MAEhD,IAAI+E,SAAS,GAAG,SAAS9D,QAAQA,CAAA,EAAG;QAClC,MAAM,IAAIlC,KAAK,CAACS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG9C,sBAAsB,CAAC,EAAE,CAAC,GAAG,iEAAiE,GAAG,yDAAyD,CAAC;MACrN,CAAC;MAED,IAAIoI,aAAa,GAAG;QAClBxE,QAAQ,EAAEsE,KAAK,CAACtE,QAAQ;QACxBS,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;UAC5B,OAAO8D,SAAS,CAACd,KAAK,CAAC,KAAK,CAAC,EAAEjE,SAAS,CAAC;QAC3C;MACF,CAAC;MACD,IAAIiF,KAAK,GAAGJ,WAAW,CAACK,GAAG,CAAC,UAAUC,UAAU,EAAE;QAChD,OAAOA,UAAU,CAACH,aAAa,CAAC;MAClC,CAAC,CAAC;MACFD,SAAS,GAAGV,OAAO,CAACJ,KAAK,CAAC,KAAK,CAAC,EAAEgB,KAAK,CAAC,CAACH,KAAK,CAAC7D,QAAQ,CAAC;MACxD,OAAOtE,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmI,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjD7D,QAAQ,EAAE8D;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;AACH;AAEA,SAASvH,WAAW,IAAI4H,yBAAyB,EAAER,eAAe,EAAEV,kBAAkB,EAAElB,eAAe,EAAEqB,OAAO,EAAE1E,WAAW,EAAEkC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}