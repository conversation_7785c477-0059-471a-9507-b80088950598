{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js\",\n  _s = $RefreshSig$();\nimport { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction RaportScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const listCar = useSelector(state => state.carList);\n  const {\n    cars\n  } = listCar;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                \"stroke-linecap\": \"round\",\n                \"stroke-linejoin\": \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Accueil\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              \"stroke-linecap\": \"round\",\n              \"stroke-linejoin\": \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Rapport\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black dark:text-white\",\n            children: \"Gestion du Rapport\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"B\\xE9n\\xE9fice net\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateNet,\n                  onChange: v => setStartDateNet(v.target.value),\n                  error: startDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateNet === \"\",\n                  value: endDateNet,\n                  onChange: v => setEndDateNet(v.target.value),\n                  error: endDateNetError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateNetError(\"\");\n                    setEndDateNetError(\"\");\n                    var check = true;\n                    if (startDateNet === \"\") {\n                      check = false;\n                      setStartDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (endDateNet === \"\") {\n                      check = false;\n                      setEndDateNetError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateNet);\n                      const endDate = new Date(endDateNet);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"R\\xE9glement\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  max: today,\n                  value: startDateReg,\n                  onChange: v => setStartDateReg(v.target.value),\n                  error: startDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  placeholder: \"\",\n                  isMax: true,\n                  disabled: startDateReg === \"\",\n                  max: today,\n                  value: endDateReg,\n                  onChange: v => setEndDateReg(v.target.value),\n                  error: endDateRegError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateRegError(\"\");\n                    setEndDateRegError(\"\");\n                    var check = true;\n                    if (startDateReg === \"\") {\n                      check = false;\n                      setStartDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (endDateReg === \"\") {\n                      check = false;\n                      setEndDateRegError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateReg);\n                      const endDate = new Date(endDateReg);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col \",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/2 w-full px-1 py-1\",\n            children: /*#__PURE__*/_jsxDEV(LayoutSection, {\n              title: \"Contrats impay\\xE9es\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 md:flex\",\n                children: [/*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date d\\xE9but\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  placeholder: \"\",\n                  value: startDateImp,\n                  onChange: v => setStartDateImp(v.target.value),\n                  error: startDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(InputModel, {\n                  label: \"Date fin\",\n                  type: \"date\",\n                  isMax: true,\n                  max: today,\n                  disabled: startDateImp === \"\",\n                  placeholder: \"\",\n                  value: endDateImp,\n                  onChange: v => setEndDateImp(v.target.value),\n                  error: endDateImpError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"md:py-2 flex justify-end items-center \",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setStartDateImpError(\"\");\n                    setEndDateImpError(\"\");\n                    var check = true;\n                    if (startDateImp === \"\") {\n                      check = false;\n                      setStartDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (endDateImp === \"\") {\n                      check = false;\n                      setEndDateImpError(\"Ce champ est requis.\");\n                    }\n                    if (check) {\n                      const startDate = new Date(startDateImp);\n                      const endDate = new Date(endDateImp);\n\n                      // Check if start date is after end date\n                      if (startDate > endDate) {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`, \"_blank\");\n                      } else {\n                        window.open(baseURL + `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`, \"_blank\");\n                      }\n                    }\n                  },\n                  className: \"bg-danger  text-white px-5 py-1.5 text-center  rounded\",\n                  children: \"Afficher\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n}\n_s(RaportScreen, \"cUrUh9kqt6VtUBgeP0q2SRsEEwI=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = RaportScreen;\nexport default RaportScreen;\nvar _c;\n$RefreshReg$(_c, \"RaportScreen\");", "map": {"version": 3, "names": ["toast", "useDispatch", "useSelector", "useLocation", "useNavigate", "addNewClient", "clientList", "LayoutSection", "getListAgences", "getMarqueList", "getModelList", "InputModel", "addNewCar", "getListCars", "ConfirmationModal", "addNewReservation", "useEffect", "useState", "DefaultLayout", "baseURL", "baseURLFile", "jsxDEV", "_jsxDEV", "RaportScreen", "_s", "navigate", "location", "dispatch", "today", "Date", "toISOString", "split", "startDateNet", "setStartDateNet", "startDateNetError", "setStartDateNetError", "endDateNet", "setEndDateNet", "endDateNetError", "setEndDateNetError", "startDateReg", "setStartDateReg", "startDateRegError", "setStartDateRegError", "endDateReg", "setEndDateReg", "endDateRegError", "setEndDateRegError", "startDateImp", "setStartDateImp", "startDateImpError", "setStartDateImpError", "endDateImp", "setEndDateImp", "endDateImpError", "setEndDateImpError", "userLogin", "state", "userInfo", "loading", "error", "listCar", "carList", "cars", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "label", "type", "placeholder", "isMax", "max", "value", "onChange", "v", "target", "disabled", "onClick", "check", "startDate", "endDate", "window", "open", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/raport/RaportScreen.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport { addNewClient, clientList } from \"../../redux/actions/clientActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\nimport { getListAgences } from \"../../redux/actions/agenceActions\";\nimport { getMarqueList } from \"../../redux/actions/marqueActions\";\nimport { getModelList } from \"../../redux/actions/modelActions\";\nimport InputModel from \"../../components/InputModel\";\nimport { addNewCar, getListCars } from \"../../redux/actions/carActions\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { addNewReservation } from \"../../redux/actions/reservationActions\";\nimport { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { baseURL, baseURLFile } from \"../../constants\";\n\nfunction RaportScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const today = new Date().toISOString().split(\"T\")[0];\n  //\n  const [startDateNet, setStartDateNet] = useState(\"\");\n  const [startDateNetError, setStartDateNetError] = useState(\"\");\n  const [endDateNet, setEndDateNet] = useState(\"\");\n  const [endDateNetError, setEndDateNetError] = useState(\"\");\n\n  const [startDateReg, setStartDateReg] = useState(\"\");\n  const [startDateRegError, setStartDateRegError] = useState(\"\");\n  const [endDateReg, setEndDateReg] = useState(\"\");\n  const [endDateRegError, setEndDateRegError] = useState(\"\");\n\n  const [startDateImp, setStartDateImp] = useState(\"\");\n  const [startDateImpError, setStartDateImpError] = useState(\"\");\n  const [endDateImp, setEndDateImp] = useState(\"\");\n  const [endDateImpError, setEndDateImpError] = useState(\"\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const listCar = useSelector((state) => state.carList);\n  const { cars } = listCar;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCars(\"0\"));\n    }\n  }, [navigate, userInfo]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Rapport</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n              Gestion du Rapport\n            </h4>\n          </div>\n          {/*  */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Bénéfice net\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateNet}\n                    onChange={(v) => setStartDateNet(v.target.value)}\n                    error={startDateNetError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateNet === \"\"}\n                    value={endDateNet}\n                    onChange={(v) => setEndDateNet(v.target.value)}\n                    error={endDateNetError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateNetError(\"\");\n                      setEndDateNetError(\"\");\n                      var check = true;\n                      if (startDateNet === \"\") {\n                        check = false;\n                        setStartDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (endDateNet === \"\") {\n                        check = false;\n                        setEndDateNetError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateNet);\n                        const endDate = new Date(endDateNet);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${endDateNet}&end_date=${startDateNet}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-net/?start_date=${startDateNet}&end_date=${endDateNet}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Réglement\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    max={today}\n                    value={startDateReg}\n                    onChange={(v) => setStartDateReg(v.target.value)}\n                    error={startDateRegError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    placeholder=\"\"\n                    isMax={true}\n                    disabled={startDateReg === \"\"}\n                    max={today}\n                    value={endDateReg}\n                    onChange={(v) => setEndDateReg(v.target.value)}\n                    error={endDateRegError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateRegError(\"\");\n                      setEndDateRegError(\"\");\n                      var check = true;\n                      if (startDateReg === \"\") {\n                        check = false;\n                        setStartDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (endDateReg === \"\") {\n                        check = false;\n                        setEndDateRegError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateReg);\n                        const endDate = new Date(endDateReg);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${endDateReg}&end_date=${startDateReg}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-reglement/?start_date=${startDateReg}&end_date=${endDateReg}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-primary bg-opacity-60  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n          <div className=\"flex md:flex-row flex-col \">\n            {/*  */}\n            <div className=\"md:w-1/2 w-full px-1 py-1\">\n              <LayoutSection title=\"Contrats impayées\">\n                <div className=\"md:py-2 md:flex\">\n                  <InputModel\n                    label=\"Date début\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    placeholder=\"\"\n                    value={startDateImp}\n                    onChange={(v) => setStartDateImp(v.target.value)}\n                    error={startDateImpError}\n                  />\n                  <InputModel\n                    label=\"Date fin\"\n                    type=\"date\"\n                    isMax={true}\n                    max={today}\n                    disabled={startDateImp === \"\"}\n                    placeholder=\"\"\n                    value={endDateImp}\n                    onChange={(v) => setEndDateImp(v.target.value)}\n                    error={endDateImpError}\n                  />\n                </div>\n                <div className=\"md:py-2 flex justify-end items-center \">\n                  <button\n                    onClick={() => {\n                      setStartDateImpError(\"\");\n                      setEndDateImpError(\"\");\n                      var check = true;\n                      if (startDateImp === \"\") {\n                        check = false;\n                        setStartDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (endDateImp === \"\") {\n                        check = false;\n                        setEndDateImpError(\"Ce champ est requis.\");\n                      }\n                      if (check) {\n                        const startDate = new Date(startDateImp);\n                        const endDate = new Date(endDateImp);\n\n                        // Check if start date is after end date\n                        if (startDate > endDate) {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${endDateImp}&end_date=${startDateImp}`,\n                            \"_blank\"\n                          );\n                        } else {\n                          window.open(\n                            baseURL +\n                              `/contrats/rapport-impayes/?start_date=${startDateImp}&end_date=${endDateImp}`,\n                            \"_blank\"\n                          );\n                        }\n                      }\n                    }}\n                    className=\"bg-danger  text-white px-5 py-1.5 text-center  rounded\"\n                  >\n                    Afficher\n                  </button>\n                </div>\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default RaportScreen;\n"], "mappings": ";;AAAA,SAASA,KAAK,QAAQ,gBAAgB;AACtC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,YAAY,EAAEC,UAAU,QAAQ,mCAAmC;AAC5E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,SAASC,cAAc,QAAQ,mCAAmC;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AACjE,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,UAAU,MAAM,6BAA6B;AACpD,SAASC,SAAS,EAAEC,WAAW,QAAQ,gCAAgC;AACvE,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,iBAAiB,QAAQ,wCAAwC;AAC1E,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,OAAO,EAAEC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAE9B,MAAM2B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACpD;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmB,UAAU,EAAEC,aAAa,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,eAAe,EAAEC,kBAAkB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAMuC,SAAS,GAAGtD,WAAW,CAAEuD,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,OAAO,GAAG3D,WAAW,CAAEuD,KAAK,IAAKA,KAAK,CAACK,OAAO,CAAC;EACrD,MAAM;IAAEC;EAAK,CAAC,GAAGF,OAAO;EAExB,MAAMG,QAAQ,GAAG,GAAG;EACpBhD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0C,QAAQ,EAAE;MACbjC,QAAQ,CAACuC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLrC,QAAQ,CAACd,WAAW,CAAC,GAAG,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACY,QAAQ,EAAEiC,QAAQ,CAAC,CAAC;EAExB,oBACEpC,OAAA,CAACJ,aAAa;IAAA+C,QAAA,eACZ3C,OAAA;MAAA2C,QAAA,gBAEE3C,OAAA;QAAK4C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD3C,OAAA;UAAG6C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB3C,OAAA;YAAK4C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D3C,OAAA;cACE8C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB3C,OAAA;gBACE,kBAAe,OAAO;gBACtB,mBAAgB,OAAO;gBACvBkD,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtD,OAAA;cAAM4C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAO;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJtD,OAAA;UAAA2C,QAAA,eACE3C,OAAA;YACE8C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB3C,OAAA;cACE,kBAAe,OAAO;cACtB,mBAAgB,OAAO;cACvBkD,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPtD,OAAA;UAAK4C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENtD,OAAA;QAAK4C,SAAS,EAAC,mIAAmI;QAAAD,QAAA,gBAChJ3C,OAAA;UAAK4C,SAAS,EAAC,kDAAkD;UAAAD,QAAA,eAC/D3C,OAAA;YAAI4C,SAAS,EAAC,qDAAqD;YAAAD,QAAA,EAAC;UAEpE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENtD,OAAA;UAAK4C,SAAS,EAAC,4BAA4B;UAAAD,QAAA,gBACzC3C,OAAA;YAAK4C,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC3C,OAAA,CAACf,aAAa;cAACsE,KAAK,EAAC,oBAAc;cAAAZ,QAAA,gBACjC3C,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B3C,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAEtD,KAAM;kBACXuD,KAAK,EAAEnD,YAAa;kBACpBoD,QAAQ,EAAGC,CAAC,IAAKpD,eAAe,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAE1B;gBAAkB;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFtD,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAEtD,KAAM;kBACX2D,QAAQ,EAAEvD,YAAY,KAAK,EAAG;kBAC9BmD,KAAK,EAAE/C,UAAW;kBAClBgD,QAAQ,EAAGC,CAAC,IAAKhD,aAAa,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAEtB;gBAAgB;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtD,OAAA;gBAAK4C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD3C,OAAA;kBACEkE,OAAO,EAAEA,CAAA,KAAM;oBACbrD,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIkD,KAAK,GAAG,IAAI;oBAChB,IAAIzD,YAAY,KAAK,EAAE,EAAE;sBACvByD,KAAK,GAAG,KAAK;sBACbtD,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBqD,KAAK,GAAG,KAAK;sBACblD,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIkD,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7D,IAAI,CAACG,YAAY,CAAC;sBACxC,MAAM2D,OAAO,GAAG,IAAI9D,IAAI,CAACO,UAAU,CAAC;;sBAEpC;sBACA,IAAIsD,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,qCAAoCiB,UAAW,aAAYJ,YAAa,EAAC,EAC5E,QACF,CAAC;sBACH,CAAC,MAAM;wBACL4D,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,qCAAoCa,YAAa,aAAYI,UAAW,EAAC,EAC5E,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACF8B,SAAS,EAAC,yDAAyD;kBAAAD,QAAA,EACpE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eAENtD,OAAA;YAAK4C,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC3C,OAAA,CAACf,aAAa;cAACsE,KAAK,EAAC,cAAW;cAAAZ,QAAA,gBAC9B3C,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B3C,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAEtD,KAAM;kBACXuD,KAAK,EAAE3C,YAAa;kBACpB4C,QAAQ,EAAGC,CAAC,IAAK5C,eAAe,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAElB;gBAAkB;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFtD,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,EAAE;kBACdC,KAAK,EAAE,IAAK;kBACZM,QAAQ,EAAE/C,YAAY,KAAK,EAAG;kBAC9B0C,GAAG,EAAEtD,KAAM;kBACXuD,KAAK,EAAEvC,UAAW;kBAClBwC,QAAQ,EAAGC,CAAC,IAAKxC,aAAa,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAEd;gBAAgB;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtD,OAAA;gBAAK4C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD3C,OAAA;kBACEkE,OAAO,EAAEA,CAAA,KAAM;oBACb7C,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAI0C,KAAK,GAAG,IAAI;oBAChB,IAAIjD,YAAY,KAAK,EAAE,EAAE;sBACvBiD,KAAK,GAAG,KAAK;sBACb9C,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrB6C,KAAK,GAAG,KAAK;sBACb1C,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAI0C,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7D,IAAI,CAACW,YAAY,CAAC;sBACxC,MAAMmD,OAAO,GAAG,IAAI9D,IAAI,CAACe,UAAU,CAAC;;sBAEpC;sBACA,IAAI8C,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,2CAA0CyB,UAAW,aAAYJ,YAAa,EAAC,EAClF,QACF,CAAC;sBACH,CAAC,MAAM;wBACLoD,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,2CAA0CqB,YAAa,aAAYI,UAAW,EAAC,EAClF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFsB,SAAS,EAAC,uEAAuE;kBAAAD,QAAA,EAClF;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNtD,OAAA;UAAK4C,SAAS,EAAC,4BAA4B;UAAAD,QAAA,eAEzC3C,OAAA;YAAK4C,SAAS,EAAC,2BAA2B;YAAAD,QAAA,eACxC3C,OAAA,CAACf,aAAa;cAACsE,KAAK,EAAC,sBAAmB;cAAAZ,QAAA,gBACtC3C,OAAA;gBAAK4C,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAC9B3C,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,eAAY;kBAClBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAEtD,KAAM;kBACXoD,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAEnC,YAAa;kBACpBoC,QAAQ,EAAGC,CAAC,IAAKpC,eAAe,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDvB,KAAK,EAAEV;gBAAkB;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFtD,OAAA,CAACX,UAAU;kBACTmE,KAAK,EAAC,UAAU;kBAChBC,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAE,IAAK;kBACZC,GAAG,EAAEtD,KAAM;kBACX2D,QAAQ,EAAEvC,YAAY,KAAK,EAAG;kBAC9BgC,WAAW,EAAC,EAAE;kBACdG,KAAK,EAAE/B,UAAW;kBAClBgC,QAAQ,EAAGC,CAAC,IAAKhC,aAAa,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC/CvB,KAAK,EAAEN;gBAAgB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNtD,OAAA;gBAAK4C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,eACrD3C,OAAA;kBACEkE,OAAO,EAAEA,CAAA,KAAM;oBACbrC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBACtB,IAAIkC,KAAK,GAAG,IAAI;oBAChB,IAAIzC,YAAY,KAAK,EAAE,EAAE;sBACvByC,KAAK,GAAG,KAAK;sBACbtC,oBAAoB,CAAC,sBAAsB,CAAC;oBAC9C;oBACA,IAAIC,UAAU,KAAK,EAAE,EAAE;sBACrBqC,KAAK,GAAG,KAAK;sBACblC,kBAAkB,CAAC,sBAAsB,CAAC;oBAC5C;oBACA,IAAIkC,KAAK,EAAE;sBACT,MAAMC,SAAS,GAAG,IAAI7D,IAAI,CAACmB,YAAY,CAAC;sBACxC,MAAM2C,OAAO,GAAG,IAAI9D,IAAI,CAACuB,UAAU,CAAC;;sBAEpC;sBACA,IAAIsC,SAAS,GAAGC,OAAO,EAAE;wBACvBC,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,yCAAwCiC,UAAW,aAAYJ,YAAa,EAAC,EAChF,QACF,CAAC;sBACH,CAAC,MAAM;wBACL4C,MAAM,CAACC,IAAI,CACT1E,OAAO,GACJ,yCAAwC6B,YAAa,aAAYI,UAAW,EAAC,EAChF,QACF,CAAC;sBACH;oBACF;kBACF,CAAE;kBACFc,SAAS,EAAC,wDAAwD;kBAAAD,QAAA,EACnE;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACpD,EAAA,CAzSQD,YAAY;EAAA,QACFnB,WAAW,EACXD,WAAW,EACXF,WAAW,EAoBVC,WAAW,EAGbA,WAAW;AAAA;AAAA4F,EAAA,GA1BpBvE,YAAY;AA2SrB,eAAeA,YAAY;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}