{"ast": null, "code": "export const USER_LOGIN_REQUEST = \"USER_LOGIN_REQUEST\";\nexport const USER_LOGIN_SUCCESS = \"USER_LOGIN_SUCCESS\";\nexport const USER_LOGIN_FAIL = \"USER_LOGIN_FAIL\";\nexport const USER_LOGOUT = \"USER_LOGOUT\";\nexport const USER_ADD_REQUEST = \"USER_ADD_REQUEST\";\nexport const USER_ADD_SUCCESS = \"USER_ADD_SUCCESS\";\nexport const USER_ADD_FAIL = \"USER_ADD_FAIL\";\nexport const USER_DELETE_REQUEST = \"USER_DELETE_REQUEST\";\nexport const USER_DELETE_SUCCESS = \"USER_DELETE_SUCCESS\";\nexport const USER_DELETE_FAIL = \"USER_DELETE_FAIL\";\nexport const USER_LIST_REQUEST = \"USER_LIST_REQUEST\";\nexport const USER_LIST_SUCCESS = \"USER_LIST_SUCCESS\";\nexport const USER_LIST_FAIL = \"USER_LIST_FAIL\";\nexport const USER_PROFILE_REQUEST = \"USER_PROFILE_REQUEST\";\nexport const USER_PROFILE_SUCCESS = \"USER_PROFILE_SUCCESS\";\nexport const USER_PROFILE_FAIL = \"USER_PROFILE_FAIL\";\nexport const USER_PROFILE_UPDATE_REQUEST = \"USER_PROFILE_UPDATE_REQUEST\";\nexport const USER_PROFILE_UPDATE_SUCCESS = \"USER_PROFILE_UPDATE_SUCCESS\";\nexport const USER_PROFILE_UPDATE_FAIL = \"USER_PROFILE_UPDATE_FAIL\";\nexport const USER_PASSWORD_UPDATE_REQUEST = \"USER_PASSWORD_UPDATE_REQUEST\";\nexport const USER_PASSWORD_UPDATE_SUCCESS = \"USER_PASSWORD_UPDATE_SUCCESS\";\nexport const USER_PASSWORD_UPDATE_FAIL = \"USER_PASSWORD_UPDATE_FAIL\";\nexport const COORDINATOR_LIST_REQUEST = \"COORDINATOR_LIST_REQUEST\";\nexport const COORDINATOR_LIST_SUCCESS = \"COORDINATOR_LIST_SUCCESS\";\nexport const COORDINATOR_LIST_FAIL = \"COORDINATOR_LIST_FAIL\";\nexport const COORDINATOR_ADD_REQUEST = \"COORDINATOR_ADD_REQUEST\";\nexport const COORDINATOR_ADD_SUCCESS = \"COORDINATOR_ADD_SUCCESS\";\nexport const COORDINATOR_ADD_FAIL = \"COORDINATOR_ADD_FAIL\";\nexport const COORDINATOR_DETAIL_REQUEST = \"COORDINATOR_DETAIL_REQUEST\";\nexport const COORDINATOR_DETAIL_SUCCESS = \"COORDINATOR_DETAIL_SUCCESS\";\nexport const COORDINATOR_DETAIL_FAIL = \"COORDINATOR_DETAIL_FAIL\";\nexport const COORDINATOR_UPDATE_REQUEST = \"COORDINATOR_UPDATE_REQUEST\";\nexport const COORDINATOR_UPDATE_SUCCESS = \"COORDINATOR_UPDATE_SUCCESS\";\nexport const COORDINATOR_UPDATE_FAIL = \"COORDINATOR_UPDATE_FAIL\";\nexport const USER_UPDATE_LOGIN_REQUEST = \"USER_UPDATE_LOGIN_REQUEST\";\nexport const USER_UPDATE_LOGIN_SUCCESS = \"USER_UPDATE_LOGIN_SUCCESS\";\nexport const USER_UPDATE_LOGIN_FAIL = \"USER_ADD_FAIL\";", "map": {"version": 3, "names": ["USER_LOGIN_REQUEST", "USER_LOGIN_SUCCESS", "USER_LOGIN_FAIL", "USER_LOGOUT", "USER_ADD_REQUEST", "USER_ADD_SUCCESS", "USER_ADD_FAIL", "USER_DELETE_REQUEST", "USER_DELETE_SUCCESS", "USER_DELETE_FAIL", "USER_LIST_REQUEST", "USER_LIST_SUCCESS", "USER_LIST_FAIL", "USER_PROFILE_REQUEST", "USER_PROFILE_SUCCESS", "USER_PROFILE_FAIL", "USER_PROFILE_UPDATE_REQUEST", "USER_PROFILE_UPDATE_SUCCESS", "USER_PROFILE_UPDATE_FAIL", "USER_PASSWORD_UPDATE_REQUEST", "USER_PASSWORD_UPDATE_SUCCESS", "USER_PASSWORD_UPDATE_FAIL", "COORDINATOR_LIST_REQUEST", "COORDINATOR_LIST_SUCCESS", "COORDINATOR_LIST_FAIL", "COORDINATOR_ADD_REQUEST", "COORDINATOR_ADD_SUCCESS", "COORDINATOR_ADD_FAIL", "COORDINATOR_DETAIL_REQUEST", "COORDINATOR_DETAIL_SUCCESS", "COORDINATOR_DETAIL_FAIL", "COORDINATOR_UPDATE_REQUEST", "COORDINATOR_UPDATE_SUCCESS", "COORDINATOR_UPDATE_FAIL", "USER_UPDATE_LOGIN_REQUEST", "USER_UPDATE_LOGIN_SUCCESS", "USER_UPDATE_LOGIN_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/userConstants.js"], "sourcesContent": ["export const USER_LOGIN_REQUEST = \"USER_LOGIN_REQUEST\";\nexport const USER_LOGIN_SUCCESS = \"USER_LOGIN_SUCCESS\";\nexport const USER_LOGIN_FAIL = \"USER_LOGIN_FAIL\";\n\nexport const USER_LOGOUT = \"USER_LOGOUT\";\n\nexport const USER_ADD_REQUEST = \"USER_ADD_REQUEST\";\nexport const USER_ADD_SUCCESS = \"USER_ADD_SUCCESS\";\nexport const USER_ADD_FAIL = \"USER_ADD_FAIL\";\n\nexport const USER_DELETE_REQUEST = \"USER_DELETE_REQUEST\";\nexport const USER_DELETE_SUCCESS = \"USER_DELETE_SUCCESS\";\nexport const USER_DELETE_FAIL = \"USER_DELETE_FAIL\";\n\nexport const USER_LIST_REQUEST = \"USER_LIST_REQUEST\";\nexport const USER_LIST_SUCCESS = \"USER_LIST_SUCCESS\";\nexport const USER_LIST_FAIL = \"USER_LIST_FAIL\";\n\nexport const USER_PROFILE_REQUEST = \"USER_PROFILE_REQUEST\";\nexport const USER_PROFILE_SUCCESS = \"USER_PROFILE_SUCCESS\";\nexport const USER_PROFILE_FAIL = \"USER_PROFILE_FAIL\";\n\nexport const USER_PROFILE_UPDATE_REQUEST = \"USER_PROFILE_UPDATE_REQUEST\";\nexport const USER_PROFILE_UPDATE_SUCCESS = \"USER_PROFILE_UPDATE_SUCCESS\";\nexport const USER_PROFILE_UPDATE_FAIL = \"USER_PROFILE_UPDATE_FAIL\";\n\nexport const USER_PASSWORD_UPDATE_REQUEST = \"USER_PASSWORD_UPDATE_REQUEST\";\nexport const USER_PASSWORD_UPDATE_SUCCESS = \"USER_PASSWORD_UPDATE_SUCCESS\";\nexport const USER_PASSWORD_UPDATE_FAIL = \"USER_PASSWORD_UPDATE_FAIL\";\n\nexport const COORDINATOR_LIST_REQUEST = \"COORDINATOR_LIST_REQUEST\";\nexport const COORDINATOR_LIST_SUCCESS = \"COORDINATOR_LIST_SUCCESS\";\nexport const COORDINATOR_LIST_FAIL = \"COORDINATOR_LIST_FAIL\";\n\nexport const COORDINATOR_ADD_REQUEST = \"COORDINATOR_ADD_REQUEST\";\nexport const COORDINATOR_ADD_SUCCESS = \"COORDINATOR_ADD_SUCCESS\";\nexport const COORDINATOR_ADD_FAIL = \"COORDINATOR_ADD_FAIL\";\n\nexport const COORDINATOR_DETAIL_REQUEST = \"COORDINATOR_DETAIL_REQUEST\";\nexport const COORDINATOR_DETAIL_SUCCESS = \"COORDINATOR_DETAIL_SUCCESS\";\nexport const COORDINATOR_DETAIL_FAIL = \"COORDINATOR_DETAIL_FAIL\";\n\nexport const COORDINATOR_UPDATE_REQUEST = \"COORDINATOR_UPDATE_REQUEST\";\nexport const COORDINATOR_UPDATE_SUCCESS = \"COORDINATOR_UPDATE_SUCCESS\";\nexport const COORDINATOR_UPDATE_FAIL = \"COORDINATOR_UPDATE_FAIL\";\n\nexport const USER_UPDATE_LOGIN_REQUEST = \"USER_UPDATE_LOGIN_REQUEST\";\nexport const USER_UPDATE_LOGIN_SUCCESS = \"USER_UPDATE_LOGIN_SUCCESS\";\nexport const USER_UPDATE_LOGIN_FAIL = \"USER_ADD_FAIL\";\n"], "mappings": "AAAA,OAAO,MAAMA,kBAAkB,GAAG,oBAAoB;AACtD,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AACtD,OAAO,MAAMC,eAAe,GAAG,iBAAiB;AAEhD,OAAO,MAAMC,WAAW,GAAG,aAAa;AAExC,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAClD,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAClD,OAAO,MAAMC,aAAa,GAAG,eAAe;AAE5C,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AACxD,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AACxD,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAElD,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AACpD,OAAO,MAAMC,cAAc,GAAG,gBAAgB;AAE9C,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAC1D,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAC1D,OAAO,MAAMC,iBAAiB,GAAG,mBAAmB;AAEpD,OAAO,MAAMC,2BAA2B,GAAG,6BAA6B;AACxE,OAAO,MAAMC,2BAA2B,GAAG,6BAA6B;AACxE,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAElE,OAAO,MAAMC,4BAA4B,GAAG,8BAA8B;AAC1E,OAAO,MAAMC,4BAA4B,GAAG,8BAA8B;AAC1E,OAAO,MAAMC,yBAAyB,GAAG,2BAA2B;AAEpE,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAClE,OAAO,MAAMC,wBAAwB,GAAG,0BAA0B;AAClE,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAE5D,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAChE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAChE,OAAO,MAAMC,oBAAoB,GAAG,sBAAsB;AAE1D,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAEhE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,0BAA0B,GAAG,4BAA4B;AACtE,OAAO,MAAMC,uBAAuB,GAAG,yBAAyB;AAEhE,OAAO,MAAMC,yBAAyB,GAAG,2BAA2B;AACpE,OAAO,MAAMC,yBAAyB,GAAG,2BAA2B;AACpE,OAAO,MAAMC,sBAAsB,GAAG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}