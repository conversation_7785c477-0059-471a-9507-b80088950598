{"ast": null, "code": "import { toast } from \"react-toastify\";\nimport { EMPLOYE_LIST_REQUEST, EMPLOYE_LIST_SUCCESS, EMPLOYE_LIST_FAIL,\n//\nEMPLOYE_ADD_REQUEST, EMPLOYE_ADD_SUCCESS, EMPLOYE_ADD_FAIL,\n//\nEMPLOYE_DETAIL_REQUEST, EMPLOYE_DETAIL_SUCCESS, EMPLOYE_DETAIL_FAIL,\n//\nEMPLOYE_UPDATE_REQUEST, EMPLOYE_UPDATE_SUCCESS, EMPLOYE_UPDATE_FAIL,\n//\nEMPLOYE_DELETE_REQUEST, EMPLOYE_DELETE_SUCCESS, EMPLOYE_DELETE_FAIL\n//\n} from \"../constants/employeConstants\";\nexport const deleteEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_DELETE_REQUEST:\n      return {\n        loadingEmployeDelete: true\n      };\n    case EMPLOYE_DELETE_SUCCESS:\n      toast.success(\"Ce Employé a été supprimer avec succès\");\n      return {\n        loadingEmployeDelete: false,\n        successEmployeDelete: true\n      };\n    case EMPLOYE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeDelete: false,\n        successEmployeDelete: false,\n        errorEmployeDelete: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const updateEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_UPDATE_REQUEST:\n      return {\n        loadingEmployeUpdate: true\n      };\n    case EMPLOYE_UPDATE_SUCCESS:\n      toast.success(\"Ce Employé a été mis à jour avec succès\");\n      return {\n        loadingEmployeUpdate: false,\n        successEmployeUpdate: true\n      };\n    case EMPLOYE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeUpdate: false,\n        successEmployeUpdate: false,\n        errorEmployeUpdate: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const detailEmployeReducer = (state = {\n  employe: {}\n}, action) => {\n  switch (action.type) {\n    case EMPLOYE_DETAIL_REQUEST:\n      return {\n        loading: true\n      };\n    case EMPLOYE_DETAIL_SUCCESS:\n      return {\n        loading: false,\n        success: true,\n        employe: action.payload\n      };\n    case EMPLOYE_DETAIL_FAIL:\n      return {\n        loading: false,\n        success: false,\n        error: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const createNewEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_ADD_REQUEST:\n      return {\n        loadingEmployeAdd: true\n      };\n    case EMPLOYE_ADD_SUCCESS:\n      toast.success(\"Ce Employé a été ajouté avec succès\");\n      return {\n        loadingEmployeAdd: false,\n        successEmployeAdd: true\n      };\n    case EMPLOYE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeAdd: false,\n        successEmployeAdd: false,\n        errorEmployeAdd: action.payload\n      };\n    default:\n      return state;\n  }\n};\nexport const employesListReducer = (state = {\n  employes: []\n}, action) => {\n  switch (action.type) {\n    case EMPLOYE_LIST_REQUEST:\n      return {\n        loadingEmploye: true,\n        employes: []\n      };\n    case EMPLOYE_LIST_SUCCESS:\n      return {\n        loadingEmploye: false,\n        employes: action.payload.employes,\n        pages: action.payload.pages,\n        page: action.payload.page\n      };\n    case EMPLOYE_LIST_FAIL:\n      return {\n        loadingEmploye: false,\n        errorEmploye: action.payload\n      };\n    default:\n      return state;\n  }\n};", "map": {"version": 3, "names": ["toast", "EMPLOYE_LIST_REQUEST", "EMPLOYE_LIST_SUCCESS", "EMPLOYE_LIST_FAIL", "EMPLOYE_ADD_REQUEST", "EMPLOYE_ADD_SUCCESS", "EMPLOYE_ADD_FAIL", "EMPLOYE_DETAIL_REQUEST", "EMPLOYE_DETAIL_SUCCESS", "EMPLOYE_DETAIL_FAIL", "EMPLOYE_UPDATE_REQUEST", "EMPLOYE_UPDATE_SUCCESS", "EMPLOYE_UPDATE_FAIL", "EMPLOYE_DELETE_REQUEST", "EMPLOYE_DELETE_SUCCESS", "EMPLOYE_DELETE_FAIL", "deleteEmployeReducer", "state", "action", "type", "loadingEmployeDelete", "success", "successEmployeDelete", "error", "payload", "errorEmployeDelete", "updateEmployeReducer", "loadingEmployeUpdate", "successEmployeUpdate", "errorEmployeUpdate", "detailEmployeReducer", "employe", "loading", "createNewEmployeReducer", "loadingEmployeAdd", "successEmployeAdd", "errorEmployeAdd", "employesListReducer", "employes", "loadingEmploye", "pages", "page", "errorEmploye"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/reducers/employeReducers.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\n\nimport {\n  EMPLOYE_LIST_REQUEST,\n  EMPLOYE_LIST_SUCCESS,\n  EMPLOYE_LIST_FAIL,\n  //\n  EMPLOYE_ADD_REQUEST,\n  EMPLOYE_ADD_SUCCESS,\n  EMPLOYE_ADD_FAIL,\n  //\n  EMPLOYE_DETAIL_REQUEST,\n  EMPLOYE_DETAIL_SUCCESS,\n  EMPLOYE_DETAIL_FAIL,\n  //\n  EMPLOYE_UPDATE_REQUEST,\n  EMPLOYE_UPDATE_SUCCESS,\n  EMPLOYE_UPDATE_FAIL,\n  //\n  EMPLOYE_DELETE_REQUEST,\n  EMPLOYE_DELETE_SUCCESS,\n  EMPLOYE_DELETE_FAIL,\n  //\n} from \"../constants/employeConstants\";\n\nexport const deleteEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_DELETE_REQUEST:\n      return { loadingEmployeDelete: true };\n    case EMPLOYE_DELETE_SUCCESS:\n      toast.success(\"Ce Employé a été supprimer avec succès\");\n      return {\n        loadingEmployeDelete: false,\n        successEmployeDelete: true,\n      };\n    case EMPLOYE_DELETE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeDelete: false,\n        successEmployeDelete: false,\n        errorEmployeDelete: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const updateEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_UPDATE_REQUEST:\n      return { loadingEmployeUpdate: true };\n    case EMPLOYE_UPDATE_SUCCESS:\n      toast.success(\"Ce Employé a été mis à jour avec succès\");\n      return {\n        loadingEmployeUpdate: false,\n        successEmployeUpdate: true,\n      };\n    case EMPLOYE_UPDATE_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeUpdate: false,\n        successEmployeUpdate: false,\n        errorEmployeUpdate: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const detailEmployeReducer = (state = { employe: {} }, action) => {\n  switch (action.type) {\n    case EMPLOYE_DETAIL_REQUEST:\n      return { loading: true };\n    case EMPLOYE_DETAIL_SUCCESS:\n      return {\n        loading: false,\n        success: true,\n        employe: action.payload,\n      };\n    case EMPLOYE_DETAIL_FAIL:\n      return {\n        loading: false,\n        success: false,\n        error: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const createNewEmployeReducer = (state = {}, action) => {\n  switch (action.type) {\n    case EMPLOYE_ADD_REQUEST:\n      return { loadingEmployeAdd: true };\n    case EMPLOYE_ADD_SUCCESS:\n      toast.success(\"Ce Employé a été ajouté avec succès\");\n      return {\n        loadingEmployeAdd: false,\n        successEmployeAdd: true,\n      };\n    case EMPLOYE_ADD_FAIL:\n      toast.error(action.payload);\n      return {\n        loadingEmployeAdd: false,\n        successEmployeAdd: false,\n        errorEmployeAdd: action.payload,\n      };\n    default:\n      return state;\n  }\n};\n\nexport const employesListReducer = (state = { employes: [] }, action) => {\n  switch (action.type) {\n    case EMPLOYE_LIST_REQUEST:\n      return { loadingEmploye: true, employes: [] };\n    case EMPLOYE_LIST_SUCCESS:\n      return {\n        loadingEmploye: false,\n        employes: action.payload.employes,\n        pages: action.payload.pages,\n        page: action.payload.page,\n      };\n    case EMPLOYE_LIST_FAIL:\n      return { loadingEmploye: false, errorEmploye: action.payload };\n    default:\n      return state;\n  }\n};\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,gBAAgB;AAEtC,SACEC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB;AACjB;AACAC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC,mBAAmB;AACnB;AACAC,sBAAsB,EACtBC,sBAAsB,EACtBC;AACA;AAAA,OACK,+BAA+B;AAEtC,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKN,sBAAsB;MACzB,OAAO;QAAEO,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKN,sBAAsB;MACzBd,KAAK,CAACqB,OAAO,CAAC,wCAAwC,CAAC;MACvD,OAAO;QACLD,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE;MACxB,CAAC;IACH,KAAKP,mBAAmB;MACtBf,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLJ,oBAAoB,EAAE,KAAK;QAC3BE,oBAAoB,EAAE,KAAK;QAC3BG,kBAAkB,EAAEP,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMS,oBAAoB,GAAGA,CAACT,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC1D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKT,sBAAsB;MACzB,OAAO;QAAEiB,oBAAoB,EAAE;MAAK,CAAC;IACvC,KAAKhB,sBAAsB;MACzBX,KAAK,CAACqB,OAAO,CAAC,yCAAyC,CAAC;MACxD,OAAO;QACLM,oBAAoB,EAAE,KAAK;QAC3BC,oBAAoB,EAAE;MACxB,CAAC;IACH,KAAKhB,mBAAmB;MACtBZ,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLG,oBAAoB,EAAE,KAAK;QAC3BC,oBAAoB,EAAE,KAAK;QAC3BC,kBAAkB,EAAEX,MAAM,CAACM;MAC7B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMa,oBAAoB,GAAGA,CAACb,KAAK,GAAG;EAAEc,OAAO,EAAE,CAAC;AAAE,CAAC,EAAEb,MAAM,KAAK;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKZ,sBAAsB;MACzB,OAAO;QAAEyB,OAAO,EAAE;MAAK,CAAC;IAC1B,KAAKxB,sBAAsB;MACzB,OAAO;QACLwB,OAAO,EAAE,KAAK;QACdX,OAAO,EAAE,IAAI;QACbU,OAAO,EAAEb,MAAM,CAACM;MAClB,CAAC;IACH,KAAKf,mBAAmB;MACtB,OAAO;QACLuB,OAAO,EAAE,KAAK;QACdX,OAAO,EAAE,KAAK;QACdE,KAAK,EAAEL,MAAM,CAACM;MAChB,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMgB,uBAAuB,GAAGA,CAAChB,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,KAAK;EAC7D,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKf,mBAAmB;MACtB,OAAO;QAAE8B,iBAAiB,EAAE;MAAK,CAAC;IACpC,KAAK7B,mBAAmB;MACtBL,KAAK,CAACqB,OAAO,CAAC,qCAAqC,CAAC;MACpD,OAAO;QACLa,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE;MACrB,CAAC;IACH,KAAK7B,gBAAgB;MACnBN,KAAK,CAACuB,KAAK,CAACL,MAAM,CAACM,OAAO,CAAC;MAC3B,OAAO;QACLU,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,eAAe,EAAElB,MAAM,CAACM;MAC1B,CAAC;IACH;MACE,OAAOP,KAAK;EAChB;AACF,CAAC;AAED,OAAO,MAAMoB,mBAAmB,GAAGA,CAACpB,KAAK,GAAG;EAAEqB,QAAQ,EAAE;AAAG,CAAC,EAAEpB,MAAM,KAAK;EACvE,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAKlB,oBAAoB;MACvB,OAAO;QAAEsC,cAAc,EAAE,IAAI;QAAED,QAAQ,EAAE;MAAG,CAAC;IAC/C,KAAKpC,oBAAoB;MACvB,OAAO;QACLqC,cAAc,EAAE,KAAK;QACrBD,QAAQ,EAAEpB,MAAM,CAACM,OAAO,CAACc,QAAQ;QACjCE,KAAK,EAAEtB,MAAM,CAACM,OAAO,CAACgB,KAAK;QAC3BC,IAAI,EAAEvB,MAAM,CAACM,OAAO,CAACiB;MACvB,CAAC;IACH,KAAKtC,iBAAiB;MACpB,OAAO;QAAEoC,cAAc,EAAE,KAAK;QAAEG,YAAY,EAAExB,MAAM,CAACM;MAAQ,CAAC;IAChE;MACE,OAAOP,KAAK;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}