{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport { BEGIN_DRAG, PUBLISH_DRAG_SOURCE, HOVER, END_DRAG, DROP } from '../actions/dragDrop';\nimport { REMOVE_TARGET } from '../actions/registry';\nimport { without } from '../utils/js_utils';\nvar initialState = {\n  itemType: null,\n  item: null,\n  sourceId: null,\n  targetIds: [],\n  dropResult: null,\n  didDrop: false,\n  isSourcePublic: null\n};\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  var payload = action.payload;\n  switch (action.type) {\n    case BEGIN_DRAG:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        itemType: payload.itemType,\n        item: payload.item,\n        sourceId: payload.sourceId,\n        isSourcePublic: payload.isSourcePublic,\n        dropResult: null,\n        didDrop: false\n      });\n    case PUBLISH_DRAG_SOURCE:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isSourcePublic: true\n      });\n    case HOVER:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        targetIds: payload.targetIds\n      });\n    case REMOVE_TARGET:\n      if (state.targetIds.indexOf(payload.targetId) === -1) {\n        return state;\n      }\n      return _objectSpread(_objectSpread({}, state), {}, {\n        targetIds: without(state.targetIds, payload.targetId)\n      });\n    case DROP:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        dropResult: payload.dropResult,\n        didDrop: true,\n        targetIds: []\n      });\n    case END_DRAG:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        itemType: null,\n        item: null,\n        sourceId: null,\n        dropResult: null,\n        didDrop: false,\n        isSourcePublic: null,\n        targetIds: []\n      });\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "obj", "value", "configurable", "writable", "BEGIN_DRAG", "PUBLISH_DRAG_SOURCE", "HOVER", "END_DRAG", "DROP", "REMOVE_TARGET", "without", "initialState", "itemType", "item", "sourceId", "targetIds", "dropResult", "didDrop", "isSourcePublic", "reduce", "state", "undefined", "action", "payload", "type", "indexOf", "targetId"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/reducers/dragOperation.js"], "sourcesContent": ["function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { BEGIN_DRAG, PUBLISH_DRAG_SOURCE, HOVER, END_DRAG, DROP } from '../actions/dragDrop';\nimport { REMOVE_TARGET } from '../actions/registry';\nimport { without } from '../utils/js_utils';\nvar initialState = {\n  itemType: null,\n  item: null,\n  sourceId: null,\n  targetIds: [],\n  dropResult: null,\n  didDrop: false,\n  isSourcePublic: null\n};\nexport function reduce() {\n  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;\n  var action = arguments.length > 1 ? arguments[1] : undefined;\n  var payload = action.payload;\n\n  switch (action.type) {\n    case BEGIN_DRAG:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        itemType: payload.itemType,\n        item: payload.item,\n        sourceId: payload.sourceId,\n        isSourcePublic: payload.isSourcePublic,\n        dropResult: null,\n        didDrop: false\n      });\n\n    case PUBLISH_DRAG_SOURCE:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isSourcePublic: true\n      });\n\n    case HOVER:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        targetIds: payload.targetIds\n      });\n\n    case REMOVE_TARGET:\n      if (state.targetIds.indexOf(payload.targetId) === -1) {\n        return state;\n      }\n\n      return _objectSpread(_objectSpread({}, state), {}, {\n        targetIds: without(state.targetIds, payload.targetId)\n      });\n\n    case DROP:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        dropResult: payload.dropResult,\n        didDrop: true,\n        targetIds: []\n      });\n\n    case END_DRAG:\n      return _objectSpread(_objectSpread({}, state), {}, {\n        itemType: null,\n        item: null,\n        sourceId: null,\n        dropResult: null,\n        didDrop: false,\n        isSourcePublic: null,\n        targetIds: []\n      });\n\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EAAE,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAAE,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAAE,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAAE,IAAIC,cAAc,EAAE;MAAEI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAAE,CAAC,CAAC;IAAE;IAAEP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAAE;EAAE,OAAOH,IAAI;AAAE;AAExV,SAASU,aAAaA,CAACC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,IAAIA,CAAC,GAAG,CAAC,EAAE;MAAEf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAAElB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAAE,CAAC,MAAM;MAAElB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAAEhB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAAE,CAAC,CAAC;IAAE;EAAE;EAAE,OAAON,MAAM;AAAE;AAErhB,SAASO,eAAeA,CAACI,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAE,IAAIN,GAAG,IAAIK,GAAG,EAAE;IAAErB,MAAM,CAACoB,cAAc,CAACC,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEhB,UAAU,EAAE,IAAI;MAAEiB,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEH,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,SAASI,UAAU,EAAEC,mBAAmB,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,qBAAqB;AAC5F,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,IAAIC,YAAY,GAAG;EACjBC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,KAAK;EACdC,cAAc,EAAE;AAClB,CAAC;AACD,OAAO,SAASC,MAAMA,CAAA,EAAG;EACvB,IAAIC,KAAK,GAAG7B,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK8B,SAAS,GAAG9B,SAAS,CAAC,CAAC,CAAC,GAAGoB,YAAY;EAC5F,IAAIW,MAAM,GAAG/B,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG8B,SAAS;EAC5D,IAAIE,OAAO,GAAGD,MAAM,CAACC,OAAO;EAE5B,QAAQD,MAAM,CAACE,IAAI;IACjB,KAAKpB,UAAU;MACb,OAAOhB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDR,QAAQ,EAAEW,OAAO,CAACX,QAAQ;QAC1BC,IAAI,EAAEU,OAAO,CAACV,IAAI;QAClBC,QAAQ,EAAES,OAAO,CAACT,QAAQ;QAC1BI,cAAc,EAAEK,OAAO,CAACL,cAAc;QACtCF,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE;MACX,CAAC,CAAC;IAEJ,KAAKZ,mBAAmB;MACtB,OAAOjB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDF,cAAc,EAAE;MAClB,CAAC,CAAC;IAEJ,KAAKZ,KAAK;MACR,OAAOlB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDL,SAAS,EAAEQ,OAAO,CAACR;MACrB,CAAC,CAAC;IAEJ,KAAKN,aAAa;MAChB,IAAIW,KAAK,CAACL,SAAS,CAACU,OAAO,CAACF,OAAO,CAACG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACpD,OAAON,KAAK;MACd;MAEA,OAAOhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDL,SAAS,EAAEL,OAAO,CAACU,KAAK,CAACL,SAAS,EAAEQ,OAAO,CAACG,QAAQ;MACtD,CAAC,CAAC;IAEJ,KAAKlB,IAAI;MACP,OAAOpB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDJ,UAAU,EAAEO,OAAO,CAACP,UAAU;QAC9BC,OAAO,EAAE,IAAI;QACbF,SAAS,EAAE;MACb,CAAC,CAAC;IAEJ,KAAKR,QAAQ;MACX,OAAOnB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEgC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjDR,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,IAAI;QACdE,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE,IAAI;QACpBH,SAAS,EAAE;MACb,CAAC,CAAC;IAEJ;MACE,OAAOK,KAAK;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}