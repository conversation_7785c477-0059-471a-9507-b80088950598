{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase,detailCase,updateCase}from\"../../redux/actions/caseActions\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{COUNTRIES}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function EditCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[caseDate,setCaseDate]=useState(\"\");const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fiels deleted\nconst[fileDeleted,setFileDeleted]=useState([]);const[itemsInitialMedicalReports,setItemsInitialMedicalReports]=useState([]);const[itemsUploadInvoice,setItemsUploadInvoice]=useState([]);const[itemsUploadAuthorizationDocuments,setItemsUploadAuthorizationDocuments]=useState([]);// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const caseUpdate=useSelector(state=>state.updateCase);const{loadingCaseUpdate,errorCaseUpdate,successCaseUpdate}=caseUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{setStepSelect(0);dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));dispatch(detailCase(id));}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseUpdate){setStepSelect(5);}},[successCaseUpdate]);useEffect(()=>{if(caseInfo!==undefined&&caseInfo!==null){var _caseInfo$case_date,_caseInfo$case_type,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$appointment,_caseInfo$service_loc,_caseInfo$invoice_num,_caseInfo$date_issued,_caseInfo$invoice_amo,_caseInfo$policy_numb,_caseInfo$assurance_n,_caseInfo$assurance_s;if(caseInfo.patient){var _caseInfo$patient$fir,_caseInfo$patient$las,_caseInfo$patient$bir,_caseInfo$patient$pat,_caseInfo$patient$pat2,_caseInfo$patient$pat3,_caseInfo$patient$pat4,_caseInfo$patient$pat5;setFirstName((_caseInfo$patient$fir=caseInfo.patient.first_name)!==null&&_caseInfo$patient$fir!==void 0?_caseInfo$patient$fir:\"\");setLastName((_caseInfo$patient$las=caseInfo.patient.last_name)!==null&&_caseInfo$patient$las!==void 0?_caseInfo$patient$las:\"\");setBirthDate((_caseInfo$patient$bir=caseInfo.patient.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"\");setPhone((_caseInfo$patient$pat=caseInfo.patient.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"\");setEmail((_caseInfo$patient$pat2=caseInfo.patient.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"\");setAddress((_caseInfo$patient$pat3=caseInfo.patient.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"\");const patientCountry=(_caseInfo$patient$pat4=caseInfo.patient.patient_country)!==null&&_caseInfo$patient$pat4!==void 0?_caseInfo$patient$pat4:\"\";const foundCountry=COUNTRIES.find(option=>option.title===patientCountry);if(foundCountry){setCountry({value:foundCountry.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:foundCountry.icon}),/*#__PURE__*/_jsx(\"span\",{children:foundCountry.title})]})});}else{setCountry(\"\");}setCity((_caseInfo$patient$pat5=caseInfo.patient.patient_city)!==null&&_caseInfo$patient$pat5!==void 0?_caseInfo$patient$pat5:\"\");}// setCoordinator(caseInfo.coordinator ?? \"\");\nif(caseInfo.coordinator_user){var _caseInfo$coordinator,_caseInfo$coordinator2;var initialCoordinator=(_caseInfo$coordinator=(_caseInfo$coordinator2=caseInfo.coordinator_user)===null||_caseInfo$coordinator2===void 0?void 0:_caseInfo$coordinator2.id)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"\";const foundCoordinator=coordinators===null||coordinators===void 0?void 0:coordinators.find(item=>item.id===initialCoordinator);if(foundCoordinator){setCoordinator({value:foundCoordinator.id,label:foundCoordinator.full_name});}else{setProviderName(\"\");}}setCaseDate((_caseInfo$case_date=caseInfo.case_date)!==null&&_caseInfo$case_date!==void 0?_caseInfo$case_date:\"\");setCaseType((_caseInfo$case_type=caseInfo.case_type)!==null&&_caseInfo$case_type!==void 0?_caseInfo$case_type:\"\");setCaseDescription((_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"\");//\nsetCoordinatStatus((_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"\");setAppointmentDate((_caseInfo$appointment=caseInfo.appointment_date)!==null&&_caseInfo$appointment!==void 0?_caseInfo$appointment:\"\");setServiceLocation((_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"\");if(caseInfo.provider){var _caseInfo$provider$id,_caseInfo$provider;var initialProvider=(_caseInfo$provider$id=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.id)!==null&&_caseInfo$provider$id!==void 0?_caseInfo$provider$id:\"\";const foundProvider=providers===null||providers===void 0?void 0:providers.find(item=>item.id===initialProvider);if(foundProvider){setProviderName({value:foundProvider.id,label:foundProvider.full_name});}else{setProviderName(\"\");}}//\nsetItemsInitialMedicalReports([]);if(caseInfo.medical_reports){setItemsInitialMedicalReports(caseInfo.medical_reports);}//\nsetInvoiceNumber((_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"\");setDateIssued((_caseInfo$date_issued=caseInfo.date_issued)!==null&&_caseInfo$date_issued!==void 0?_caseInfo$date_issued:\"\");setAmount((_caseInfo$invoice_amo=caseInfo.invoice_amount)!==null&&_caseInfo$invoice_amo!==void 0?_caseInfo$invoice_amo:0);setItemsUploadInvoice([]);if(caseInfo.upload_invoices){setItemsUploadInvoice(caseInfo.upload_invoices);}//\nif(caseInfo.assurance){var _caseInfo$assurance$i,_caseInfo$assurance;var initialInsurance=(_caseInfo$assurance$i=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.id)!==null&&_caseInfo$assurance$i!==void 0?_caseInfo$assurance$i:\"\";var foundInsurance=insurances===null||insurances===void 0?void 0:insurances.find(item=>item.id===initialInsurance);if(foundInsurance){console.log(\"here 2\");setInsuranceCompany({value:foundInsurance.id,label:foundInsurance.assurance_name||\"\"});}else{console.log(\"here 3\");setInsuranceCompany({value:\"\",label:\"\"});}}setPolicyNumber((_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"\");setInsuranceNumber((_caseInfo$assurance_n=caseInfo.assurance_number)!==null&&_caseInfo$assurance_n!==void 0?_caseInfo$assurance_n:\"\");setInitialStatus((_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"\");setItemsUploadAuthorizationDocuments([]);if(caseInfo.upload_authorization){setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);}//\n}},[caseInfo]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Edit Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"Edit Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{// onClick={() => setStepSelect(step.index)}\nclassName:\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\",children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},className:\"text-sm\",options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"City\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"City\",value:city,onChange:v=>setCity(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA ID\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"CIA ID\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(caseDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border \".concat(caseTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:coordinatStatus,onChange:v=>setCoordinatStatus(v.target.value),className:\"outline-none border \".concat(coordinatStatusError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusError?coordinatStatusError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{setProviderName(option);},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");if(coordinatStatus===\"\"){setCoordinatStatusError(\"This field is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsInitialMedicalReports===null||itemsInitialMedicalReports===void 0?void 0:itemsInitialMedicalReports.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadInvoice===null||itemsUploadInvoice===void 0?void 0:itemsUploadInvoice.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col \",children:[itemsUploadAuthorizationDocuments===null||itemsUploadAuthorizationDocuments===void 0?void 0:itemsUploadAuthorizationDocuments.filter(file=>!fileDeleted.includes(file.id)).map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[parseFloat(file.file_size).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFileDeleted([...fileDeleted,file.id]);},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.file_name)),filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseUpdate,onClick:async()=>{var _coordinator$value,_providerName$value,_insuranceCompany$val;// update\nawait dispatch(updateCase(id,{first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate!==null&&birthDate!==void 0?birthDate:\"\",patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:(_coordinator$value=coordinator.value)!==null&&_coordinator$value!==void 0?_coordinator$value:\"\",case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,appointment_date:appointmentDate,service_location:serviceLocation,provider:(_providerName$value=providerName.value)!==null&&_providerName$value!==void 0?_providerName$value:\"\",//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:(_insuranceCompany$val=insuranceCompany.value)!==null&&_insuranceCompany$val!==void 0?_insuranceCompany$val:\"\",assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments,files_deleted:fileDeleted}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseUpdate?\"Loading..\":\"Update\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Updated Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully updates and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default EditCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "caseDate", "setCaseDate", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "undefined", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "find", "option", "value", "label", "className", "children", "icon", "patient_city", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "foundCoordinator", "item", "full_name", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "console", "log", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "src", "onError", "e", "target", "onerror", "concat", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "filterOption", "inputValue", "toLowerCase", "includes", "rows", "onClick", "check", "error", "style", "filter", "class", "file_name", "parseFloat", "file_size", "toFixed", "name", "size", "_", "indexToRemove", "disabled", "_coordinator$value", "_providerName$value", "_insuranceCompany$val", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n      // setCoordinator(caseInfo.coordinator ?? \"\");\n      if (caseInfo.coordinator_user) {\n        var initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        const foundCoordinator = coordinators?.find(\n          (item) => item.id === initialCoordinator\n        );\n\n        if (foundCoordinator) {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  // onClick={() => setStepSelect(step.index)}\n                  className=\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\"\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">City</div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA ID\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA ID\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OACEC,UAAU,CACVC,UAAU,CACVC,UAAU,KACL,iCAAiC,CAExC,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,SAAS,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,cAAcA,CAAA,CAAG,CACxB,KAAM,CAAAC,QAAQ,CAAG5B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6B,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+B,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEkC,EAAG,CAAC,CAAG9B,SAAS,CAAC,CAAC,CAExB;AACA,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACsC,cAAc,CAAEC,iBAAiB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACwC,QAAQ,CAAEC,WAAW,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0C,aAAa,CAAEC,gBAAgB,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC4C,KAAK,CAAEC,QAAQ,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC8C,UAAU,CAAEC,aAAa,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACgD,SAAS,CAAEC,YAAY,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkD,cAAc,CAAEC,iBAAiB,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACoD,KAAK,CAAEC,QAAQ,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsD,UAAU,CAAEC,aAAa,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACwD,OAAO,CAAEC,UAAU,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0D,YAAY,CAAEC,eAAe,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAAC4D,IAAI,CAAEC,OAAO,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC8D,SAAS,CAAEC,YAAY,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAACgE,OAAO,CAAEC,UAAU,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACoE,WAAW,CAAEC,cAAc,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACwE,QAAQ,CAAEC,WAAW,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0E,aAAa,CAAEC,gBAAgB,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC4E,QAAQ,CAAEC,WAAW,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC8E,aAAa,CAAEC,gBAAgB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACoF,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwF,eAAe,CAAEC,kBAAkB,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAAC4F,eAAe,CAAEC,kBAAkB,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgG,YAAY,CAAEC,eAAe,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACoG,aAAa,CAAEC,gBAAgB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACwG,aAAa,CAAEC,gBAAgB,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0G,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC4G,eAAe,CAAEC,kBAAkB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgH,aAAa,CAAEC,gBAAgB,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkH,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoH,UAAU,CAAEC,aAAa,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACsH,eAAe,CAAEC,kBAAkB,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACwH,MAAM,CAAEC,SAAS,CAAC,CAAGzH,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAAC0H,WAAW,CAAEC,cAAc,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAAC4H,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC8H,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAACgI,eAAe,CAAEC,kBAAkB,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoI,YAAY,CAAEC,eAAe,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACsI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACwI,aAAa,CAAEC,gBAAgB,CAAC,CAAGzI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC0I,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3I,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAC4I,WAAW,CAAEC,cAAc,CAAC,CAAG7I,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAAC8I,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG/I,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CAACgJ,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjJ,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJkJ,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CAEhB;AACA;AACA,KAAM,CAACoJ,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGrJ,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJsJ,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAG3I,WAAW,CAAC,CACd4I,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhK,SAAS,CAAC,IAAM,CACd,MAAO,IACLqJ,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGxK,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJsJ,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAG5J,WAAW,CAAC,CACd4I,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhK,SAAS,CAAC,IAAM,CACd,MAAO,IACLwK,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAG5K,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJsJ,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGhK,WAAW,CAAC,CACd4I,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFhK,SAAS,CAAC,IAAM,CACd,MAAO,IACL4K,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGhL,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAiL,SAAS,CAAG/K,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGlL,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,cAAc,CAAGvL,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,UAAU,CAAG5L,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACvK,UAAU,CAAC,CAC3D,KAAM,CAAEoL,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,gBAAgB,CAAGjM,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,UAAU,CAAGtM,WAAW,CAAEgL,KAAK,EAAKA,KAAK,CAACtK,UAAU,CAAC,CAC3D,KAAM,CAAE6L,iBAAiB,CAAEC,eAAe,CAAEC,iBAAkB,CAAC,CAAGH,UAAU,CAE5E,KAAM,CAAAI,QAAQ,CAAG,GAAG,CACpB7M,SAAS,CAAC,IAAM,CACd,GAAI,CAACoL,QAAQ,CAAE,CACbnJ,QAAQ,CAAC4K,QAAQ,CAAC,CACpB,CAAC,IAAM,CACL5B,aAAa,CAAC,CAAC,CAAC,CAChB9I,QAAQ,CAAClB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCkB,QAAQ,CAACzB,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5ByB,QAAQ,CAACnB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/BmB,QAAQ,CAACvB,UAAU,CAACwB,EAAE,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEmJ,QAAQ,CAAEjJ,QAAQ,CAAC,CAAC,CAElCnC,SAAS,CAAC,IAAM,CACd,GAAI4M,iBAAiB,CAAE,CACrB3B,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAAC2B,iBAAiB,CAAC,CAAC,CAEvB5M,SAAS,CAAC,IAAM,CACd,GAAImM,QAAQ,GAAKW,SAAS,EAAIX,QAAQ,GAAK,IAAI,CAAE,KAAAY,mBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC/C,GAAIvB,QAAQ,CAACwB,OAAO,CAAE,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACpB7L,YAAY,EAAAsL,qBAAA,CAACzB,QAAQ,CAACwB,OAAO,CAACS,UAAU,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/ClL,WAAW,EAAAmL,qBAAA,CAAC1B,QAAQ,CAACwB,OAAO,CAACU,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7C3K,YAAY,EAAA4K,qBAAA,CAAC3B,QAAQ,CAACwB,OAAO,CAACW,SAAS,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CxK,QAAQ,EAAAyK,qBAAA,CAAC5B,QAAQ,CAACwB,OAAO,CAACY,aAAa,UAAAR,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC9CjL,QAAQ,EAAAkL,sBAAA,CAAC7B,QAAQ,CAACwB,OAAO,CAACa,aAAa,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9CtK,UAAU,EAAAuK,sBAAA,CAAC9B,QAAQ,CAACwB,OAAO,CAACc,eAAe,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAElD,KAAM,CAAAS,cAAc,EAAAR,sBAAA,CAAG/B,QAAQ,CAACwB,OAAO,CAACgB,eAAe,UAAAT,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAC7D,KAAM,CAAAU,YAAY,CAAG1N,SAAS,CAAC2N,IAAI,CAChCC,MAAM,EAAKA,MAAM,CAACrN,KAAK,GAAKiN,cAC/B,CAAC,CAED,GAAIE,YAAY,CAAE,CAChB1K,UAAU,CAAC,CACT6K,KAAK,CAAEH,YAAY,CAACnN,KAAK,CACzBuN,KAAK,cACH1N,KAAA,QAAK2N,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC9N,IAAA,SAAM6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEN,YAAY,CAACO,IAAI,CAAO,CAAC,cACjD/N,IAAA,SAAA8N,QAAA,CAAON,YAAY,CAACnN,KAAK,CAAO,CAAC,EAC9B,CAET,CAAC,CAAC,CACJ,CAAC,IAAM,CACLyC,UAAU,CAAC,EAAE,CAAC,CAChB,CAEAJ,OAAO,EAAAqK,sBAAA,CAAChC,QAAQ,CAACwB,OAAO,CAACyB,YAAY,UAAAjB,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAAC,CAC9C,CACA;AACA,GAAIhC,QAAQ,CAACkD,gBAAgB,CAAE,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,GAAI,CAAAC,kBAAkB,EAAAF,qBAAA,EAAAC,sBAAA,CAAGpD,QAAQ,CAACkD,gBAAgB,UAAAE,sBAAA,iBAAzBA,sBAAA,CAA2BnN,EAAE,UAAAkN,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAC5D,KAAM,CAAAG,gBAAgB,CAAGnD,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEuC,IAAI,CACxCa,IAAI,EAAKA,IAAI,CAACtN,EAAE,GAAKoN,kBACxB,CAAC,CAED,GAAIC,gBAAgB,CAAE,CACpBnL,cAAc,CAAC,CACbyK,KAAK,CAAEU,gBAAgB,CAACrN,EAAE,CAC1B4M,KAAK,CAAES,gBAAgB,CAACE,SAC1B,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzJ,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACAxB,WAAW,EAAAqI,mBAAA,CAACZ,QAAQ,CAACyD,SAAS,UAAA7C,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrCjI,WAAW,EAAAkI,mBAAA,CAACb,QAAQ,CAAC0D,SAAS,UAAA7C,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAAC,CACrC9H,kBAAkB,EAAA+H,qBAAA,CAACd,QAAQ,CAAC2D,gBAAgB,UAAA7C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD;AACA3H,kBAAkB,EAAA4H,qBAAA,CAACf,QAAQ,CAAC4D,mBAAmB,UAAA7C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACtDxH,kBAAkB,EAAAyH,qBAAA,CAAChB,QAAQ,CAAC6D,gBAAgB,UAAA7C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnDrH,kBAAkB,EAAAsH,qBAAA,CAACjB,QAAQ,CAAC8D,gBAAgB,UAAA7C,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD,GAAIjB,QAAQ,CAAC+D,QAAQ,CAAE,KAAAC,qBAAA,CAAAC,kBAAA,CACrB,GAAI,CAAAC,eAAe,EAAAF,qBAAA,EAAAC,kBAAA,CAAGjE,QAAQ,CAAC+D,QAAQ,UAAAE,kBAAA,iBAAjBA,kBAAA,CAAmBhO,EAAE,UAAA+N,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACjD,KAAM,CAAAG,aAAa,CAAG/E,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEsD,IAAI,CAClCa,IAAI,EAAKA,IAAI,CAACtN,EAAE,GAAKiO,eACxB,CAAC,CACD,GAAIC,aAAa,CAAE,CACjBpK,eAAe,CAAC,CACd6I,KAAK,CAAEuB,aAAa,CAAClO,EAAE,CACvB4M,KAAK,CAAEsB,aAAa,CAACX,SACvB,CAAC,CAAC,CACJ,CAAC,IAAM,CACLzJ,eAAe,CAAC,EAAE,CAAC,CACrB,CACF,CACA;AACA8C,6BAA6B,CAAC,EAAE,CAAC,CACjC,GAAImD,QAAQ,CAACoE,eAAe,CAAE,CAC5BvH,6BAA6B,CAACmD,QAAQ,CAACoE,eAAe,CAAC,CACzD,CACA;AACArJ,gBAAgB,EAAAmG,qBAAA,CAAClB,QAAQ,CAACqE,cAAc,UAAAnD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC/C/F,aAAa,EAAAgG,qBAAA,CAACnB,QAAQ,CAACsE,WAAW,UAAAnD,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACzC5F,SAAS,EAAA6F,qBAAA,CAACpB,QAAQ,CAACuE,cAAc,UAAAnD,qBAAA,UAAAA,qBAAA,CAAI,CAAC,CAAC,CACvCrE,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIiD,QAAQ,CAACwE,eAAe,CAAE,CAC5BzH,qBAAqB,CAACiD,QAAQ,CAACwE,eAAe,CAAC,CACjD,CACA;AACA,GAAIxE,QAAQ,CAACyE,SAAS,CAAE,KAAAC,qBAAA,CAAAC,mBAAA,CACtB,GAAI,CAAAC,gBAAgB,EAAAF,qBAAA,EAAAC,mBAAA,CAAG3E,QAAQ,CAACyE,SAAS,UAAAE,mBAAA,iBAAlBA,mBAAA,CAAoB1O,EAAE,UAAAyO,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAEnD,GAAI,CAAAG,cAAc,CAAGpF,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEiD,IAAI,CAClCa,IAAI,EAAKA,IAAI,CAACtN,EAAE,GAAK2O,gBACxB,CAAC,CAED,GAAIC,cAAc,CAAE,CAClBC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBpJ,mBAAmB,CAAC,CAClBiH,KAAK,CAAEiC,cAAc,CAAC5O,EAAE,CACxB4M,KAAK,CAAEgC,cAAc,CAACG,cAAc,EAAI,EAC1C,CAAC,CAAC,CACJ,CAAC,IAAM,CACLF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrBpJ,mBAAmB,CAAC,CAClBiH,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,EACT,CAAC,CAAC,CACJ,CACF,CACA1G,eAAe,EAAAkF,qBAAA,CAACrB,QAAQ,CAACiF,aAAa,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CAC7CtF,kBAAkB,EAAAuF,qBAAA,CAACtB,QAAQ,CAACkF,gBAAgB,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACnD/E,gBAAgB,EAAAgF,qBAAA,CAACvB,QAAQ,CAACmF,gBAAgB,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CAAC,CACjDtE,oCAAoC,CAAC,EAAE,CAAC,CACxC,GAAI+C,QAAQ,CAACoF,oBAAoB,CAAE,CACjCnI,oCAAoC,CAAC+C,QAAQ,CAACoF,oBAAoB,CAAC,CACrE,CACA;AACF,CACF,CAAC,CAAE,CAACpF,QAAQ,CAAC,CAAC,CAEd,mBACE/K,IAAA,CAACb,aAAa,EAAA2O,QAAA,cACZ5N,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf5N,KAAA,QAAK2N,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAEtD9N,IAAA,MAAGoQ,IAAI,CAAC,YAAY,CAAAtC,QAAA,cAClB5N,KAAA,QAAK2N,SAAS,CAAC,+CAA+C,CAAAC,QAAA,eAC5D9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnB9N,IAAA,SACEyQ,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN3Q,IAAA,SAAM6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ9N,IAAA,SAAA8N,QAAA,cACE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,SAAS,CAAAC,QAAA,cAEnB9N,IAAA,SACEyQ,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP3Q,IAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAC,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7C9N,IAAA,OAAI6N,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAAC,WAEpE,CAAI,CAAC,CACF,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,mIAAmI,CAAAC,QAAA,cAChJ5N,KAAA,QAAK2N,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC5N,KAAA,QAAK2N,SAAS,CAAC,2DAA2D,CAAAC,QAAA,eACxE9N,IAAA,QAAK6N,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G1N,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAEwI,GAAG,CAAC,CAACiI,IAAI,CAAExQ,KAAK,gBAC1BF,KAAA,QACE;AACA2N,SAAS,CAAC,2EAA2E,CAAAC,QAAA,EAEpFlE,UAAU,CAAGgH,IAAI,CAACxQ,KAAK,cACtBJ,IAAA,QAAK6N,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjH9N,IAAA,QACE6Q,GAAG,CAAEzR,eAAgB,CACrByO,SAAS,CAAC,QAAQ,CAClBiD,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJjH,UAAU,GAAKgH,IAAI,CAACxQ,KAAK,cAC3BJ,IAAA,QAAK6N,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExE7N,IAAA,QAAK6N,SAAS,CAAC,oGAAoG,CAAAC,QAAA,cACjH9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,QAAQ,CAAAC,QAAA,cAElB9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDzQ,KAAA,QAAK2N,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC9N,IAAA,QAAK6N,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAE8C,IAAI,CAACvQ,KAAK,CAAM,CAAC,CACtDuJ,UAAU,GAAKgH,IAAI,CAACxQ,KAAK,cACxBJ,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAChD8C,IAAI,CAACtQ,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAK2N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,EAEtDlE,UAAU,GAAK,CAAC,cACf1J,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9N,IAAA,QAAK6N,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,aACjC,cAAA9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,yBAAAqD,MAAA,CACP/P,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpCgQ,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBzD,KAAK,CAAE1M,SAAU,CACjBoQ,QAAQ,CAAGC,CAAC,EAAKpQ,YAAY,CAACoQ,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC/C,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3M,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAE7C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBzD,KAAK,CAAEtM,QAAS,CAChBgQ,QAAQ,CAAGC,CAAC,EAAKhQ,WAAW,CAACgQ,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENzN,KAAA,QAAK2N,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,yBAAAqD,MAAA,CACPvP,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCwP,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BzD,KAAK,CAAElM,KAAM,CACb4P,QAAQ,CAAGC,CAAC,EAAK5P,QAAQ,CAAC4P,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC3C,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnM,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENzB,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,QACrC,cAAA9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,wBAAAqD,MAAA,CACP/O,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCgP,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBzD,KAAK,CAAE1L,KAAM,CACboP,QAAQ,CAAGC,CAAC,EAAKpP,QAAQ,CAACoP,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC3C,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3L,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENjC,KAAA,QAAK2N,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5N,KAAA,QAAK2N,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,UACpC,cAAA9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACN,MAAM,EACLiO,KAAK,CAAE9K,OAAQ,CACfwO,QAAQ,CAAG3D,MAAM,EAAK,CACpB5K,UAAU,CAAC4K,MAAM,CAAC,CACpB,CAAE,CACFG,SAAS,CAAC,SAAS,CACnB0D,OAAO,CAAEzR,SAAS,CAAC6I,GAAG,CAAE9F,OAAO,GAAM,CACnC8K,KAAK,CAAE9K,OAAO,CAACxC,KAAK,CACpBuN,KAAK,cACH1N,KAAA,QACE2N,SAAS,IAAAqD,MAAA,CACPrO,OAAO,CAACxC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAAyN,QAAA,eAE9B9N,IAAA,SAAM6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAEjL,OAAO,CAACkL,IAAI,CAAO,CAAC,cAC5C/N,IAAA,SAAA8N,QAAA,CAAOjL,OAAO,CAACxC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJ+Q,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5H,KAAK,IAAM,CACzB,GAAG4H,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE9O,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvB+O,SAAS,CAAE/H,KAAK,CAACgI,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFnE,MAAM,CAAGiE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhS,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC/K,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN7C,KAAA,QAAK2N,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,cACxD5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,yBAAAqD,MAAA,CACPvO,SAAS,CAAG,eAAe,CAAG,kBAAkB,sCACb,CACrCwO,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBzD,KAAK,CAAElL,IAAK,CACZ4O,QAAQ,CAAGC,CAAC,EAAK5O,OAAO,CAAC4O,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC1C,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnL,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENzC,KAAA,QAAK2N,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACN,MAAM,EACLiO,KAAK,CAAElH,gBAAiB,CACxB4K,QAAQ,CAAG3D,MAAM,EAAK,CACpBhH,mBAAmB,CAACgH,MAAM,CAAC,CAC7B,CAAE,CACF6D,OAAO,CAAE/G,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAE6G,SAAS,GAAM,CACvC7B,KAAK,CAAE6B,SAAS,CAACxO,EAAE,CACnB4M,KAAK,CAAE4B,SAAS,CAACO,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJmC,YAAY,CAAEA,CAACxE,MAAM,CAAEyE,UAAU,GAC/BzE,MAAM,CAACE,KAAK,CACTwE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvE,SAAS,CAAC,SAAS,CACnBuD,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5H,KAAK,IAAM,CACzB,GAAG4H,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElL,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBmL,SAAS,CAAE/H,KAAK,CAACgI,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFnE,MAAM,CAAGiE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhS,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnH,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNzG,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,QAE9C,CAAK,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,yBAAAqD,MAAA,CACPnK,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCoK,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,QAAQ,CACpBzD,KAAK,CAAE9G,eAAgB,CACvBwK,QAAQ,CAAGC,CAAC,EAAKxK,kBAAkB,CAACwK,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACrD,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC/G,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAEN/G,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxB9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,CAACN,MAAM,EACLiO,KAAK,CAAE1K,WAAY,CACnBoO,QAAQ,CAAG3D,MAAM,EAAK,CACpBxK,cAAc,CAACwK,MAAM,CAAC,CACxB,CAAE,CACFG,SAAS,CAAC,SAAS,CACnB0D,OAAO,CAAErG,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEvC,GAAG,CAAE2F,IAAI,GAAM,CACpCX,KAAK,CAAEW,IAAI,CAACtN,EAAE,CACd4M,KAAK,CAAEU,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ2D,YAAY,CAAEA,CAACxE,MAAM,CAAEyE,UAAU,GAC/BzE,MAAM,CAACE,KAAK,CACTwE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhB,WAAW,CAAC,uBAAuB,CACnCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5H,KAAK,IAAM,CACzB,GAAG4H,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE1O,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvB2O,SAAS,CAAE/H,KAAK,CAACgI,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFnE,MAAM,CAAGiE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACFhS,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3K,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,cAENjD,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtB9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE9N,IAAA,UACE6N,SAAS,yBAAAqD,MAAA,CACP3N,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CACpC4N,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCzD,KAAK,CAAEtK,QAAS,CAChBgO,QAAQ,CAAGC,CAAC,EAAKhO,WAAW,CAACgO,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC9C,CAAC,cACF3N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCvK,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENvD,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,OACvC,cAAA9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,WACEyN,KAAK,CAAElK,QAAS,CAChB4N,QAAQ,CAAGC,CAAC,EAAK5N,WAAW,CAAC4N,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC7CE,SAAS,yBAAAqD,MAAA,CACPvN,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CAAAmK,QAAA,eAEpC9N,IAAA,WAAQ2N,KAAK,CAAE,EAAG,CAAAG,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvC9N,IAAA,WAAQ2N,KAAK,CAAE,SAAU,CAAAG,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C9N,IAAA,WAAQ2N,KAAK,CAAE,WAAY,CAAAG,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT9N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrCnK,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN3D,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,aAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,aACE2N,KAAK,CAAE9J,eAAgB,CACvByO,IAAI,CAAE,CAAE,CACRjB,QAAQ,CAAGC,CAAC,EAAKxN,kBAAkB,CAACwN,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACpDE,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN7N,IAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1D9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBpR,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBoB,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,mBAAmB,CAAC,EAAE,CAAC,CACvBR,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CAEnB,GAAI/B,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CoR,KAAK,CAAG,KAAK,CACf,CAEA,GAAIvQ,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCoQ,KAAK,CAAG,KAAK,CACf,CAEA,GAAI3P,OAAO,GAAK,EAAE,EAAIA,OAAO,CAAC8K,KAAK,GAAK,EAAE,CAAE,CAC1C3K,eAAe,CAAC,yBAAyB,CAAC,CAC1CwP,KAAK,CAAG,KAAK,CACf,CAEA,GAAIvP,WAAW,GAAK,EAAE,EAAIA,WAAW,CAAC0K,KAAK,GAAK,EAAE,CAAE,CAClDvK,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CoP,KAAK,CAAG,KAAK,CACf,CAEA,GAAI/O,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3C4O,KAAK,CAAG,KAAK,CACf,CACA,GAAInP,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CgP,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACT3I,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxK,KAAK,CAACoT,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF5E,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlE,UAAU,GAAK,CAAC,cACf1J,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9N,IAAA,QAAK6N,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD9N,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC5N,KAAA,QAAK2N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,EAAC,SACrC,cAAA9N,IAAA,WAAQ6N,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN5N,KAAA,QAAA4N,QAAA,eACE5N,KAAA,WACEyN,KAAK,CAAE1J,eAAgB,CACvBoN,QAAQ,CAAGC,CAAC,EAAKpN,kBAAkB,CAACoN,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACpDE,SAAS,wBAAAqD,MAAA,CACP/M,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CAAA2J,QAAA,eAErC9N,IAAA,WAAQ2N,KAAK,CAAE,EAAG,CAAAG,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC9N,IAAA,WAAQ2N,KAAK,CAAE,sBAAuB,CAAAG,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACT9N,IAAA,WAAQ2N,KAAK,CAAE,yBAA0B,CAAAG,QAAA,CAAC,2BAE1C,CAAQ,CAAC,cACT9N,IAAA,WAAQ2N,KAAK,CAAE,6BAA8B,CAAAG,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACT9N,IAAA,WACE2N,KAAK,CAAE,qCAAsC,CAAAG,QAAA,CAC9C,qCAED,CAAQ,CAAC,cACT9N,IAAA,WAAQ2N,KAAK,CAAE,kCAAmC,CAAAG,QAAA,CAAC,mCAEnD,CAAQ,CAAC,cACT9N,IAAA,WAAQ2N,KAAK,CAAE,mBAAoB,CAAAG,QAAA,CAAC,mBAEpC,CAAQ,CAAC,EACH,CAAC,cACT9N,IAAA,QAAK6N,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrC3J,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENnE,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BzD,KAAK,CAAEtJ,eAAgB,CACvBgN,QAAQ,CAAGC,CAAC,EAAKhN,kBAAkB,CAACgN,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAENzN,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BzD,KAAK,CAAElJ,eAAgB,CACvB4M,QAAQ,CAAGC,CAAC,EAAK5M,kBAAkB,CAAC4M,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN3N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD9N,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,CAACN,MAAM,EACLiO,KAAK,CAAE9I,YAAa,CACpBwM,QAAQ,CAAG3D,MAAM,EAAK,CACpB5I,eAAe,CAAC4I,MAAM,CAAC,CACzB,CAAE,CACFG,SAAS,CAAC,SAAS,CACnB0D,OAAO,CAAEpH,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAE2F,IAAI,GAAM,CACjCX,KAAK,CAAEW,IAAI,CAACtN,EAAE,CACd4M,KAAK,CAAEU,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJ2D,YAAY,CAAEA,CAACxE,MAAM,CAAEyE,UAAU,GAC/BzE,MAAM,CAACE,KAAK,CACTwE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDhB,WAAW,CAAC,oBAAoB,CAChCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5H,KAAK,IAAM,CACzB,GAAG4H,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE9M,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvB+M,SAAS,CAAE/H,KAAK,CAACgI,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFnE,MAAM,CAAGiE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN9R,KAAA,QAAK2N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBpO,uBAAuB,CAAC,EAAE,CAAC,CAE3B,GAAIH,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CAAC,yBAAyB,CAAC,CAClDoO,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT3I,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACLxK,KAAK,CAACoT,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF5E,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlE,UAAU,GAAK,CAAC,cACf1J,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9N,IAAA,QAAK6N,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,WACMkI,0BAA0B,CAAC,CAAEyF,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElF9N,IAAA,aAAWsI,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5CtI,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN3Q,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAO0S,KAAK,CAAEnS,eAAgB,CAAAuN,QAAA,cAC5B5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCnG,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CACvBgL,MAAM,CAAE/J,IAAI,EAAK,CAACnB,WAAW,CAAC4K,QAAQ,CAACzJ,IAAI,CAAC5H,EAAE,CAAC,CAAC,CACjD2H,GAAG,CAAC,CAACC,IAAI,CAAExI,KAAK,gBACfF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACiK,SAAS,CACZ,CAAC,cACN3S,KAAA,QAAA4N,QAAA,EACGgF,UAAU,CAAClK,IAAI,CAACmK,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb7K,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC5H,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF6M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/H,IAAI,CAACiK,SA0CP,CACN,CAAC,CACH5K,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAExI,KAAK,gBAC3CF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACqK,IAAI,CACP,CAAC,cACN/S,KAAA,QAAA4N,QAAA,EACG,CAAClF,IAAI,CAACsK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACbrK,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAACiK,MAAM,CACd,CAACQ,CAAC,CAAEC,aAAa,GACfhT,KAAK,GAAKgT,aACd,CACF,CAAC,CACH,CAAE,CACFvF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ/H,IAAI,CAACqK,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN/S,KAAA,QAAK2N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlE,UAAU,GAAK,CAAC,cACf1J,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9N,IAAA,QAAK6N,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,UAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCzD,KAAK,CAAE9H,aAAc,CACrBwL,QAAQ,CAAGC,CAAC,EAAKxL,gBAAgB,CAACwL,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAENzN,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCzD,KAAK,CAAE1H,UAAW,CAClBoL,QAAQ,CAAGC,CAAC,EAAKpL,aAAa,CAACoL,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN3N,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BzD,KAAK,CAAEtH,MAAO,CACdgL,QAAQ,CAAGC,CAAC,EAAKhL,SAAS,CAACgL,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN3N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,WACMoJ,yBAAyB,CAAC,CAAEuE,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElF9N,IAAA,aAAWuJ,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3CvJ,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN3Q,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAO0S,KAAK,CAAEnS,eAAgB,CAAAuN,QAAA,cAC5B5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnCjG,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CACf8K,MAAM,CAAE/J,IAAI,EAAK,CAACnB,WAAW,CAAC4K,QAAQ,CAACzJ,IAAI,CAAC5H,EAAE,CAAC,CAAC,CACjD2H,GAAG,CAAC,CAACC,IAAI,CAAExI,KAAK,gBACfF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACiK,SAAS,CACZ,CAAC,cACN3S,KAAA,QAAA4N,QAAA,EACGgF,UAAU,CAAClK,IAAI,CAACmK,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb7K,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC5H,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF6M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/H,IAAI,CAACiK,SA0CP,CACN,CAAC,CACHzJ,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAExI,KAAK,gBACnCF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACqK,IAAI,CACP,CAAC,cACN/S,KAAA,QAAA4N,QAAA,EACG,CAAClF,IAAI,CAACsK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACblJ,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAACiK,MAAM,CACd,CAACQ,CAAC,CAAEC,aAAa,GACfhT,KAAK,GAAKgT,aACd,CACF,CAAC,CACH,CAAE,CACFvF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJ/H,IAAI,CAACqK,IA+CP,CACN,CAAC,EACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN/S,KAAA,QAAK2N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPlE,UAAU,GAAK,CAAC,cACf1J,KAAA,QAAK2N,SAAS,CAAC,EAAE,CAAAC,QAAA,eACf9N,IAAA,QAAK6N,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5N,KAAA,QAAK2N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1C5N,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,CAACN,MAAM,EACLiO,KAAK,CAAElH,gBAAiB,CACxB4K,QAAQ,CAAG3D,MAAM,EAAK,CACpBhH,mBAAmB,CAACgH,MAAM,CAAC,CAC7B,CAAE,CACF6D,OAAO,CAAE/G,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAE7B,GAAG,CAAE6G,SAAS,GAAM,CACvC7B,KAAK,CAAE6B,SAAS,CAACxO,EAAE,CACnB4M,KAAK,CAAE4B,SAAS,CAACO,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJmC,YAAY,CAAEA,CAACxE,MAAM,CAAEyE,UAAU,GAC/BzE,MAAM,CAACE,KAAK,CACTwE,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvE,SAAS,CAAC,SAAS,CACnBuD,WAAW,CAAC,qBAAqB,CACjCI,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAE5H,KAAK,IAAM,CACzB,GAAG4H,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElL,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBmL,SAAS,CAAE/H,KAAK,CAACgI,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFnE,MAAM,CAAGiE,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPnR,OAAO,CAAE,MAAM,CACfwR,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN9R,KAAA,QAAK2N,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE9N,IAAA,UACE6N,SAAS,CAAC,wEAAwE,CAClFsD,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BzD,KAAK,CAAE1G,YAAa,CACpBoK,QAAQ,CAAGC,CAAC,EAAKpK,eAAe,CAACoK,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN3N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD9N,IAAA,QAAK6N,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C5N,KAAA,QAAK2N,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC9N,IAAA,QAAK6N,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN9N,IAAA,QAAA8N,QAAA,cACE5N,KAAA,WACEyN,KAAK,CAAEtG,aAAc,CACrBgK,QAAQ,CAAGC,CAAC,EAAKhK,gBAAgB,CAACgK,CAAC,CAACN,MAAM,CAACrD,KAAK,CAAE,CAClDE,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElF9N,IAAA,WAAQ2N,KAAK,CAAE,EAAG,CAAAG,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC9N,IAAA,WAAQ2N,KAAK,CAAE,SAAU,CAAAG,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C9N,IAAA,WAAQ2N,KAAK,CAAE,UAAW,CAAAG,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5C9N,IAAA,WAAQ2N,KAAK,CAAE,QAAS,CAAAG,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN9N,IAAA,QAAK6N,SAAS,CAAC,0CAA0C,CAAAC,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN5N,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD5N,KAAA,WACMwJ,wCAAwC,CAAC,CAC3CmE,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wEAAwE,CAAAC,QAAA,eAElF9N,IAAA,aAAW2J,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1D3J,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnB9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cAE3D9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACN3Q,IAAA,QAAK6N,SAAS,CAAC,MAAM,CAAAC,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN9N,IAAA,UAAO0S,KAAK,CAAEnS,eAAgB,CAAAuN,QAAA,cAC5B5N,KAAA,QAAK2N,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACnC/F,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAC9B4K,MAAM,CAAE/J,IAAI,EAAK,CAACnB,WAAW,CAAC4K,QAAQ,CAACzJ,IAAI,CAAC5H,EAAE,CAAC,CAAC,CACjD2H,GAAG,CAAC,CAACC,IAAI,CAAExI,KAAK,gBACfF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACiK,SAAS,CACZ,CAAC,cACN3S,KAAA,QAAA4N,QAAA,EACGgF,UAAU,CAAClK,IAAI,CAACmK,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC,KACzC,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb7K,cAAc,CAAC,CAAC,GAAGD,WAAW,CAAEmB,IAAI,CAAC5H,EAAE,CAAC,CAAC,CAC3C,CAAE,CACF6M,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAzCJ/H,IAAI,CAACiK,SA0CP,CACN,CAAC,CACHrJ,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAExI,KAAK,gBACVF,KAAA,QACE2N,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eAGpF9N,IAAA,QAAK6N,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/E5N,KAAA,QACEmQ,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBsC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,eAEd9N,IAAA,SAAM2Q,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChO3Q,IAAA,SAAM2Q,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNzQ,KAAA,QAAK2N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,eACjD9N,IAAA,QAAK6N,SAAS,CAAC,gFAAgF,CAAAC,QAAA,CAC5FlF,IAAI,CAACqK,IAAI,CACP,CAAC,cACN/S,KAAA,QAAA4N,QAAA,EACG,CAAClF,IAAI,CAACsK,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEF,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNhT,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM,CACb9I,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAACiK,MAAM,CACd,CAACQ,CAAC,CAAEC,aAAa,GACfhT,KAAK,GAAKgT,aACd,CACJ,CAAC,CACH,CAAE,CACFvF,SAAS,CAAC,wDAAwD,CAAAC,QAAA,cAElE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBoC,KAAK,CAAC,QAAQ,CAAA9E,QAAA,cAEd9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJ/H,IAAI,CAACqK,IAgDP,CAET,CAAC,EACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN/S,KAAA,QAAK2N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1D9N,IAAA,WACEuS,OAAO,CAAEA,CAAA,GAAM1I,aAAa,CAAC,CAAC,CAAE,CAChCgE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,CACxE,MAED,CAAQ,CAAC,cACT9N,IAAA,WACEqT,QAAQ,CAAE/H,iBAAkB,CAC5BiH,OAAO,CAAE,KAAAA,CAAA,GAAY,KAAAe,kBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CACnB;AACA,KAAM,CAAAzS,QAAQ,CACZtB,UAAU,CAACuB,EAAE,CAAE,CACbgM,UAAU,CAAE/L,SAAS,CACrBgM,SAAS,CAAE5L,QAAQ,CACnBkN,SAAS,CAAEtN,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrC6L,SAAS,CAAErL,SAAS,SAATA,SAAS,UAATA,SAAS,CAAI,EAAE,CAC1BsL,aAAa,CAAElL,KAAK,CACpBmL,aAAa,CAAE3L,KAAK,CACpB4L,eAAe,CAAEhL,OAAO,CACxB2L,YAAY,CAAEvL,IAAI,CAClB8K,eAAe,CAAE1K,OAAO,CAAC8K,KAAK,CAC9B;AACA1K,WAAW,EAAAqQ,kBAAA,CAAErQ,WAAW,CAAC0K,KAAK,UAAA2F,kBAAA,UAAAA,kBAAA,CAAI,EAAE,CACpC9E,SAAS,CAAEnL,QAAQ,CACnBoL,SAAS,CAAEhL,QAAQ,CACnBiL,gBAAgB,CAAE7K,eAAe,CACjC;AACA8K,mBAAmB,CAAE1K,eAAe,CACpC2K,gBAAgB,CAAEvK,eAAe,CACjCwK,gBAAgB,CAAEpK,eAAe,CACjCqK,QAAQ,EAAAyE,mBAAA,CAAE1O,YAAY,CAAC8I,KAAK,UAAA4F,mBAAA,UAAAA,mBAAA,CAAI,EAAE,CAClC;AACAnE,cAAc,CAAEvJ,aAAa,CAC7BwJ,WAAW,CAAEpJ,UAAU,CACvBqJ,cAAc,CAAEjJ,MAAM,CACtBmJ,SAAS,EAAAgE,qBAAA,CAAE/M,gBAAgB,CAACkH,KAAK,UAAA6F,qBAAA,UAAAA,qBAAA,CAAI,EAAE,CACvCvD,gBAAgB,CAAEpJ,eAAe,CACjCmJ,aAAa,CAAE/I,YAAY,CAC3BiJ,gBAAgB,CAAE7I,aAAa,CAC/B;AACAoM,uBAAuB,CAAExL,0BAA0B,CACnDyL,cAAc,CAAEtK,kBAAkB,CAClCuK,8BAA8B,CAC5BnK,iCAAiC,CACnCoK,aAAa,CAAEnM,WACjB,CAAC,CACH,CAAC,CACH,CAAE,CACFoG,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CAEjExC,iBAAiB,CAAG,WAAW,CAAG,QAAQ,CACrC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACf5J,IAAA,QAAK6N,SAAS,CAAC,EAAE,CAAAC,QAAA,cACf9N,IAAA,QAAK6N,SAAS,CAAC,oCAAoC,CAAAC,QAAA,cACjD5N,KAAA,QAAK2N,SAAS,CAAC,oDAAoD,CAAAC,QAAA,eACjE9N,IAAA,QACEqQ,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB3C,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cAE9E9N,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2Q,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACN3Q,IAAA,QAAK6N,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4BAExD,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACN9N,IAAA,QAAK6N,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAS1D9N,IAAA,MACEoQ,IAAI,CAAC,YAAY,CACjBvC,SAAS,CAAC,wDAAwD,CAAAC,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAlN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}