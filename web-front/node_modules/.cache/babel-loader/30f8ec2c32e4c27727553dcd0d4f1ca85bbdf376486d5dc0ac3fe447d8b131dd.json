{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{Link,useLocation,useNavigate,useSearchParams}from\"react-router-dom\";import{useDispatch,useSelector}from\"react-redux\";import{clientList,deleteClient}from\"../../redux/actions/clientActions\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import InputModel from\"../../components/InputModel\";import{Tooltip}from\"react-tooltip\";import\"react-tooltip/dist/react-tooltip.css\";import ConfirmationModal from\"../../components/ConfirmationModal\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function ClientScreen(){const navigate=useNavigate();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const dispatch=useDispatch();const[code,setCode]=useState(\"\");const[firstName,setFirstName]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[gsmPhone,setGsmPhone]=useState(\"\");const[cinNumber,setCinNunber]=useState(\"\");const[permiNumber,setPermiNumber]=useState(\"\");const[orderBy,setOrderBy]=useState(\"-created_at\");const[isUpdate,setIsUpdate]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[clientId,setClientId]=useState(\"\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listClient=useSelector(state=>state.clientList);const{clients,loading,error,pages}=listClient;const clientDelete=useSelector(state=>state.deleteClient);const{loadingClientDelete,errorClientDelete,successClientDelete}=clientDelete;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(clientList(page,code,firstName,lastName,encodeURIComponent(gsmPhone),cinNumber,permiNumber,orderBy));}},[navigate,userInfo,dispatch,page,code,firstName,lastName,gsmPhone,cinNumber,permiNumber,orderBy]);useEffect(()=>{if(successClientDelete){dispatch(clientList(page,code,firstName,lastName,encodeURIComponent(gsmPhone),cinNumber,permiNumber,orderBy));}},[successClientDelete]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Clients\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Gestion des clients\"}),/*#__PURE__*/_jsxs(Link,{to:\"/clients/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Ajouter\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full px-1 py-1 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"N\\xB0\",type:\"text\",placeholder:\"\",value:code,onChange:value=>{setCode(value.target.value);},error:\"\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Nom\",type:\"text\",placeholder:\"\",value:firstName,onChange:value=>{setFirstName(value.target.value);},error:\"\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full px-1 py-1 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"Pr\\xE9nom\",type:\"text\",placeholder:\"\",value:lastName,onChange:value=>{setLastName(value.target.value);},error:\"\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"GSM\",type:\"text\",placeholder:\"\",value:gsmPhone,onChange:value=>{setGsmPhone(value.target.value);},error:\"\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full px-1 py-1 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"N\\xB0 CIN\",type:\"text\",placeholder:\"\",value:cinNumber,onChange:value=>{setCinNunber(value.target.value);},error:\"\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex mx-1 w-full\",children:/*#__PURE__*/_jsx(InputModel,{label:\"N\\xB0 Permis\",type:\"text\",placeholder:\"\",value:permiNumber,onChange:value=>{setPermiNumber(value.target.value);},error:\"\"})})]})]}),loading?/*#__PURE__*/_jsx(Loader,{}):error?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:error}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Nom\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Pr\\xE9nom\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"CIN\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Permis\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Contart\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"GSM\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Email\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"N\\xE9 le\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[clients===null||clients===void 0?void 0:clients.map((client,id)=>{var _client$first_name,_client$last_name,_client$cin_number,_client$permi_number,_client$gsm_phone,_client$email,_client$date_birth;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4    \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:client.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$first_name=client.first_name)!==null&&_client$first_name!==void 0?_client$first_name:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$last_name=client.last_name)!==null&&_client$last_name!==void 0?_client$last_name:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$cin_number=client.cin_number)!==null&&_client$cin_number!==void 0?_client$cin_number:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$permi_number=client.permi_number)!==null&&_client$permi_number!==void 0?_client$permi_number:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:\"0\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$gsm_phone=client.gsm_phone)!==null&&_client$gsm_phone!==void 0?_client$gsm_phone:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$email=client.email)!==null&&_client$email!==void 0?_client$email:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_client$date_birth=client.date_birth)!==null&&_client$date_birth!==void 0?_client$date_birth:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row\",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/clients/edit/\"+client.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"button\",{className:\"mx-1 delete-class\",onClick:()=>{setEventType(\"delete\");setClientId(client.id);setIsUpdate(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 contrat-class\",to:\"/clients/contrat/\"+client.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"})})}),/*#__PURE__*/_jsx(Link,{rel:\"noopener\",target:\"_blank\",className:\"mx-1 declaration-class\",to:baseURLFile+\"/api/clients/4/declaration/\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/clients?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isUpdate,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer ce client ?\":\"Êtes-vous sûr de vouloir ?\",onConfirm:async()=>{if(eventType===\"cancel\"){setIsUpdate(false);setEventType(\"\");setLoadEvent(false);}else if(eventType===\"delete\"&&clientId!==\"\"){setLoadEvent(true);dispatch(deleteClient(clientId));setIsUpdate(false);setEventType(\"\");setLoadEvent(false);}else{setIsUpdate(false);setEventType(\"\");setLoadEvent(false);}},onCancel:()=>{setIsUpdate(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default ClientScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "Link", "useLocation", "useNavigate", "useSearchParams", "useDispatch", "useSelector", "clientList", "deleteClient", "Loader", "<PERSON><PERSON>", "Paginate", "InputModel", "<PERSON><PERSON><PERSON>", "ConfirmationModal", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "ClientScreen", "navigate", "searchParams", "page", "get", "dispatch", "code", "setCode", "firstName", "setFirstName", "lastName", "setLastName", "gsmPhone", "setGsmPhone", "cinNumber", "setCinNunber", "permiNumber", "setPermiNumber", "orderBy", "setOrderBy", "isUpdate", "setIsUpdate", "loadEvent", "setLoadEvent", "eventType", "setEventType", "clientId", "setClientId", "userLogin", "state", "userInfo", "listClient", "clients", "loading", "error", "pages", "clientDelete", "loadingClientDelete", "errorClientDelete", "successClientDelete", "redirect", "encodeURIComponent", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "to", "label", "type", "placeholder", "value", "onChange", "target", "message", "map", "client", "id", "_client$first_name", "_client$last_name", "_client$cin_number", "_client$permi_number", "_client$gsm_phone", "_client$email", "_client$date_birth", "first_name", "last_name", "cin_number", "permi_number", "gsm_phone", "email", "date_birth", "onClick", "rel", "route", "search", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/client/ClientScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { clientList, deleteClient } from \"../../redux/actions/clientActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport InputModel from \"../../components/InputModel\";\n\nimport { Tooltip } from \"react-tooltip\";\nimport \"react-tooltip/dist/react-tooltip.css\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction ClientScreen() {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [code, setCode] = useState(\"\");\n  const [firstName, setFirstName] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [gsmPhone, setGsmPhone] = useState(\"\");\n  const [cinNumber, setCinNunber] = useState(\"\");\n  const [permiNumber, setPermiNumber] = useState(\"\");\n  const [orderBy, setOrderBy] = useState(\"-created_at\");\n\n  const [isUpdate, setIsUpdate] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [clientId, setClientId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients, loading, error, pages } = listClient;\n\n  const clientDelete = useSelector((state) => state.deleteClient);\n  const { loadingClientDelete, errorClientDelete, successClientDelete } =\n    clientDelete;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(\n        clientList(\n          page,\n          code,\n          firstName,\n          lastName,\n          encodeURIComponent(gsmPhone),\n          cinNumber,\n          permiNumber,\n          orderBy\n        )\n      );\n    }\n  }, [\n    navigate,\n    userInfo,\n    dispatch,\n    page,\n    code,\n    firstName,\n    lastName,\n    gsmPhone,\n    cinNumber,\n    permiNumber,\n    orderBy,\n  ]);\n\n  useEffect(() => {\n    if (successClientDelete) {\n      dispatch(\n        clientList(\n          page,\n          code,\n          firstName,\n          lastName,\n          encodeURIComponent(gsmPhone),\n          cinNumber,\n          permiNumber,\n          orderBy\n        )\n      );\n    }\n  }, [successClientDelete]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Clients</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Gestion des clients\n            </h4>\n            <Link\n              to={\"/clients/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n          {/* search */}\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N°\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={code}\n                  onChange={(value) => {\n                    setCode(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n              {/*  */}\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"Nom\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={firstName}\n                  onChange={(value) => {\n                    setFirstName(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"Prénom\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={lastName}\n                  onChange={(value) => {\n                    setLastName(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"GSM\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={gsmPhone}\n                  onChange={(value) => {\n                    setGsmPhone(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N° CIN\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={cinNumber}\n                  onChange={(value) => {\n                    setCinNunber(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N° Permis\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={permiNumber}\n                  onChange={(value) => {\n                    setPermiNumber(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n          </div>\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      N°\n                    </th>\n                    <th className=\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Nom\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prénom\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      CIN\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Permis\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Contart\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      GSM\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Email\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Né le\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {clients?.map((client, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4    \">\n                        <p className=\"text-black  text-xs w-max\">{client.id}</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.first_name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.last_name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.cin_number ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.permi_number ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">0</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.gsm_phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.email ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.date_birth ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/clients/edit/\" + client.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setClientId(client.id);\n                              setIsUpdate(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                          {/* list contrat */}\n                          <Link\n                            className=\"mx-1 contrat-class\"\n                            to={\"/clients/contrat/\" + client.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* declaration */}\n                          <Link\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            className=\"mx-1 declaration-class\"\n                            to={baseURLFile + \"/api/clients/4/declaration/\"}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/clients?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <ConfirmationModal\n          isOpen={isUpdate}\n          message={\n            eventType === \"delete\"\n              ? \"Êtes-vous sûr de vouloir supprimer ce client ?\"\n              : \"Êtes-vous sûr de vouloir ?\"\n          }\n          onConfirm={async () => {\n            if (eventType === \"cancel\") {\n              setIsUpdate(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else if (eventType === \"delete\" && clientId !== \"\") {\n              setLoadEvent(true);\n              dispatch(deleteClient(clientId));\n              setIsUpdate(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            } else {\n              setIsUpdate(false);\n              setEventType(\"\");\n              setLoadEvent(false);\n            }\n          }}\n          onCancel={() => {\n            setIsUpdate(false);\n            setEventType(\"\");\n            setLoadEvent(false);\n          }}\n          loadEvent={loadEvent}\n        />\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ClientScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OACEC,IAAI,CACJC,WAAW,CACXC,WAAW,CACXC,eAAe,KACV,kBAAkB,CACzB,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,UAAU,CAAEC,YAAY,KAAQ,mCAAmC,CAC5E,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,MAAO,CAAAC,UAAU,KAAM,6BAA6B,CAEpD,OAASC,OAAO,KAAQ,eAAe,CACvC,MAAO,sCAAsC,CAC7C,MAAO,CAAAC,iBAAiB,KAAM,oCAAoC,CAClE,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,YAAYA,CAAA,CAAG,CACtB,KAAM,CAAAC,QAAQ,CAAGlB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACmB,YAAY,CAAC,CAAGlB,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAmB,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAC5C,KAAM,CAAAC,QAAQ,CAAGpB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACqB,IAAI,CAAEC,OAAO,CAAC,CAAG5B,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiC,QAAQ,CAAEC,WAAW,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACmC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACqC,WAAW,CAAEC,cAAc,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACuC,OAAO,CAAEC,UAAU,CAAC,CAAGxC,QAAQ,CAAC,aAAa,CAAC,CAErD,KAAM,CAACyC,QAAQ,CAAEC,WAAW,CAAC,CAAG1C,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAAC2C,SAAS,CAAEC,YAAY,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC6C,SAAS,CAAEC,YAAY,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC+C,QAAQ,CAAEC,WAAW,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAE5C,KAAM,CAAAiD,SAAS,CAAG1C,WAAW,CAAE2C,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,UAAU,CAAG7C,WAAW,CAAE2C,KAAK,EAAKA,KAAK,CAAC1C,UAAU,CAAC,CAC3D,KAAM,CAAE6C,OAAO,CAAEC,OAAO,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAGJ,UAAU,CAErD,KAAM,CAAAK,YAAY,CAAGlD,WAAW,CAAE2C,KAAK,EAAKA,KAAK,CAACzC,YAAY,CAAC,CAC/D,KAAM,CAAEiD,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEH,YAAY,CAEd,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpB9D,SAAS,CAAC,IAAM,CACd,GAAI,CAACoD,QAAQ,CAAE,CACb7B,QAAQ,CAACuC,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLnC,QAAQ,CACNlB,UAAU,CACRgB,IAAI,CACJG,IAAI,CACJE,SAAS,CACTE,QAAQ,CACR+B,kBAAkB,CAAC7B,QAAQ,CAAC,CAC5BE,SAAS,CACTE,WAAW,CACXE,OACF,CACF,CAAC,CACH,CACF,CAAC,CAAE,CACDjB,QAAQ,CACR6B,QAAQ,CACRzB,QAAQ,CACRF,IAAI,CACJG,IAAI,CACJE,SAAS,CACTE,QAAQ,CACRE,QAAQ,CACRE,SAAS,CACTE,WAAW,CACXE,OAAO,CACR,CAAC,CAEFxC,SAAS,CAAC,IAAM,CACd,GAAI6D,mBAAmB,CAAE,CACvBlC,QAAQ,CACNlB,UAAU,CACRgB,IAAI,CACJG,IAAI,CACJE,SAAS,CACTE,QAAQ,CACR+B,kBAAkB,CAAC7B,QAAQ,CAAC,CAC5BE,SAAS,CACTE,WAAW,CACXE,OACF,CACF,CAAC,CACH,CACF,CAAC,CAAE,CAACqB,mBAAmB,CAAC,CAAC,CAEzB,mBACE1C,IAAA,CAACjB,aAAa,EAAA8D,QAAA,cACZ3C,KAAA,QAAA2C,QAAA,eACE3C,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD7C,IAAA,MAAG+C,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB3C,KAAA,QAAK4C,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpD,IAAA,SAAM8C,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJ7C,IAAA,SAAA6C,QAAA,cACE7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpD,IAAA,QAAK8C,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,EAC5B,CAAC,cACN3C,KAAA,QAAK4C,SAAS,CAAC,8GAA8G,CAAAD,QAAA,eAC3H3C,KAAA,QAAK4C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/D7C,IAAA,OAAI8C,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,qBAEnE,CAAI,CAAC,cACL3C,KAAA,CAAClB,IAAI,EACHqE,EAAE,CAAE,cAAe,CACnBP,SAAS,CAAC,+DAA+D,CAAAD,QAAA,eAEzE7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,EACJ,CAAC,cAENlD,KAAA,QAAK4C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3C,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD7C,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,OAAI,CACVC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEhD,IAAK,CACZiD,QAAQ,CAAGD,KAAK,EAAK,CACnB/C,OAAO,CAAC+C,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CAC7B,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,cAENrC,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,KAAK,CACXC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE9C,SAAU,CACjB+C,QAAQ,CAAGD,KAAK,EAAK,CACnB7C,YAAY,CAAC6C,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CAClC,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,EACH,CAAC,cAENnC,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD7C,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,WAAQ,CACdC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE5C,QAAS,CAChB6C,QAAQ,CAAGD,KAAK,EAAK,CACnB3C,WAAW,CAAC2C,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CACjC,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,cAENrC,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,KAAK,CACXC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE1C,QAAS,CAChB2C,QAAQ,CAAGD,KAAK,EAAK,CACnBzC,WAAW,CAACyC,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CACjC,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,EACH,CAAC,cAENnC,KAAA,QAAK4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD7C,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,WAAQ,CACdC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAExC,SAAU,CACjByC,QAAQ,CAAGD,KAAK,EAAK,CACnBvC,YAAY,CAACuC,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CAClC,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,cACNrC,IAAA,QAAK8C,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C7C,IAAA,CAACL,UAAU,EACT2D,KAAK,CAAC,cAAW,CACjBC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEtC,WAAY,CACnBuC,QAAQ,CAAGD,KAAK,EAAK,CACnBrC,cAAc,CAACqC,KAAK,CAACE,MAAM,CAACF,KAAK,CAAC,CACpC,CAAE,CACFpB,KAAK,CAAE,EAAG,CACX,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAELD,OAAO,cACNpC,IAAA,CAACR,MAAM,GAAE,CAAC,CACR6C,KAAK,cACPrC,IAAA,CAACP,KAAK,EAAC8D,IAAI,CAAC,OAAO,CAACK,OAAO,CAAEvB,KAAM,CAAE,CAAC,cAEtCnC,KAAA,QAAK4C,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3C,KAAA,UAAO4C,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClC7C,IAAA,UAAA6C,QAAA,cACE3C,KAAA,OAAI4C,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClC7C,IAAA,OAAI8C,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,WAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,QAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,OAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,SAE9D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAER3C,KAAA,UAAA2C,QAAA,EACGV,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE0B,GAAG,CAAC,CAACC,MAAM,CAAEC,EAAE,QAAAC,kBAAA,CAAAC,iBAAA,CAAAC,kBAAA,CAAAC,oBAAA,CAAAC,iBAAA,CAAAC,aAAA,CAAAC,kBAAA,oBACvBpE,KAAA,OAAA2C,QAAA,eACE7C,IAAA,OAAI8C,SAAS,CAAC,mDAAmD,CAAAD,QAAA,cAC/D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAEiB,MAAM,CAACC,EAAE,CAAI,CAAC,CACtD,CAAC,cACL/D,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAmB,kBAAA,CACrCF,MAAM,CAACS,UAAU,UAAAP,kBAAA,UAAAA,kBAAA,CAAI,KAAK,CAC1B,CAAC,CACF,CAAC,cACLhE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAoB,iBAAA,CACrCH,MAAM,CAACU,SAAS,UAAAP,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACLjE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAqB,kBAAA,CACrCJ,MAAM,CAACW,UAAU,UAAAP,kBAAA,UAAAA,kBAAA,CAAI,KAAK,CAC1B,CAAC,CACF,CAAC,cACLlE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAsB,oBAAA,CACrCL,MAAM,CAACY,YAAY,UAAAP,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CAC5B,CAAC,CACF,CAAC,cACLnE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CAAC,GAAC,CAAG,CAAC,CAC5C,CAAC,cACL7C,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAuB,iBAAA,CACrCN,MAAM,CAACa,SAAS,UAAAP,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACLpE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAwB,aAAA,CACrCP,MAAM,CAACc,KAAK,UAAAP,aAAA,UAAAA,aAAA,CAAI,KAAK,CACrB,CAAC,CACF,CAAC,cACLrE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D7C,IAAA,MAAG8C,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAyB,kBAAA,CACrCR,MAAM,CAACe,UAAU,UAAAP,kBAAA,UAAAA,kBAAA,CAAI,KAAK,CAC1B,CAAC,CACF,CAAC,cACLtE,IAAA,OAAI8C,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9D3C,KAAA,MAAG4C,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEpD7C,IAAA,CAAChB,IAAI,EACH8D,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,gBAAgB,CAAGS,MAAM,CAACC,EAAG,CAAAlB,QAAA,cAEjC7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzE7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,cAEPpD,IAAA,WACE8C,SAAS,CAAC,mBAAmB,CAC7BgC,OAAO,CAAEA,CAAA,GAAM,CACblD,YAAY,CAAC,QAAQ,CAAC,CACtBE,WAAW,CAACgC,MAAM,CAACC,EAAE,CAAC,CACtBvC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAE,CAAAqB,QAAA,cAEF7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExE7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACA,CAAC,cAETpD,IAAA,CAAChB,IAAI,EACH8D,SAAS,CAAC,oBAAoB,CAC9BO,EAAE,CAAE,mBAAmB,CAAGS,MAAM,CAACC,EAAG,CAAAlB,QAAA,cAEpC7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtE7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,slBAAslB,CACzlB,CAAC,CACC,CAAC,CACF,CAAC,cAEPpD,IAAA,CAAChB,IAAI,EACH+F,GAAG,CAAC,UAAU,CACdpB,MAAM,CAAC,QAAQ,CACfb,SAAS,CAAC,wBAAwB,CAClCO,EAAE,CAAEvD,WAAW,CAAG,6BAA8B,CAAA+C,QAAA,cAEhD7C,IAAA,QACEgD,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,4DAA4D,CAAAD,QAAA,cAEtE7C,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoD,CAAC,CAAC,mQAAmQ,CACtQ,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACFpD,IAAA,OAAI8C,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACR9C,IAAA,QAAK8C,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf7C,IAAA,CAACN,QAAQ,EACPsF,KAAK,CAAE,WAAY,CACnBC,MAAM,CAAE,EAAG,CACX3E,IAAI,CAAEA,IAAK,CACXgC,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAENtC,IAAA,CAACH,iBAAiB,EAChBqF,MAAM,CAAE3D,QAAS,CACjBqC,OAAO,CACLjC,SAAS,GAAK,QAAQ,CAClB,gDAAgD,CAChD,4BACL,CACDwD,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAIxD,SAAS,GAAK,QAAQ,CAAE,CAC1BH,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,IAAIC,SAAS,GAAK,QAAQ,EAAIE,QAAQ,GAAK,EAAE,CAAE,CACpDH,YAAY,CAAC,IAAI,CAAC,CAClBlB,QAAQ,CAACjB,YAAY,CAACsC,QAAQ,CAAC,CAAC,CAChCL,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLF,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAE,CACF0D,QAAQ,CAAEA,CAAA,GAAM,CACd5D,WAAW,CAAC,KAAK,CAAC,CAClBI,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cACFzB,IAAA,QAAK8C,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAA3C,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}