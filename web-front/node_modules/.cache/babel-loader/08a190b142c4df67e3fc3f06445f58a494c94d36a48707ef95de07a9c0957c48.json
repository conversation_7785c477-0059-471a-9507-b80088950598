{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { matchesType } from '../../utils/matchesType';\nimport { HOVER } from './types';\nexport function createHover(manager) {\n  return function hover(targetIdsArg) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n      clientOffset = _ref.clientOffset;\n    verifyTargetIdsIsArray(targetIdsArg);\n    var targetIds = targetIdsArg.slice(0);\n    var monitor = manager.getMonitor();\n    var registry = manager.getRegistry();\n    checkInvariants(targetIds, monitor, registry);\n    var draggedItemType = monitor.getItemType();\n    removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n    hoverAllTargets(targetIds, monitor, registry);\n    return {\n      type: HOVER,\n      payload: {\n        targetIds: targetIds,\n        clientOffset: clientOffset || null\n      }\n    };\n  };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n  invariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n  invariant(monitor.isDragging(), 'Cannot call hover while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call hover after drop.');\n  for (var i = 0; i < targetIds.length; i++) {\n    var targetId = targetIds[i];\n    invariant(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n    var target = registry.getTarget(targetId);\n    invariant(target, 'Expected targetIds to be registered.');\n  }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n  // Remove those targetIds that don't match the targetType.  This\n  // fixes shallow isOver which would only be non-shallow because of\n  // non-matching targets.\n  for (var i = targetIds.length - 1; i >= 0; i--) {\n    var targetId = targetIds[i];\n    var targetType = registry.getTargetType(targetId);\n    if (!matchesType(targetType, draggedItemType)) {\n      targetIds.splice(i, 1);\n    }\n  }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n  // Finally call hover on all matching targets.\n  targetIds.forEach(function (targetId) {\n    var target = registry.getTarget(targetId);\n    target.hover(monitor, targetId);\n  });\n}", "map": {"version": 3, "names": ["invariant", "matchesType", "HOVER", "createHover", "manager", "hover", "targetIdsArg", "_ref", "arguments", "length", "undefined", "clientOffset", "verifyTargetIdsIsArray", "targetIds", "slice", "monitor", "getMonitor", "registry", "getRegistry", "checkInvariants", "draggedItemType", "getItemType", "removeNonMatchingTargetIds", "hoverAllTargets", "type", "payload", "Array", "isArray", "isDragging", "didDrop", "i", "targetId", "lastIndexOf", "target", "get<PERSON><PERSON><PERSON>", "targetType", "getTargetType", "splice", "for<PERSON>ach"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/dnd-core/dist/esm/actions/dragDrop/hover.js"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant';\nimport { matchesType } from '../../utils/matchesType';\nimport { HOVER } from './types';\nexport function createHover(manager) {\n  return function hover(targetIdsArg) {\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        clientOffset = _ref.clientOffset;\n\n    verifyTargetIdsIsArray(targetIdsArg);\n    var targetIds = targetIdsArg.slice(0);\n    var monitor = manager.getMonitor();\n    var registry = manager.getRegistry();\n    checkInvariants(targetIds, monitor, registry);\n    var draggedItemType = monitor.getItemType();\n    removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n    hoverAllTargets(targetIds, monitor, registry);\n    return {\n      type: HOVER,\n      payload: {\n        targetIds: targetIds,\n        clientOffset: clientOffset || null\n      }\n    };\n  };\n}\n\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n  invariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\n\nfunction checkInvariants(targetIds, monitor, registry) {\n  invariant(monitor.isDragging(), 'Cannot call hover while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call hover after drop.');\n\n  for (var i = 0; i < targetIds.length; i++) {\n    var targetId = targetIds[i];\n    invariant(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n    var target = registry.getTarget(targetId);\n    invariant(target, 'Expected targetIds to be registered.');\n  }\n}\n\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n  // Remove those targetIds that don't match the targetType.  This\n  // fixes shallow isOver which would only be non-shallow because of\n  // non-matching targets.\n  for (var i = targetIds.length - 1; i >= 0; i--) {\n    var targetId = targetIds[i];\n    var targetType = registry.getTargetType(targetId);\n\n    if (!matchesType(targetType, draggedItemType)) {\n      targetIds.splice(i, 1);\n    }\n  }\n}\n\nfunction hoverAllTargets(targetIds, monitor, registry) {\n  // Finally call hover on all matching targets.\n  targetIds.forEach(function (targetId) {\n    var target = registry.getTarget(targetId);\n    target.hover(monitor, targetId);\n  });\n}"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAChD,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,KAAK,QAAQ,SAAS;AAC/B,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,SAASC,KAAKA,CAACC,YAAY,EAAE;IAClC,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7EG,YAAY,GAAGJ,IAAI,CAACI,YAAY;IAEpCC,sBAAsB,CAACN,YAAY,CAAC;IACpC,IAAIO,SAAS,GAAGP,YAAY,CAACQ,KAAK,CAAC,CAAC,CAAC;IACrC,IAAIC,OAAO,GAAGX,OAAO,CAACY,UAAU,CAAC,CAAC;IAClC,IAAIC,QAAQ,GAAGb,OAAO,CAACc,WAAW,CAAC,CAAC;IACpCC,eAAe,CAACN,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;IAC7C,IAAIG,eAAe,GAAGL,OAAO,CAACM,WAAW,CAAC,CAAC;IAC3CC,0BAA0B,CAACT,SAAS,EAAEI,QAAQ,EAAEG,eAAe,CAAC;IAChEG,eAAe,CAACV,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;IAC7C,OAAO;MACLO,IAAI,EAAEtB,KAAK;MACXuB,OAAO,EAAE;QACPZ,SAAS,EAAEA,SAAS;QACpBF,YAAY,EAAEA,YAAY,IAAI;MAChC;IACF,CAAC;EACH,CAAC;AACH;AAEA,SAASC,sBAAsBA,CAACN,YAAY,EAAE;EAC5CN,SAAS,CAAC0B,KAAK,CAACC,OAAO,CAACrB,YAAY,CAAC,EAAE,oCAAoC,CAAC;AAC9E;AAEA,SAASa,eAAeA,CAACN,SAAS,EAAEE,OAAO,EAAEE,QAAQ,EAAE;EACrDjB,SAAS,CAACe,OAAO,CAACa,UAAU,CAAC,CAAC,EAAE,uCAAuC,CAAC;EACxE5B,SAAS,CAAC,CAACe,OAAO,CAACc,OAAO,CAAC,CAAC,EAAE,+BAA+B,CAAC;EAE9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,SAAS,CAACJ,MAAM,EAAEqB,CAAC,EAAE,EAAE;IACzC,IAAIC,QAAQ,GAAGlB,SAAS,CAACiB,CAAC,CAAC;IAC3B9B,SAAS,CAACa,SAAS,CAACmB,WAAW,CAACD,QAAQ,CAAC,KAAKD,CAAC,EAAE,sDAAsD,CAAC;IACxG,IAAIG,MAAM,GAAGhB,QAAQ,CAACiB,SAAS,CAACH,QAAQ,CAAC;IACzC/B,SAAS,CAACiC,MAAM,EAAE,sCAAsC,CAAC;EAC3D;AACF;AAEA,SAASX,0BAA0BA,CAACT,SAAS,EAAEI,QAAQ,EAAEG,eAAe,EAAE;EACxE;EACA;EACA;EACA,KAAK,IAAIU,CAAC,GAAGjB,SAAS,CAACJ,MAAM,GAAG,CAAC,EAAEqB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAIC,QAAQ,GAAGlB,SAAS,CAACiB,CAAC,CAAC;IAC3B,IAAIK,UAAU,GAAGlB,QAAQ,CAACmB,aAAa,CAACL,QAAQ,CAAC;IAEjD,IAAI,CAAC9B,WAAW,CAACkC,UAAU,EAAEf,eAAe,CAAC,EAAE;MAC7CP,SAAS,CAACwB,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC;IACxB;EACF;AACF;AAEA,SAASP,eAAeA,CAACV,SAAS,EAAEE,OAAO,EAAEE,QAAQ,EAAE;EACrD;EACAJ,SAAS,CAACyB,OAAO,CAAC,UAAUP,QAAQ,EAAE;IACpC,IAAIE,MAAM,GAAGhB,QAAQ,CAACiB,SAAS,CAACH,QAAQ,CAAC;IACzCE,MAAM,CAAC5B,KAAK,CAACU,OAAO,EAAEgB,QAAQ,CAAC;EACjC,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}