{"ast": null, "code": "var debug;\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = require(\"debug\")(\"follow-redirects\");\n    } catch (error) {/* */}\n    if (typeof debug !== \"function\") {\n      debug = function () {/* */};\n    }\n  }\n  debug.apply(null, arguments);\n};", "map": {"version": 3, "names": ["debug", "module", "exports", "require", "error", "apply", "arguments"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/follow-redirects/debug.js"], "sourcesContent": ["var debug;\n\nmodule.exports = function () {\n  if (!debug) {\n    try {\n      /* eslint global-require: off */\n      debug = require(\"debug\")(\"follow-redirects\");\n    }\n    catch (error) { /* */ }\n    if (typeof debug !== \"function\") {\n      debug = function () { /* */ };\n    }\n  }\n  debug.apply(null, arguments);\n};\n"], "mappings": "AAAA,IAAIA,KAAK;AAETC,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,IAAI,CAACF,KAAK,EAAE;IACV,IAAI;MACF;MACAA,KAAK,GAAGG,OAAO,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC;IAC9C,CAAC,CACD,OAAOC,KAAK,EAAE,CAAE;IAChB,IAAI,OAAOJ,KAAK,KAAK,UAAU,EAAE;MAC/BA,KAAK,GAAG,SAAAA,CAAA,EAAY,CAAE,MAAO;IAC/B;EACF;EACAA,KAAK,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}