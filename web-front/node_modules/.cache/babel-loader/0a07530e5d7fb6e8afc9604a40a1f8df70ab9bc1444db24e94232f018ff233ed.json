{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase, detailCase, updateCase } from \"../../redux/actions/caseActions\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport Select from \"react-select\";\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STEPSLIST = [{\n  index: 0,\n  title: \"General Information\",\n  description: \"Please enter the general information about the patient and the case.\"\n}, {\n  index: 1,\n  title: \"Coordination Details\",\n  description: \"Provide information about the initial coordination & appointment details for this case.\"\n}, {\n  index: 2,\n  title: \"Medical Reports\",\n  description: \"Upload any initial medical reports related to the case.\"\n}, {\n  index: 3,\n  title: \"Invoices\",\n  description: \"If there are any initial invoices related to the case, please provide the details and upload the documents.\"\n}, {\n  index: 4,\n  title: \"Insurance Authorization\",\n  description: \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"\n}, {\n  index: 5,\n  title: \"Finish\",\n  description: \"You can go back to any step to make changes.\"\n}];\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16\n};\nfunction EditCaseScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState([]);\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n  const [caseDate, setCaseDate] = useState(new Date().toISOString().split(\"T\")[0]);\n  const [caseDateError, setCaseDateError] = useState(\"\");\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  const [isPay, setIsPay] = useState(false);\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState([]);\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [itemsUploadAuthorizationDocuments, setItemsUploadAuthorizationDocuments] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState([]);\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesInitialMedicalReports(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesInitialMedicalReports.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadInvoice(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadInvoice.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [filesUploadAuthorizationDocuments, setFilesUploadAuthorizationDocuments] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments\n  } = useDropzone({\n    accept: {\n      \"*\": []\n    },\n    onDrop: acceptedFiles => {\n      setFilesUploadAuthorizationDocuments(prevFiles => [...prevFiles, ...acceptedFiles.map(file => Object.assign(file, {\n        preview: URL.createObjectURL(file)\n      }))]);\n    }\n  });\n  useEffect(() => {\n    return () => filesUploadAuthorizationDocuments.forEach(file => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listProviders = useSelector(state => state.providerList);\n  const {\n    providers,\n    loadingProviders,\n    errorProviders\n  } = listProviders;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances\n  } = listInsurances;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  const listCoordinators = useSelector(state => state.coordinatorsList);\n  const {\n    coordinators,\n    loadingCoordinators,\n    errorCoordinators\n  } = listCoordinators;\n  const caseUpdate = useSelector(state => state.updateCase);\n  const {\n    loadingCaseUpdate,\n    errorCaseUpdate,\n    successCaseUpdate\n  } = caseUpdate;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n      console.log(CurrencyList.getAll(\"en_US\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      var _caseInfo$currency_pr, _caseInfo$price_tatal, _caseInfo$case_date, _caseInfo$case_type, _caseInfo$case_descri, _caseInfo$case_status, _caseInfo$status_coor, _caseInfo$appointment, _caseInfo$service_loc, _caseInfo$invoice_num, _caseInfo$date_issued, _caseInfo$invoice_amo, _caseInfo$policy_numb, _caseInfo$assurance_n, _caseInfo$assurance_s;\n      if (caseInfo.patient) {\n        var _caseInfo$patient$fir, _caseInfo$patient$las, _caseInfo$patient$bir, _caseInfo$patient$pat, _caseInfo$patient$pat2, _caseInfo$patient$pat3, _caseInfo$patient$pat4, _caseInfo$patient$pat5;\n        setFirstName((_caseInfo$patient$fir = caseInfo.patient.first_name) !== null && _caseInfo$patient$fir !== void 0 ? _caseInfo$patient$fir : \"\");\n        setLastName((_caseInfo$patient$las = caseInfo.patient.last_name) !== null && _caseInfo$patient$las !== void 0 ? _caseInfo$patient$las : \"\");\n        setBirthDate((_caseInfo$patient$bir = caseInfo.patient.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"\");\n        setPhone((_caseInfo$patient$pat = caseInfo.patient.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"\");\n        setEmail((_caseInfo$patient$pat2 = caseInfo.patient.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"\");\n        setAddress((_caseInfo$patient$pat3 = caseInfo.patient.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"\");\n        const patientCountry = (_caseInfo$patient$pat4 = caseInfo.patient.patient_country) !== null && _caseInfo$patient$pat4 !== void 0 ? _caseInfo$patient$pat4 : \"\";\n        const foundCountry = COUNTRIES.find(option => option.title === patientCountry);\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"mr-2\",\n                children: foundCountry.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: foundCountry.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)\n          });\n        } else {\n          setCountry(\"\");\n        }\n        setCity((_caseInfo$patient$pat5 = caseInfo.patient.patient_city) !== null && _caseInfo$patient$pat5 !== void 0 ? _caseInfo$patient$pat5 : \"\");\n      }\n      setIsPay(caseInfo.is_pay);\n      setCurrencyCode((_caseInfo$currency_pr = caseInfo.currency_price) !== null && _caseInfo$currency_pr !== void 0 ? _caseInfo$currency_pr : \"\");\n      setPriceTotal((_caseInfo$price_tatal = caseInfo.price_tatal) !== null && _caseInfo$price_tatal !== void 0 ? _caseInfo$price_tatal : 0);\n      // setCoordinator(caseInfo.coordinator ?? \"\");\n      if (caseInfo.coordinator_user) {\n        var _caseInfo$coordinator, _caseInfo$coordinator2;\n        var initialCoordinator = (_caseInfo$coordinator = (_caseInfo$coordinator2 = caseInfo.coordinator_user) === null || _caseInfo$coordinator2 === void 0 ? void 0 : _caseInfo$coordinator2.id) !== null && _caseInfo$coordinator !== void 0 ? _caseInfo$coordinator : \"\";\n        const foundCoordinator = coordinators === null || coordinators === void 0 ? void 0 : coordinators.find(item => item.id === initialCoordinator);\n        if (foundCoordinator) {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name\n          });\n        } else {\n          setCoordinator(\"\");\n        }\n      }\n      setCaseDate((_caseInfo$case_date = caseInfo.case_date) !== null && _caseInfo$case_date !== void 0 ? _caseInfo$case_date : \"\");\n      setCaseType((_caseInfo$case_type = caseInfo.case_type) !== null && _caseInfo$case_type !== void 0 ? _caseInfo$case_type : \"\");\n      setCaseDescription((_caseInfo$case_descri = caseInfo.case_description) !== null && _caseInfo$case_descri !== void 0 ? _caseInfo$case_descri : \"\");\n      //\n      const statuses = (caseInfo === null || caseInfo === void 0 ? void 0 : (_caseInfo$case_status = caseInfo.case_status) === null || _caseInfo$case_status === void 0 ? void 0 : _caseInfo$case_status.map(status => status === null || status === void 0 ? void 0 : status.status_coordination)) || []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus((_caseInfo$status_coor = caseInfo.status_coordination) !== null && _caseInfo$status_coor !== void 0 ? _caseInfo$status_coor : \"\");\n      setAppointmentDate((_caseInfo$appointment = caseInfo.appointment_date) !== null && _caseInfo$appointment !== void 0 ? _caseInfo$appointment : \"\");\n      setServiceLocation((_caseInfo$service_loc = caseInfo.service_location) !== null && _caseInfo$service_loc !== void 0 ? _caseInfo$service_loc : \"\");\n      if (caseInfo.provider) {\n        var _caseInfo$provider$id, _caseInfo$provider;\n        var initialProvider = (_caseInfo$provider$id = (_caseInfo$provider = caseInfo.provider) === null || _caseInfo$provider === void 0 ? void 0 : _caseInfo$provider.id) !== null && _caseInfo$provider$id !== void 0 ? _caseInfo$provider$id : \"\";\n        const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        var _caseInfo$provider_se;\n        setProviderMultiSelectLast((_caseInfo$provider_se = caseInfo.provider_services) !== null && _caseInfo$provider_se !== void 0 ? _caseInfo$provider_se : []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber((_caseInfo$invoice_num = caseInfo.invoice_number) !== null && _caseInfo$invoice_num !== void 0 ? _caseInfo$invoice_num : \"\");\n      setDateIssued((_caseInfo$date_issued = caseInfo.date_issued) !== null && _caseInfo$date_issued !== void 0 ? _caseInfo$date_issued : \"\");\n      setAmount((_caseInfo$invoice_amo = caseInfo.invoice_amount) !== null && _caseInfo$invoice_amo !== void 0 ? _caseInfo$invoice_amo : 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var _caseInfo$assurance$i, _caseInfo$assurance;\n        var initialInsurance = (_caseInfo$assurance$i = (_caseInfo$assurance = caseInfo.assurance) === null || _caseInfo$assurance === void 0 ? void 0 : _caseInfo$assurance.id) !== null && _caseInfo$assurance$i !== void 0 ? _caseInfo$assurance$i : \"\";\n        var foundInsurance = insurances === null || insurances === void 0 ? void 0 : insurances.find(item => item.id === initialInsurance);\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\"\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\"\n          });\n        }\n      }\n      setPolicyNumber((_caseInfo$policy_numb = caseInfo.policy_number) !== null && _caseInfo$policy_numb !== void 0 ? _caseInfo$policy_numb : \"\");\n      setInsuranceNumber((_caseInfo$assurance_n = caseInfo.assurance_number) !== null && _caseInfo$assurance_n !== void 0 ? _caseInfo$assurance_n : \"\");\n      setInitialStatus((_caseInfo$assurance_s = caseInfo.assurance_status) !== null && _caseInfo$assurance_s !== void 0 ? _caseInfo$assurance_s : \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"Edit Case\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 15\n            }, this), STEPSLIST === null || STEPSLIST === void 0 ? void 0 : STEPSLIST.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              onClick: () => {\n                if (stepSelect > step.index && stepSelect !== 5) {\n                  setStepSelect(step.index);\n                }\n              },\n              className: `flex flex-row mb-3 md:min-h-20 ${stepSelect > step.index && stepSelect !== 5 ? \"cursor-pointer\" : \"\"} md:items-start items-center`,\n              children: [stepSelect < step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: addreactionface,\n                  className: \"size-5\",\n                  onError: e => {\n                    e.target.onerror = null;\n                    e.target.src = \"/assets/placeholder.png\";\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 21\n              }, this) : stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-white z-10  border-[11px] rounded-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  className: \"size-5\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 538,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 529,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-black flex-1 px-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 21\n                }, this), stepSelect === step.index ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs font-light md:block hidden\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 550,\n                  columnNumber: 23\n                }, this) : null]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",\n            children: [stepSelect === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"General Information\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Patient Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 573,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"First Name\",\n                        value: firstName,\n                        onChange: v => setFirstName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 576,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: firstNameError ? firstNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 587,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 575,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 571,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: \"Last Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Last Name\",\n                        value: lastName,\n                        onChange: v => setLastName(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 598,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 597,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Email\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${emailError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"email\",\n                        placeholder: \"Email Address\",\n                        value: email,\n                        onChange: v => setEmail(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 615,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: emailError ? emailError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 624,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 614,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 610,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"phone \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 632,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 631,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: `outline-none border ${phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"Phone no\",\n                        value: phone,\n                        onChange: v => setPhone(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: phoneError ? phoneError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 644,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 634,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 630,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Country \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 654,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: country,\n                        onChange: option => {\n                          setCountry(option);\n                        },\n                        className: \"text-sm\",\n                        options: COUNTRIES.map(country => ({\n                          value: country.title,\n                          label: /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"mr-2\",\n                              children: country.icon\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 671,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: country.title\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 672,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 666,\n                            columnNumber: 33\n                          }, this)\n                        })),\n                        placeholder: \"Select a country...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 657,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: countryError ? countryError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 702,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 656,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 709,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                        apiKey: \"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\",\n                        className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setCity(v.target.value);\n                        },\n                        onPlaceSelected: place => {\n                          if (place && place.geometry) {\n                            var _place$formatted_addr;\n                            setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                            // setCityVl(place.formatted_address ?? \"\");\n                            //   const latitude = place.geometry.location.lat();\n                            //   const longitude = place.geometry.location.lng();\n                            //   setLocationX(latitude ?? \"\");\n                            //   setLocationY(longitude ?? \"\");\n                          }\n                        },\n                        defaultValue: city,\n                        types: [\"city\"],\n                        language: \"en\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 712,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: cityError ? cityError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 743,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 711,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 651,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 752,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 754,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceCompanyError ? insuranceCompanyError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 753,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 751,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"CIA Reference\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 801,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${insuranceNumberError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"text\",\n                        placeholder: \"CIA Reference\",\n                        value: insuranceNumber,\n                        onChange: v => setInsuranceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 805,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: insuranceNumberError ? insuranceNumberError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 816,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 804,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 800,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 750,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Case Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Assigned Coordinator\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 832,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 830,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: coordinator,\n                        onChange: option => {\n                          setCoordinator(option);\n                        },\n                        className: \"text-sm\",\n                        options: coordinators === null || coordinators === void 0 ? void 0 : coordinators.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Coordinator...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: coordinatorError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 835,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatorError ? coordinatorError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 876,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 834,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs mb-1\",\n                      children: [\"Case Creation Date\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 888,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 886,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${caseDateError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        type: \"date\",\n                        placeholder: \"Case Creation Date\",\n                        value: caseDate,\n                        onChange: v => setCaseDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 891,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseDateError ? caseDateError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 902,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 890,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 885,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Type \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 909,\n                        columnNumber: 32\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 908,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        value: caseType,\n                        onChange: v => setCaseType(v.target.value),\n                        className: ` outline-none border ${caseTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Type\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 921,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Medical\",\n                          children: \"Medical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 922,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Technical\",\n                          children: \"Technical\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 923,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 912,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: caseTypeError ? caseTypeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 925,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 884,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Currency Code\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: currencyCode,\n                        onChange: option => {\n                          setCurrencyCode(option);\n                        },\n                        options: CURRENCYITEMS === null || CURRENCYITEMS === void 0 ? void 0 : CURRENCYITEMS.map(currency => ({\n                          value: currency.code,\n                          label: currency.name !== \"\" ? currency.name + \" (\" + currency.code + \") \" || \"\" : \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Currency Code ...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 939,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: currencyCodeError ? currencyCodeError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 986,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 938,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Total Case \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 38\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 992,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        className: ` outline-none border ${priceTotalError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        type: \"number\",\n                        min: 0,\n                        step: 0.01,\n                        placeholder: \"0.00\",\n                        value: priceTotal,\n                        onChange: v => setPriceTotal(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 996,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: priceTotalError ? priceTotalError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1009,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 932,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"ispay\",\n                        id: \"ispay\",\n                        checked: isPay === true,\n                        onChange: v => {\n                          setIsPay(true);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1018,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"ispay\",\n                        children: \"Paid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1027,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        name: \"notpay\",\n                        id: \"notpay\",\n                        checked: isPay === false,\n                        onChange: v => {\n                          setIsPay(false);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1037,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\",\n                        for: \"notpay\",\n                        children: \"Unpaid\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1046,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1036,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1035,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Description\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1059,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n                        value: caseDescription,\n                        rows: 5,\n                        onChange: v => setCaseDescription(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1063,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1058,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setLastNameError(\"\");\n                    setBirthDateError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setAddressError(\"\");\n                    setCaseTypeError(\"\");\n                    setCaseDateError(\"\");\n                    setCoordinatorError(\"\");\n                    setCityError(\"\");\n                    setCountryError(\"\");\n                    setCurrencyCodeError(\"\");\n                    setPriceTotalError(\"\");\n                    if (firstName === \"\") {\n                      setFirstNameError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (phone === \"\") {\n                      setPhoneError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (country === \"\" || country.value === \"\") {\n                      setCountryError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (coordinator === \"\" || coordinator.value === \"\") {\n                      setCoordinatorError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseType === \"\") {\n                      setCaseTypeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (caseDate === \"\") {\n                      setCaseDateError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (currencyCode === \"\" || currencyCode.value === \"\") {\n                      setCurrencyCodeError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (priceTotal === \"\") {\n                      setPriceTotalError(\"This field is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(1);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1076,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this) : null, stepSelect === 1 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Coordination Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Coordination Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: [\"Status \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                        className: \"text-danger\",\n                        children: \"*\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1158,\n                        columnNumber: 34\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1157,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-wrap\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-danger\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"pending-coordination\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"pending-coordination\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"pending-coordination\"));\n                              }\n                            },\n                            id: \"pending-coordination\",\n                            type: \"checkbox\",\n                            checked: coordinatStatusList.includes(\"pending-coordination\"),\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1163,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"pending-coordination\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Pending Coordination\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1190,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1162,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-m-r\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-m-r\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-m-r\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-m-r\"),\n                            id: \"coordinated-Missing-m-r\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1198,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-Missing-m-r\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing M.R.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1225,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1197,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#FFA500]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-missing-invoice\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-missing-invoice\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-missing-invoice\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-missing-invoice\"),\n                            id: \"coordinated-missing-invoice\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1233,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-missing-invoice\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Missing Invoice\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1261,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1232,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"waiting-for-insurance-authorization\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"waiting-for-insurance-authorization\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"waiting-for-insurance-authorization\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"waiting-for-insurance-authorization\"),\n                            id: \"waiting-for-insurance-authorization\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1269,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"waiting-for-insurance-authorization\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Waiting for Insurance Authorization\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1297,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1268,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-primary\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"coordinated-patient-not-seen-yet\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"coordinated-patient-not-seen-yet\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"coordinated-patient-not-seen-yet\"),\n                            id: \"coordinated-patient-not-seen-yet\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1305,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"coordinated-patient-not-seen-yet\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Coordinated, Patient not seen yet\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1333,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1304,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#008000]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"fully-coordinated\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"fully-coordinated\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"fully-coordinated\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"fully-coordinated\"),\n                            id: \"fully-coordinated\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1341,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"fully-coordinated\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Fully Coordinated\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1368,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1340,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row text-xs items-center my-3 text-[#d34053]\",\n                          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                            onChange: v => {\n                              if (!coordinatStatusList.includes(\"failed\")) {\n                                setCoordinatStatusList([...coordinatStatusList, \"failed\"]);\n                              } else {\n                                setCoordinatStatusList(coordinatStatusList.filter(status => status !== \"failed\"));\n                              }\n                            },\n                            checked: coordinatStatusList.includes(\"failed\"),\n                            id: \"failed\",\n                            type: \"checkbox\",\n                            className: \"mx-1\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1376,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                            for: \"failed\",\n                            className: \"flex-1 mx-1  cursor-pointer \",\n                            children: \"Failed\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1396,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1375,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1161,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: coordinatStatusListError ? coordinatStatusListError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1436,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1160,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1156,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1155,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Appointment Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1446,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Appointment Date\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1452,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Appointment Date\",\n                        value: appointmentDate,\n                        onChange: v => setAppointmentDate(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1456,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1455,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1451,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Service Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1467,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \" Service Location\",\n                        value: serviceLocation,\n                        onChange: v => setServiceLocation(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1471,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1470,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1466,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1450,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1449,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Provider Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1483,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1489,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(Select, {\n                        value: providerName,\n                        onChange: option => {\n                          var _option$value;\n                          setProviderName(option);\n                          //\n                          var initialProvider = (_option$value = option === null || option === void 0 ? void 0 : option.value) !== null && _option$value !== void 0 ? _option$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => item.id === initialProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic;\n                            setProviderServices((_foundProvider$servic = foundProvider.services) !== null && _foundProvider$servic !== void 0 ? _foundProvider$servic : []);\n                          } else {\n                            setProviderServices([]);\n                          }\n                        },\n                        className: \"text-sm\",\n                        options: providers === null || providers === void 0 ? void 0 : providers.map(item => ({\n                          value: item.id,\n                          label: item.full_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        placeholder: \"Select Provider...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: providerNameError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1493,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerNameError ? providerNameError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1546,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1492,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1488,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2  w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Provider Service\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1553,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                        className: `outline-none border ${providerServiceError ? \"border-danger\" : \"border-[#F1F3FF]\"}  px-3 py-2 w-full rounded text-sm`,\n                        onChange: v => {\n                          setProviderService(v.target.value);\n                        },\n                        value: providerService,\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1568,\n                          columnNumber: 29\n                        }, this), providerServices === null || providerServices === void 0 ? void 0 : providerServices.map((service, index) => {\n                          var _service$service_type;\n                          return /*#__PURE__*/_jsxDEV(\"option\", {\n                            value: service.id,\n                            children: [(_service$service_type = service.service_type) !== null && _service$service_type !== void 0 ? _service$service_type : \"\", service.service_specialist !== \"\" ? \" : \" + service.service_specialist : \"\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1570,\n                            columnNumber: 31\n                          }, this);\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1557,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" text-[8px] text-danger\",\n                        children: providerServiceError ? providerServiceError : \"\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1578,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1556,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1552,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      // providerMultiSelect\n                      var check = true;\n                      setProviderNameError(\"\");\n                      setProviderServiceError(\"\");\n                      if (providerName === \"\" || providerName.value === \"\") {\n                        setProviderNameError(\"These fields are required.\");\n                        toast.error(\" Provider is required\");\n                        check = false;\n                      }\n                      if (providerService === \"\") {\n                        setProviderServiceError(\"These fields are required.\");\n                        toast.error(\" Provider Service is required\");\n                        check = false;\n                      }\n                      if (check) {\n                        const exists = providerMultiSelect.some(provider => {\n                          var _provider$provider, _provider$service;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider = provider.provider) === null || _provider$provider === void 0 ? void 0 : _provider$provider.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$service = provider.service) === null || _provider$service === void 0 ? void 0 : _provider$service.id) === String(providerService);\n                        });\n                        const existsLast = providerMultiSelectLast.some(provider => {\n                          var _provider$provider2, _provider$provider_se;\n                          return String(provider === null || provider === void 0 ? void 0 : (_provider$provider2 = provider.provider) === null || _provider$provider2 === void 0 ? void 0 : _provider$provider2.id) === String(providerName.value) && String(provider === null || provider === void 0 ? void 0 : (_provider$provider_se = provider.provider_service) === null || _provider$provider_se === void 0 ? void 0 : _provider$provider_se.id) === String(providerService);\n                        });\n                        if (!exists && !existsLast) {\n                          var _providerName$value;\n                          // find provider\n                          var initialProvider = (_providerName$value = providerName.value) !== null && _providerName$value !== void 0 ? _providerName$value : \"\";\n                          const foundProvider = providers === null || providers === void 0 ? void 0 : providers.find(item => String(item.id) === String(initialProvider));\n                          console.log(foundProvider);\n                          if (foundProvider) {\n                            var _foundProvider$servic2, _foundProvider$servic3;\n                            // found service\n                            var initialService = providerService !== null && providerService !== void 0 ? providerService : \"\";\n                            foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic2 = foundProvider.services) === null || _foundProvider$servic2 === void 0 ? void 0 : _foundProvider$servic2.forEach(element => {\n                              console.log(element.id);\n                            });\n                            const foundService = foundProvider === null || foundProvider === void 0 ? void 0 : (_foundProvider$servic3 = foundProvider.services) === null || _foundProvider$servic3 === void 0 ? void 0 : _foundProvider$servic3.find(item => String(item.id) === String(initialService));\n                            if (foundService) {\n                              // Add the new item if it doesn't exist\n                              setProviderMultiSelect([...providerMultiSelect, {\n                                provider: foundProvider,\n                                service: foundService\n                              }]);\n                              setProviderName(\"\");\n                              setProviderService(\"\");\n                              console.log(providerMultiSelect);\n                            } else {\n                              setProviderNameError(\"This provider service not exist!\");\n                              toast.error(\"This provider service not exist!\");\n                            }\n                          } else {\n                            setProviderNameError(\"This provider not exist!\");\n                            toast.error(\"This provider not exist!\");\n                          }\n                        } else {\n                          setProviderNameError(\"This provider or service is already added!\");\n                          toast.error(\"This provider or service is already added!\");\n                        }\n                      }\n                    },\n                    className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      class: \"size-4\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1694,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1686,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \" Add Provider \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1700,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1586,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                      children: \"Providers\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1703,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"my-2 text-black text-sm\",\n                      children: [providerMultiSelectLast === null || providerMultiSelectLast === void 0 ? void 0 : providerMultiSelectLast.map((itemProvider, index) => {\n                        var _itemProvider$provide, _itemProvider$provide2, _itemProvider$provide3, _itemProvider$provide4, _itemProvider$provide5, _itemProvider$provide6;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelectLast.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelectDelete([...providerMultiSelectDelete, itemProvider.id]);\n                                setProviderMultiSelectLast(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1737,\n                                  columnNumber: 39\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1729,\n                                columnNumber: 37\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1714,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1713,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1747,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide = (_itemProvider$provide2 = itemProvider.provider) === null || _itemProvider$provide2 === void 0 ? void 0 : _itemProvider$provide2.full_name) !== null && _itemProvider$provide !== void 0 ? _itemProvider$provide : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1746,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1751,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide3 = (_itemProvider$provide4 = itemProvider.provider_service) === null || _itemProvider$provide4 === void 0 ? void 0 : _itemProvider$provide4.service_type) !== null && _itemProvider$provide3 !== void 0 ? _itemProvider$provide3 : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1750,\n                              columnNumber: 35\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1756,\n                                columnNumber: 37\n                              }, this), \" \", (_itemProvider$provide5 = (_itemProvider$provide6 = itemProvider.provider_service) === null || _itemProvider$provide6 === void 0 ? void 0 : _itemProvider$provide6.service_specialist) !== null && _itemProvider$provide5 !== void 0 ? _itemProvider$provide5 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1755,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1745,\n                            columnNumber: 33\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1709,\n                          columnNumber: 31\n                        }, this);\n                      }), providerMultiSelect === null || providerMultiSelect === void 0 ? void 0 : providerMultiSelect.map((itemProvider, index) => {\n                        var _itemProvider$provide7, _itemProvider$provide8, _itemProvider$service, _itemProvider$service2, _itemProvider$service3, _itemProvider$service4;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-row items-center my-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"min-w-6 text-center\",\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => {\n                                const updatedServices = providerMultiSelect.filter((_, indexF) => indexF !== index);\n                                setProviderMultiSelect(updatedServices);\n                              },\n                              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                fill: \"none\",\n                                viewBox: \"0 0 24 24\",\n                                \"stroke-width\": \"1.5\",\n                                stroke: \"currentColor\",\n                                class: \"size-6\",\n                                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                                  \"stroke-linecap\": \"round\",\n                                  \"stroke-linejoin\": \"round\",\n                                  d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1787,\n                                  columnNumber: 37\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1779,\n                                columnNumber: 35\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1770,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1769,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex-1 mx-1 border-l px-1\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Provider:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1797,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$provide7 = (_itemProvider$provide8 = itemProvider.provider) === null || _itemProvider$provide8 === void 0 ? void 0 : _itemProvider$provide8.full_name) !== null && _itemProvider$provide7 !== void 0 ? _itemProvider$provide7 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1796,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Service:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1801,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service = (_itemProvider$service2 = itemProvider.service) === null || _itemProvider$service2 === void 0 ? void 0 : _itemProvider$service2.service_type) !== null && _itemProvider$service !== void 0 ? _itemProvider$service : \"--\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1800,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                                children: \"Speciality:\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1805,\n                                columnNumber: 35\n                              }, this), \" \", (_itemProvider$service3 = (_itemProvider$service4 = itemProvider.service) === null || _itemProvider$service4 === void 0 ? void 0 : _itemProvider$service4.service_specialist) !== null && _itemProvider$service3 !== void 0 ? _itemProvider$service3 : \"---\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1804,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1795,\n                            columnNumber: 31\n                          }, this)]\n                        }, index, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1765,\n                          columnNumber: 29\n                        }, this);\n                      })]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1706,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1702,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1585,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(0),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1819,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    var check = true;\n                    setCoordinatStatusError(\"\");\n                    setCoordinatStatusListError(\"\");\n                    if (coordinatStatusList.length === 0) {\n                      setCoordinatStatusListError(\"This fields is required.\");\n                      check = false;\n                    }\n                    if (check) {\n                      setStepSelect(2);\n                    } else {\n                      toast.error(\"Some fields are empty or invalid. please try again\");\n                    }\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1825,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1818,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 17\n            }, this) : null, stepSelect === 2 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Medical Reports\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1856,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Initial Medical Reports:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1860,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsInitialMedical({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsInitialMedical()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1869,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1879,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1871,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1870,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1886,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1864,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsInitialMedicalReports === null || itemsInitialMedicalReports === void 0 ? void 0 : itemsInitialMedicalReports.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1906,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1907,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1900,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1899,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1911,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1914,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1910,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1932,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1924,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1918,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1895,\n                      columnNumber: 29\n                    }, this)), filesInitialMedicalReports === null || filesInitialMedicalReports === void 0 ? void 0 : filesInitialMedicalReports.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1953,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1954,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1947,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1946,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1958,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1961,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1957,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesInitialMedicalReports(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1984,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1976,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1965,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1942,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1891,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1890,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1863,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(1),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1998,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2004,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1855,\n              columnNumber: 17\n            }, this) : null, stepSelect === 3 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Invoices\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2016,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Invoice Information:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2020,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Invoice Number (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2026,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Invoice Number (Optional)\",\n                        value: invoiceNumber,\n                        onChange: v => setInvoiceNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2030,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2029,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2025,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Date Issued (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2041,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"date\",\n                        placeholder: \"Date Issued (Optional)\",\n                        value: dateIssued,\n                        onChange: v => setDateIssued(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2045,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2044,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2040,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2024,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Amount (Optional)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2058,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"number\",\n                        placeholder: \"Amount (Optional)\",\n                        value: amount,\n                        onChange: v => setAmount(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2062,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2061,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2057,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2056,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2023,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Invoice\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2073,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadInvoice({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadInvoice()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2082,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2092,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2084,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2083,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2099,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2077,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadInvoice === null || itemsUploadInvoice === void 0 ? void 0 : itemsUploadInvoice.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2119,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2120,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2113,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2112,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2124,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2127,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2123,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2145,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2137,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2131,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2108,\n                      columnNumber: 29\n                    }, this)), filesUploadInvoice === null || filesUploadInvoice === void 0 ? void 0 : filesUploadInvoice.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2166,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2167,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2160,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2159,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2171,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2174,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2170,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadInvoice(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2197,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2189,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2178,\n                        columnNumber: 29\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2155,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2104,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2103,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2076,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(2),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(4),\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: \"Save & Continue\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2218,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2211,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2015,\n              columnNumber: 17\n            }, this) : null, stepSelect === 4 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#0388A6] font-semibold text-xl\",\n                children: \"Insurance Authorization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2230,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Insurance Details:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Insurance Company Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2240,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(Select, {\n                        value: insuranceCompany,\n                        onChange: option => {\n                          setInsuranceCompany(option);\n                        },\n                        options: insurances === null || insurances === void 0 ? void 0 : insurances.map(assurance => ({\n                          value: assurance.id,\n                          label: assurance.assurance_name || \"\"\n                        })),\n                        filterOption: (option, inputValue) => option.label.toLowerCase().includes(inputValue.toLowerCase()),\n                        className: \"text-sm\",\n                        placeholder: \"Select Insurance...\",\n                        isSearchable: true,\n                        styles: {\n                          control: (base, state) => ({\n                            ...base,\n                            background: \"#fff\",\n                            border: insuranceCompanyError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                            boxShadow: state.isFocused ? \"none\" : \"none\",\n                            \"&:hover\": {\n                              border: \"1px solid #F1F3FF\"\n                            }\n                          }),\n                          option: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          }),\n                          singleValue: base => ({\n                            ...base,\n                            display: \"flex\",\n                            alignItems: \"center\"\n                          })\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2244,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2243,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2239,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Policy Number\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2289,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"input\", {\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        type: \"text\",\n                        placeholder: \"Policy Number\",\n                        value: policyNumber,\n                        onChange: v => setPolicyNumber(v.target.value)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2293,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2292,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2288,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2238,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Authorization Status:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2305,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex md:flex-row flex-col  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full  md:pr-1 my-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-[#B4B4B4] text-xs  mb-1\",\n                      children: \"Initial Status\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2311,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: /*#__PURE__*/_jsxDEV(\"select\", {\n                        value: initialStatus,\n                        onChange: v => setInitialStatus(v.target.value),\n                        className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"\",\n                          children: \"Select Status\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2320,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Pending\",\n                          children: \"Pending\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2321,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Approved\",\n                          children: \"Approved\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2322,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                          value: \"Denied\",\n                          children: \"Denied\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2323,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2315,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2314,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2310,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2309,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs font-medium mt-5 mb-2 text-black\",\n                children: \"Upload Authorization Documents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2330,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  ...getRootPropsUploadAuthorizationDocuments({\n                    className: \"dropzone\"\n                  }),\n                  // style={dropzoneStyle}\n                  className: \"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    ...getInputPropsUploadAuthorizationDocuments()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2341,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      fill: \"none\",\n                      viewBox: \"0 0 24 24\",\n                      \"stroke-width\": \"1.5\",\n                      stroke: \"currentColor\",\n                      className: \"size-8 p-2 bg-[#0388A6] rounded-full text-white\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        \"stroke-linecap\": \"round\",\n                        \"stroke-linejoin\": \"round\",\n                        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2351,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2343,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2342,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-2\",\n                    children: \"Drag & Drop Image File or BROWSE\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2358,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2334,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"aside\", {\n                  style: thumbsContainer,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-full flex flex-col \",\n                    children: [itemsUploadAuthorizationDocuments === null || itemsUploadAuthorizationDocuments === void 0 ? void 0 : itemsUploadAuthorizationDocuments.filter(file => !fileDeleted.includes(file.id)).map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2378,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2379,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2372,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2371,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.file_name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2383,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [parseFloat(file.file_size).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2386,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2382,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFileDeleted([...fileDeleted, file.id]);\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2404,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2396,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2390,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.file_name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2367,\n                      columnNumber: 29\n                    }, this)), filesUploadAuthorizationDocuments === null || filesUploadAuthorizationDocuments === void 0 ? void 0 : filesUploadAuthorizationDocuments.map((file, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          viewBox: \"0 0 24 24\",\n                          fill: \"currentColor\",\n                          class: \"size-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2426,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                            d: \"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2427,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2420,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2419,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 px-5 text-[#303030] text-sm\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",\n                          children: file.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2431,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [(file.size / (1024 * 1024)).toFixed(2), \" mb\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2434,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2430,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setFilesUploadAuthorizationDocuments(prevFiles => prevFiles.filter((_, indexToRemove) => index !== indexToRemove));\n                        },\n                        className: \"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          class: \"size-5\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            \"stroke-linecap\": \"round\",\n                            \"stroke-linejoin\": \"round\",\n                            d: \"M6 18 18 6M6 6l12 12\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 2458,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 2450,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2438,\n                        columnNumber: 31\n                      }, this)]\n                    }, file.name, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2415,\n                      columnNumber: 29\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2363,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2362,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2333,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-row items-center justify-end my-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setStepSelect(3),\n                  className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                  children: \"Back\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  disabled: loadingCaseUpdate,\n                  onClick: async () => {\n                    var _coordinator$value, _providerName$value2, _insuranceCompany$val;\n                    const providerItems = providerMultiSelect.map(item => {\n                      var _item$service, _item$provider;\n                      return {\n                        service: (_item$service = item.service) === null || _item$service === void 0 ? void 0 : _item$service.id,\n                        provider: (_item$provider = item.provider) === null || _item$provider === void 0 ? void 0 : _item$provider.id\n                      };\n                    });\n                    // update\n                    await dispatch(updateCase(id, {\n                      first_name: firstName,\n                      last_name: lastName,\n                      full_name: firstName + \" \" + lastName,\n                      birth_day: birthDate !== null && birthDate !== void 0 ? birthDate : \"\",\n                      patient_phone: phone,\n                      patient_email: email,\n                      patient_address: address,\n                      patient_city: city,\n                      patient_country: country.value,\n                      //\n                      coordinator: (_coordinator$value = coordinator.value) !== null && _coordinator$value !== void 0 ? _coordinator$value : \"\",\n                      case_date: caseDate,\n                      case_type: caseType,\n                      case_description: caseDescription,\n                      //\n                      status_coordination: coordinatStatus,\n                      case_status: coordinatStatusList,\n                      appointment_date: appointmentDate,\n                      service_location: serviceLocation,\n                      provider: (_providerName$value2 = providerName.value) !== null && _providerName$value2 !== void 0 ? _providerName$value2 : \"\",\n                      //\n                      invoice_number: invoiceNumber,\n                      date_issued: dateIssued,\n                      invoice_amount: amount,\n                      assurance: (_insuranceCompany$val = insuranceCompany.value) !== null && _insuranceCompany$val !== void 0 ? _insuranceCompany$val : \"\",\n                      assurance_number: insuranceNumber,\n                      policy_number: policyNumber,\n                      assurance_status: initialStatus,\n                      // files\n                      initial_medical_reports: filesInitialMedicalReports,\n                      upload_invoice: filesUploadInvoice,\n                      upload_authorization_documents: filesUploadAuthorizationDocuments,\n                      files_deleted: fileDeleted,\n                      providers: providerItems !== null && providerItems !== void 0 ? providerItems : [],\n                      providers_deleted: providerMultiSelectDelete !== null && providerMultiSelectDelete !== void 0 ? providerMultiSelectDelete : []\n                    }));\n                  },\n                  className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                  children: loadingCaseUpdate ? \"Loading..\" : \"Update\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2479,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2472,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2229,\n              columnNumber: 17\n            }, this) : null, stepSelect === 5 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 bg-white py-4 px-2 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"min-h-30 flex flex-col items-center justify-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    fill: \"none\",\n                    viewBox: \"0 0 24 24\",\n                    \"stroke-width\": \"1.5\",\n                    stroke: \"currentColor\",\n                    className: \"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      \"stroke-linecap\": \"round\",\n                      \"stroke-linejoin\": \"round\",\n                      d: \"m4.5 12.75 6 6 9-13.5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2550,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2542,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"my-5 font-semibold text-2xl text-black\",\n                    children: \"Case Updated Successfully!\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2556,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-base text-center md:w-2/3 mx-auto w-full px-3\",\n                    children: \"Your case has been successfully updates and saved. You can now view the case details or create another case.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2559,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-row items-center justify-end my-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"a\", {\n                      href: \"/dashboard\",\n                      className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                      children: \"Go to Dahboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2572,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2563,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2541,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2540,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2539,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 450,\n    columnNumber: 5\n  }, this);\n}\n_s(EditCaseScreen, \"1bFE/ENXjad+eYT+zVESLY1AlSA=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useDropzone, useDropzone, useDropzone, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c = EditCaseScreen;\nexport default EditCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"EditCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "detailCase", "updateCase", "GoogleComponent", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "CURRENCYITEMS", "CurrencyList", "jsxDEV", "_jsxDEV", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "EditCaseScreen", "_s", "navigate", "location", "dispatch", "id", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "providerServices", "setProviderServices", "providerMultiSelect", "setProviderMultiSelect", "providerMultiSelectDelete", "setProviderMultiSelectDelete", "providerMultiSelectLast", "setProviderMultiSelectLast", "providerService", "setProviderService", "providerServiceError", "setProviderServiceError", "caseDate", "setCaseDate", "Date", "toISOString", "split", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "isPay", "setIsPay", "currencyCode", "setCurrencyCode", "currencyCodeError", "setCurrencyCodeError", "priceTotal", "setPriceTotal", "priceTotalError", "setPriceTotalError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "coordinatStatusList", "setCoordinatStatusList", "coordinatStatusListError", "setCoordinatStatusListError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "fileDeleted", "setFileDeleted", "itemsInitialMedicalReports", "setItemsInitialMedicalReports", "itemsUploadInvoice", "setItemsUploadInvoice", "itemsUploadAuthorizationDocuments", "setItemsUploadAuthorizationDocuments", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "caseUpdate", "loadingCaseUpdate", "errorCaseUpdate", "successCaseUpdate", "redirect", "console", "log", "getAll", "undefined", "_caseInfo$currency_pr", "_caseInfo$price_tatal", "_caseInfo$case_date", "_caseInfo$case_type", "_caseInfo$case_descri", "_caseInfo$case_status", "_caseInfo$status_coor", "_caseInfo$appointment", "_caseInfo$service_loc", "_caseInfo$invoice_num", "_caseInfo$date_issued", "_caseInfo$invoice_amo", "_caseInfo$policy_numb", "_caseInfo$assurance_n", "_caseInfo$assurance_s", "patient", "_caseInfo$patient$fir", "_caseInfo$patient$las", "_caseInfo$patient$bir", "_caseInfo$patient$pat", "_caseInfo$patient$pat2", "_caseInfo$patient$pat3", "_caseInfo$patient$pat4", "_caseInfo$patient$pat5", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patientCountry", "patient_country", "foundCountry", "find", "option", "value", "label", "className", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "patient_city", "is_pay", "currency_price", "price_tatal", "coordinator_user", "_caseInfo$coordinator", "_caseInfo$coordinator2", "initialCoordinator", "foundCoordinator", "item", "full_name", "case_date", "case_type", "case_description", "statuses", "case_status", "status", "status_coordination", "appointment_date", "service_location", "provider", "_caseInfo$provider$id", "_caseInfo$provider", "initialProvider", "<PERSON><PERSON><PERSON><PERSON>", "provider_services", "_caseInfo$provider_se", "medical_reports", "invoice_number", "date_issued", "invoice_amount", "upload_invoices", "assurance", "_caseInfo$assurance$i", "_caseInfo$assurance", "initialInsurance", "foundInsurance", "assurance_name", "policy_number", "assurance_number", "assurance_status", "upload_authorization", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "onClick", "src", "onError", "e", "target", "onerror", "type", "placeholder", "onChange", "v", "options", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "formatted_address", "defaultValue", "types", "language", "filterOption", "inputValue", "toLowerCase", "includes", "currency", "code", "name", "min", "checked", "for", "rows", "check", "error", "filter", "_option$value", "_foundProvider$servic", "services", "service", "_service$service_type", "service_type", "service_specialist", "exists", "some", "_provider$provider", "_provider$service", "String", "existsLast", "_provider$provider2", "_provider$provider_se", "provider_service", "_providerName$value", "_foundProvider$servic2", "_foundProvider$servic3", "initialService", "element", "foundService", "class", "itemProvider", "_itemProvider$provide", "_itemProvider$provide2", "_itemProvider$provide3", "_itemProvider$provide4", "_itemProvider$provide5", "_itemProvider$provide6", "updatedServices", "_", "indexF", "_itemProvider$provide7", "_itemProvider$provide8", "_itemProvider$service", "_itemProvider$service2", "_itemProvider$service3", "_itemProvider$service4", "length", "style", "file_name", "parseFloat", "file_size", "toFixed", "size", "indexToRemove", "disabled", "_coordinator$value", "_providerName$value2", "_insuranceCompany$val", "providerItems", "_item$service", "_item$provider", "initial_medical_reports", "upload_invoice", "upload_authorization_documents", "files_deleted", "providers_deleted", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/EditCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport {\n  addNewCase,\n  detailCase,\n  updateCase,\n} from \"../../redux/actions/caseActions\";\n\nimport GoogleComponent from \"react-google-autocomplete\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES, CURRENCYITEMS } from \"../../constants\";\nimport CurrencyList from \"currency-list\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction EditCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [providerServices, setProviderServices] = useState([]);\n  const [providerMultiSelect, setProviderMultiSelect] = useState([]);\n  const [providerMultiSelectDelete, setProviderMultiSelectDelete] = useState(\n    []\n  );\n  const [providerMultiSelectLast, setProviderMultiSelectLast] = useState([]);\n\n  const [providerService, setProviderService] = useState(\"\");\n  const [providerServiceError, setProviderServiceError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\n    new Date().toISOString().split(\"T\")[0]\n  );\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n\n  const [isPay, setIsPay] = useState(false);\n\n  const [currencyCode, setCurrencyCode] = useState(\"\");\n  const [currencyCodeError, setCurrencyCodeError] = useState(\"\");\n\n  const [priceTotal, setPriceTotal] = useState(0);\n  const [priceTotalError, setPriceTotalError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [coordinatStatusList, setCoordinatStatusList] = useState([]);\n  const [coordinatStatusListError, setCoordinatStatusListError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fiels deleted\n  const [fileDeleted, setFileDeleted] = useState([]);\n  const [itemsInitialMedicalReports, setItemsInitialMedicalReports] = useState(\n    []\n  );\n  const [itemsUploadInvoice, setItemsUploadInvoice] = useState([]);\n  const [\n    itemsUploadAuthorizationDocuments,\n    setItemsUploadAuthorizationDocuments,\n  ] = useState([]);\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const caseUpdate = useSelector((state) => state.updateCase);\n  const { loadingCaseUpdate, errorCaseUpdate, successCaseUpdate } = caseUpdate;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      dispatch(detailCase(id));\n\n      console.log(CurrencyList.getAll(\"en_US\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseUpdate) {\n      setStepSelect(5);\n    }\n  }, [successCaseUpdate]);\n\n  useEffect(() => {\n    if (caseInfo !== undefined && caseInfo !== null) {\n      if (caseInfo.patient) {\n        setFirstName(caseInfo.patient.first_name ?? \"\");\n        setLastName(caseInfo.patient.last_name ?? \"\");\n        setBirthDate(caseInfo.patient.birth_day ?? \"\");\n        setPhone(caseInfo.patient.patient_phone ?? \"\");\n        setEmail(caseInfo.patient.patient_email ?? \"\");\n        setAddress(caseInfo.patient.patient_address ?? \"\");\n\n        const patientCountry = caseInfo.patient.patient_country ?? \"\";\n        const foundCountry = COUNTRIES.find(\n          (option) => option.title === patientCountry\n        );\n\n        if (foundCountry) {\n          setCountry({\n            value: foundCountry.title,\n            label: (\n              <div className=\"flex flex-row items-center\">\n                <span className=\"mr-2\">{foundCountry.icon}</span>\n                <span>{foundCountry.title}</span>\n              </div>\n            ),\n          });\n        } else {\n          setCountry(\"\");\n        }\n\n        setCity(caseInfo.patient.patient_city ?? \"\");\n      }\n\n      setIsPay(caseInfo.is_pay);\n      setCurrencyCode(caseInfo.currency_price ?? \"\");\n      setPriceTotal(caseInfo.price_tatal ?? 0);\n      // setCoordinator(caseInfo.coordinator ?? \"\");\n      if (caseInfo.coordinator_user) {\n        var initialCoordinator = caseInfo.coordinator_user?.id ?? \"\";\n        const foundCoordinator = coordinators?.find(\n          (item) => item.id === initialCoordinator\n        );\n\n        if (foundCoordinator) {\n          setCoordinator({\n            value: foundCoordinator.id,\n            label: foundCoordinator.full_name,\n          });\n        } else {\n          setCoordinator(\"\");\n        }\n      }\n      setCaseDate(caseInfo.case_date ?? \"\");\n      setCaseType(caseInfo.case_type ?? \"\");\n      setCaseDescription(caseInfo.case_description ?? \"\");\n      //\n      const statuses =\n        caseInfo?.case_status?.map((status) => status?.status_coordination) ||\n        []; // Default to an empty array if case_status is undefined or not an array\n\n      setCoordinatStatusList(statuses);\n\n      //\n      setCoordinatStatus(caseInfo.status_coordination ?? \"\");\n      setAppointmentDate(caseInfo.appointment_date ?? \"\");\n      setServiceLocation(caseInfo.service_location ?? \"\");\n      if (caseInfo.provider) {\n        var initialProvider = caseInfo.provider?.id ?? \"\";\n        const foundProvider = providers?.find(\n          (item) => item.id === initialProvider\n        );\n        if (foundProvider) {\n          setProviderName({\n            value: foundProvider.id,\n            label: foundProvider.full_name,\n          });\n        } else {\n          setProviderName(\"\");\n        }\n      }\n      if (caseInfo.provider_services) {\n        setProviderMultiSelectLast(caseInfo.provider_services ?? []);\n      }\n      //\n      setItemsInitialMedicalReports([]);\n      if (caseInfo.medical_reports) {\n        setItemsInitialMedicalReports(caseInfo.medical_reports);\n      }\n      //\n      setInvoiceNumber(caseInfo.invoice_number ?? \"\");\n      setDateIssued(caseInfo.date_issued ?? \"\");\n      setAmount(caseInfo.invoice_amount ?? 0);\n      setItemsUploadInvoice([]);\n      if (caseInfo.upload_invoices) {\n        setItemsUploadInvoice(caseInfo.upload_invoices);\n      }\n      //\n      if (caseInfo.assurance) {\n        var initialInsurance = caseInfo.assurance?.id ?? \"\";\n\n        var foundInsurance = insurances?.find(\n          (item) => item.id === initialInsurance\n        );\n\n        if (foundInsurance) {\n          console.log(\"here 2\");\n          setInsuranceCompany({\n            value: foundInsurance.id,\n            label: foundInsurance.assurance_name || \"\",\n          });\n        } else {\n          console.log(\"here 3\");\n          setInsuranceCompany({\n            value: \"\",\n            label: \"\",\n          });\n        }\n      }\n      setPolicyNumber(caseInfo.policy_number ?? \"\");\n      setInsuranceNumber(caseInfo.assurance_number ?? \"\");\n      setInitialStatus(caseInfo.assurance_status ?? \"\");\n      setItemsUploadAuthorizationDocuments([]);\n      if (caseInfo.upload_authorization) {\n        setItemsUploadAuthorizationDocuments(caseInfo.upload_authorization);\n      }\n      //\n    }\n  }, [caseInfo]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Edit Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            Edit Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  onClick={() => {\n                    if (stepSelect > step.index && stepSelect !== 5) {\n                      setStepSelect(step.index);\n                    }\n                  }}\n                  className={`flex flex-row mb-3 md:min-h-20 ${\n                    stepSelect > step.index && stepSelect !== 5\n                      ? \"cursor-pointer\"\n                      : \"\"\n                  } md:items-start items-center`}\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            className=\"text-sm\"\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          City <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <GoogleComponent\n                            apiKey=\"AIzaSyBtrUF56GBpFDiaXyLLGfdO8nIK5NWXUIU\"\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setCity(v.target.value);\n                            }}\n                            onPlaceSelected={(place) => {\n                              if (place && place.geometry) {\n                                setCity(place.formatted_address ?? \"\");\n                                // setCityVl(place.formatted_address ?? \"\");\n                                //   const latitude = place.geometry.location.lat();\n                                //   const longitude = place.geometry.location.lng();\n                                //   setLocationX(latitude ?? \"\");\n                                //   setLocationY(longitude ?? \"\");\n                              }\n                            }}\n                            defaultValue={city}\n                            types={[\"city\"]}\n                            language=\"en\"\n                          />\n                          {/* <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          /> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA Reference\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA Reference\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Currency Code{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={currencyCode}\n                            onChange={(option) => {\n                              setCurrencyCode(option);\n                            }}\n                            options={CURRENCYITEMS?.map((currency) => ({\n                              value: currency.code,\n                              label:\n                                currency.name !== \"\"\n                                  ? currency.name +\n                                      \" (\" +\n                                      currency.code +\n                                      \") \" || \"\"\n                                  : \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Currency Code ...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {currencyCodeError ? currencyCodeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Total Case <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              priceTotalError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"number\"\n                            min={0}\n                            step={0.01}\n                            placeholder=\"0.00\"\n                            value={priceTotal}\n                            onChange={(v) => setPriceTotal(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {priceTotalError ? priceTotalError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"ispay\"\n                            id=\"ispay\"\n                            checked={isPay === true}\n                            onChange={(v) => {\n                              setIsPay(true);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"ispay\"\n                          >\n                            Paid\n                          </label>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div>\n                          <input\n                            type={\"checkbox\"}\n                            name=\"notpay\"\n                            id=\"notpay\"\n                            checked={isPay === false}\n                            onChange={(v) => {\n                              setIsPay(false);\n                            }}\n                          />\n                          <label\n                            className=\"mx-1 text-[#B4B4B4] text-sm  cursor-pointer\"\n                            for=\"notpay\"\n                          >\n                            Unpaid\n                          </label>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n                        setCurrencyCodeError(\"\");\n                        setPriceTotalError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (currencyCode === \"\" || currencyCode.value === \"\") {\n                          setCurrencyCodeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (priceTotal === \"\") {\n                          setPriceTotalError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <div className=\"flex flex-wrap\">\n                            <div className=\"flex flex-row text-xs items-center my-3 text-danger\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"pending-coordination\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"pending-coordination\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"pending-coordination\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                id=\"pending-coordination\"\n                                type={\"checkbox\"}\n                                checked={coordinatStatusList.includes(\n                                  \"pending-coordination\"\n                                )}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"pending-coordination\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Pending Coordination\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-m-r\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-m-r\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"coordinated-missing-m-r\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-m-r\"\n                                )}\n                                id=\"coordinated-Missing-m-r\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-Missing-m-r\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing M.R.\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#FFA500]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-missing-invoice\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-missing-invoice\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-missing-invoice\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-missing-invoice\"\n                                )}\n                                id=\"coordinated-missing-invoice\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-missing-invoice\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Missing Invoice\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"waiting-for-insurance-authorization\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"waiting-for-insurance-authorization\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"waiting-for-insurance-authorization\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"waiting-for-insurance-authorization\"\n                                )}\n                                id=\"waiting-for-insurance-authorization\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"waiting-for-insurance-authorization\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Waiting for Insurance Authorization\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-primary\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"coordinated-patient-not-seen-yet\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"coordinated-patient-not-seen-yet\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !==\n                                          \"coordinated-patient-not-seen-yet\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"coordinated-patient-not-seen-yet\"\n                                )}\n                                id=\"coordinated-patient-not-seen-yet\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"coordinated-patient-not-seen-yet\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Coordinated, Patient not seen yet\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#008000]\">\n                              <input\n                                onChange={(v) => {\n                                  if (\n                                    !coordinatStatusList.includes(\n                                      \"fully-coordinated\"\n                                    )\n                                  ) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"fully-coordinated\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) =>\n                                          status !== \"fully-coordinated\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\n                                  \"fully-coordinated\"\n                                )}\n                                id=\"fully-coordinated\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"fully-coordinated\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Fully Coordinated\n                              </label>\n                            </div>\n                            <div className=\"flex flex-row text-xs items-center my-3 text-[#d34053]\">\n                              <input\n                                onChange={(v) => {\n                                  if (!coordinatStatusList.includes(\"failed\")) {\n                                    setCoordinatStatusList([\n                                      ...coordinatStatusList,\n                                      \"failed\",\n                                    ]);\n                                  } else {\n                                    setCoordinatStatusList(\n                                      coordinatStatusList.filter(\n                                        (status) => status !== \"failed\"\n                                      )\n                                    );\n                                  }\n                                }}\n                                checked={coordinatStatusList.includes(\"failed\")}\n                                id=\"failed\"\n                                type={\"checkbox\"}\n                                className=\"mx-1\"\n                              />\n                              <label\n                                for=\"failed\"\n                                className=\"flex-1 mx-1  cursor-pointer \"\n                              >\n                                Failed\n                              </label>\n                            </div>\n                          </div>\n                          {/* <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                            <option value={\"failed\"}>Failed</option>\n                          </select> */}\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusListError\n                              ? coordinatStatusListError\n                              : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                              //\n                              var initialProvider = option?.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) => item.id === initialProvider\n                              );\n                              if (foundProvider) {\n                                setProviderServices(\n                                  foundProvider.services ?? []\n                                );\n                              } else {\n                                setProviderServices([]);\n                              }\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {providerNameError ? providerNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2  w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Service\n                        </div>\n                        <div>\n                          <select\n                            className={`outline-none border ${\n                              providerServiceError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            onChange={(v) => {\n                              setProviderService(v.target.value);\n                            }}\n                            value={providerService}\n                          >\n                            <option value={\"\"}></option>\n                            {providerServices?.map((service, index) => (\n                              <option value={service.id}>\n                                {service.service_type ?? \"\"}\n                                {service.service_specialist !== \"\"\n                                  ? \" : \" + service.service_specialist\n                                  : \"\"}\n                              </option>\n                            ))}\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {providerServiceError ? providerServiceError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/* add  */}\n                    <div className=\"flex flex-col  \">\n                      <button\n                        onClick={() => {\n                          // providerMultiSelect\n                          var check = true;\n                          setProviderNameError(\"\");\n                          setProviderServiceError(\"\");\n                          if (\n                            providerName === \"\" ||\n                            providerName.value === \"\"\n                          ) {\n                            setProviderNameError(\"These fields are required.\");\n                            toast.error(\" Provider is required\");\n                            check = false;\n                          }\n                          if (providerService === \"\") {\n                            setProviderServiceError(\n                              \"These fields are required.\"\n                            );\n                            toast.error(\" Provider Service is required\");\n                            check = false;\n                          }\n\n                          if (check) {\n                            const exists = providerMultiSelect.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.service?.id) ===\n                                  String(providerService)\n                            );\n\n                            const existsLast = providerMultiSelectLast.some(\n                              (provider) =>\n                                String(provider?.provider?.id) ===\n                                  String(providerName.value) &&\n                                String(provider?.provider_service?.id) ===\n                                  String(providerService)\n                            );\n\n                            if (!exists && !existsLast) {\n                              // find provider\n                              var initialProvider = providerName.value ?? \"\";\n                              const foundProvider = providers?.find(\n                                (item) =>\n                                  String(item.id) === String(initialProvider)\n                              );\n                              console.log(foundProvider);\n\n                              if (foundProvider) {\n                                // found service\n                                var initialService = providerService ?? \"\";\n\n                                foundProvider?.services?.forEach((element) => {\n                                  console.log(element.id);\n                                });\n\n                                const foundService =\n                                  foundProvider?.services?.find(\n                                    (item) =>\n                                      String(item.id) === String(initialService)\n                                  );\n\n                                if (foundService) {\n                                  // Add the new item if it doesn't exist\n                                  setProviderMultiSelect([\n                                    ...providerMultiSelect,\n                                    {\n                                      provider: foundProvider,\n                                      service: foundService,\n                                    },\n                                  ]);\n                                  setProviderName(\"\");\n                                  setProviderService(\"\");\n                                  console.log(providerMultiSelect);\n                                } else {\n                                  setProviderNameError(\n                                    \"This provider service not exist!\"\n                                  );\n                                  toast.error(\n                                    \"This provider service not exist!\"\n                                  );\n                                }\n                              } else {\n                                setProviderNameError(\n                                  \"This provider not exist!\"\n                                );\n                                toast.error(\"This provider not exist!\");\n                              }\n                            } else {\n                              setProviderNameError(\n                                \"This provider or service is already added!\"\n                              );\n                              toast.error(\n                                \"This provider or service is already added!\"\n                              );\n                            }\n                          }\n                        }}\n                        className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n                      >\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          class=\"size-4\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                          />\n                        </svg>\n                        <span> Add Provider </span>\n                      </button>\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                          Providers\n                        </div>\n                        <div className=\"my-2 text-black text-sm\">\n                          {providerMultiSelectLast?.map(\n                            (itemProvider, index) => (\n                              <div\n                                key={index}\n                                className=\"flex flex-row items-center my-1\"\n                              >\n                                <div className=\"min-w-6 text-center\">\n                                  <button\n                                    onClick={() => {\n                                      const updatedServices =\n                                        providerMultiSelectLast.filter(\n                                          (_, indexF) => indexF !== index\n                                        );\n                                      setProviderMultiSelectDelete([\n                                        ...providerMultiSelectDelete,\n                                        itemProvider.id,\n                                      ]);\n                                      setProviderMultiSelectLast(\n                                        updatedServices\n                                      );\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      class=\"size-6\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                      />\n                                    </svg>\n                                  </button>\n                                </div>\n                                <div className=\"flex-1 mx-1 border-l px-1\">\n                                  <div>\n                                    <b>Provider:</b>{\" \"}\n                                    {itemProvider.provider?.full_name ?? \"---\"}\n                                  </div>\n                                  <div>\n                                    <b>Service:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_type ?? \"--\"}\n                                  </div>\n                                  <div>\n                                    <b>Speciality:</b>{\" \"}\n                                    {itemProvider.provider_service\n                                      ?.service_specialist ?? \"---\"}\n                                  </div>\n                                </div>\n                              </div>\n                            )\n                          )}\n                          {providerMultiSelect?.map((itemProvider, index) => (\n                            <div\n                              key={index}\n                              className=\"flex flex-row items-center my-1\"\n                            >\n                              <div className=\"min-w-6 text-center\">\n                                <button\n                                  onClick={() => {\n                                    const updatedServices =\n                                      providerMultiSelect.filter(\n                                        (_, indexF) => indexF !== index\n                                      );\n                                    setProviderMultiSelect(updatedServices);\n                                  }}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    class=\"size-6\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                                    />\n                                  </svg>\n                                </button>\n                              </div>\n                              <div className=\"flex-1 mx-1 border-l px-1\">\n                                <div>\n                                  <b>Provider:</b>{\" \"}\n                                  {itemProvider.provider?.full_name ?? \"---\"}\n                                </div>\n                                <div>\n                                  <b>Service:</b>{\" \"}\n                                  {itemProvider.service?.service_type ?? \"--\"}\n                                </div>\n                                <div>\n                                  <b>Speciality:</b>{\" \"}\n                                  {itemProvider.service?.service_specialist ??\n                                    \"---\"}\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n                        setCoordinatStatusListError(\"\");\n\n                        if (coordinatStatusList.length === 0) {\n                          setCoordinatStatusListError(\n                            \"This fields is required.\"\n                          );\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsInitialMedicalReports\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadInvoice\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {itemsUploadAuthorizationDocuments\n                          ?.filter((file) => !fileDeleted.includes(file.id))\n                          .map((file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.file_name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.file_name}\n                                </div>\n                                <div>\n                                  {parseFloat(file.file_size).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFileDeleted([...fileDeleted, file.id]);\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          ))}\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseUpdate}\n                      onClick={async () => {\n                        const providerItems = providerMultiSelect.map(\n                          (item) => ({\n                            service: item.service?.id,\n                            provider: item.provider?.id,\n                          })\n                        );\n                        // update\n                        await dispatch(\n                          updateCase(id, {\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate ?? \"\",\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value ?? \"\",\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            case_status: coordinatStatusList,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value ?? \"\",\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value ?? \"\",\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                            files_deleted: fileDeleted,\n                            providers: providerItems ?? [],\n                            providers_deleted: providerMultiSelectDelete ?? [],\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseUpdate ? \"Loading..\" : \"Update\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Updated Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully updates and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default EditCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,eAAe,MAAM,oCAAoC;AAChE,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,aAAa,QAAQ,qCAAqC;AACnE,SACEC,UAAU,EACVC,UAAU,EACVC,UAAU,QACL,iCAAiC;AAExC,OAAOC,eAAe,MAAM,2BAA2B;AAEvD,OAAOC,MAAM,MAAM,cAAc;AAEjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,gBAAgB,QAAQ,sCAAsC;AACvE,SAASC,mBAAmB,QAAQ,iCAAiC;AACrE,SAASC,SAAS,EAAEC,aAAa,QAAQ,iBAAiB;AAC1D,OAAOC,YAAY,MAAM,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,qBAAqB;EAC5BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,sBAAsB;EAC7BC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,iBAAiB;EACxBC,WAAW,EAAE;AACf,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,UAAU;EACjBC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,yBAAyB;EAChCC,WAAW,EACT;AACJ,CAAC,EACD;EACEF,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,QAAQ;EACfC,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,eAAe,GAAG;EACtBC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE;AACb,CAAC;AAED,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEoC;EAAG,CAAC,GAAGhC,SAAS,CAAC,CAAC;;EAExB;EACA,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwC,cAAc,EAAEC,iBAAiB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC4C,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgD,UAAU,EAAEC,aAAa,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACkD,SAAS,EAAEC,YAAY,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACsD,KAAK,EAAEC,QAAQ,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAAC0D,OAAO,EAAEC,UAAU,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC4D,YAAY,EAAEC,eAAe,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAAC8D,IAAI,EAAEC,OAAO,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAACkE,OAAO,EAAEC,UAAU,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EACpD;EACA,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7E,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC8E,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG/E,QAAQ,CACxE,EACF,CAAC;EACD,MAAM,CAACgF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;EAE1E,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACoF,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACsF,QAAQ,EAAEC,WAAW,CAAC,GAAGvF,QAAQ,CACtC,IAAIwF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CACvC,CAAC;EACD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC6F,QAAQ,EAAEC,WAAW,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACiG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACmG,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACqG,KAAK,EAAEC,QAAQ,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EAEzC,MAAM,CAACuG,YAAY,EAAEC,eAAe,CAAC,GAAGxG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACyG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1G,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAAC2G,UAAU,EAAEC,aAAa,CAAC,GAAG5G,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6G,eAAe,EAAEC,kBAAkB,CAAC,GAAG9G,QAAQ,CAAC,EAAE,CAAC;EAC1D;EACA,MAAM,CAAC+G,eAAe,EAAEC,kBAAkB,CAAC,GAAGhH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlH,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACmH,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpH,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAACqH,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtH,QAAQ,CAAC,EAAE,CAAC;EAE5E,MAAM,CAACuH,eAAe,EAAEC,kBAAkB,CAAC,GAAGxH,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyH,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG1H,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAAC2H,eAAe,EAAEC,kBAAkB,CAAC,GAAG5H,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6H,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9H,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+H,YAAY,EAAEC,eAAe,CAAC,GAAGhI,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiI,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlI,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACmI,aAAa,EAAEC,gBAAgB,CAAC,GAAGpI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtI,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuI,aAAa,EAAEC,gBAAgB,CAAC,GAAGxI,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyI,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1I,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAAC2I,eAAe,EAAEC,kBAAkB,CAAC,GAAG5I,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6I,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG9I,QAAQ,CAAC,EAAE,CAAC;EACpE;EACA,MAAM,CAAC+I,aAAa,EAAEC,gBAAgB,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlJ,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqJ,eAAe,EAAEC,kBAAkB,CAAC,GAAGtJ,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM,CAACuJ,MAAM,EAAEC,SAAS,CAAC,GAAGxJ,QAAQ,CAAC,CAAC,CAAC;EACvC,MAAM,CAACyJ,WAAW,EAAEC,cAAc,CAAC,GAAG1J,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAAC2J,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC6J,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG9J,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC+J,eAAe,EAAEC,kBAAkB,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACiK,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGlK,QAAQ,CAAC,EAAE,CAAC;EAEpE,MAAM,CAACmK,YAAY,EAAEC,eAAe,CAAC,GAAGpK,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtK,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAM,CAACuK,aAAa,EAAEC,gBAAgB,CAAC,GAAGxK,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1K,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAM,CAAC2K,WAAW,EAAEC,cAAc,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6K,0BAA0B,EAAEC,6BAA6B,CAAC,GAAG9K,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM,CAAC+K,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CACJiL,iCAAiC,EACjCC,oCAAoC,CACrC,GAAGlL,QAAQ,CAAC,EAAE,CAAC;;EAEhB;EACA;EACA,MAAM,CAACmL,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGpL,QAAQ,CAC1E,EACF,CAAC;EACD,MAAM;IACJqL,YAAY,EAAEC,0BAA0B;IACxCC,aAAa,EAAEC;EACjB,CAAC,GAAGzK,WAAW,CAAC;IACd0K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBP,6BAA6B,CAAEQ,SAAS,IAAK,CAC3C,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACLoL,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,IACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM,CAACK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvM,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM;IACJqL,YAAY,EAAEmB,yBAAyB;IACvCjB,aAAa,EAAEkB;EACjB,CAAC,GAAG1L,WAAW,CAAC;IACd0K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBY,qBAAqB,CAAEX,SAAS,IAAK,CACnC,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACLuM,kBAAkB,CAACF,OAAO,CAAEN,IAAI,IAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;EACN;EACA,MAAM,CACJS,iCAAiC,EACjCC,oCAAoC,CACrC,GAAG3M,QAAQ,CAAC,EAAE,CAAC;EAChB,MAAM;IACJqL,YAAY,EAAEuB,wCAAwC;IACtDrB,aAAa,EAAEsB;EACjB,CAAC,GAAG9L,WAAW,CAAC;IACd0K,MAAM,EAAE;MACN,GAAG,EAAE;IACP,CAAC;IACDC,MAAM,EAAGC,aAAa,IAAK;MACzBgB,oCAAoC,CAAEf,SAAS,IAAK,CAClD,GAAGA,SAAS,EACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,IACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,EAAE;QAClBG,OAAO,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;MACnC,CAAC,CACH,CAAC,CACF,CAAC;IACJ;EACF,CAAC,CAAC;EAEF/L,SAAS,CAAC,MAAM;IACd,OAAO,MACL2M,iCAAiC,CAACN,OAAO,CAAEN,IAAI,IAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;;EAEA;;EAEA,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAG/M,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAMgN,SAAS,GAAG9M,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,aAAa,GAAGjN,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACG,YAAY,CAAC;EAChE,MAAM;IAAEC,SAAS;IAAEC,gBAAgB;IAAEC;EAAe,CAAC,GAAGJ,aAAa;EAErE,MAAMK,cAAc,GAAGtN,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACQ,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC;EAAgB,CAAC,GAAGJ,cAAc;EAEzE,MAAMK,UAAU,GAAG3N,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACtM,UAAU,CAAC;EAC3D,MAAM;IAAEmN,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EAEZ,MAAMK,gBAAgB,GAAGhO,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACkB,gBAAgB,CAAC;EACvE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAkB,CAAC,GAC5DJ,gBAAgB;EAElB,MAAMK,UAAU,GAAGrO,WAAW,CAAE+M,KAAK,IAAKA,KAAK,CAACrM,UAAU,CAAC;EAC3D,MAAM;IAAE4N,iBAAiB;IAAEC,eAAe;IAAEC;EAAkB,CAAC,GAAGH,UAAU;EAE5E,MAAMI,QAAQ,GAAG,GAAG;EACpB5O,SAAS,CAAC,MAAM;IACd,IAAI,CAACmN,QAAQ,EAAE;MACbhL,QAAQ,CAACyM,QAAQ,CAAC;IACpB,CAAC,MAAM;MACL5B,aAAa,CAAC,CAAC,CAAC;MAChB3K,QAAQ,CAACnB,mBAAmB,CAAC,GAAG,CAAC,CAAC;MAClCmB,QAAQ,CAAC3B,aAAa,CAAC,GAAG,CAAC,CAAC;MAC5B2B,QAAQ,CAACpB,gBAAgB,CAAC,GAAG,CAAC,CAAC;MAC/BoB,QAAQ,CAACzB,UAAU,CAAC0B,EAAE,CAAC,CAAC;MAExBuM,OAAO,CAACC,GAAG,CAACzN,YAAY,CAAC0N,MAAM,CAAC,OAAO,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC5M,QAAQ,EAAEgL,QAAQ,EAAE9K,QAAQ,CAAC,CAAC;EAElCrC,SAAS,CAAC,MAAM;IACd,IAAI2O,iBAAiB,EAAE;MACrB3B,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC2B,iBAAiB,CAAC,CAAC;EAEvB3O,SAAS,CAAC,MAAM;IACd,IAAIkO,QAAQ,KAAKc,SAAS,IAAId,QAAQ,KAAK,IAAI,EAAE;MAAA,IAAAe,qBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MAC/C,IAAI7B,QAAQ,CAAC8B,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACpBhO,YAAY,EAAAyN,qBAAA,GAAC/B,QAAQ,CAAC8B,OAAO,CAACS,UAAU,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC/CrN,WAAW,EAAAsN,qBAAA,GAAChC,QAAQ,CAAC8B,OAAO,CAACU,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC7C9M,YAAY,EAAA+M,qBAAA,GAACjC,QAAQ,CAAC8B,OAAO,CAACW,SAAS,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9C3M,QAAQ,EAAA4M,qBAAA,GAAClC,QAAQ,CAAC8B,OAAO,CAACY,aAAa,cAAAR,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;QAC9CpN,QAAQ,EAAAqN,sBAAA,GAACnC,QAAQ,CAAC8B,OAAO,CAACa,aAAa,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAC9CzM,UAAU,EAAA0M,sBAAA,GAACpC,QAAQ,CAAC8B,OAAO,CAACc,eAAe,cAAAR,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;QAElD,MAAMS,cAAc,IAAAR,sBAAA,GAAGrC,QAAQ,CAAC8B,OAAO,CAACgB,eAAe,cAAAT,sBAAA,cAAAA,sBAAA,GAAI,EAAE;QAC7D,MAAMU,YAAY,GAAG9P,SAAS,CAAC+P,IAAI,CAChCC,MAAM,IAAKA,MAAM,CAACzP,KAAK,KAAKqP,cAC/B,CAAC;QAED,IAAIE,YAAY,EAAE;UAChB7M,UAAU,CAAC;YACTgN,KAAK,EAAEH,YAAY,CAACvP,KAAK;YACzB2P,KAAK,eACH9P,OAAA;cAAK+P,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzChQ,OAAA;gBAAM+P,SAAS,EAAC,MAAM;gBAAAC,QAAA,EAAEN,YAAY,CAACO;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACjDrQ,OAAA;gBAAAgQ,QAAA,EAAON,YAAY,CAACvP;cAAK;gBAAA+P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAET,CAAC,CAAC;QACJ,CAAC,MAAM;UACLxN,UAAU,CAAC,EAAE,CAAC;QAChB;QAEAJ,OAAO,EAAAwM,sBAAA,GAACtC,QAAQ,CAAC8B,OAAO,CAAC6B,YAAY,cAAArB,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;MAC9C;MAEAjK,QAAQ,CAAC2H,QAAQ,CAAC4D,MAAM,CAAC;MACzBrL,eAAe,EAAAwI,qBAAA,GAACf,QAAQ,CAAC6D,cAAc,cAAA9C,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC9CpI,aAAa,EAAAqI,qBAAA,GAAChB,QAAQ,CAAC8D,WAAW,cAAA9C,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACxC;MACA,IAAIhB,QAAQ,CAAC+D,gBAAgB,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA;QAC7B,IAAIC,kBAAkB,IAAAF,qBAAA,IAAAC,sBAAA,GAAGjE,QAAQ,CAAC+D,gBAAgB,cAAAE,sBAAA,uBAAzBA,sBAAA,CAA2B7P,EAAE,cAAA4P,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAC5D,MAAMG,gBAAgB,GAAGhE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6C,IAAI,CACxCoB,IAAI,IAAKA,IAAI,CAAChQ,EAAE,KAAK8P,kBACxB,CAAC;QAED,IAAIC,gBAAgB,EAAE;UACpB7N,cAAc,CAAC;YACb4M,KAAK,EAAEiB,gBAAgB,CAAC/P,EAAE;YAC1B+O,KAAK,EAAEgB,gBAAgB,CAACE;UAC1B,CAAC,CAAC;QACJ,CAAC,MAAM;UACL/N,cAAc,CAAC,EAAE,CAAC;QACpB;MACF;MACAgB,WAAW,EAAA2J,mBAAA,GAACjB,QAAQ,CAACsE,SAAS,cAAArD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrCpJ,WAAW,EAAAqJ,mBAAA,GAAClB,QAAQ,CAACuE,SAAS,cAAArD,mBAAA,cAAAA,mBAAA,GAAI,EAAE,CAAC;MACrCjJ,kBAAkB,EAAAkJ,qBAAA,GAACnB,QAAQ,CAACwE,gBAAgB,cAAArD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD;MACA,MAAMsD,QAAQ,GACZ,CAAAzE,QAAQ,aAARA,QAAQ,wBAAAoB,qBAAA,GAARpB,QAAQ,CAAE0E,WAAW,cAAAtD,qBAAA,uBAArBA,qBAAA,CAAuBxD,GAAG,CAAE+G,MAAM,IAAKA,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,mBAAmB,CAAC,KACnE,EAAE,CAAC,CAAC;;MAENzL,sBAAsB,CAACsL,QAAQ,CAAC;;MAEhC;MACA1L,kBAAkB,EAAAsI,qBAAA,GAACrB,QAAQ,CAAC4E,mBAAmB,cAAAvD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACtD9H,kBAAkB,EAAA+H,qBAAA,GAACtB,QAAQ,CAAC6E,gBAAgB,cAAAvD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD3H,kBAAkB,EAAA4H,qBAAA,GAACvB,QAAQ,CAAC8E,gBAAgB,cAAAvD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnD,IAAIvB,QAAQ,CAAC+E,QAAQ,EAAE;QAAA,IAAAC,qBAAA,EAAAC,kBAAA;QACrB,IAAIC,eAAe,IAAAF,qBAAA,IAAAC,kBAAA,GAAGjF,QAAQ,CAAC+E,QAAQ,cAAAE,kBAAA,uBAAjBA,kBAAA,CAAmB7Q,EAAE,cAAA4Q,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QACjD,MAAMG,aAAa,GAAG/F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,IAAKA,IAAI,CAAChQ,EAAE,KAAK8Q,eACxB,CAAC;QACD,IAAIC,aAAa,EAAE;UACjBpL,eAAe,CAAC;YACdmJ,KAAK,EAAEiC,aAAa,CAAC/Q,EAAE;YACvB+O,KAAK,EAAEgC,aAAa,CAACd;UACvB,CAAC,CAAC;QACJ,CAAC,MAAM;UACLtK,eAAe,CAAC,EAAE,CAAC;QACrB;MACF;MACA,IAAIiG,QAAQ,CAACoF,iBAAiB,EAAE;QAAA,IAAAC,qBAAA;QAC9BrO,0BAA0B,EAAAqO,qBAAA,GAACrF,QAAQ,CAACoF,iBAAiB,cAAAC,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC9D;MACA;MACAxI,6BAA6B,CAAC,EAAE,CAAC;MACjC,IAAImD,QAAQ,CAACsF,eAAe,EAAE;QAC5BzI,6BAA6B,CAACmD,QAAQ,CAACsF,eAAe,CAAC;MACzD;MACA;MACAvK,gBAAgB,EAAAyG,qBAAA,GAACxB,QAAQ,CAACuF,cAAc,cAAA/D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC/CrG,aAAa,EAAAsG,qBAAA,GAACzB,QAAQ,CAACwF,WAAW,cAAA/D,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACzClG,SAAS,EAAAmG,qBAAA,GAAC1B,QAAQ,CAACyF,cAAc,cAAA/D,qBAAA,cAAAA,qBAAA,GAAI,CAAC,CAAC;MACvC3E,qBAAqB,CAAC,EAAE,CAAC;MACzB,IAAIiD,QAAQ,CAAC0F,eAAe,EAAE;QAC5B3I,qBAAqB,CAACiD,QAAQ,CAAC0F,eAAe,CAAC;MACjD;MACA;MACA,IAAI1F,QAAQ,CAAC2F,SAAS,EAAE;QAAA,IAAAC,qBAAA,EAAAC,mBAAA;QACtB,IAAIC,gBAAgB,IAAAF,qBAAA,IAAAC,mBAAA,GAAG7F,QAAQ,CAAC2F,SAAS,cAAAE,mBAAA,uBAAlBA,mBAAA,CAAoBzR,EAAE,cAAAwR,qBAAA,cAAAA,qBAAA,GAAI,EAAE;QAEnD,IAAIG,cAAc,GAAGtG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEuD,IAAI,CAClCoB,IAAI,IAAKA,IAAI,CAAChQ,EAAE,KAAK0R,gBACxB,CAAC;QAED,IAAIC,cAAc,EAAE;UAClBpF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjF,mBAAmB,CAAC;YAClBuH,KAAK,EAAE6C,cAAc,CAAC3R,EAAE;YACxB+O,KAAK,EAAE4C,cAAc,CAACC,cAAc,IAAI;UAC1C,CAAC,CAAC;QACJ,CAAC,MAAM;UACLrF,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrBjF,mBAAmB,CAAC;YAClBuH,KAAK,EAAE,EAAE;YACTC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;MACAhH,eAAe,EAAAwF,qBAAA,GAAC3B,QAAQ,CAACiG,aAAa,cAAAtE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MAC7C5F,kBAAkB,EAAA6F,qBAAA,GAAC5B,QAAQ,CAACkG,gBAAgB,cAAAtE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACnDrF,gBAAgB,EAAAsF,qBAAA,GAAC7B,QAAQ,CAACmG,gBAAgB,cAAAtE,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;MACjD5E,oCAAoC,CAAC,EAAE,CAAC;MACxC,IAAI+C,QAAQ,CAACoG,oBAAoB,EAAE;QACjCnJ,oCAAoC,CAAC+C,QAAQ,CAACoG,oBAAoB,CAAC;MACrE;MACA;IACF;EACF,CAAC,EAAE,CAACpG,QAAQ,CAAC,CAAC;EAEd,oBACE3M,OAAA,CAAChB,aAAa;IAAAgR,QAAA,eACZhQ,OAAA;MAAK+P,SAAS,EAAC,EAAE;MAAAC,QAAA,gBACfhQ,OAAA;QAAK+P,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtDhQ,OAAA;UAAGgT,IAAI,EAAC,YAAY;UAAAhD,QAAA,eAClBhQ,OAAA;YAAK+P,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DhQ,OAAA;cACEiT,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBrD,SAAS,EAAC,SAAS;cAAAC,QAAA,eAEnBhQ,OAAA;gBACEqT,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNrQ,OAAA;cAAM+P,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJrQ,OAAA;UAAAgQ,QAAA,eACEhQ,OAAA;YACEiT,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBrD,SAAS,EAAC,SAAS;YAAAC,QAAA,eAEnBhQ,OAAA;cACEqT,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAArD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPrQ,OAAA;UAAK+P,SAAS,EAAC,EAAE;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAENrQ,OAAA;QAAK+P,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7ChQ,OAAA;UAAI+P,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAAC;QAEpE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAENrQ,OAAA;QAAK+P,SAAS,EAAC,mIAAmI;QAAAC,QAAA,eAChJhQ,OAAA;UAAK+P,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxChQ,OAAA;YAAK+P,SAAS,EAAC,2DAA2D;YAAAC,QAAA,gBACxEhQ,OAAA;cAAK+P,SAAS,EAAC;YAAwF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC7GpQ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsK,GAAG,CAAC,CAACiJ,IAAI,EAAEtT,KAAK,kBAC1BF,OAAA;cACEyT,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIjI,UAAU,GAAGgI,IAAI,CAACtT,KAAK,IAAIsL,UAAU,KAAK,CAAC,EAAE;kBAC/CC,aAAa,CAAC+H,IAAI,CAACtT,KAAK,CAAC;gBAC3B;cACF,CAAE;cACF6P,SAAS,EAAG,kCACVvE,UAAU,GAAGgI,IAAI,CAACtT,KAAK,IAAIsL,UAAU,KAAK,CAAC,GACvC,gBAAgB,GAChB,EACL,8BAA8B;cAAAwE,QAAA,GAE9BxE,UAAU,GAAGgI,IAAI,CAACtT,KAAK,gBACtBF,OAAA;gBAAK+P,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHhQ,OAAA;kBACE0T,GAAG,EAAEzU,eAAgB;kBACrB8Q,SAAS,EAAC,QAAQ;kBAClB4D,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;oBACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,GAAG,yBAAyB;kBAC1C;gBAAE;kBAAAxD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,GACJ7E,UAAU,KAAKgI,IAAI,CAACtT,KAAK,gBAC3BF,OAAA;gBAAK+P,SAAS,EAAC;cAAkD;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,gBAExErQ,OAAA;gBAAK+P,SAAS,EAAC,oGAAoG;gBAAAC,QAAA,eACjHhQ,OAAA;kBACEiT,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBrD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eAElBhQ,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBuT,CAAC,EAAC;kBAAuB;oBAAArD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAEDrQ,OAAA;gBAAK+P,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrChQ,OAAA;kBAAK+P,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAEwD,IAAI,CAACrT;gBAAK;kBAAA+P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EACtD7E,UAAU,KAAKgI,IAAI,CAACtT,KAAK,gBACxBF,OAAA;kBAAK+P,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAChDwD,IAAI,CAACpT;gBAAW;kBAAA8P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,GACJ,IAAI;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrQ,OAAA;YAAK+P,SAAS,EAAC,0CAA0C;YAAAC,QAAA,GAEtDxE,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfhQ,OAAA;gBAAK+P,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,aACjC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,wBACV7O,cAAc,GACV,eAAe,GACf,kBACL,mCAAmC;wBACpC6S,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,YAAY;wBACxBnE,KAAK,EAAE7O,SAAU;wBACjBiT,QAAQ,EAAGC,CAAC,IAAKjT,YAAY,CAACiT,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/C,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC9O,cAAc,GAAGA,cAAc,GAAG;sBAAE;wBAAAgP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAE7C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,WAAW;wBACvBnE,KAAK,EAAEzO,QAAS;wBAChB6S,QAAQ,EAAGC,CAAC,IAAK7S,WAAW,CAAC6S,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzChQ,OAAA;oBAAK+P,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,wBACVrO,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpCqS,IAAI,EAAC,OAAO;wBACZC,WAAW,EAAC,eAAe;wBAC3BnE,KAAK,EAAErO,KAAM;wBACbyS,QAAQ,EAAGC,CAAC,IAAKzS,QAAQ,CAACyS,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCtO,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAwO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,QACrC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,uBACV7N,UAAU,GAAG,eAAe,GAAG,kBAChC,mCAAmC;wBACpC6R,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,UAAU;wBACtBnE,KAAK,EAAE7N,KAAM;wBACbiS,QAAQ,EAAGC,CAAC,IAAKjS,QAAQ,CAACiS,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3C,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC9N,UAAU,GAAGA,UAAU,GAAG;sBAAE;wBAAAgO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzChQ,OAAA;oBAAK+P,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,UACpC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAEjN,OAAQ;wBACfqR,QAAQ,EAAGrE,MAAM,IAAK;0BACpB/M,UAAU,CAAC+M,MAAM,CAAC;wBACpB,CAAE;wBACFG,SAAS,EAAC,SAAS;wBACnBoE,OAAO,EAAEvU,SAAS,CAAC2K,GAAG,CAAE3H,OAAO,KAAM;0BACnCiN,KAAK,EAAEjN,OAAO,CAACzC,KAAK;0BACpB2P,KAAK,eACH9P,OAAA;4BACE+P,SAAS,EAAG,GACVnN,OAAO,CAACzC,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;4BAAA6P,QAAA,gBAE9BhQ,OAAA;8BAAM+P,SAAS,EAAC,MAAM;8BAAAC,QAAA,EAAEpN,OAAO,CAACqN;4BAAI;8BAAAC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC,eAC5CrQ,OAAA;8BAAAgQ,QAAA,EAAOpN,OAAO,CAACzC;4BAAK;8BAAA+P,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAET,CAAC,CAAC,CAAE;wBACJ2D,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE3R,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;4BACvB4R,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrClN,YAAY,GAAGA,YAAY,GAAG;sBAAE;wBAAAoN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,qBAAqB;oBAAAC,QAAA,gBAClChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACT,eAAe;wBACduV,MAAM,EAAC,yCAAyC;wBAChD/E,SAAS,EAAG,wBACVrN,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;wBACpCuR,QAAQ,EAAGC,CAAC,IAAK;0BACfzR,OAAO,CAACyR,CAAC,CAACL,MAAM,CAAChE,KAAK,CAAC;wBACzB,CAAE;wBACFkF,eAAe,EAAGC,KAAK,IAAK;0BAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;4BAAA,IAAAC,qBAAA;4BAC3BzS,OAAO,EAAAyS,qBAAA,GAACF,KAAK,CAACG,iBAAiB,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;4BACtC;4BACA;4BACA;4BACA;4BACA;0BACF;wBACF,CAAE;wBACFE,YAAY,EAAE5S,IAAK;wBACnB6S,KAAK,EAAE,CAAC,MAAM,CAAE;wBAChBC,QAAQ,EAAC;sBAAI;wBAAApF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACd,CAAC,eAUFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCtN,SAAS,GAAGA,SAAS,GAAG;sBAAE;wBAAAwN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAAG;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvDrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAExH,gBAAiB;wBACxB4L,QAAQ,EAAGrE,MAAM,IAAK;0BACpBtH,mBAAmB,CAACsH,MAAM,CAAC;wBAC7B,CAAE;wBACFuE,OAAO,EAAE/H,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7B,GAAG,CAAE+H,SAAS,KAAM;0BACvCzC,KAAK,EAAEyC,SAAS,CAACvR,EAAE;0BACnB+O,KAAK,EAAEwC,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC3F,MAAM,EAAE4F,UAAU,KAC/B5F,MAAM,CAACE,KAAK,CACT2F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD1F,SAAS,EAAC,SAAS;wBACnBiE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAElM,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBmM,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCzH,qBAAqB,GAAGA,qBAAqB,GAAG;sBAAE;wBAAA2H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,wBACVpH,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCoL,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BnE,KAAK,EAAEpH,eAAgB;wBACvBwL,QAAQ,EAAGC,CAAC,IAAKxL,kBAAkB,CAACwL,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCrH,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAuH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,sBACxB,EAAC,GAAG,eACxBhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAE7M,WAAY;wBACnBiR,QAAQ,EAAGrE,MAAM,IAAK;0BACpB3M,cAAc,CAAC2M,MAAM,CAAC;wBACxB,CAAE;wBACFG,SAAS,EAAC,SAAS;wBACnBoE,OAAO,EAAErH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEvC,GAAG,CAAEwG,IAAI,KAAM;0BACpClB,KAAK,EAAEkB,IAAI,CAAChQ,EAAE;0BACd+O,KAAK,EAAEiB,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJuE,YAAY,EAAEA,CAAC3F,MAAM,EAAE4F,UAAU,KAC/B5F,MAAM,CAACE,KAAK,CACT2F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,uBAAuB;wBACnCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAEvR,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;4BACvBwR,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC9M,gBAAgB,GAAGA,gBAAgB,GAAG;sBAAE;wBAAAgN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,GAAC,oBACzB,EAAC,GAAG,eACtBhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,wBACV1L,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBACpC0P,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,oBAAoB;wBAChCnE,KAAK,EAAE7L,QAAS;wBAChBiQ,QAAQ,EAAGC,CAAC,IAAKjQ,WAAW,CAACiQ,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC3L,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAA6L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,OACvC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE6P,KAAK,EAAEtL,QAAS;wBAChB0P,QAAQ,EAAGC,CAAC,IAAK1P,WAAW,CAAC0P,CAAC,CAACL,MAAM,CAAChE,KAAK,CAAE;wBAC7CE,SAAS,EAAG,wBACVtL,aAAa,GACT,eAAe,GACf,kBACL,mCAAmC;wBAAAuL,QAAA,gBAEpChQ,OAAA;0BAAQ6P,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAW;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACvCrQ,OAAA;0BAAQ6P,KAAK,EAAE,SAAU;0BAAAG,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1CrQ,OAAA;0BAAQ6P,KAAK,EAAE,WAAY;0BAAAG,QAAA,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC,eACTrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCvL,aAAa,GAAGA,aAAa,GAAG;sBAAE;wBAAAyL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,eAC/B,EAAC,GAAG,eACjBhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAE5K,YAAa;wBACpBgP,QAAQ,EAAGrE,MAAM,IAAK;0BACpB1K,eAAe,CAAC0K,MAAM,CAAC;wBACzB,CAAE;wBACFuE,OAAO,EAAEtU,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE0K,GAAG,CAAEoL,QAAQ,KAAM;0BACzC9F,KAAK,EAAE8F,QAAQ,CAACC,IAAI;0BACpB9F,KAAK,EACH6F,QAAQ,CAACE,IAAI,KAAK,EAAE,GAChBF,QAAQ,CAACE,IAAI,GACX,IAAI,GACJF,QAAQ,CAACC,IAAI,GACb,IAAI,IAAI,EAAE,GACZ;wBACR,CAAC,CAAC,CAAE;wBACJL,YAAY,EAAEA,CAAC3F,MAAM,EAAE4F,UAAU,KAC/B5F,MAAM,CAACE,KAAK,CACT2F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD1F,SAAS,EAAC,SAAS;wBACnBiE,WAAW,EAAC,0BAA0B;wBACtCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAElM,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBmM,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrC7K,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAA+K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,aACjC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,wBACVxK,eAAe,GACX,eAAe,GACf,kBACL,oCAAoC;wBACrCwO,IAAI,EAAC,QAAQ;wBACb+B,GAAG,EAAE,CAAE;wBACPtC,IAAI,EAAE,IAAK;wBACXQ,WAAW,EAAC,MAAM;wBAClBnE,KAAK,EAAExK,UAAW;wBAClB4O,QAAQ,EAAGC,CAAC,IAAK5O,aAAa,CAAC4O,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCzK,eAAe,GAAGA,eAAe,GAAG;sBAAE;wBAAA2K,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrQ,OAAA;kBAAK+P,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5ChQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+T,IAAI,EAAE,UAAW;wBACjB8B,IAAI,EAAC,OAAO;wBACZ9U,EAAE,EAAC,OAAO;wBACVgV,OAAO,EAAEhR,KAAK,KAAK,IAAK;wBACxBkP,QAAQ,EAAGC,CAAC,IAAK;0BACflP,QAAQ,CAAC,IAAI,CAAC;wBAChB;sBAAE;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBACE+P,SAAS,EAAC,6CAA6C;wBACvDiG,GAAG,EAAC,OAAO;wBAAAhG,QAAA,EACZ;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,eAC5ChQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+T,IAAI,EAAE,UAAW;wBACjB8B,IAAI,EAAC,QAAQ;wBACb9U,EAAE,EAAC,QAAQ;wBACXgV,OAAO,EAAEhR,KAAK,KAAK,KAAM;wBACzBkP,QAAQ,EAAGC,CAAC,IAAK;0BACflP,QAAQ,CAAC,KAAK,CAAC;wBACjB;sBAAE;wBAAAkL,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBACE+P,SAAS,EAAC,6CAA6C;wBACvDiG,GAAG,EAAC,QAAQ;wBAAAhG,QAAA,EACb;sBAED;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE6P,KAAK,EAAElL,eAAgB;wBACvBsR,IAAI,EAAE,CAAE;wBACRhC,QAAQ,EAAGC,CAAC,IAAKtP,kBAAkB,CAACsP,CAAC,CAACL,MAAM,CAAChE,KAAK,CAAE;wBACpDE,SAAS,EAAC;sBAAwE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrQ,OAAA;gBAAK+P,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,eAC1DhQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIyC,KAAK,GAAG,IAAI;oBAChB/U,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,gBAAgB,CAAC,EAAE,CAAC;oBACpBQ,iBAAiB,CAAC,EAAE,CAAC;oBACrBI,aAAa,CAAC,EAAE,CAAC;oBACjBR,aAAa,CAAC,EAAE,CAAC;oBACjBY,eAAe,CAAC,EAAE,CAAC;oBACnBmC,gBAAgB,CAAC,EAAE,CAAC;oBACpBJ,gBAAgB,CAAC,EAAE,CAAC;oBACpBnB,mBAAmB,CAAC,EAAE,CAAC;oBACvBR,YAAY,CAAC,EAAE,CAAC;oBAChBI,eAAe,CAAC,EAAE,CAAC;oBACnBqC,oBAAoB,CAAC,EAAE,CAAC;oBACxBI,kBAAkB,CAAC,EAAE,CAAC;oBAEtB,IAAIxE,SAAS,KAAK,EAAE,EAAE;sBACpBG,iBAAiB,CAAC,yBAAyB,CAAC;sBAC5C+U,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIlU,KAAK,KAAK,EAAE,EAAE;sBAChBG,aAAa,CAAC,yBAAyB,CAAC;sBACxC+T,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAItT,OAAO,KAAK,EAAE,IAAIA,OAAO,CAACiN,KAAK,KAAK,EAAE,EAAE;sBAC1C9M,eAAe,CAAC,yBAAyB,CAAC;sBAC1CmT,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIlT,WAAW,KAAK,EAAE,IAAIA,WAAW,CAAC6M,KAAK,KAAK,EAAE,EAAE;sBAClD1M,mBAAmB,CAAC,yBAAyB,CAAC;sBAC9C+S,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAI3R,QAAQ,KAAK,EAAE,EAAE;sBACnBG,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3CwR,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIlS,QAAQ,KAAK,EAAE,EAAE;sBACnBM,gBAAgB,CAAC,yBAAyB,CAAC;sBAC3C4R,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIjR,YAAY,KAAK,EAAE,IAAIA,YAAY,CAAC4K,KAAK,KAAK,EAAE,EAAE;sBACpDzK,oBAAoB,CAAC,yBAAyB,CAAC;sBAC/C8Q,KAAK,GAAG,KAAK;oBACf;oBACA,IAAI7Q,UAAU,KAAK,EAAE,EAAE;sBACrBG,kBAAkB,CAAC,yBAAyB,CAAC;sBAC7C0Q,KAAK,GAAG,KAAK;oBACf;oBACA,IAAIA,KAAK,EAAE;sBACTzK,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLvM,KAAK,CAACiX,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFpG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEP7E,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfhQ,OAAA;gBAAK+P,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,GAAC,SACrC,eAAAhQ,OAAA;wBAAQ+P,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAAC;sBAAC;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBAAK+P,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,gBAC7BhQ,OAAA;0BAAK+P,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,gBAClEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,sBACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,sBAAsB,CACvB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KAAK,sBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFvQ,EAAE,EAAC,sBAAsB;4BACzBgT,IAAI,EAAE,UAAW;4BACjBgC,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,sBACF,CAAE;4BACF3F,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,sBAAsB;4BAC1BjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,yBACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,yBAAyB,CAC1B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KAAK,yBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,yBACF,CAAE;4BACF3U,EAAE,EAAC,yBAAyB;4BAC5BgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,yBAAyB;4BAC7BjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,6BACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,6BAA6B,CAC9B,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KACN,6BACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,6BACF,CAAE;4BACF3U,EAAE,EAAC,6BAA6B;4BAChCgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,6BAA6B;4BACjCjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,qCACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,qCAAqC,CACtC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KACN,qCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,qCACF,CAAE;4BACF3U,EAAE,EAAC,qCAAqC;4BACxCgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,qCAAqC;4BACzCjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,sDAAsD;0BAAAC,QAAA,gBACnEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,kCACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,kCAAkC,CACnC,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KACN,kCACJ,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,kCACF,CAAE;4BACF3U,EAAE,EAAC,kCAAkC;4BACrCgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,kCAAkC;4BACtCjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IACE,CAACrO,mBAAmB,CAAC6P,QAAQ,CAC3B,mBACF,CAAC,EACD;gCACA5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,mBAAmB,CACpB,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IACLA,MAAM,KAAK,mBACf,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CACnC,mBACF,CAAE;4BACF3U,EAAE,EAAC,mBAAmB;4BACtBgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,mBAAmB;4BACvBjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACNrQ,OAAA;0BAAK+P,SAAS,EAAC,wDAAwD;0BAAAC,QAAA,gBACrEhQ,OAAA;4BACEiU,QAAQ,EAAGC,CAAC,IAAK;8BACf,IAAI,CAACrO,mBAAmB,CAAC6P,QAAQ,CAAC,QAAQ,CAAC,EAAE;gCAC3C5P,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB,QAAQ,CACT,CAAC;8BACJ,CAAC,MAAM;gCACLC,sBAAsB,CACpBD,mBAAmB,CAACuQ,MAAM,CACvB9E,MAAM,IAAKA,MAAM,KAAK,QACzB,CACF,CAAC;8BACH;4BACF,CAAE;4BACFyE,OAAO,EAAElQ,mBAAmB,CAAC6P,QAAQ,CAAC,QAAQ,CAAE;4BAChD3U,EAAE,EAAC,QAAQ;4BACXgT,IAAI,EAAE,UAAW;4BACjBhE,SAAS,EAAC;0BAAM;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACFrQ,OAAA;4BACEgW,GAAG,EAAC,QAAQ;4BACZjG,SAAS,EAAC,8BAA8B;4BAAAC,QAAA,EACzC;0BAED;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAiCNrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCjK,wBAAwB,GACrBA,wBAAwB,GACxB;sBAAE;wBAAAmK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,kBAAkB;wBAC9BnE,KAAK,EAAE5J,eAAgB;wBACvBgO,QAAQ,EAAGC,CAAC,IAAKhO,kBAAkB,CAACgO,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,mBAAmB;wBAC/BnE,KAAK,EAAExJ,eAAgB;wBACvB4N,QAAQ,EAAGC,CAAC,IAAK5N,kBAAkB,CAAC4N,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAEpJ,YAAa;wBACpBwN,QAAQ,EAAGrE,MAAM,IAAK;0BAAA,IAAAyG,aAAA;0BACpB3P,eAAe,CAACkJ,MAAM,CAAC;0BACvB;0BACA,IAAIiC,eAAe,IAAAwE,aAAA,GAAGzG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,KAAK,cAAAwG,aAAA,cAAAA,aAAA,GAAI,EAAE;0BACzC,MAAMvE,aAAa,GAAG/F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,IAAKA,IAAI,CAAChQ,EAAE,KAAK8Q,eACxB,CAAC;0BACD,IAAIC,aAAa,EAAE;4BAAA,IAAAwE,qBAAA;4BACjBjT,mBAAmB,EAAAiT,qBAAA,GACjBxE,aAAa,CAACyE,QAAQ,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAC5B,CAAC;0BACH,CAAC,MAAM;4BACLjT,mBAAmB,CAAC,EAAE,CAAC;0BACzB;wBACF,CAAE;wBACF0M,SAAS,EAAC,SAAS;wBACnBoE,OAAO,EAAEpI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAExB,GAAG,CAAEwG,IAAI,KAAM;0BACjClB,KAAK,EAAEkB,IAAI,CAAChQ,EAAE;0BACd+O,KAAK,EAAEiB,IAAI,CAACC,SAAS,IAAI;wBAC3B,CAAC,CAAC,CAAE;wBACJuE,YAAY,EAAEA,CAAC3F,MAAM,EAAE4F,UAAU,KAC/B5F,MAAM,CAACE,KAAK,CACT2F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACDzB,WAAW,EAAC,oBAAoB;wBAChCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAE9N,iBAAiB,GACrB,mBAAmB,GACnB,mBAAmB;4BACvB+N,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrCrJ,iBAAiB,GAAGA,iBAAiB,GAAG;sBAAE;wBAAAuJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,gCAAgC;oBAAAC,QAAA,gBAC7ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,gBACEhQ,OAAA;wBACE+P,SAAS,EAAG,uBACVjM,oBAAoB,GAChB,eAAe,GACf,kBACL,oCAAoC;wBACrCmQ,QAAQ,EAAGC,CAAC,IAAK;0BACfrQ,kBAAkB,CAACqQ,CAAC,CAACL,MAAM,CAAChE,KAAK,CAAC;wBACpC,CAAE;wBACFA,KAAK,EAAEjM,eAAgB;wBAAAoM,QAAA,gBAEvBhQ,OAAA;0BAAQ6P,KAAK,EAAE;wBAAG;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAS,CAAC,EAC3BjN,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEmH,GAAG,CAAC,CAACiM,OAAO,EAAEtW,KAAK;0BAAA,IAAAuW,qBAAA;0BAAA,oBACpCzW,OAAA;4BAAQ6P,KAAK,EAAE2G,OAAO,CAACzV,EAAG;4BAAAiP,QAAA,IAAAyG,qBAAA,GACvBD,OAAO,CAACE,YAAY,cAAAD,qBAAA,cAAAA,qBAAA,GAAI,EAAE,EAC1BD,OAAO,CAACG,kBAAkB,KAAK,EAAE,GAC9B,KAAK,GAAGH,OAAO,CAACG,kBAAkB,GAClC,EAAE;0BAAA;4BAAAzG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACA,CAAC;wBAAA,CACV,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI,CAAC,eACTrQ,OAAA;wBAAK+P,SAAS,EAAC,yBAAyB;wBAAAC,QAAA,EACrClM,oBAAoB,GAAGA,oBAAoB,GAAG;sBAAE;wBAAAoM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9C,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BhQ,OAAA;oBACEyT,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA,IAAIyC,KAAK,GAAG,IAAI;sBAChBtP,oBAAoB,CAAC,EAAE,CAAC;sBACxB7C,uBAAuB,CAAC,EAAE,CAAC;sBAC3B,IACE0C,YAAY,KAAK,EAAE,IACnBA,YAAY,CAACoJ,KAAK,KAAK,EAAE,EACzB;wBACAjJ,oBAAoB,CAAC,4BAA4B,CAAC;wBAClD1H,KAAK,CAACiX,KAAK,CAAC,uBAAuB,CAAC;wBACpCD,KAAK,GAAG,KAAK;sBACf;sBACA,IAAItS,eAAe,KAAK,EAAE,EAAE;wBAC1BG,uBAAuB,CACrB,4BACF,CAAC;wBACD7E,KAAK,CAACiX,KAAK,CAAC,+BAA+B,CAAC;wBAC5CD,KAAK,GAAG,KAAK;sBACf;sBAEA,IAAIA,KAAK,EAAE;wBACT,MAAMU,MAAM,GAAGtT,mBAAmB,CAACuT,IAAI,CACpCnF,QAAQ;0BAAA,IAAAoF,kBAAA,EAAAC,iBAAA;0BAAA,OACPC,MAAM,CAACtF,QAAQ,aAARA,QAAQ,wBAAAoF,kBAAA,GAARpF,QAAQ,CAAEA,QAAQ,cAAAoF,kBAAA,uBAAlBA,kBAAA,CAAoB/V,EAAE,CAAC,KAC5BiW,MAAM,CAACvQ,YAAY,CAACoJ,KAAK,CAAC,IAC5BmH,MAAM,CAACtF,QAAQ,aAARA,QAAQ,wBAAAqF,iBAAA,GAARrF,QAAQ,CAAE8E,OAAO,cAAAO,iBAAA,uBAAjBA,iBAAA,CAAmBhW,EAAE,CAAC,KAC3BiW,MAAM,CAACpT,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,MAAMqT,UAAU,GAAGvT,uBAAuB,CAACmT,IAAI,CAC5CnF,QAAQ;0BAAA,IAAAwF,mBAAA,EAAAC,qBAAA;0BAAA,OACPH,MAAM,CAACtF,QAAQ,aAARA,QAAQ,wBAAAwF,mBAAA,GAARxF,QAAQ,CAAEA,QAAQ,cAAAwF,mBAAA,uBAAlBA,mBAAA,CAAoBnW,EAAE,CAAC,KAC5BiW,MAAM,CAACvQ,YAAY,CAACoJ,KAAK,CAAC,IAC5BmH,MAAM,CAACtF,QAAQ,aAARA,QAAQ,wBAAAyF,qBAAA,GAARzF,QAAQ,CAAE0F,gBAAgB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4BpW,EAAE,CAAC,KACpCiW,MAAM,CAACpT,eAAe,CAAC;wBAAA,CAC7B,CAAC;wBAED,IAAI,CAACgT,MAAM,IAAI,CAACK,UAAU,EAAE;0BAAA,IAAAI,mBAAA;0BAC1B;0BACA,IAAIxF,eAAe,IAAAwF,mBAAA,GAAG5Q,YAAY,CAACoJ,KAAK,cAAAwH,mBAAA,cAAAA,mBAAA,GAAI,EAAE;0BAC9C,MAAMvF,aAAa,GAAG/F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE4D,IAAI,CAClCoB,IAAI,IACHiG,MAAM,CAACjG,IAAI,CAAChQ,EAAE,CAAC,KAAKiW,MAAM,CAACnF,eAAe,CAC9C,CAAC;0BACDvE,OAAO,CAACC,GAAG,CAACuE,aAAa,CAAC;0BAE1B,IAAIA,aAAa,EAAE;4BAAA,IAAAwF,sBAAA,EAAAC,sBAAA;4BACjB;4BACA,IAAIC,cAAc,GAAG5T,eAAe,aAAfA,eAAe,cAAfA,eAAe,GAAI,EAAE;4BAE1CkO,aAAa,aAAbA,aAAa,wBAAAwF,sBAAA,GAAbxF,aAAa,CAAEyE,QAAQ,cAAAe,sBAAA,uBAAvBA,sBAAA,CAAyBxM,OAAO,CAAE2M,OAAO,IAAK;8BAC5CnK,OAAO,CAACC,GAAG,CAACkK,OAAO,CAAC1W,EAAE,CAAC;4BACzB,CAAC,CAAC;4BAEF,MAAM2W,YAAY,GAChB5F,aAAa,aAAbA,aAAa,wBAAAyF,sBAAA,GAAbzF,aAAa,CAAEyE,QAAQ,cAAAgB,sBAAA,uBAAvBA,sBAAA,CAAyB5H,IAAI,CAC1BoB,IAAI,IACHiG,MAAM,CAACjG,IAAI,CAAChQ,EAAE,CAAC,KAAKiW,MAAM,CAACQ,cAAc,CAC7C,CAAC;4BAEH,IAAIE,YAAY,EAAE;8BAChB;8BACAnU,sBAAsB,CAAC,CACrB,GAAGD,mBAAmB,EACtB;gCACEoO,QAAQ,EAAEI,aAAa;gCACvB0E,OAAO,EAAEkB;8BACX,CAAC,CACF,CAAC;8BACFhR,eAAe,CAAC,EAAE,CAAC;8BACnB7C,kBAAkB,CAAC,EAAE,CAAC;8BACtByJ,OAAO,CAACC,GAAG,CAACjK,mBAAmB,CAAC;4BAClC,CAAC,MAAM;8BACLsD,oBAAoB,CAClB,kCACF,CAAC;8BACD1H,KAAK,CAACiX,KAAK,CACT,kCACF,CAAC;4BACH;0BACF,CAAC,MAAM;4BACLvP,oBAAoB,CAClB,0BACF,CAAC;4BACD1H,KAAK,CAACiX,KAAK,CAAC,0BAA0B,CAAC;0BACzC;wBACF,CAAC,MAAM;0BACLvP,oBAAoB,CAClB,4CACF,CAAC;0BACD1H,KAAK,CAACiX,KAAK,CACT,4CACF,CAAC;wBACH;sBACF;oBACF,CAAE;oBACFpG,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,gBAEjEhQ,OAAA;sBACEiT,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBuE,KAAK,EAAC,QAAQ;sBAAA3H,QAAA,eAEdhQ,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBuT,CAAC,EAAC;sBAAmD;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,EAAM;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB,CAAC,eACTrQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,gBACpChQ,OAAA;sBAAK+P,SAAS,EAAC,0CAA0C;sBAAAC,QAAA,EAAC;oBAE1D;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAK+P,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,GACrCtM,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAE6G,GAAG,CAC3B,CAACqN,YAAY,EAAE1X,KAAK;wBAAA,IAAA2X,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;wBAAA,oBAClBlY,OAAA;0BAEE+P,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3ChQ,OAAA;4BAAK+P,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClChQ,OAAA;8BACEyT,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAM0E,eAAe,GACnBzU,uBAAuB,CAAC0S,MAAM,CAC5B,CAACgC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAKnY,KAC5B,CAAC;gCACHuD,4BAA4B,CAAC,CAC3B,GAAGD,yBAAyB,EAC5BoU,YAAY,CAAC7W,EAAE,CAChB,CAAC;gCACF4C,0BAA0B,CACxBwU,eACF,CAAC;8BACH,CAAE;8BAAAnI,QAAA,eAEFhQ,OAAA;gCACEiT,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBuE,KAAK,EAAC,QAAQ;gCAAA3H,QAAA,eAEdhQ,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBuT,CAAC,EAAC;gCAAuE;kCAAArD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNrQ,OAAA;4BAAK+P,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxChQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAwH,qBAAA,IAAAC,sBAAA,GACnBF,YAAY,CAAClG,QAAQ,cAAAoG,sBAAA,uBAArBA,sBAAA,CAAuB9G,SAAS,cAAA6G,qBAAA,cAAAA,qBAAA,GAAI,KAAK;4BAAA;8BAAA3H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNrQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA0H,sBAAA,IAAAC,sBAAA,GAClBJ,YAAY,CAACR,gBAAgB,cAAAY,sBAAA,uBAA7BA,sBAAA,CACGtB,YAAY,cAAAqB,sBAAA,cAAAA,sBAAA,GAAI,IAAI;4BAAA;8BAAA7H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACrB,CAAC,eACNrQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAA4H,sBAAA,IAAAC,sBAAA,GACrBN,YAAY,CAACR,gBAAgB,cAAAc,sBAAA,uBAA7BA,sBAAA,CACGvB,kBAAkB,cAAAsB,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAA/H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC5B,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GAlDDnQ,KAAK;0BAAAgQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAmDP,CAAC;sBAAA,CAEV,CAAC,EACA/M,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEiH,GAAG,CAAC,CAACqN,YAAY,EAAE1X,KAAK;wBAAA,IAAAoY,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;wBAAA,oBAC5C3Y,OAAA;0BAEE+P,SAAS,EAAC,iCAAiC;0BAAAC,QAAA,gBAE3ChQ,OAAA;4BAAK+P,SAAS,EAAC,qBAAqB;4BAAAC,QAAA,eAClChQ,OAAA;8BACEyT,OAAO,EAAEA,CAAA,KAAM;gCACb,MAAM0E,eAAe,GACnB7U,mBAAmB,CAAC8S,MAAM,CACxB,CAACgC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAKnY,KAC5B,CAAC;gCACHqD,sBAAsB,CAAC4U,eAAe,CAAC;8BACzC,CAAE;8BAAAnI,QAAA,eAEFhQ,OAAA;gCACEiT,KAAK,EAAC,4BAA4B;gCAClCC,IAAI,EAAC,MAAM;gCACXC,OAAO,EAAC,WAAW;gCACnB,gBAAa,KAAK;gCAClBC,MAAM,EAAC,cAAc;gCACrBuE,KAAK,EAAC,QAAQ;gCAAA3H,QAAA,eAEdhQ,OAAA;kCACE,kBAAe,OAAO;kCACtB,mBAAgB,OAAO;kCACvBuT,CAAC,EAAC;gCAAuE;kCAAArD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC1E;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACC;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACA;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC,eACNrQ,OAAA;4BAAK+P,SAAS,EAAC,2BAA2B;4BAAAC,QAAA,gBACxChQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAS;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAiI,sBAAA,IAAAC,sBAAA,GACnBX,YAAY,CAAClG,QAAQ,cAAA6G,sBAAA,uBAArBA,sBAAA,CAAuBvH,SAAS,cAAAsH,sBAAA,cAAAA,sBAAA,GAAI,KAAK;4BAAA;8BAAApI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvC,CAAC,eACNrQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAQ;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAmI,qBAAA,IAAAC,sBAAA,GAClBb,YAAY,CAACpB,OAAO,cAAAiC,sBAAA,uBAApBA,sBAAA,CAAsB/B,YAAY,cAAA8B,qBAAA,cAAAA,qBAAA,GAAI,IAAI;4BAAA;8BAAAtI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACxC,CAAC,eACNrQ,OAAA;8BAAAgQ,QAAA,gBACEhQ,OAAA;gCAAAgQ,QAAA,EAAG;8BAAW;gCAAAE,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAG,CAAC,EAAC,GAAG,GAAAqI,sBAAA,IAAAC,sBAAA,GACrBf,YAAY,CAACpB,OAAO,cAAAmC,sBAAA,uBAApBA,sBAAA,CAAsBhC,kBAAkB,cAAA+B,sBAAA,cAAAA,sBAAA,GACvC,KAAK;4BAAA;8BAAAxI,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC;wBAAA,GA3CDnQ,KAAK;0BAAAgQ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA4CP,CAAC;sBAAA,CACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNrQ,OAAA;gBAAK+P,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DhQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAM;oBACb,IAAIyC,KAAK,GAAG,IAAI;oBAChBtQ,uBAAuB,CAAC,EAAE,CAAC;oBAC3BI,2BAA2B,CAAC,EAAE,CAAC;oBAE/B,IAAIH,mBAAmB,CAAC+S,MAAM,KAAK,CAAC,EAAE;sBACpC5S,2BAA2B,CACzB,0BACF,CAAC;sBACDkQ,KAAK,GAAG,KAAK;oBACf;oBAEA,IAAIA,KAAK,EAAE;sBACTzK,aAAa,CAAC,CAAC,CAAC;oBAClB,CAAC,MAAM;sBACLvM,KAAK,CAACiX,KAAK,CACT,oDACF,CAAC;oBACH;kBACF,CAAE;kBACFpG,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEP7E,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfhQ,OAAA;gBAAK+P,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAA,GACMgK,0BAA0B,CAAC;oBAAE+F,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACzD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFhQ,OAAA;oBAAA,GAAWkK,2BAA2B,CAAC;kBAAC;oBAAAgG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC5CrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBhQ,OAAA;sBACEiT,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBrD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DhQ,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBuT,CAAC,EAAC;sBAA4G;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrQ,OAAA;kBAAO6Y,KAAK,EAAExY,eAAgB;kBAAA2P,QAAA,eAC5BhQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCzG,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CACvB6M,MAAM,CAAE5L,IAAI,IAAK,CAACnB,WAAW,CAACqM,QAAQ,CAAClL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,EAAEtK,KAAK,kBACfF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACsO;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG+I,UAAU,CAACvO,IAAI,CAACwO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFgP,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJ7F,IAAI,CAACsO,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHxG,0BAA0B,aAA1BA,0BAA0B,uBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,EAAEtK,KAAK,kBAC3CF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACqL;wBAAI;0BAAA3F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG,CAACxF,IAAI,CAAC0O,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACb3J,6BAA6B,CAAEQ,SAAS,IACtCA,SAAS,CAAC8L,MAAM,CACd,CAACgC,CAAC,EAAEe,aAAa,KACfjZ,KAAK,KAAKiZ,aACd,CACF,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJ7F,IAAI,CAACqL,IAAI;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DhQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEP7E,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfhQ,OAAA;gBAAK+P,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,2BAA2B;wBACvCnE,KAAK,EAAEpI,aAAc;wBACrBwM,QAAQ,EAAGC,CAAC,IAAKxM,gBAAgB,CAACwM,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,wBAAwB;wBACpCnE,KAAK,EAAEhI,UAAW;wBAClBoM,QAAQ,EAAGC,CAAC,IAAKpM,aAAa,CAACoM,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,QAAQ;wBACbC,WAAW,EAAC,mBAAmB;wBAC/BnE,KAAK,EAAE5H,MAAO;wBACdgM,QAAQ,EAAGC,CAAC,IAAKhM,SAAS,CAACgM,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC5C;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAA,GACMkL,yBAAyB,CAAC;oBAAE6E,SAAS,EAAE;kBAAW,CAAC,CAAC;kBACxD;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFhQ,OAAA;oBAAA,GAAWmL,0BAA0B,CAAC;kBAAC;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC3CrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBhQ,OAAA;sBACEiT,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBrD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DhQ,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBuT,CAAC,EAAC;sBAA4G;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrQ,OAAA;kBAAO6Y,KAAK,EAAExY,eAAgB;kBAAA2P,QAAA,eAC5BhQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCvG,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CACf2M,MAAM,CAAE5L,IAAI,IAAK,CAACnB,WAAW,CAACqM,QAAQ,CAAClL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,EAAEtK,KAAK,kBACfF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACsO;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG+I,UAAU,CAACvO,IAAI,CAACwO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFgP,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJ7F,IAAI,CAACsO,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHrF,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,EAAEtK,KAAK,kBACnCF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACqL;wBAAI;0BAAA3F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG,CAACxF,IAAI,CAAC0O,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACbxI,qBAAqB,CAAEX,SAAS,IAC9BA,SAAS,CAAC8L,MAAM,CACd,CAACgC,CAAC,EAAEe,aAAa,KACfjZ,KAAK,KAAKiZ,aACd,CACF,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA9CJ7F,IAAI,CAACqL,IAAI;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA+CX,CACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAGNrQ,OAAA;gBAAK+P,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DhQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EACnE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEP7E,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,gBACfhQ,OAAA;gBAAK+P,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAEtD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA,CAACR,MAAM;wBACLqQ,KAAK,EAAExH,gBAAiB;wBACxB4L,QAAQ,EAAGrE,MAAM,IAAK;0BACpBtH,mBAAmB,CAACsH,MAAM,CAAC;wBAC7B,CAAE;wBACFuE,OAAO,EAAE/H,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7B,GAAG,CAAE+H,SAAS,KAAM;0BACvCzC,KAAK,EAAEyC,SAAS,CAACvR,EAAE;0BACnB+O,KAAK,EAAEwC,SAAS,CAACK,cAAc,IAAI;wBACrC,CAAC,CAAC,CAAE;wBACJ4C,YAAY,EAAEA,CAAC3F,MAAM,EAAE4F,UAAU,KAC/B5F,MAAM,CAACE,KAAK,CACT2F,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC;wBACD1F,SAAS,EAAC,SAAS;wBACnBiE,WAAW,EAAC,qBAAqB;wBACjCI,YAAY;wBACZC,MAAM,EAAE;0BACNC,OAAO,EAAEA,CAACC,IAAI,EAAE5I,KAAK,MAAM;4BACzB,GAAG4I,IAAI;4BACPC,UAAU,EAAE,MAAM;4BAClBC,MAAM,EAAElM,qBAAqB,GACzB,mBAAmB,GACnB,mBAAmB;4BACvBmM,SAAS,EAAE/I,KAAK,CAACgJ,SAAS,GAAG,MAAM,GAAG,MAAM;4BAC5C,SAAS,EAAE;8BACTF,MAAM,EAAE;4BACV;0BACF,CAAC,CAAC;0BACF7E,MAAM,EAAG2E,IAAI,KAAM;4BACjB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC,CAAC;0BACFC,WAAW,EAAGN,IAAI,KAAM;4BACtB,GAAGA,IAAI;4BACPjU,OAAO,EAAE,MAAM;4BACfsU,UAAU,EAAE;0BACd,CAAC;wBACH;sBAAE;wBAAA1E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENrQ,OAAA;oBAAK+P,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,gBAC5ChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE+P,SAAS,EAAC,wEAAwE;wBAClFgE,IAAI,EAAC,MAAM;wBACXC,WAAW,EAAC,eAAe;wBAC3BnE,KAAK,EAAEhH,YAAa;wBACpBoL,QAAQ,EAAGC,CAAC,IAAKpL,eAAe,CAACoL,CAAC,CAACL,MAAM,CAAChE,KAAK;sBAAE;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ChQ,OAAA;oBAAK+P,SAAS,EAAC,sBAAsB;oBAAAC,QAAA,gBACnChQ,OAAA;sBAAK+P,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,EAAC;oBAE9C;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNrQ,OAAA;sBAAAgQ,QAAA,eACEhQ,OAAA;wBACE6P,KAAK,EAAE5G,aAAc;wBACrBgL,QAAQ,EAAGC,CAAC,IAAKhL,gBAAgB,CAACgL,CAAC,CAACL,MAAM,CAAChE,KAAK,CAAE;wBAClDE,SAAS,EAAC,wEAAwE;wBAAAC,QAAA,gBAElFhQ,OAAA;0BAAQ6P,KAAK,EAAE,EAAG;0BAAAG,QAAA,EAAC;wBAAa;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACzCrQ,OAAA;0BAAQ6P,KAAK,EAAE,SAAU;0BAAAG,QAAA,EAAC;wBAAO;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC1CrQ,OAAA;0BAAQ6P,KAAK,EAAE,UAAW;0BAAAG,QAAA,EAAC;wBAAQ;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eAC5CrQ,OAAA;0BAAQ6P,KAAK,EAAE,QAAS;0BAAAG,QAAA,EAAC;wBAAM;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,0CAA0C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNrQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,gBACjDhQ,OAAA;kBAAA,GACMsL,wCAAwC,CAAC;oBAC3CyE,SAAS,EAAE;kBACb,CAAC,CAAC;kBACF;kBACAA,SAAS,EAAC,wEAAwE;kBAAAC,QAAA,gBAElFhQ,OAAA;oBAAA,GAAWuL,yCAAyC,CAAC;kBAAC;oBAAA2E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1DrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,eACnBhQ,OAAA;sBACEiT,KAAK,EAAC,4BAA4B;sBAClCC,IAAI,EAAC,MAAM;sBACXC,OAAO,EAAC,WAAW;sBACnB,gBAAa,KAAK;sBAClBC,MAAM,EAAC,cAAc;sBACrBrD,SAAS,EAAC,iDAAiD;sBAAAC,QAAA,eAE3DhQ,OAAA;wBACE,kBAAe,OAAO;wBACtB,mBAAgB,OAAO;wBACvBuT,CAAC,EAAC;sBAA4G;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/G;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAC;kBAEtB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNrQ,OAAA;kBAAO6Y,KAAK,EAAExY,eAAgB;kBAAA2P,QAAA,eAC5BhQ,OAAA;oBAAK+P,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GACnCrG,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAC9ByM,MAAM,CAAE5L,IAAI,IAAK,CAACnB,WAAW,CAACqM,QAAQ,CAAClL,IAAI,CAACzJ,EAAE,CAAC,CAAC,CACjDwJ,GAAG,CAAC,CAACC,IAAI,EAAEtK,KAAK,kBACfF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACsO;wBAAS;0BAAA5I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG+I,UAAU,CAACvO,IAAI,CAACwO,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAAC,KACzC;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACbnK,cAAc,CAAC,CAAC,GAAGD,WAAW,EAAEmB,IAAI,CAACzJ,EAAE,CAAC,CAAC;wBAC3C,CAAE;wBACFgP,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GAzCJ7F,IAAI,CAACsO,SAAS;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OA0ChB,CACN,CAAC,EACHjF,iCAAiC,aAAjCA,iCAAiC,uBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,EAAEtK,KAAK,kBACVF,OAAA;sBACE+P,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,gBAGpFhQ,OAAA;wBAAK+P,SAAS,EAAC,kEAAkE;wBAAAC,QAAA,eAC/EhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCE,OAAO,EAAC,WAAW;0BACnBD,IAAI,EAAC,cAAc;0BACnByE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,gBAEdhQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAqN;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAChOrQ,OAAA;4BAAMuT,CAAC,EAAC;0BAAuI;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC/I;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBAAK+P,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjDhQ,OAAA;0BAAK+P,SAAS,EAAC,gFAAgF;0BAAAC,QAAA,EAC5FxF,IAAI,CAACqL;wBAAI;0BAAA3F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACP,CAAC,eACNrQ,OAAA;0BAAAgQ,QAAA,GACG,CAACxF,IAAI,CAAC0O,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,KAC1C;wBAAA;0BAAA/I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrQ,OAAA;wBACEyT,OAAO,EAAEA,CAAA,KAAM;0BACbpI,oCAAoC,CACjCf,SAAS,IACRA,SAAS,CAAC8L,MAAM,CACd,CAACgC,CAAC,EAAEe,aAAa,KACfjZ,KAAK,KAAKiZ,aACd,CACJ,CAAC;wBACH,CAAE;wBACFpJ,SAAS,EAAC,wDAAwD;wBAAAC,QAAA,eAElEhQ,OAAA;0BACEiT,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBuE,KAAK,EAAC,QAAQ;0BAAA3H,QAAA,eAEdhQ,OAAA;4BACE,kBAAe,OAAO;4BACtB,mBAAgB,OAAO;4BACvBuT,CAAC,EAAC;0BAAsB;4BAAArD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA,GA/CJ7F,IAAI,CAACqL,IAAI;sBAAA3F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAgDX,CAET,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENrQ,OAAA;gBAAK+P,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,gBAC1DhQ,OAAA;kBACEyT,OAAO,EAAEA,CAAA,KAAMhI,aAAa,CAAC,CAAC,CAAE;kBAChCsE,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,EACxE;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrQ,OAAA;kBACEoZ,QAAQ,EAAElM,iBAAkB;kBAC5BuG,OAAO,EAAE,MAAAA,CAAA,KAAY;oBAAA,IAAA4F,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA;oBACnB,MAAMC,aAAa,GAAGlW,mBAAmB,CAACiH,GAAG,CAC1CwG,IAAI;sBAAA,IAAA0I,aAAA,EAAAC,cAAA;sBAAA,OAAM;wBACTlD,OAAO,GAAAiD,aAAA,GAAE1I,IAAI,CAACyF,OAAO,cAAAiD,aAAA,uBAAZA,aAAA,CAAc1Y,EAAE;wBACzB2Q,QAAQ,GAAAgI,cAAA,GAAE3I,IAAI,CAACW,QAAQ,cAAAgI,cAAA,uBAAbA,cAAA,CAAe3Y;sBAC3B,CAAC;oBAAA,CACH,CAAC;oBACD;oBACA,MAAMD,QAAQ,CACZxB,UAAU,CAACyB,EAAE,EAAE;sBACbmO,UAAU,EAAElO,SAAS;sBACrBmO,SAAS,EAAE/N,QAAQ;sBACnB4P,SAAS,EAAEhQ,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrCgO,SAAS,EAAExN,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,EAAE;sBAC1ByN,aAAa,EAAErN,KAAK;sBACpBsN,aAAa,EAAE9N,KAAK;sBACpB+N,eAAe,EAAEnN,OAAO;sBACxBkO,YAAY,EAAE9N,IAAI;sBAClBiN,eAAe,EAAE7M,OAAO,CAACiN,KAAK;sBAC9B;sBACA7M,WAAW,GAAAqW,kBAAA,GAAErW,WAAW,CAAC6M,KAAK,cAAAwJ,kBAAA,cAAAA,kBAAA,GAAI,EAAE;sBACpCpI,SAAS,EAAEjN,QAAQ;sBACnBkN,SAAS,EAAE3M,QAAQ;sBACnB4M,gBAAgB,EAAExM,eAAe;sBACjC;sBACA4M,mBAAmB,EAAE9L,eAAe;sBACpC4L,WAAW,EAAExL,mBAAmB;sBAChC2L,gBAAgB,EAAEvL,eAAe;sBACjCwL,gBAAgB,EAAEpL,eAAe;sBACjCqL,QAAQ,GAAA4H,oBAAA,GAAE7S,YAAY,CAACoJ,KAAK,cAAAyJ,oBAAA,cAAAA,oBAAA,GAAI,EAAE;sBAClC;sBACApH,cAAc,EAAEzK,aAAa;sBAC7B0K,WAAW,EAAEtK,UAAU;sBACvBuK,cAAc,EAAEnK,MAAM;sBACtBqK,SAAS,GAAAiH,qBAAA,GAAElR,gBAAgB,CAACwH,KAAK,cAAA0J,qBAAA,cAAAA,qBAAA,GAAI,EAAE;sBACvC1G,gBAAgB,EAAEpK,eAAe;sBACjCmK,aAAa,EAAE/J,YAAY;sBAC3BiK,gBAAgB,EAAE7J,aAAa;sBAC/B;sBACA0Q,uBAAuB,EAAE9P,0BAA0B;sBACnD+P,cAAc,EAAE5O,kBAAkB;sBAClC6O,8BAA8B,EAC5BzO,iCAAiC;sBACnC0O,aAAa,EAAEzQ,WAAW;sBAC1B0C,SAAS,EAAEyN,aAAa,aAAbA,aAAa,cAAbA,aAAa,GAAI,EAAE;sBAC9BO,iBAAiB,EAAEvW,yBAAyB,aAAzBA,yBAAyB,cAAzBA,yBAAyB,GAAI;oBAClD,CAAC,CACH,CAAC;kBACH,CAAE;kBACFuM,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,EAEjE9C,iBAAiB,GAAG,WAAW,GAAG;gBAAQ;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI,EAEP7E,UAAU,KAAK,CAAC,gBACfxL,OAAA;cAAK+P,SAAS,EAAC,EAAE;cAAAC,QAAA,eACfhQ,OAAA;gBAAK+P,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,eACjDhQ,OAAA;kBAAK+P,SAAS,EAAC,oDAAoD;kBAAAC,QAAA,gBACjEhQ,OAAA;oBACEiT,KAAK,EAAC,4BAA4B;oBAClCC,IAAI,EAAC,MAAM;oBACXC,OAAO,EAAC,WAAW;oBACnB,gBAAa,KAAK;oBAClBC,MAAM,EAAC,cAAc;oBACrBrD,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eAE9EhQ,OAAA;sBACE,kBAAe,OAAO;sBACtB,mBAAgB,OAAO;sBACvBuT,CAAC,EAAC;oBAAuB;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,wCAAwC;oBAAAC,QAAA,EAAC;kBAExD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,EAAC;kBAGpE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNrQ,OAAA;oBAAK+P,SAAS,EAAC,6CAA6C;oBAAAC,QAAA,eAS1DhQ,OAAA;sBACEgT,IAAI,EAAC,YAAY;sBACjBjD,SAAS,EAAC,wDAAwD;sBAAAC,QAAA,EACnE;oBAED;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC1P,EAAA,CAz9EQD,cAAc;EAAA,QACJ5B,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EA2HlBU,WAAW,EA4BXA,WAAW,EA4BXA,WAAW,EA6BGb,WAAW,EAGPA,WAAW,EAGVA,WAAW,EAGfA,WAAW,EAILA,WAAW,EAIjBA,WAAW;AAAA;AAAAob,EAAA,GArOvBtZ,cAAc;AA29EvB,eAAeA,cAAc;AAAC,IAAAsZ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}