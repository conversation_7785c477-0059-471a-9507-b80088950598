{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import DefaultLayout from\"../../layouts/DefaultLayout\";import{Link,useLocation,useNavigate}from\"react-router-dom\";import{useDispatch,useSelector}from\"react-redux\";import{getDashData}from\"../../redux/actions/dashActions\";import LayoutSection from\"../../components/LayoutSection\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function DashboardScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const[alertSelect,setAlertSelect]=useState(\"Assurance\");const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const dashData=useSelector(state=>state.getDashData);const{loadingDashData,errorDashData,alert,notvalid,valid,desponible,notdesponible,assurances,cartgris,visitetechniques}=dashData;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getDashData());}},[navigate,userInfo,dispatch]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 md:flex justify-between\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black \",children:\"Acceuil\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex \",children:[/*#__PURE__*/_jsxs(Link,{to:\"/depenses/charges/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Charges\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/contrats/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Contrat\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/clients/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"Client\"]}),/*#__PURE__*/_jsxs(Link,{to:\"/reservations/add\",className:\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 4.5v15m7.5-7.5h-15\"})}),\"R\\xE9servation\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-5 grid-cols-3 gap-4 my-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#e7191b]  rounded m-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between item-center p-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-10 h-10 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl text-white font-bold text-center \",children:alert})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs text-right px-1\",children:\"alertes des voitures\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center bg-[#bc0d0e] mt-2 p-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs\",children:\"Voir d\\xE9tail\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"w-6 h-6 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#852b99]  rounded m-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between item-center p-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-10 h-10 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl text-white font-bold text-center \",children:parseFloat(notvalid).toFixed(2)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-white font-bold text-xs text-right px-1\",children:[\"Total des impay\\xE9s \",new Date().getFullYear()]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center bg-[#6e1881]  mt-2 p-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs\",children:\"Voir d\\xE9tail\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"w-6 h-6 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#28b779]  rounded m-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between item-center p-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-10 h-10 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl text-white font-bold text-center \",children:parseFloat(valid).toFixed(2)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-white font-bold text-xs text-right px-1\",children:[\"Les paiement \\xE0 valider \",new Date().getFullYear()]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs\",children:\"Voir d\\xE9tail\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"w-6 h-6 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#ffb848]  rounded m-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between item-center p-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"img\",{className:\"w-10 h-10 text-white\",src:\"data:image/png;base64,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\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl text-white font-bold text-center \",children:notdesponible})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs text-right px-1\",children:\"R\\xE9servations\"}),/*#__PURE__*/_jsxs(\"a\",{href:\"/reservations\",className:\"flex justify-between items-center bg-[#cb871b]  mt-2 p-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs\",children:\"Voir d\\xE9tail\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"w-6 h-6 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#28b779]  rounded m-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between item-center p-1 \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsx(\"img\",{className:\"w-10 h-10 text-white\",src:\"data:image/png;base64,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\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl text-white font-bold text-center \",children:desponible})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs text-right px-1\",children:\"Disponibles\"}),/*#__PURE__*/_jsxs(\"a\",{href:\"/contrats\",className:\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-white font-bold text-xs\",children:\"Voir d\\xE9tail\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"w-6 h-6 text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"})})})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col \",children:/*#__PURE__*/_jsx(\"div\",{className:\" w-full px-1 py-1\",children:/*#__PURE__*/_jsxs(LayoutSection,{title:\"Les alertes des voitures\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row\",children:[/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setAlertSelect(\"Assurance\");},className:\"px-3 text-xs font-bold cursor-pointer py-2  \".concat(alertSelect===\"Assurance\"?\"border-[#d12610]  border-t-2\":\"\"),children:[\"Assurance \",assurances.length]}),/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setAlertSelect(\"Visite technique\");},className:\"px-3 text-xs font-bold cursor-pointer py-2  \".concat(alertSelect===\"Visite technique\"?\"border-[#d12610] border-t-2\":\"\"),children:[\"Visite technique \",visitetechniques.length]}),/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setAlertSelect(\"Carte Gris\");},className:\"px-3 text-xs font-bold cursor-pointer py-2  \".concat(alertSelect===\"Carte Gris\"?\"border-[#d12610]  border-t-2\":\"\"),children:[\"Carte Gris \",cartgris.length]})]}),alertSelect===\"Assurance\"?/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left dark:bg-meta-4\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Voitures\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Fin assurance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:assurances===null||assurances===void 0?void 0:assurances.map((car,id)=>{var _car$marque$marque_ca,_car$model$model_car,_car$matricule;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:car.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_car$marque$marque_ca=car.marque.marque_car)!==null&&_car$marque$marque_ca!==void 0?_car$marque$marque_ca:\"---\",\" \",(_car$model$model_car=car.model.model_car)!==null&&_car$model$model_car!==void 0?_car$model$model_car:\"---\",\" -\",\" \",(_car$matricule=car.matricule)!==null&&_car$matricule!==void 0?_car$matricule:\"---\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:car.end_assurance})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cars/edit/\"+car.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})})]});})})]})}):null,alertSelect===\"Visite technique\"?/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left dark:bg-meta-4\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Voitures\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Fin assurance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:visitetechniques===null||visitetechniques===void 0?void 0:visitetechniques.map((car,id)=>{var _car$marque$marque_ca2,_car$model$model_car2,_car$matricule2;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:car.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_car$marque$marque_ca2=car.marque.marque_car)!==null&&_car$marque$marque_ca2!==void 0?_car$marque$marque_ca2:\"---\",\" \",(_car$model$model_car2=car.model.model_car)!==null&&_car$model$model_car2!==void 0?_car$model$model_car2:\"---\",\" -\",\" \",(_car$matricule2=car.matricule)!==null&&_car$matricule2!==void 0?_car$matricule2:\"---\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:car.end_assurance})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cars/edit/\"+car.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})})]});})})]})}):null,alertSelect===\"Carte Gris\"?/*#__PURE__*/_jsx(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left dark:bg-meta-4\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"N\\xB0\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Voitures\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Fin assurance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"py-4 px-4 font-bold text-black text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:cartgris===null||cartgris===void 0?void 0:cartgris.map((car,id)=>{var _car$marque$marque_ca3,_car$model$model_car3,_car$matricule3;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max \",children:car.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_car$marque$marque_ca3=car.marque.marque_car)!==null&&_car$marque$marque_ca3!==void 0?_car$marque$marque_ca3:\"---\",\" \",(_car$model$model_car3=car.model.model_car)!==null&&_car$model$model_car3!==void 0?_car$model$model_car3:\"---\",\" -\",\" \",(_car$matricule3=car.matricule)!==null&&_car$matricule3!==void 0?_car$matricule3:\"---\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:car.end_assurance})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px] \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max flex flex-row \",children:/*#__PURE__*/_jsx(Link,{className:\"mx-1 update-class\",to:\"/cars/edit/\"+car.id,children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})})})})]});})})]})}):null]})})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default DashboardScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "Link", "useLocation", "useNavigate", "useDispatch", "useSelector", "getDashData", "LayoutSection", "jsx", "_jsx", "jsxs", "_jsxs", "DashboardScreen", "navigate", "location", "dispatch", "alertSelect", "setAlertSelect", "userLogin", "state", "userInfo", "dashData", "loadingDashData", "errorDashData", "alert", "notvalid", "valid", "desponible", "notdesponible", "assurances", "<PERSON><PERSON><PERSON>", "visitetechniques", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "to", "class", "parseFloat", "toFixed", "Date", "getFullYear", "src", "title", "onClick", "concat", "length", "map", "car", "id", "_car$marque$marque_ca", "_car$model$model_car", "_car$matricule", "marque", "marque_car", "model", "model_car", "matricule", "end_assurance", "_car$marque$marque_ca2", "_car$model$model_car2", "_car$matricule2", "_car$marque$marque_ca3", "_car$model$model_car3", "_car$matricule3"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { getDashData } from \"../../redux/actions/dashActions\";\nimport LayoutSection from \"../../components/LayoutSection\";\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const dispatch = useDispatch();\n\n  const [alertSelect, setAlertSelect] = useState(\"Assurance\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const dashData = useSelector((state) => state.getDashData);\n  const {\n    loadingDashData,\n    errorDashData,\n    alert,\n    notvalid,\n    valid,\n    desponible,\n    notdesponible,\n    assurances,\n    cartgris,\n    visitetechniques,\n  } = dashData;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getDashData());\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        {/* top dash */}\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 md:flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black \">Acceuil</h4>\n            <div className=\"flex \">\n              <Link\n                to={\"/depenses/charges/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Charges\n              </Link>\n              <Link\n                to={\"/contrats/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Contrat\n              </Link>\n              <Link\n                to={\"/clients/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Client\n              </Link>\n              <Link\n                to={\"/reservations/add\"}\n                className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm mx-1\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-6 h-6\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 4.5v15m7.5-7.5h-15\"\n                  />\n                </svg>\n                Réservation\n              </Link>\n            </div>\n          </div>\n          {/* list */}\n          <div className=\"grid md:grid-cols-5 grid-cols-3 gap-4 my-5\">\n            <div className=\"bg-[#e7191b]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {alert}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                alertes des voitures\n              </div>\n              <div className=\"flex justify-between items-center bg-[#bc0d0e] mt-2 p-1\">\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-[#852b99]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {parseFloat(notvalid).toFixed(2)}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Total des impayés {new Date().getFullYear()}\n              </div>\n              <div className=\"flex justify-between items-center bg-[#6e1881]  mt-2 p-1\">\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-[#28b779]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    className=\"w-10 h-10 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {parseFloat(valid).toFixed(2)}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Les paiement à valider {new Date().getFullYear()}\n              </div>\n              <div className=\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\">\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n            <div className=\"bg-[#ffb848]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <img\n                    className=\"w-10 h-10 text-white\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {notdesponible}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Réservations\n              </div>\n              <a\n                href=\"/reservations\"\n                className=\"flex justify-between items-center bg-[#cb871b]  mt-2 p-1\"\n              >\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </a>\n            </div>\n            <div className=\"bg-[#28b779]  rounded m-1\">\n              <div className=\"flex justify-between item-center p-1 \">\n                <div className=\"text-center\">\n                  <img\n                    className=\"w-10 h-10 text-white\"\n                    src=\"data:image/png;base64,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\"\n                  />\n                </div>\n                <div className=\"text-2xl text-white font-bold text-center \">\n                  {desponible}\n                </div>\n              </div>\n              <div className=\"text-white font-bold text-xs text-right px-1\">\n                Disponibles\n              </div>\n              <a\n                href=\"/contrats\"\n                className=\"flex justify-between items-center bg-[#10a062]  mt-2 p-1\"\n              >\n                <div className=\"text-white font-bold text-xs\">Voir détail</div>\n                <div>\n                  <svg\n                    xmlns=\"http://www.w3.org/2000/svg\"\n                    fill=\"none\"\n                    viewBox=\"0 0 24 24\"\n                    stroke-width=\"1.5\"\n                    stroke=\"currentColor\"\n                    class=\"w-6 h-6 text-white\"\n                  >\n                    <path\n                      stroke-linecap=\"round\"\n                      stroke-linejoin=\"round\"\n                      d=\"m12.75 15 3-3m0 0-3-3m3 3h-7.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                    />\n                  </svg>\n                </div>\n              </a>\n            </div>\n          </div>\n          {/* list */}\n          <div className=\"flex md:flex-row flex-col \">\n            <div className=\" w-full px-1 py-1\">\n              <LayoutSection title=\"Les alertes des voitures\">\n                <div className=\"flex flex-row\">\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Assurance\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Assurance\"\n                        ? \"border-[#d12610]  border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Assurance {assurances.length}\n                  </div>\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Visite technique\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Visite technique\"\n                        ? \"border-[#d12610] border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Visite technique {visitetechniques.length}\n                  </div>\n                  <div\n                    onClick={() => {\n                      setAlertSelect(\"Carte Gris\");\n                    }}\n                    className={`px-3 text-xs font-bold cursor-pointer py-2  ${\n                      alertSelect === \"Carte Gris\"\n                        ? \"border-[#d12610]  border-t-2\"\n                        : \"\"\n                    }`}\n                  >\n                    Carte Gris {cartgris.length}\n                  </div>\n                </div>\n                {/* assurances */}\n                {alertSelect === \"Assurance\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {assurances?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n                {/* Visite technique */}\n                {alertSelect === \"Visite technique\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {visitetechniques?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n                {/* Carte Gris */}\n                {alertSelect === \"Carte Gris\" ? (\n                  <div className=\"max-w-full overflow-x-auto mt-3\">\n                    <table className=\"w-full table-auto\">\n                      <thead>\n                        <tr className=\"bg-gray-2 text-left dark:bg-meta-4\">\n                          <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            N°\n                          </th>\n                          <th className=\"min-w-[100px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Voitures\n                          </th>\n                          <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                            Fin assurance\n                          </th>\n                          <th className=\"py-4 px-4 font-bold text-black text-xs w-max \">\n                            Actions\n                          </th>\n                        </tr>\n                      </thead>\n                      <tbody>\n                        {cartgris?.map((car, id) => (\n                          <tr>\n                            <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4\">\n                              <p className=\"text-black  text-xs w-max \">\n                                {car.id}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.marque.marque_car ?? \"---\"}{\" \"}\n                                {car.model.model_car ?? \"---\"} -{\" \"}\n                                {car.matricule ?? \"---\"}\n                              </p>\n                            </td>\n                            <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                              <p className=\"text-black  text-xs w-max\">\n                                {car.end_assurance}\n                              </p>\n                            </td>\n                            <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px] \">\n                              <p className=\"text-black  text-xs w-max flex flex-row \">\n                                {/* edit */}\n                                <Link\n                                  className=\"mx-1 update-class\"\n                                  to={\"/cars/edit/\" + car.id}\n                                >\n                                  <svg\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                    fill=\"none\"\n                                    viewBox=\"0 0 24 24\"\n                                    stroke-width=\"1.5\"\n                                    stroke=\"currentColor\"\n                                    className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                  >\n                                    <path\n                                      stroke-linecap=\"round\"\n                                      stroke-linejoin=\"round\"\n                                      d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                    />\n                                  </svg>\n                                </Link>\n                              </p>\n                            </td>\n                          </tr>\n                        ))}\n                      </tbody>\n                    </table>\n                  </div>\n                ) : null}\n              </LayoutSection>\n            </div>\n          </div>\n        </div>\n\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,KAAQ,iCAAiC,CAC7D,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3D,QAAS,CAAAC,eAAeA,CAAA,CAAG,CACzB,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAW,QAAQ,CAAGZ,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAa,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACY,WAAW,CAAEC,cAAc,CAAC,CAAGlB,QAAQ,CAAC,WAAW,CAAC,CAE3D,KAAM,CAAAmB,SAAS,CAAGb,WAAW,CAAEc,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,QAAQ,CAAGhB,WAAW,CAAEc,KAAK,EAAKA,KAAK,CAACb,WAAW,CAAC,CAC1D,KAAM,CACJgB,eAAe,CACfC,aAAa,CACbC,KAAK,CACLC,QAAQ,CACRC,KAAK,CACLC,UAAU,CACVC,aAAa,CACbC,UAAU,CACVC,QAAQ,CACRC,gBACF,CAAC,CAAGV,QAAQ,CAEZ,KAAM,CAAAW,QAAQ,CAAG,GAAG,CAEpBlC,SAAS,CAAC,IAAM,CACd,GAAI,CAACsB,QAAQ,CAAE,CACbP,QAAQ,CAACmB,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLjB,QAAQ,CAACT,WAAW,CAAC,CAAC,CAAC,CACzB,CACF,CAAC,CAAE,CAACO,QAAQ,CAAEO,QAAQ,CAAEL,QAAQ,CAAC,CAAC,CAElC,mBACEN,IAAA,CAACT,aAAa,EAAAiC,QAAA,cACZtB,KAAA,QAAAsB,QAAA,eAEExB,IAAA,QAAKyB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,cAEtDxB,IAAA,MAAG0B,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBtB,KAAA,QAAKuB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN/B,IAAA,SAAMyB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,CACD,CAAC,cACNtB,KAAA,QAAKuB,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1HtB,KAAA,QAAKuB,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClExB,IAAA,OAAIyB,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,SAAO,CAAI,CAAC,cACjEtB,KAAA,QAAKuB,SAAS,CAAC,OAAO,CAAAD,QAAA,eACpBtB,KAAA,CAACV,IAAI,EACHwC,EAAE,CAAE,uBAAwB,CAC5BP,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eAE9ExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,cACP7B,KAAA,CAACV,IAAI,EACHwC,EAAE,CAAE,eAAgB,CACpBP,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eAE9ExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,UAER,EAAM,CAAC,cACP7B,KAAA,CAACV,IAAI,EACHwC,EAAE,CAAE,cAAe,CACnBP,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eAE9ExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,SAER,EAAM,CAAC,cACP7B,KAAA,CAACV,IAAI,EACHwC,EAAE,CAAE,mBAAoB,CACxBP,SAAS,CAAC,oEAAoE,CAAAD,QAAA,eAE9ExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,wBAAwB,CAC3B,CAAC,CACC,CAAC,iBAER,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,cAEN7B,KAAA,QAAKuB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,eACzDtB,KAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxB,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cAEhCxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,wNAAwN,CAC3N,CAAC,CACC,CAAC,CACH,CAAC,cACN/B,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDT,KAAK,CACH,CAAC,EACH,CAAC,cACNf,IAAA,QAAKyB,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,sBAE9D,CAAK,CAAC,cACNtB,KAAA,QAAKuB,SAAS,CAAC,yDAAyD,CAAAD,QAAA,eACtExB,IAAA,QAAKyB,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,cAC/DxB,IAAA,QAAAwB,QAAA,cACExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBG,KAAK,CAAC,oBAAoB,CAAAT,QAAA,cAE1BxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,qEAAqE,CACxE,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cACN7B,KAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxB,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cAEhCxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,CACH,CAAC,cACN/B,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDU,UAAU,CAAClB,QAAQ,CAAC,CAACmB,OAAO,CAAC,CAAC,CAAC,CAC7B,CAAC,EACH,CAAC,cACNjC,KAAA,QAAKuB,SAAS,CAAC,8CAA8C,CAAAD,QAAA,EAAC,uBAC1C,CAAC,GAAI,CAAAY,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EACxC,CAAC,cACNnC,KAAA,QAAKuB,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvExB,IAAA,QAAKyB,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,cAC/DxB,IAAA,QAAAwB,QAAA,cACExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBG,KAAK,CAAC,oBAAoB,CAAAT,QAAA,cAE1BxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,qEAAqE,CACxE,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cACN7B,KAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxB,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cAEhCxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,CACH,CAAC,cACN/B,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDU,UAAU,CAACjB,KAAK,CAAC,CAACkB,OAAO,CAAC,CAAC,CAAC,CAC1B,CAAC,EACH,CAAC,cACNjC,KAAA,QAAKuB,SAAS,CAAC,8CAA8C,CAAAD,QAAA,EAAC,4BACrC,CAAC,GAAI,CAAAY,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAC7C,CAAC,cACNnC,KAAA,QAAKuB,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvExB,IAAA,QAAKyB,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,cAC/DxB,IAAA,QAAAwB,QAAA,cACExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBG,KAAK,CAAC,oBAAoB,CAAAT,QAAA,cAE1BxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,qEAAqE,CACxE,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cACN7B,KAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxB,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,IAAA,QACEyB,SAAS,CAAC,sBAAsB,CAChCa,GAAG,CAAC,wuEAAwuE,CAC7uE,CAAC,CACC,CAAC,cACNtC,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDL,aAAa,CACX,CAAC,EACH,CAAC,cACNnB,IAAA,QAAKyB,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,iBAE9D,CAAK,CAAC,cACNtB,KAAA,MACEwB,IAAI,CAAC,eAAe,CACpBD,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eAEpExB,IAAA,QAAKyB,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,cAC/DxB,IAAA,QAAAwB,QAAA,cACExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBG,KAAK,CAAC,oBAAoB,CAAAT,QAAA,cAE1BxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,qEAAqE,CACxE,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,EACD,CAAC,cACN7B,KAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCtB,KAAA,QAAKuB,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACpDxB,IAAA,QAAKyB,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BxB,IAAA,QACEyB,SAAS,CAAC,sBAAsB,CAChCa,GAAG,CAAC,wuEAAwuE,CAC7uE,CAAC,CACC,CAAC,cACNtC,IAAA,QAAKyB,SAAS,CAAC,4CAA4C,CAAAD,QAAA,CACxDN,UAAU,CACR,CAAC,EACH,CAAC,cACNlB,IAAA,QAAKyB,SAAS,CAAC,8CAA8C,CAAAD,QAAA,CAAC,aAE9D,CAAK,CAAC,cACNtB,KAAA,MACEwB,IAAI,CAAC,WAAW,CAChBD,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eAEpExB,IAAA,QAAKyB,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,cAC/DxB,IAAA,QAAAwB,QAAA,cACExB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBG,KAAK,CAAC,oBAAoB,CAAAT,QAAA,cAE1BxB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,qEAAqE,CACxE,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,EACD,CAAC,EACH,CAAC,cAEN/B,IAAA,QAAKyB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,cACzCxB,IAAA,QAAKyB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,cAChCtB,KAAA,CAACJ,aAAa,EAACyC,KAAK,CAAC,0BAA0B,CAAAf,QAAA,eAC7CtB,KAAA,QAAKuB,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BtB,KAAA,QACEsC,OAAO,CAAEA,CAAA,GAAM,CACbhC,cAAc,CAAC,WAAW,CAAC,CAC7B,CAAE,CACFiB,SAAS,gDAAAgB,MAAA,CACPlC,WAAW,GAAK,WAAW,CACvB,8BAA8B,CAC9B,EAAE,CACL,CAAAiB,QAAA,EACJ,YACW,CAACJ,UAAU,CAACsB,MAAM,EACzB,CAAC,cACNxC,KAAA,QACEsC,OAAO,CAAEA,CAAA,GAAM,CACbhC,cAAc,CAAC,kBAAkB,CAAC,CACpC,CAAE,CACFiB,SAAS,gDAAAgB,MAAA,CACPlC,WAAW,GAAK,kBAAkB,CAC9B,6BAA6B,CAC7B,EAAE,CACL,CAAAiB,QAAA,EACJ,mBACkB,CAACF,gBAAgB,CAACoB,MAAM,EACtC,CAAC,cACNxC,KAAA,QACEsC,OAAO,CAAEA,CAAA,GAAM,CACbhC,cAAc,CAAC,YAAY,CAAC,CAC9B,CAAE,CACFiB,SAAS,gDAAAgB,MAAA,CACPlC,WAAW,GAAK,YAAY,CACxB,8BAA8B,CAC9B,EAAE,CACL,CAAAiB,QAAA,EACJ,aACY,CAACH,QAAQ,CAACqB,MAAM,EACxB,CAAC,EACH,CAAC,CAELnC,WAAW,GAAK,WAAW,cAC1BP,IAAA,QAAKyB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9CtB,KAAA,UAAOuB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCxB,IAAA,UAAAwB,QAAA,cACEtB,KAAA,OAAIuB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAChDxB,IAAA,OAAIyB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,eAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,SAE9D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRxB,IAAA,UAAAwB,QAAA,CACGJ,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEuB,GAAG,CAAC,CAACC,GAAG,CAAEC,EAAE,QAAAC,qBAAA,CAAAC,oBAAA,CAAAC,cAAA,oBACvB9C,KAAA,OAAAsB,QAAA,eACExB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DxB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACtCoB,GAAG,CAACC,EAAE,CACN,CAAC,CACF,CAAC,cACL7C,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DtB,KAAA,MAAGuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAAsB,qBAAA,CACrCF,GAAG,CAACK,MAAM,CAACC,UAAU,UAAAJ,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAE,GAAG,EAAAC,oBAAA,CACnCH,GAAG,CAACO,KAAK,CAACC,SAAS,UAAAL,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CAAC,IAAE,CAAC,GAAG,EAAAC,cAAA,CACnCJ,GAAG,CAACS,SAAS,UAAAL,cAAA,UAAAA,cAAA,CAAI,KAAK,EACtB,CAAC,CACF,CAAC,cACLhD,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCoB,GAAG,CAACU,aAAa,CACjB,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cAErDxB,IAAA,CAACR,IAAI,EACHiC,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,aAAa,CAAGY,GAAG,CAACC,EAAG,CAAArB,QAAA,cAE3BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzExB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACJ,IAAI,CAEPxB,WAAW,GAAK,kBAAkB,cACjCP,IAAA,QAAKyB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9CtB,KAAA,UAAOuB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCxB,IAAA,UAAAwB,QAAA,cACEtB,KAAA,OAAIuB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAChDxB,IAAA,OAAIyB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,eAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,SAE9D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRxB,IAAA,UAAAwB,QAAA,CACGF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEqB,GAAG,CAAC,CAACC,GAAG,CAAEC,EAAE,QAAAU,sBAAA,CAAAC,qBAAA,CAAAC,eAAA,oBAC7BvD,KAAA,OAAAsB,QAAA,eACExB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DxB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACtCoB,GAAG,CAACC,EAAE,CACN,CAAC,CACF,CAAC,cACL7C,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DtB,KAAA,MAAGuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAA+B,sBAAA,CACrCX,GAAG,CAACK,MAAM,CAACC,UAAU,UAAAK,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAE,GAAG,EAAAC,qBAAA,CACnCZ,GAAG,CAACO,KAAK,CAACC,SAAS,UAAAI,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAC,IAAE,CAAC,GAAG,EAAAC,eAAA,CACnCb,GAAG,CAACS,SAAS,UAAAI,eAAA,UAAAA,eAAA,CAAI,KAAK,EACtB,CAAC,CACF,CAAC,cACLzD,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCoB,GAAG,CAACU,aAAa,CACjB,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cAErDxB,IAAA,CAACR,IAAI,EACHiC,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,aAAa,CAAGY,GAAG,CAACC,EAAG,CAAArB,QAAA,cAE3BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzExB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACJ,IAAI,CAEPxB,WAAW,GAAK,YAAY,cAC3BP,IAAA,QAAKyB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAC9CtB,KAAA,UAAOuB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCxB,IAAA,UAAAwB,QAAA,cACEtB,KAAA,OAAIuB,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eAChDxB,IAAA,OAAIyB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,OAE3E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,eAE5E,CAAI,CAAC,cACLxB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,CAAC,SAE9D,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRxB,IAAA,UAAAwB,QAAA,CACGH,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEsB,GAAG,CAAC,CAACC,GAAG,CAAEC,EAAE,QAAAa,sBAAA,CAAAC,qBAAA,CAAAC,eAAA,oBACrB1D,KAAA,OAAAsB,QAAA,eACExB,IAAA,OAAIyB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,cAC3DxB,IAAA,MAAGyB,SAAS,CAAC,4BAA4B,CAAAD,QAAA,CACtCoB,GAAG,CAACC,EAAE,CACN,CAAC,CACF,CAAC,cACL7C,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DtB,KAAA,MAAGuB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAAkC,sBAAA,CACrCd,GAAG,CAACK,MAAM,CAACC,UAAU,UAAAQ,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAAE,GAAG,EAAAC,qBAAA,CACnCf,GAAG,CAACO,KAAK,CAACC,SAAS,UAAAO,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAC,IAAE,CAAC,GAAG,EAAAC,eAAA,CACnChB,GAAG,CAACS,SAAS,UAAAO,eAAA,UAAAA,eAAA,CAAI,KAAK,EACtB,CAAC,CACF,CAAC,cACL5D,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCoB,GAAG,CAACU,aAAa,CACjB,CAAC,CACF,CAAC,cACLtD,IAAA,OAAIyB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DxB,IAAA,MAAGyB,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cAErDxB,IAAA,CAACR,IAAI,EACHiC,SAAS,CAAC,mBAAmB,CAC7BO,EAAE,CAAE,aAAa,CAAGY,GAAG,CAACC,EAAG,CAAArB,QAAA,cAE3BxB,IAAA,QACE2B,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzExB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB+B,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACF,CAAC,CACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CACJ,IAAI,EACK,CAAC,CACb,CAAC,CACH,CAAC,EACH,CAAC,cAGN/B,IAAA,QAAKyB,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAtB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}