{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ConfirmPasswordScreen() {\n  _s();\n  const {\n    uidb64,\n    token\n  } = useParams();\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"ConfirmPasswordScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 10\n  }, this);\n}\n_s(ConfirmPasswordScreen, \"9gxd+ApWCcKCmQcr0GHoiajOnD0=\", false, function () {\n  return [useParams];\n});\n_c = ConfirmPasswordScreen;\nexport default ConfirmPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ConfirmPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useParams", "jsxDEV", "_jsxDEV", "ConfirmPasswordScreen", "_s", "uidb64", "token", "newPassword", "setNewPassword", "newPasswordError", "setNewPasswordError", "confirmNewPassword", "setConfirmNewPassword", "ConfirmNewPasswordError", "setConfirmNewPasswordError", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ConfirmPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useParams } from \"react-router-dom\";\n\nfunction ConfirmPasswordScreen() {\n  const { uidb64, token } = useParams();\n\n  const [newPassword, setNewPassword] = useState(\"\");\n  const [newPasswordError, setNewPasswordError] = useState(\"\");\n\n  const [confirmNewPassword, setConfirmNewPassword] = useState(\"\");\n  const [ConfirmNewPasswordError, setConfirmNewPasswordError] = useState(\"\");\n\n  return <div>ConfirmPasswordScreen</div>;\n}\n\nexport default ConfirmPasswordScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAM;IAAEC,MAAM;IAAEC;EAAM,CAAC,GAAGN,SAAS,CAAC,CAAC;EAErC,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACY,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACc,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAE1E,oBAAOG,OAAA;IAAAa,QAAA,EAAK;EAAqB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACzC;AAACf,EAAA,CAVQD,qBAAqB;EAAA,QACFH,SAAS;AAAA;AAAAoB,EAAA,GAD5BjB,qBAAqB;AAY9B,eAAeA,qBAAqB;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}