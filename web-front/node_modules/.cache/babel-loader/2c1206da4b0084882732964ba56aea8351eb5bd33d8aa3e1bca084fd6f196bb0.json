{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../../layouts/DefaultLayout\";import InputModel from\"../../../components/InputModel\";import LayoutSection from\"../../../components/LayoutSection\";import{addNewCharge,addNewEntretien,deleteCharge,deleteEntretien,getListCharges,getListEntretiens,updateCharge,updateEntretien}from\"../../../redux/actions/designationActions\";import Loader from\"../../../components/Loader\";import Alert from\"../../../components/Alert\";import{WithContext as ReactTags}from\"react-tag-input\";import{toast}from\"react-toastify\";import ConfirmationModal from\"../../../components/ConfirmationModal\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const KeyCodes={comma:188,enter:13};const delimiters=[KeyCodes.comma,KeyCodes.enter];function DesignationScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();const[isAddModelCharge,setIsAddModelCharge]=useState(false);const[isAddModelEntretien,setIsAddModelEntretien]=useState(false);const[loadEvent,setLoadEvent]=useState(false);const[eventType,setEventType]=useState(\"\");const[isConfirmCharge,setIsConfirmCharge]=useState(false);const[isConfirmEntretien,setIsConfirmEntretien]=useState(false);const[chargeId,setChargeId]=useState(\"\");const[entretienId,setEntretienId]=useState(\"\");const[chargeName,setChargeName]=useState(\"\");const[chargeNameError,setChargeNameError]=useState(\"\");const[entretienName,setEntretienName]=useState(\"\");const[entretienNameError,setEntretienNameError]=useState(\"\");const[forLocation,setForLocation]=useState(false);const[chargeTageName,setChargeTageName]=useState(\"\");const[chargeTageNameError,setChargeTageNameError]=useState(\"\");const[entretienTageName,setEntretienTageName]=useState(\"\");const[entretienTageNameError,setEntretienTageNameError]=useState(\"\");const[chargeTages,setChargeTages]=useState([]);const[chargeTagesList,setChargeTagesList]=useState([]);const[entretienTages,setEntretienTages]=useState([]);const[entretienTagesList,setEntretienTagesList]=useState([]);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listCharge=useSelector(state=>state.chargeList);const{charges,loadingCharge,errorCharge,successCharge}=listCharge;const addCharge=useSelector(state=>state.createNewCharge);const{loadingChargeAdd,errorChargeAdd,successChargeAdd}=addCharge;const chargeDelete=useSelector(state=>state.deleteCharge);const{loadingChargeDelete,errorChargeDelete,successChargeDelete}=chargeDelete;const chargeUpdate=useSelector(state=>state.updateCharge);const{loadingChargeUpdate,errorChargeUpdate,successChargeUpdate}=chargeUpdate;//\nconst listEntretien=useSelector(state=>state.entretienList);const{entretiens,loadingEntretien,errorEntretien,successEntretien}=listEntretien;const entretienDelete=useSelector(state=>state.deleteEntretien);const{loadingEntretienDelete,errorEntretienDelete,successEntretienDelete}=entretienDelete;const addEntretien=useSelector(state=>state.createNewEntretien);const{loadingEntretienAdd,errorEntretienAdd,successEntretienAdd}=addEntretien;const entretienUpdate=useSelector(state=>state.updateEntretien);const{loadingEntretienUpdate,errorEntretienUpdate,successEntretienUpdate}=entretienUpdate;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getListCharges());dispatch(getListEntretiens());}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successEntretienUpdate){dispatch(getListEntretiens());setEntretienId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmEntretien(false);setIsAddModelEntretien(false);setEntretienName(\"\");setEntretienNameError(\"\");setEntretienTageName(\"\");setEntretienTages([]);setEntretienTagesList([]);}if(successEntretienAdd){dispatch(getListCharges());setEntretienId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmEntretien(false);setIsAddModelEntretien(false);setEntretienName(\"\");setEntretienNameError(\"\");setEntretienTageName(\"\");setEntretienTages([]);setEntretienTagesList([]);}if(successEntretienDelete){dispatch(getListEntretiens());setEntretienId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmEntretien(false);setIsAddModelEntretien(false);setEntretienName(\"\");setEntretienNameError(\"\");setEntretienTageName(\"\");setEntretienTages([]);setEntretienTagesList([]);}if(successChargeAdd){dispatch(getListCharges());setChargeId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmCharge(false);setIsAddModelCharge(false);setChargeName(\"\");setChargeNameError(\"\");setChargeTageName(\"\");setChargeTages([]);setChargeTagesList([]);}if(successChargeDelete){dispatch(getListCharges());setChargeId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmCharge(false);setIsAddModelCharge(false);setChargeName(\"\");setChargeNameError(\"\");setChargeTageName(\"\");setChargeTages([]);setChargeTagesList([]);}if(successChargeUpdate){dispatch(getListCharges());setChargeId(\"\");setEventType(\"\");setLoadEvent(false);setIsConfirmCharge(false);setIsAddModelCharge(false);setChargeName(\"\");setChargeNameError(\"\");setChargeTageName(\"\");setChargeTages([]);setChargeTagesList([]);}},[successChargeDelete,successChargeUpdate,successChargeAdd,successEntretienDelete,successEntretienAdd,successEntretienUpdate]);const suggestions=[].map(item=>{return{};});return/*#__PURE__*/_jsxs(DefaultLayout,{children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Param\\xE9trages\"}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"D\\xE9signation\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  \",children:\"Gestion des D\\xE9signations\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full mt-3\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:mr-1\",children:/*#__PURE__*/_jsx(LayoutSection,{title:\"Charges\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 mx-2 text-right\",children:[/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setLoadEvent(false);setEventType(\"add\");setIsAddModelCharge(true);setChargeName(\"\");setChargeNameError(\"\");setChargeTageName(\"\");setChargeTageNameError(\"\");setChargeTages([]);},className:\"text-xs cursor-pointer text-primary font-bold hover:text-danger\",children:\"Ajouter une nouvelle\"}),isAddModelCharge?/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[#00000040] z-99999\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded shadow-md  md:w-1/3 mx-3  text-left\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-base font-bold mb-4 w-full text-left\",children:eventType==\"add\"?\"Ajouter un nouveau charge\":\"Modification de type charge N°\"+chargeId}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"my-4\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Nom de charge\",type:\"text\",placeholder:\"\",value:chargeName,onChange:v=>setChargeName(v.target.value),error:chargeNameError})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2 flex \",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:forLocation,className:\"mx-1\",onChange:v=>setForLocation(!forLocation)}),/*#__PURE__*/_jsxs(\"div\",{onClick:()=>setForLocation(!forLocation),className:\"text-sm mx-1 cursor-pointer\",children:[\"De location ?\",\" \"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 mb-3 flex flex-wrap\",children:[eventType===\"update\"?chargeTagesList===null||chargeTagesList===void 0?void 0:chargeTagesList.map((tag,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setChargeTagesList(chargeTagesList.filter((tag,i)=>i!==index));},className:\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\",children:[/*#__PURE__*/_jsx(\"div\",{children:tag.sub_name}),/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-2 h-2\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})]})):null,chargeTages===null||chargeTages===void 0?void 0:chargeTages.map((tag,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setChargeTages(chargeTages.filter((tag,i)=>i!==index));},className:\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\",children:[/*#__PURE__*/_jsx(\"div\",{children:tag}),/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-2 h-2\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Sous charge\",type:\"text\",placeholder:\"\",value:chargeTageName,onChange:v=>setChargeTageName(v.target.value),error:chargeTageNameError}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{onClick:()=>{setChargeNameError(\"\");if(chargeTageName===\"\"){setChargeTageNameError(\"Ce champ est requis.\");}else{if(chargeTageName.trim()!==\"\"){setChargeTages([...chargeTages,chargeTageName.trim()]);setChargeTageName(\"\");}}},xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6 cursor-pointer\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z\"})})})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",onClick:async()=>{var icheck=true;if(chargeName===\"\"){setChargeNameError(\"Ce champ est requis.\");icheck=false;}if(icheck){setLoadEvent(true);if(eventType===\"add\"){await dispatch(addNewCharge({designation_name:chargeName,for_location:forLocation?\"True\":\"False\",tages:JSON.stringify(chargeTages)})).then(()=>{});await dispatch(getListCharges()).then(()=>{});}else if(eventType===\"update\"&&chargeId!==\"\"){await dispatch(updateCharge(chargeId,{designation_name:chargeName,for_location:forLocation?\"True\":\"False\",tages:JSON.stringify(chargeTages),tageslast:JSON.stringify(chargeTagesList)})).then(()=>{});await dispatch(getListCharges()).then(()=>{});}setLoadEvent(false);setIsAddModelCharge(false);}else{toast.error(\"Certains champs sont obligatoires veuillez vérifier\");}},disabled:loadEvent,children:[\" \",loadEvent?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",className:\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Loading...\"})]}):\"Confirm\",\" \"]}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",onClick:()=>{setIsAddModelCharge(false);setLoadEvent(false);},disabled:loadEvent,children:\"Annuler\"})]})]})}):null,loadingCharge?/*#__PURE__*/_jsx(Loader,{}):errorCharge?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCharge}):/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto overflow-x-auto mt-2\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Charge\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Sous Charge\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:charges===null||charges===void 0?void 0:charges.map((charge,id)=>{var _charge$items;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max  flex \",children:[charge.designation_name,charge.for_location?/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4 mx-1\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"})}):null]})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"div\",{className:\" flex flex-wrap \",children:(_charge$items=charge.items)===null||_charge$items===void 0?void 0:_charge$items.map((item,index)=>/*#__PURE__*/_jsx(\"p\",{className:\"mx-1 my-1 bg-primary text-white font-bold text-[9px] px-2 py-0.5 rounded cursor-pointer flex \",children:item.sub_name}))})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 update-class cursor-pointer\",onClick:()=>{setChargeId(charge.id);setEventType(\"update\");setChargeName(charge.designation_name);setForLocation(charge.for_location);setChargeTagesList(charge.items);setIsAddModelCharge(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setChargeId(charge.id);setEventType(\"delete\");setIsConfirmCharge(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]});})})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full  md:ml-1\",children:/*#__PURE__*/_jsx(LayoutSection,{title:\"Entretiens\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 mx-2 text-right\",children:[/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setLoadEvent(false);setEventType(\"add\");setIsAddModelEntretien(true);setEntretienName(\"\");setEntretienNameError(\"\");setEntretienTageName(\"\");setEntretienTageNameError(\"\");setEntretienTages([]);},className:\"text-xs cursor-pointer text-primary font-bold hover:text-danger\",children:\"Ajouter une nouvelle\"}),isAddModelEntretien?/*#__PURE__*/_jsx(\"div\",{className:\"fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[#00000040] z-99999\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded shadow-md  md:w-1/3 mx-3  text-left\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-base font-bold mb-4 w-full text-left\",children:eventType==\"add\"?\"Ajouter un nouveau entretien\":\"Modification de type entretien N°\"+entretienId}),/*#__PURE__*/_jsx(\"hr\",{}),/*#__PURE__*/_jsx(\"div\",{className:\"my-4\",children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:py-2 md:flex \",children:/*#__PURE__*/_jsx(InputModel,{label:\"Nom de entretien\",type:\"text\",placeholder:\"\",value:entretienName,onChange:v=>setEntretienName(v.target.value),error:entretienNameError})}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:py-2  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 mb-3 flex flex-wrap\",children:[eventType===\"update\"?entretienTagesList===null||entretienTagesList===void 0?void 0:entretienTagesList.map((tag,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setEntretienTagesList(entretienTagesList.filter((tag,i)=>i!==index));},className:\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\",children:[/*#__PURE__*/_jsx(\"div\",{children:tag.sub_name}),/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-2 h-2\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})]})):null,entretienTages===null||entretienTages===void 0?void 0:entretienTages.map((tag,index)=>/*#__PURE__*/_jsxs(\"div\",{onClick:()=>{setEntretienTages(entretienTages.filter((tag,i)=>i!==index));},className:\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\",children:[/*#__PURE__*/_jsx(\"div\",{children:tag}),/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-2 h-2\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center\",children:[/*#__PURE__*/_jsx(InputModel,{label:\"Sous Entretien\",type:\"text\",placeholder:\"\",value:entretienTageName,onChange:v=>setEntretienTageName(v.target.value),error:entretienTageNameError}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{onClick:()=>{setEntretienNameError(\"\");if(entretienTageName===\"\"){setEntretienTageNameError(\"Ce champ est requis.\");}else{if(entretienTageName.trim()!==\"\"){setEntretienTages([...entretienTages,entretienTageName.trim()]);setEntretienTageName(\"\");}}},xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-6 h-6 cursor-pointer\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z\"})})})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-end\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\",onClick:async()=>{var icheck=true;if(entretienName===\"\"){setEntretienNameError(\"Ce champ est requis.\");icheck=false;}if(icheck){setLoadEvent(true);if(eventType===\"add\"){await dispatch(addNewEntretien({entretien_name:entretienName,tages:JSON.stringify(entretienTages)})).then(()=>{});await dispatch(getListEntretiens()).then(()=>{});}else if(eventType===\"update\"&&entretienId!==\"\"){await dispatch(updateEntretien(entretienId,{entretien_name:entretienName,tages:JSON.stringify(entretienTages),tageslast:JSON.stringify(entretienTagesList)})).then(()=>{});await dispatch(getListEntretiens()).then(()=>{});}setLoadEvent(false);setIsAddModelEntretien(false);}else{toast.error(\"Certains champs sont obligatoires veuillez vérifier\");}},disabled:loadEvent,children:[\" \",loadEvent?/*#__PURE__*/_jsxs(\"div\",{role:\"status\",children:[/*#__PURE__*/_jsxs(\"svg\",{\"aria-hidden\":\"true\",className:\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\",viewBox:\"0 0 100 101\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\",fill:\"currentColor\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\",fill:\"currentFill\"})]}),/*#__PURE__*/_jsx(\"span\",{className:\"sr-only\",children:\"Loading...\"})]}):\"Confirm\",\" \"]}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\",onClick:()=>{setIsAddModelEntretien(false);setLoadEvent(false);},disabled:loadEvent,children:\"Annuler\"})]})]})}):null,loadingEntretien?/*#__PURE__*/_jsx(Loader,{}):errorEntretien?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorEntretien}):/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto overflow-x-auto mt-2\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Entretien\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Sous Entretien\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \",children:\"Actions\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:entretiens===null||entretiens===void 0?void 0:entretiens.map((entretien,id)=>{var _entretien$items;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max  flex \",children:entretien.entretien_name})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsx(\"div\",{className:\" flex flex-wrap \",children:(_entretien$items=entretien.items)===null||_entretien$items===void 0?void 0:_entretien$items.map((item,index)=>/*#__PURE__*/_jsx(\"p\",{className:\"mx-1 my-1 bg-primary text-white font-bold text-[9px] px-2 py-0.5 rounded cursor-pointer flex \",children:item.sub_name}))})}),/*#__PURE__*/_jsx(\"td\",{className:\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row  \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"mx-1 update-class cursor-pointer\",onClick:()=>{setEntretienId(entretien.id);setEventType(\"update\");setEntretienName(entretien.entretien_name);setEntretienTagesList(entretien.items);setIsAddModelEntretien(true);},children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"})})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>{setEntretienId(entretien.id);setEventType(\"delete\");setIsConfirmEntretien(true);},className:\"mx-1 delete-class cursor-pointer\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"})})})]})})]});})})]})]})})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isConfirmCharge,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer cet Type de Charge?\":eventType===\"update\"?\"Êtes-vous sûr de vouloir modifé cet Type de Charge?\":\"Êtes-vous sûr de vouloir ajouter cet Type de Charge?\",onConfirm:async()=>{if(eventType===\"delete\"){if(chargeId!==\"\"){setLoadEvent(true);await dispatch(deleteCharge(chargeId)).then(()=>{});setLoadEvent(false);setEventType(\"\");setIsConfirmCharge(false);}}},onCancel:()=>{setIsConfirmCharge(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent}),/*#__PURE__*/_jsx(ConfirmationModal,{isOpen:isConfirmEntretien,message:eventType===\"delete\"?\"Êtes-vous sûr de vouloir supprimer cet Type de Entretien?\":eventType===\"update\"?\"Êtes-vous sûr de vouloir modifé cet Type de Entretien?\":\"Êtes-vous sûr de vouloir ajouter cet Type de Entretien?\",onConfirm:async()=>{if(eventType===\"delete\"){if(entretienId!==\"\"){setLoadEvent(true);await dispatch(deleteEntretien(entretienId)).then(()=>{});setLoadEvent(false);setEventType(\"\");setIsConfirmEntretien(false);}}},onCancel:()=>{setIsConfirmEntretien(false);setEventType(\"\");setLoadEvent(false);},loadEvent:loadEvent})]});}export default DesignationScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "DefaultLayout", "InputModel", "LayoutSection", "addNewCharge", "addNewEntretien", "deleteCharge", "deleteEntretien", "getListCharges", "getListEntretiens", "updateCharge", "updateEntretien", "Loader", "<PERSON><PERSON>", "WithContext", "ReactTags", "toast", "ConfirmationModal", "jsx", "_jsx", "jsxs", "_jsxs", "KeyCodes", "comma", "enter", "delimiters", "DesignationScreen", "navigate", "location", "dispatch", "isAddModelCharge", "setIsAddModelCharge", "isAddModelEntretien", "setIsAddModelEntretien", "loadEvent", "setLoadEvent", "eventType", "setEventType", "isConfirmCharge", "setIsConfirmCharge", "isConfirmEntretien", "setIsConfirmEntretien", "chargeId", "setChargeId", "entretienId", "setEntretienId", "chargeName", "setChargeName", "chargeNameError", "setChargeNameError", "entretienName", "setEntretienName", "entretienNameError", "setEntretienNameError", "forLocation", "setForLocation", "chargeTageName", "setChargeTageName", "chargeTageNameError", "setChargeTageNameError", "entretienTageName", "setEntretienTageName", "entretienTageNameError", "setEntretienTageNameError", "chargeTages", "setChargeTages", "chargeTagesList", "setChargeTagesList", "entretienTages", "setEntretienTages", "entretienTagesList", "setEntretienTagesList", "userLogin", "state", "userInfo", "listCharge", "chargeList", "charges", "loadingCharge", "errorCharge", "successCharge", "addCharge", "createNewCharge", "loadingChargeAdd", "errorChargeAdd", "successChargeAdd", "chargeDelete", "loadingChargeDelete", "errorChargeDelete", "successChargeDelete", "chargeUpdate", "loadingChargeUpdate", "errorChargeUpdate", "successChargeUpdate", "listEntretien", "entretienList", "entretiens", "loadingEntretien", "errorE<PERSON><PERSON><PERSON>", "successEntretien", "entretienDelete", "loadingEntretienDelete", "errorEntretienDelete", "successEntretienDelete", "addEntretien", "createNewEntretien", "loadingEntretienAdd", "errorEntretienAdd", "successEntretienAdd", "entretienUpdate", "loadingEntretienUpdate", "errorEntretienUpdate", "successEntretienUpdate", "redirect", "suggestions", "map", "item", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "title", "onClick", "label", "type", "placeholder", "value", "onChange", "v", "target", "error", "checked", "tag", "index", "filter", "i", "sub_name", "trim", "icheck", "designation_name", "for_location", "tages", "JSON", "stringify", "then", "tageslast", "disabled", "role", "message", "charge", "id", "_charge$items", "items", "entretien_name", "<PERSON><PERSON><PERSON>", "_entretien$items", "isOpen", "onConfirm", "onCancel"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/settings/designations/DesignationScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../../layouts/DefaultLayout\";\nimport InputModel from \"../../../components/InputModel\";\nimport LayoutSection from \"../../../components/LayoutSection\";\nimport {\n  addNewCharge,\n  addNewEntretien,\n  deleteCharge,\n  deleteEntretien,\n  getListCharges,\n  getListEntretiens,\n  updateCharge,\n  updateEntretien,\n} from \"../../../redux/actions/designationActions\";\nimport Loader from \"../../../components/Loader\";\nimport Alert from \"../../../components/Alert\";\nimport { WithContext as ReactTags } from \"react-tag-input\";\nimport { toast } from \"react-toastify\";\nimport ConfirmationModal from \"../../../components/ConfirmationModal\";\n\nconst KeyCodes = {\n  comma: 188,\n  enter: 13,\n};\nconst delimiters = [KeyCodes.comma, KeyCodes.enter];\n\nfunction DesignationScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [isAddModelCharge, setIsAddModelCharge] = useState(false);\n  const [isAddModelEntretien, setIsAddModelEntretien] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [isConfirmCharge, setIsConfirmCharge] = useState(false);\n  const [isConfirmEntretien, setIsConfirmEntretien] = useState(false);\n\n  const [chargeId, setChargeId] = useState(\"\");\n  const [entretienId, setEntretienId] = useState(\"\");\n\n  const [chargeName, setChargeName] = useState(\"\");\n  const [chargeNameError, setChargeNameError] = useState(\"\");\n\n  const [entretienName, setEntretienName] = useState(\"\");\n  const [entretienNameError, setEntretienNameError] = useState(\"\");\n\n  const [forLocation, setForLocation] = useState(false);\n\n  const [chargeTageName, setChargeTageName] = useState(\"\");\n  const [chargeTageNameError, setChargeTageNameError] = useState(\"\");\n\n  const [entretienTageName, setEntretienTageName] = useState(\"\");\n  const [entretienTageNameError, setEntretienTageNameError] = useState(\"\");\n\n  const [chargeTages, setChargeTages] = useState([]);\n  const [chargeTagesList, setChargeTagesList] = useState([]);\n\n  const [entretienTages, setEntretienTages] = useState([]);\n  const [entretienTagesList, setEntretienTagesList] = useState([]);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCharge = useSelector((state) => state.chargeList);\n  const { charges, loadingCharge, errorCharge, successCharge } = listCharge;\n\n  const addCharge = useSelector((state) => state.createNewCharge);\n  const { loadingChargeAdd, errorChargeAdd, successChargeAdd } = addCharge;\n\n  const chargeDelete = useSelector((state) => state.deleteCharge);\n  const { loadingChargeDelete, errorChargeDelete, successChargeDelete } =\n    chargeDelete;\n\n  const chargeUpdate = useSelector((state) => state.updateCharge);\n  const { loadingChargeUpdate, errorChargeUpdate, successChargeUpdate } =\n    chargeUpdate;\n\n  //\n  const listEntretien = useSelector((state) => state.entretienList);\n  const { entretiens, loadingEntretien, errorEntretien, successEntretien } =\n    listEntretien;\n\n  const entretienDelete = useSelector((state) => state.deleteEntretien);\n  const {\n    loadingEntretienDelete,\n    errorEntretienDelete,\n    successEntretienDelete,\n  } = entretienDelete;\n\n  const addEntretien = useSelector((state) => state.createNewEntretien);\n  const { loadingEntretienAdd, errorEntretienAdd, successEntretienAdd } =\n    addEntretien;\n\n  const entretienUpdate = useSelector((state) => state.updateEntretien);\n  const {\n    loadingEntretienUpdate,\n    errorEntretienUpdate,\n    successEntretienUpdate,\n  } = entretienUpdate;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getListCharges());\n      dispatch(getListEntretiens());\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successEntretienUpdate) {\n      dispatch(getListEntretiens());\n      setEntretienId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmEntretien(false);\n      setIsAddModelEntretien(false);\n      setEntretienName(\"\");\n      setEntretienNameError(\"\");\n      setEntretienTageName(\"\");\n      setEntretienTages([]);\n      setEntretienTagesList([]);\n    }\n    if (successEntretienAdd) {\n      dispatch(getListCharges());\n      setEntretienId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmEntretien(false);\n      setIsAddModelEntretien(false);\n      setEntretienName(\"\");\n      setEntretienNameError(\"\");\n      setEntretienTageName(\"\");\n      setEntretienTages([]);\n      setEntretienTagesList([]);\n    }\n\n    if (successEntretienDelete) {\n      dispatch(getListEntretiens());\n      setEntretienId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmEntretien(false);\n      setIsAddModelEntretien(false);\n      setEntretienName(\"\");\n      setEntretienNameError(\"\");\n      setEntretienTageName(\"\");\n      setEntretienTages([]);\n      setEntretienTagesList([]);\n    }\n\n    if (successChargeAdd) {\n      dispatch(getListCharges());\n      setChargeId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmCharge(false);\n      setIsAddModelCharge(false);\n      setChargeName(\"\");\n      setChargeNameError(\"\");\n      setChargeTageName(\"\");\n      setChargeTages([]);\n      setChargeTagesList([]);\n    }\n    if (successChargeDelete) {\n      dispatch(getListCharges());\n      setChargeId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmCharge(false);\n      setIsAddModelCharge(false);\n      setChargeName(\"\");\n      setChargeNameError(\"\");\n      setChargeTageName(\"\");\n      setChargeTages([]);\n      setChargeTagesList([]);\n    }\n\n    if (successChargeUpdate) {\n      dispatch(getListCharges());\n      setChargeId(\"\");\n      setEventType(\"\");\n      setLoadEvent(false);\n      setIsConfirmCharge(false);\n      setIsAddModelCharge(false);\n      setChargeName(\"\");\n      setChargeNameError(\"\");\n      setChargeTageName(\"\");\n      setChargeTages([]);\n      setChargeTagesList([]);\n    }\n  }, [\n    successChargeDelete,\n    successChargeUpdate,\n    successChargeAdd,\n    successEntretienDelete,\n    successEntretienAdd,\n    successEntretienUpdate,\n  ]);\n\n  const suggestions = [].map((item) => {\n    return {};\n  });\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Paramétrages</div>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Désignation</div>\n        </div>\n\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  \">\n              Gestion des Désignations\n            </h4>\n          </div>\n          {/* search */}\n\n          {/* list */}\n          <div className=\"w-full mt-3\">\n            <div className=\"flex md:flex-row flex-col\">\n              <div className=\"md:w-1/2 w-full  md:mr-1\">\n                <LayoutSection title=\"Charges\">\n                  <div className=\"my-1 mx-2 text-right\">\n                    <div\n                      onClick={() => {\n                        setLoadEvent(false);\n                        setEventType(\"add\");\n                        setIsAddModelCharge(true);\n                        setChargeName(\"\");\n                        setChargeNameError(\"\");\n                        setChargeTageName(\"\");\n                        setChargeTageNameError(\"\");\n                        setChargeTages([]);\n                      }}\n                      className=\"text-xs cursor-pointer text-primary font-bold hover:text-danger\"\n                    >\n                      Ajouter une nouvelle\n                    </div>\n                    {/* model add new charge */}\n                    {isAddModelCharge ? (\n                      <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[#00000040] z-99999\">\n                        <div className=\"bg-white p-6 rounded shadow-md  md:w-1/3 mx-3  text-left\">\n                          <h3 className=\"text-base font-bold mb-4 w-full text-left\">\n                            {eventType == \"add\"\n                              ? \"Ajouter un nouveau charge\"\n                              : \"Modification de type charge N°\" + chargeId}\n                          </h3>\n                          <hr />\n                          <div className=\"my-4\">\n                            <div>\n                              <div className=\"md:py-2 md:flex \">\n                                <InputModel\n                                  label=\"Nom de charge\"\n                                  type=\"text\"\n                                  placeholder=\"\"\n                                  value={chargeName}\n                                  onChange={(v) =>\n                                    setChargeName(v.target.value)\n                                  }\n                                  error={chargeNameError}\n                                />\n                              </div>\n                              <div className=\"md:py-2 flex \">\n                                <input\n                                  type={\"checkbox\"}\n                                  checked={forLocation}\n                                  className=\"mx-1\"\n                                  onChange={(v) => setForLocation(!forLocation)}\n                                />\n                                <div\n                                  onClick={() => setForLocation(!forLocation)}\n                                  className=\"text-sm mx-1 cursor-pointer\"\n                                >\n                                  De location ?{\" \"}\n                                </div>\n                              </div>\n                              <div className=\"md:py-2  \">\n                                <div className=\"mx-1 mb-3 flex flex-wrap\">\n                                  {eventType === \"update\"\n                                    ? chargeTagesList?.map((tag, index) => (\n                                        <div\n                                          onClick={() => {\n                                            setChargeTagesList(\n                                              chargeTagesList.filter(\n                                                (tag, i) => i !== index\n                                              )\n                                            );\n                                          }}\n                                          className=\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\"\n                                        >\n                                          <div>{tag.sub_name}</div>\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"w-2 h-2\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M6 18 18 6M6 6l12 12\"\n                                            />\n                                          </svg>\n                                        </div>\n                                      ))\n                                    : null}\n                                  {chargeTages?.map((tag, index) => (\n                                    <div\n                                      onClick={() => {\n                                        setChargeTages(\n                                          chargeTages.filter(\n                                            (tag, i) => i !== index\n                                          )\n                                        );\n                                      }}\n                                      className=\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\"\n                                    >\n                                      <div>{tag}</div>\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        stroke-width=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-2 h-2\"\n                                      >\n                                        <path\n                                          stroke-linecap=\"round\"\n                                          stroke-linejoin=\"round\"\n                                          d=\"M6 18 18 6M6 6l12 12\"\n                                        />\n                                      </svg>\n                                    </div>\n                                  ))}\n                                </div>\n                                <div className=\"flex flex-row  items-center\">\n                                  <InputModel\n                                    label=\"Sous charge\"\n                                    type=\"text\"\n                                    placeholder=\"\"\n                                    value={chargeTageName}\n                                    onChange={(v) =>\n                                      setChargeTageName(v.target.value)\n                                    }\n                                    error={chargeTageNameError}\n                                  />\n                                  <div>\n                                    <svg\n                                      onClick={() => {\n                                        setChargeNameError(\"\");\n                                        if (chargeTageName === \"\") {\n                                          setChargeTageNameError(\n                                            \"Ce champ est requis.\"\n                                          );\n                                        } else {\n                                          if (chargeTageName.trim() !== \"\") {\n                                            setChargeTages([\n                                              ...chargeTages,\n                                              chargeTageName.trim(),\n                                            ]);\n                                            setChargeTageName(\"\");\n                                          }\n                                        }\n                                      }}\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-6 h-6 cursor-pointer\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z\"\n                                      />\n                                    </svg>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex justify-end\">\n                            <button\n                              className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                              onClick={async () => {\n                                var icheck = true;\n                                if (chargeName === \"\") {\n                                  setChargeNameError(\"Ce champ est requis.\");\n                                  icheck = false;\n                                }\n                                if (icheck) {\n                                  setLoadEvent(true);\n                                  if (eventType === \"add\") {\n                                    await dispatch(\n                                      addNewCharge({\n                                        designation_name: chargeName,\n                                        for_location: forLocation\n                                          ? \"True\"\n                                          : \"False\",\n                                        tages: JSON.stringify(chargeTages),\n                                      })\n                                    ).then(() => {});\n                                    await dispatch(getListCharges()).then(\n                                      () => {}\n                                    );\n                                  } else if (\n                                    eventType === \"update\" &&\n                                    chargeId !== \"\"\n                                  ) {\n                                    await dispatch(\n                                      updateCharge(chargeId, {\n                                        designation_name: chargeName,\n                                        for_location: forLocation\n                                          ? \"True\"\n                                          : \"False\",\n                                        tages: JSON.stringify(chargeTages),\n                                        tageslast:\n                                          JSON.stringify(chargeTagesList),\n                                      })\n                                    ).then(() => {});\n                                    await dispatch(getListCharges()).then(\n                                      () => {}\n                                    );\n                                  }\n                                  setLoadEvent(false);\n                                  setIsAddModelCharge(false);\n                                } else {\n                                  toast.error(\n                                    \"Certains champs sont obligatoires veuillez vérifier\"\n                                  );\n                                }\n                              }}\n                              disabled={loadEvent}\n                            >\n                              {\" \"}\n                              {loadEvent ? (\n                                <div role=\"status\">\n                                  <svg\n                                    aria-hidden=\"true\"\n                                    className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                                    viewBox=\"0 0 100 101\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                  >\n                                    <path\n                                      d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                                      fill=\"currentColor\"\n                                    />\n                                    <path\n                                      d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                                      fill=\"currentFill\"\n                                    />\n                                  </svg>\n                                  <span className=\"sr-only\">Loading...</span>\n                                </div>\n                              ) : (\n                                \"Confirm\"\n                              )}{\" \"}\n                            </button>\n                            <button\n                              className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                              onClick={() => {\n                                setIsAddModelCharge(false);\n                                setLoadEvent(false);\n                              }}\n                              disabled={loadEvent}\n                            >\n                              Annuler\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    ) : null}\n                    {/* table */}\n                    {loadingCharge ? (\n                      <Loader />\n                    ) : errorCharge ? (\n                      <Alert type=\"error\" message={errorCharge} />\n                    ) : (\n                      <table className=\"w-full table-auto overflow-x-auto mt-2\">\n                        <thead>\n                          <tr className=\"bg-gray-2 text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Charge\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Sous Charge\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Actions\n                            </th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          {charges?.map((charge, id) => (\n                            <tr>\n                              <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4 \">\n                                <p className=\"text-black  text-xs w-max  flex \">\n                                  {charge.designation_name}\n                                  {charge.for_location ? (\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-4 h-4 mx-1\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M11.48 3.499a.562.562 0 0 1 1.04 0l2.125 5.111a.563.563 0 0 0 .475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 0 0-.182.557l1.285 5.385a.562.562 0 0 1-.84.61l-4.725-2.885a.562.562 0 0 0-.586 0L6.982 20.54a.562.562 0 0 1-.84-.61l1.285-5.386a.562.562 0 0 0-.182-.557l-4.204-3.602a.562.562 0 0 1 .321-.988l5.518-.442a.563.563 0 0 0 .475-.345L11.48 3.5Z\"\n                                      />\n                                    </svg>\n                                  ) : null}\n                                </p>\n                              </td>\n                              <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                                <div className=\" flex flex-wrap \">\n                                  {charge.items?.map((item, index) => (\n                                    <p className=\"mx-1 my-1 bg-primary text-white font-bold text-[9px] px-2 py-0.5 rounded cursor-pointer flex \">\n                                      {item.sub_name}\n                                    </p>\n                                  ))}\n                                </div>\n                              </td>\n                              <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  {/* edit */}\n                                  <div\n                                    className=\"mx-1 update-class cursor-pointer\"\n                                    onClick={() => {\n                                      setChargeId(charge.id);\n                                      setEventType(\"update\");\n                                      setChargeName(charge.designation_name);\n                                      setForLocation(charge.for_location);\n                                      setChargeTagesList(charge.items);\n\n                                      setIsAddModelCharge(true);\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                      />\n                                    </svg>\n                                  </div>\n                                  <div\n                                    onClick={() => {\n                                      setChargeId(charge.id);\n                                      setEventType(\"delete\");\n                                      setIsConfirmCharge(true);\n                                    }}\n                                    className=\"mx-1 delete-class cursor-pointer\"\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                      />\n                                    </svg>\n                                  </div>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    )}\n                  </div>\n                </LayoutSection>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:ml-1\">\n                <LayoutSection title=\"Entretiens\">\n                  <div className=\"my-1 mx-2 text-right\">\n                    <div\n                      onClick={() => {\n                        setLoadEvent(false);\n                        setEventType(\"add\");\n                        setIsAddModelEntretien(true);\n                        setEntretienName(\"\");\n                        setEntretienNameError(\"\");\n                        setEntretienTageName(\"\");\n                        setEntretienTageNameError(\"\");\n                        setEntretienTages([]);\n                      }}\n                      className=\"text-xs cursor-pointer text-primary font-bold hover:text-danger\"\n                    >\n                      Ajouter une nouvelle\n                    </div>\n                    {/* model add new charge */}\n                    {isAddModelEntretien ? (\n                      <div className=\"fixed top-0 left-0 w-full h-full flex items-center justify-center bg-[#00000040] z-99999\">\n                        <div className=\"bg-white p-6 rounded shadow-md  md:w-1/3 mx-3  text-left\">\n                          <h3 className=\"text-base font-bold mb-4 w-full text-left\">\n                            {eventType == \"add\"\n                              ? \"Ajouter un nouveau entretien\"\n                              : \"Modification de type entretien N°\" +\n                                entretienId}\n                          </h3>\n                          <hr />\n                          <div className=\"my-4\">\n                            <div>\n                              <div className=\"md:py-2 md:flex \">\n                                <InputModel\n                                  label=\"Nom de entretien\"\n                                  type=\"text\"\n                                  placeholder=\"\"\n                                  value={entretienName}\n                                  onChange={(v) =>\n                                    setEntretienName(v.target.value)\n                                  }\n                                  error={entretienNameError}\n                                />\n                              </div>\n                              <div className=\"md:py-2  \">\n                                <div className=\"mx-1 mb-3 flex flex-wrap\">\n                                  {eventType === \"update\"\n                                    ? entretienTagesList?.map((tag, index) => (\n                                        <div\n                                          onClick={() => {\n                                            setEntretienTagesList(\n                                              entretienTagesList.filter(\n                                                (tag, i) => i !== index\n                                              )\n                                            );\n                                          }}\n                                          className=\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\"\n                                        >\n                                          <div>{tag.sub_name}</div>\n                                          <svg\n                                            xmlns=\"http://www.w3.org/2000/svg\"\n                                            fill=\"none\"\n                                            viewBox=\"0 0 24 24\"\n                                            stroke-width=\"1.5\"\n                                            stroke=\"currentColor\"\n                                            className=\"w-2 h-2\"\n                                          >\n                                            <path\n                                              stroke-linecap=\"round\"\n                                              stroke-linejoin=\"round\"\n                                              d=\"M6 18 18 6M6 6l12 12\"\n                                            />\n                                          </svg>\n                                        </div>\n                                      ))\n                                    : null}\n                                  {entretienTages?.map((tag, index) => (\n                                    <div\n                                      onClick={() => {\n                                        setEntretienTages(\n                                          entretienTages.filter(\n                                            (tag, i) => i !== index\n                                          )\n                                        );\n                                      }}\n                                      className=\"mx-1 my-1 bg-primary text-white font-bold text-xs px-3 py-1 rounded cursor-pointer flex\"\n                                    >\n                                      <div>{tag}</div>\n                                      <svg\n                                        xmlns=\"http://www.w3.org/2000/svg\"\n                                        fill=\"none\"\n                                        viewBox=\"0 0 24 24\"\n                                        stroke-width=\"1.5\"\n                                        stroke=\"currentColor\"\n                                        className=\"w-2 h-2\"\n                                      >\n                                        <path\n                                          stroke-linecap=\"round\"\n                                          stroke-linejoin=\"round\"\n                                          d=\"M6 18 18 6M6 6l12 12\"\n                                        />\n                                      </svg>\n                                    </div>\n                                  ))}\n                                </div>\n                                <div className=\"flex flex-row  items-center\">\n                                  <InputModel\n                                    label=\"Sous Entretien\"\n                                    type=\"text\"\n                                    placeholder=\"\"\n                                    value={entretienTageName}\n                                    onChange={(v) =>\n                                      setEntretienTageName(v.target.value)\n                                    }\n                                    error={entretienTageNameError}\n                                  />\n                                  <div>\n                                    <svg\n                                      onClick={() => {\n                                        setEntretienNameError(\"\");\n                                        if (entretienTageName === \"\") {\n                                          setEntretienTageNameError(\n                                            \"Ce champ est requis.\"\n                                          );\n                                        } else {\n                                          if (entretienTageName.trim() !== \"\") {\n                                            setEntretienTages([\n                                              ...entretienTages,\n                                              entretienTageName.trim(),\n                                            ]);\n                                            setEntretienTageName(\"\");\n                                          }\n                                        }\n                                      }}\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-6 h-6 cursor-pointer\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"M12 10.5v6m3-3H9m4.06-7.19-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z\"\n                                      />\n                                    </svg>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          </div>\n                          <div className=\"flex justify-end\">\n                            <button\n                              className=\"bg-primary hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2\"\n                              onClick={async () => {\n                                var icheck = true;\n                                if (entretienName === \"\") {\n                                  setEntretienNameError(\"Ce champ est requis.\");\n                                  icheck = false;\n                                }\n                                if (icheck) {\n                                  setLoadEvent(true);\n                                  if (eventType === \"add\") {\n                                    await dispatch(\n                                      addNewEntretien({\n                                        entretien_name: entretienName,\n\n                                        tages: JSON.stringify(entretienTages),\n                                      })\n                                    ).then(() => {});\n                                    await dispatch(getListEntretiens()).then(\n                                      () => {}\n                                    );\n                                  } else if (\n                                    eventType === \"update\" &&\n                                    entretienId !== \"\"\n                                  ) {\n                                    await dispatch(\n                                      updateEntretien(entretienId, {\n                                        entretien_name: entretienName,\n                                        tages: JSON.stringify(entretienTages),\n                                        tageslast:\n                                          JSON.stringify(entretienTagesList),\n                                      })\n                                    ).then(() => {});\n                                    await dispatch(getListEntretiens()).then(\n                                      () => {}\n                                    );\n                                  }\n                                  setLoadEvent(false);\n                                  setIsAddModelEntretien(false);\n                                } else {\n                                  toast.error(\n                                    \"Certains champs sont obligatoires veuillez vérifier\"\n                                  );\n                                }\n                              }}\n                              disabled={loadEvent}\n                            >\n                              {\" \"}\n                              {loadEvent ? (\n                                <div role=\"status\">\n                                  <svg\n                                    aria-hidden=\"true\"\n                                    className=\"w-5 h-5 mr-2 text-gray-200 animate-spin dark:text-gray-600 fill-danger\"\n                                    viewBox=\"0 0 100 101\"\n                                    fill=\"none\"\n                                    xmlns=\"http://www.w3.org/2000/svg\"\n                                  >\n                                    <path\n                                      d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\n                                      fill=\"currentColor\"\n                                    />\n                                    <path\n                                      d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\n                                      fill=\"currentFill\"\n                                    />\n                                  </svg>\n                                  <span className=\"sr-only\">Loading...</span>\n                                </div>\n                              ) : (\n                                \"Confirm\"\n                              )}{\" \"}\n                            </button>\n                            <button\n                              className=\"bg-danger hover:bg-gray-600 text-white font-bold py-2 px-4 rounded\"\n                              onClick={() => {\n                                setIsAddModelEntretien(false);\n                                setLoadEvent(false);\n                              }}\n                              disabled={loadEvent}\n                            >\n                              Annuler\n                            </button>\n                          </div>\n                        </div>\n                      </div>\n                    ) : null}\n                    {/* table */}\n                    {loadingEntretien ? (\n                      <Loader />\n                    ) : errorEntretien ? (\n                      <Alert type=\"error\" message={errorEntretien} />\n                    ) : (\n                      <table className=\"w-full table-auto overflow-x-auto mt-2\">\n                        <thead>\n                          <tr className=\"bg-gray-2 text-left \">\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Entretien\n                            </th>\n                            <th className=\"min-w-[120px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Sous Entretien\n                            </th>\n                            <th className=\"min-w-[60px] py-4 px-4 font-bold text-black text-xs w-max \">\n                              Actions\n                            </th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          {entretiens?.map((entretien, id) => (\n                            <tr>\n                              <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4 \">\n                                <p className=\"text-black  text-xs w-max  flex \">\n                                  {entretien.entretien_name}\n                                </p>\n                              </td>\n                              <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                                <div className=\" flex flex-wrap \">\n                                  {entretien.items?.map((item, index) => (\n                                    <p className=\"mx-1 my-1 bg-primary text-white font-bold text-[9px] px-2 py-0.5 rounded cursor-pointer flex \">\n                                      {item.sub_name}\n                                    </p>\n                                  ))}\n                                </div>\n                              </td>\n                              <td className=\"border-b border-[#eee] py-2 px-4 min-w-[120px]  \">\n                                <p className=\"text-black  text-xs w-max flex flex-row  \">\n                                  {/* edit */}\n                                  <div\n                                    className=\"mx-1 update-class cursor-pointer\"\n                                    onClick={() => {\n                                      setEntretienId(entretien.id);\n                                      setEventType(\"update\");\n                                      setEntretienName(\n                                        entretien.entretien_name\n                                      );\n                                      setEntretienTagesList(entretien.items);\n\n                                      setIsAddModelEntretien(true);\n                                    }}\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                                      />\n                                    </svg>\n                                  </div>\n                                  <div\n                                    onClick={() => {\n                                      setEntretienId(entretien.id);\n                                      setEventType(\"delete\");\n                                      setIsConfirmEntretien(true);\n                                    }}\n                                    className=\"mx-1 delete-class cursor-pointer\"\n                                  >\n                                    <svg\n                                      xmlns=\"http://www.w3.org/2000/svg\"\n                                      fill=\"none\"\n                                      viewBox=\"0 0 24 24\"\n                                      stroke-width=\"1.5\"\n                                      stroke=\"currentColor\"\n                                      className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                                    >\n                                      <path\n                                        stroke-linecap=\"round\"\n                                        stroke-linejoin=\"round\"\n                                        d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                                      />\n                                    </svg>\n                                  </div>\n                                </p>\n                              </td>\n                            </tr>\n                          ))}\n                        </tbody>\n                      </table>\n                    )}\n                  </div>\n                </LayoutSection>\n              </div>\n            </div>\n          </div>\n        </div>\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n      {/* confirmation charge */}\n      <ConfirmationModal\n        isOpen={isConfirmCharge}\n        message={\n          eventType === \"delete\"\n            ? \"Êtes-vous sûr de vouloir supprimer cet Type de Charge?\"\n            : eventType === \"update\"\n            ? \"Êtes-vous sûr de vouloir modifé cet Type de Charge?\"\n            : \"Êtes-vous sûr de vouloir ajouter cet Type de Charge?\"\n        }\n        onConfirm={async () => {\n          if (eventType === \"delete\") {\n            if (chargeId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(deleteCharge(chargeId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsConfirmCharge(false);\n            }\n          }\n        }}\n        onCancel={() => {\n          setIsConfirmCharge(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        }}\n        loadEvent={loadEvent}\n      />\n      {/* confirmation entretien */}\n      <ConfirmationModal\n        isOpen={isConfirmEntretien}\n        message={\n          eventType === \"delete\"\n            ? \"Êtes-vous sûr de vouloir supprimer cet Type de Entretien?\"\n            : eventType === \"update\"\n            ? \"Êtes-vous sûr de vouloir modifé cet Type de Entretien?\"\n            : \"Êtes-vous sûr de vouloir ajouter cet Type de Entretien?\"\n        }\n        onConfirm={async () => {\n          if (eventType === \"delete\") {\n            if (entretienId !== \"\") {\n              setLoadEvent(true);\n              await dispatch(deleteEntretien(entretienId)).then(() => {});\n              setLoadEvent(false);\n              setEventType(\"\");\n              setIsConfirmEntretien(false);\n            }\n          }\n        }}\n        onCancel={() => {\n          setIsConfirmEntretien(false);\n          setEventType(\"\");\n          setLoadEvent(false);\n        }}\n        loadEvent={loadEvent}\n      />\n    </DefaultLayout>\n  );\n}\n\nexport default DesignationScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,MAAO,CAAAC,aAAa,KAAM,gCAAgC,CAC1D,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CACvD,MAAO,CAAAC,aAAa,KAAM,mCAAmC,CAC7D,OACEC,YAAY,CACZC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,YAAY,CACZC,eAAe,KACV,2CAA2C,CAClD,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,KAAK,KAAM,2BAA2B,CAC7C,OAASC,WAAW,GAAI,CAAAC,SAAS,KAAQ,iBAAiB,CAC1D,OAASC,KAAK,KAAQ,gBAAgB,CACtC,MAAO,CAAAC,iBAAiB,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtE,KAAM,CAAAC,QAAQ,CAAG,CACfC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,EACT,CAAC,CACD,KAAM,CAAAC,UAAU,CAAG,CAACH,QAAQ,CAACC,KAAK,CAAED,QAAQ,CAACE,KAAK,CAAC,CAEnD,QAAS,CAAAE,iBAAiBA,CAAA,CAAG,CAC3B,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4B,QAAQ,CAAG7B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8B,QAAQ,CAAGjC,WAAW,CAAC,CAAC,CAE9B,KAAM,CAACkC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpC,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACqC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CACrE,KAAM,CAACuC,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACyC,SAAS,CAAEC,YAAY,CAAC,CAAG1C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC2C,eAAe,CAAEC,kBAAkB,CAAC,CAAG5C,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC6C,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAEnE,KAAM,CAAC+C,QAAQ,CAAEC,WAAW,CAAC,CAAGhD,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACiD,WAAW,CAAEC,cAAc,CAAC,CAAGlD,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAACmD,UAAU,CAAEC,aAAa,CAAC,CAAGpD,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACqD,eAAe,CAAEC,kBAAkB,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACuD,aAAa,CAAEC,gBAAgB,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACyD,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG1D,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAC2D,WAAW,CAAEC,cAAc,CAAC,CAAG5D,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAC6D,cAAc,CAAEC,iBAAiB,CAAC,CAAG9D,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC+D,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGhE,QAAQ,CAAC,EAAE,CAAC,CAElE,KAAM,CAACiE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGlE,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACmE,sBAAsB,CAAEC,yBAAyB,CAAC,CAAGpE,QAAQ,CAAC,EAAE,CAAC,CAExE,KAAM,CAACqE,WAAW,CAAEC,cAAc,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACuE,eAAe,CAAEC,kBAAkB,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACyE,cAAc,CAAEC,iBAAiB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CACxD,KAAM,CAAC2E,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5E,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAAA6E,SAAS,CAAG3E,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,UAAU,CAAG9E,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACG,UAAU,CAAC,CAC3D,KAAM,CAAEC,OAAO,CAAEC,aAAa,CAAEC,WAAW,CAAEC,aAAc,CAAC,CAAGL,UAAU,CAEzE,KAAM,CAAAM,SAAS,CAAGpF,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACS,eAAe,CAAC,CAC/D,KAAM,CAAEC,gBAAgB,CAAEC,cAAc,CAAEC,gBAAiB,CAAC,CAAGJ,SAAS,CAExE,KAAM,CAAAK,YAAY,CAAGzF,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACnE,YAAY,CAAC,CAC/D,KAAM,CAAEiF,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEH,YAAY,CAEd,KAAM,CAAAI,YAAY,CAAG7F,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAAC/D,YAAY,CAAC,CAC/D,KAAM,CAAEiF,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEH,YAAY,CAEd;AACA,KAAM,CAAAI,aAAa,CAAGjG,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACsB,aAAa,CAAC,CACjE,KAAM,CAAEC,UAAU,CAAEC,gBAAgB,CAAEC,cAAc,CAAEC,gBAAiB,CAAC,CACtEL,aAAa,CAEf,KAAM,CAAAM,eAAe,CAAGvG,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAAClE,eAAe,CAAC,CACrE,KAAM,CACJ8F,sBAAsB,CACtBC,oBAAoB,CACpBC,sBACF,CAAC,CAAGH,eAAe,CAEnB,KAAM,CAAAI,YAAY,CAAG3G,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAACgC,kBAAkB,CAAC,CACrE,KAAM,CAAEC,mBAAmB,CAAEC,iBAAiB,CAAEC,mBAAoB,CAAC,CACnEJ,YAAY,CAEd,KAAM,CAAAK,eAAe,CAAGhH,WAAW,CAAE4E,KAAK,EAAKA,KAAK,CAAC9D,eAAe,CAAC,CACrE,KAAM,CACJmG,sBAAsB,CACtBC,oBAAoB,CACpBC,sBACF,CAAC,CAAGH,eAAe,CAEnB,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAEpBvH,SAAS,CAAC,IAAM,CACd,GAAI,CAACgF,QAAQ,CAAE,CACb/C,QAAQ,CAACsF,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLpF,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAC1BqB,QAAQ,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAC/B,CACF,CAAC,CAAE,CAACkB,QAAQ,CAAE+C,QAAQ,CAAE7C,QAAQ,CAAC,CAAC,CAElCnC,SAAS,CAAC,IAAM,CACd,GAAIsH,sBAAsB,CAAE,CAC1BnF,QAAQ,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAC7BoC,cAAc,CAAC,EAAE,CAAC,CAClBR,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBM,qBAAqB,CAAC,KAAK,CAAC,CAC5BR,sBAAsB,CAAC,KAAK,CAAC,CAC7BkB,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,oBAAoB,CAAC,EAAE,CAAC,CACxBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CACA,GAAIqC,mBAAmB,CAAE,CACvB/E,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAC1BqC,cAAc,CAAC,EAAE,CAAC,CAClBR,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBM,qBAAqB,CAAC,KAAK,CAAC,CAC5BR,sBAAsB,CAAC,KAAK,CAAC,CAC7BkB,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,oBAAoB,CAAC,EAAE,CAAC,CACxBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CAEA,GAAIgC,sBAAsB,CAAE,CAC1B1E,QAAQ,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAC7BoC,cAAc,CAAC,EAAE,CAAC,CAClBR,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBM,qBAAqB,CAAC,KAAK,CAAC,CAC5BR,sBAAsB,CAAC,KAAK,CAAC,CAC7BkB,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,oBAAoB,CAAC,EAAE,CAAC,CACxBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CAEA,GAAIc,gBAAgB,CAAE,CACpBxD,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAC1BmC,WAAW,CAAC,EAAE,CAAC,CACfN,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBI,kBAAkB,CAAC,KAAK,CAAC,CACzBR,mBAAmB,CAAC,KAAK,CAAC,CAC1BgB,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBQ,cAAc,CAAC,EAAE,CAAC,CAClBE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CACA,GAAIsB,mBAAmB,CAAE,CACvB5D,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAC1BmC,WAAW,CAAC,EAAE,CAAC,CACfN,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBI,kBAAkB,CAAC,KAAK,CAAC,CACzBR,mBAAmB,CAAC,KAAK,CAAC,CAC1BgB,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBQ,cAAc,CAAC,EAAE,CAAC,CAClBE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CAEA,GAAI0B,mBAAmB,CAAE,CACvBhE,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAC1BmC,WAAW,CAAC,EAAE,CAAC,CACfN,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACnBI,kBAAkB,CAAC,KAAK,CAAC,CACzBR,mBAAmB,CAAC,KAAK,CAAC,CAC1BgB,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBQ,cAAc,CAAC,EAAE,CAAC,CAClBE,kBAAkB,CAAC,EAAE,CAAC,CACxB,CACF,CAAC,CAAE,CACDsB,mBAAmB,CACnBI,mBAAmB,CACnBR,gBAAgB,CAChBkB,sBAAsB,CACtBK,mBAAmB,CACnBI,sBAAsB,CACvB,CAAC,CAEF,KAAM,CAAAE,WAAW,CAAG,EAAE,CAACC,GAAG,CAAEC,IAAI,EAAK,CACnC,MAAO,CAAC,CAAC,CACX,CAAC,CAAC,CAEF,mBACE/F,KAAA,CAACpB,aAAa,EAAAoH,QAAA,eACZhG,KAAA,QAAAgG,QAAA,eACEhG,KAAA,QAAKiG,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDlG,IAAA,MAAGoG,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBhG,KAAA,QAAKiG,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DlG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNzG,IAAA,SAAMmG,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJlG,IAAA,SAAAkG,QAAA,cACElG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzG,IAAA,QAAKmG,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAY,CAAK,CAAC,cACpClG,IAAA,SAAAkG,QAAA,cACElG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPzG,IAAA,QAAKmG,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,gBAAW,CAAK,CAAC,EAChC,CAAC,cAENhG,KAAA,QAAKiG,SAAS,CAAC,mIAAmI,CAAAD,QAAA,eAChJlG,IAAA,QAAKmG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DlG,IAAA,OAAImG,SAAS,CAAC,uCAAuC,CAAAD,QAAA,CAAC,6BAEtD,CAAI,CAAC,CACF,CAAC,cAINlG,IAAA,QAAKmG,SAAS,CAAC,aAAa,CAAAD,QAAA,cAC1BhG,KAAA,QAAKiG,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxClG,IAAA,QAAKmG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACvClG,IAAA,CAAChB,aAAa,EAAC0H,KAAK,CAAC,SAAS,CAAAR,QAAA,cAC5BhG,KAAA,QAAKiG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnClG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACb3F,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,KAAK,CAAC,CACnBN,mBAAmB,CAAC,IAAI,CAAC,CACzBgB,aAAa,CAAC,EAAE,CAAC,CACjBE,kBAAkB,CAAC,EAAE,CAAC,CACtBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBE,sBAAsB,CAAC,EAAE,CAAC,CAC1BM,cAAc,CAAC,EAAE,CAAC,CACpB,CAAE,CACFqD,SAAS,CAAC,iEAAiE,CAAAD,QAAA,CAC5E,sBAED,CAAK,CAAC,CAELvF,gBAAgB,cACfX,IAAA,QAAKmG,SAAS,CAAC,0FAA0F,CAAAD,QAAA,cACvGhG,KAAA,QAAKiG,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvElG,IAAA,OAAImG,SAAS,CAAC,2CAA2C,CAAAD,QAAA,CACtDjF,SAAS,EAAI,KAAK,CACf,2BAA2B,CAC3B,gCAAgC,CAAGM,QAAQ,CAC7C,CAAC,cACLvB,IAAA,QAAK,CAAC,cACNA,IAAA,QAAKmG,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBhG,KAAA,QAAAgG,QAAA,eACElG,IAAA,QAAKmG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BlG,IAAA,CAACjB,UAAU,EACT6H,KAAK,CAAC,eAAe,CACrBC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEpF,UAAW,CAClBqF,QAAQ,CAAGC,CAAC,EACVrF,aAAa,CAACqF,CAAC,CAACC,MAAM,CAACH,KAAK,CAC7B,CACDI,KAAK,CAAEtF,eAAgB,CACxB,CAAC,CACC,CAAC,cACN3B,KAAA,QAAKiG,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5BlG,IAAA,UACE6G,IAAI,CAAE,UAAW,CACjBO,OAAO,CAAEjF,WAAY,CACrBgE,SAAS,CAAC,MAAM,CAChBa,QAAQ,CAAGC,CAAC,EAAK7E,cAAc,CAAC,CAACD,WAAW,CAAE,CAC/C,CAAC,cACFjC,KAAA,QACEyG,OAAO,CAAEA,CAAA,GAAMvE,cAAc,CAAC,CAACD,WAAW,CAAE,CAC5CgE,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EACxC,eACc,CAAC,GAAG,EACd,CAAC,EACH,CAAC,cACNhG,KAAA,QAAKiG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBhG,KAAA,QAAKiG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EACtCjF,SAAS,GAAK,QAAQ,CACnB8B,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiD,GAAG,CAAC,CAACqB,GAAG,CAAEC,KAAK,gBAC9BpH,KAAA,QACEyG,OAAO,CAAEA,CAAA,GAAM,CACb3D,kBAAkB,CAChBD,eAAe,CAACwE,MAAM,CACpB,CAACF,GAAG,CAAEG,CAAC,GAAKA,CAAC,GAAKF,KACpB,CACF,CAAC,CACH,CAAE,CACFnB,SAAS,CAAC,yFAAyF,CAAAD,QAAA,eAEnGlG,IAAA,QAAAkG,QAAA,CAAMmB,GAAG,CAACI,QAAQ,CAAM,CAAC,cACzBzH,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,EACH,CACN,CAAC,CACF,IAAI,CACP5D,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmD,GAAG,CAAC,CAACqB,GAAG,CAAEC,KAAK,gBAC3BpH,KAAA,QACEyG,OAAO,CAAEA,CAAA,GAAM,CACb7D,cAAc,CACZD,WAAW,CAAC0E,MAAM,CAChB,CAACF,GAAG,CAAEG,CAAC,GAAKA,CAAC,GAAKF,KACpB,CACF,CAAC,CACH,CAAE,CACFnB,SAAS,CAAC,yFAAyF,CAAAD,QAAA,eAEnGlG,IAAA,QAAAkG,QAAA,CAAMmB,GAAG,CAAM,CAAC,cAChBrH,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNvG,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ClG,IAAA,CAACjB,UAAU,EACT6H,KAAK,CAAC,aAAa,CACnBC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAE1E,cAAe,CACtB2E,QAAQ,CAAGC,CAAC,EACV3E,iBAAiB,CAAC2E,CAAC,CAACC,MAAM,CAACH,KAAK,CACjC,CACDI,KAAK,CAAE5E,mBAAoB,CAC5B,CAAC,cACFvC,IAAA,QAAAkG,QAAA,cACElG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACb7E,kBAAkB,CAAC,EAAE,CAAC,CACtB,GAAIO,cAAc,GAAK,EAAE,CAAE,CACzBG,sBAAsB,CACpB,sBACF,CAAC,CACH,CAAC,IAAM,CACL,GAAIH,cAAc,CAACqF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAChC5E,cAAc,CAAC,CACb,GAAGD,WAAW,CACdR,cAAc,CAACqF,IAAI,CAAC,CAAC,CACtB,CAAC,CACFpF,iBAAiB,CAAC,EAAE,CAAC,CACvB,CACF,CACF,CAAE,CACF+D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAElClG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,6MAA6M,CAChN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cACNvG,KAAA,QAAKiG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BhG,KAAA,WACEiG,SAAS,CAAC,0EAA0E,CACpFQ,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAgB,MAAM,CAAG,IAAI,CACjB,GAAIhG,UAAU,GAAK,EAAE,CAAE,CACrBG,kBAAkB,CAAC,sBAAsB,CAAC,CAC1C6F,MAAM,CAAG,KAAK,CAChB,CACA,GAAIA,MAAM,CAAE,CACV3G,YAAY,CAAC,IAAI,CAAC,CAClB,GAAIC,SAAS,GAAK,KAAK,CAAE,CACvB,KAAM,CAAAP,QAAQ,CACZzB,YAAY,CAAC,CACX2I,gBAAgB,CAAEjG,UAAU,CAC5BkG,YAAY,CAAE1F,WAAW,CACrB,MAAM,CACN,OAAO,CACX2F,KAAK,CAAEC,IAAI,CAACC,SAAS,CAACnF,WAAW,CACnC,CAAC,CACH,CAAC,CAACoF,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB,KAAM,CAAAvH,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAAC4I,IAAI,CACnC,IAAM,CAAC,CACT,CAAC,CACH,CAAC,IAAM,IACLhH,SAAS,GAAK,QAAQ,EACtBM,QAAQ,GAAK,EAAE,CACf,CACA,KAAM,CAAAb,QAAQ,CACZnB,YAAY,CAACgC,QAAQ,CAAE,CACrBqG,gBAAgB,CAAEjG,UAAU,CAC5BkG,YAAY,CAAE1F,WAAW,CACrB,MAAM,CACN,OAAO,CACX2F,KAAK,CAAEC,IAAI,CAACC,SAAS,CAACnF,WAAW,CAAC,CAClCqF,SAAS,CACPH,IAAI,CAACC,SAAS,CAACjF,eAAe,CAClC,CAAC,CACH,CAAC,CAACkF,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB,KAAM,CAAAvH,QAAQ,CAACrB,cAAc,CAAC,CAAC,CAAC,CAAC4I,IAAI,CACnC,IAAM,CAAC,CACT,CAAC,CACH,CACAjH,YAAY,CAAC,KAAK,CAAC,CACnBJ,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,IAAM,CACLf,KAAK,CAACsH,KAAK,CACT,qDACF,CAAC,CACH,CACF,CAAE,CACFgB,QAAQ,CAAEpH,SAAU,CAAAmF,QAAA,EAEnB,GAAG,CACHnF,SAAS,cACRb,KAAA,QAAKkI,IAAI,CAAC,QAAQ,CAAAlC,QAAA,eAChBhG,KAAA,QACE,cAAY,MAAM,CAClBiG,SAAS,CAAC,wEAAwE,CAClFI,OAAO,CAAC,aAAa,CACrBD,IAAI,CAAC,MAAM,CACXD,KAAK,CAAC,4BAA4B,CAAAH,QAAA,eAElClG,IAAA,SACEyG,CAAC,CAAC,8WAA8W,CAChXH,IAAI,CAAC,cAAc,CACpB,CAAC,cACFtG,IAAA,SACEyG,CAAC,CAAC,+kBAA+kB,CACjlBH,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACNtG,IAAA,SAAMmG,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,EACxC,CAAC,CAEN,SACD,CAAE,GAAG,EACA,CAAC,cACTlG,IAAA,WACEmG,SAAS,CAAC,oEAAoE,CAC9EQ,OAAO,CAAEA,CAAA,GAAM,CACb/F,mBAAmB,CAAC,KAAK,CAAC,CAC1BI,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFmH,QAAQ,CAAEpH,SAAU,CAAAmF,QAAA,CACrB,SAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPvC,aAAa,cACZ3D,IAAA,CAACP,MAAM,GAAE,CAAC,CACRmE,WAAW,cACb5D,IAAA,CAACN,KAAK,EAACmH,IAAI,CAAC,OAAO,CAACwB,OAAO,CAAEzE,WAAY,CAAE,CAAC,cAE5C1D,KAAA,UAAOiG,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACvDlG,IAAA,UAAAkG,QAAA,cACEhG,KAAA,OAAIiG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClClG,IAAA,OAAImG,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,QAE3E,CAAI,CAAC,cACLlG,IAAA,OAAImG,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,aAE5E,CAAI,CAAC,cACLlG,IAAA,OAAImG,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,SAE3E,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRlG,IAAA,UAAAkG,QAAA,CACGxC,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEsC,GAAG,CAAC,CAACsC,MAAM,CAAEC,EAAE,QAAAC,aAAA,oBACvBtI,KAAA,OAAAgG,QAAA,eACElG,IAAA,OAAImG,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC5DhG,KAAA,MAAGiG,SAAS,CAAC,kCAAkC,CAAAD,QAAA,EAC5CoC,MAAM,CAACV,gBAAgB,CACvBU,MAAM,CAACT,YAAY,cAClB7H,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,cAAc,CAAAD,QAAA,cAExBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,iXAAiX,CACpX,CAAC,CACC,CAAC,CACJ,IAAI,EACP,CAAC,CACF,CAAC,cACLzG,IAAA,OAAImG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9DlG,IAAA,QAAKmG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,EAAAsC,aAAA,CAC9BF,MAAM,CAACG,KAAK,UAAAD,aAAA,iBAAZA,aAAA,CAAcxC,GAAG,CAAC,CAACC,IAAI,CAAEqB,KAAK,gBAC7BtH,IAAA,MAAGmG,SAAS,CAAC,+FAA+F,CAAAD,QAAA,CACzGD,IAAI,CAACwB,QAAQ,CACb,CACJ,CAAC,CACC,CAAC,CACJ,CAAC,cACLzH,IAAA,OAAImG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9DhG,KAAA,MAAGiG,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eAEtDlG,IAAA,QACEmG,SAAS,CAAC,kCAAkC,CAC5CQ,OAAO,CAAEA,CAAA,GAAM,CACbnF,WAAW,CAAC8G,MAAM,CAACC,EAAE,CAAC,CACtBrH,YAAY,CAAC,QAAQ,CAAC,CACtBU,aAAa,CAAC0G,MAAM,CAACV,gBAAgB,CAAC,CACtCxF,cAAc,CAACkG,MAAM,CAACT,YAAY,CAAC,CACnC7E,kBAAkB,CAACsF,MAAM,CAACG,KAAK,CAAC,CAEhC7H,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CAAAsF,QAAA,cAEFlG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzElG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACH,CAAC,cACNzG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACbnF,WAAW,CAAC8G,MAAM,CAACC,EAAE,CAAC,CACtBrH,YAAY,CAAC,QAAQ,CAAC,CACtBE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACF+E,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5ClG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExElG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,CACG,CAAC,EACH,CACR,EACE,CAAC,CACO,CAAC,CACb,CAAC,cACNzG,IAAA,QAAKmG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACvClG,IAAA,CAAChB,aAAa,EAAC0H,KAAK,CAAC,YAAY,CAAAR,QAAA,cAC/BhG,KAAA,QAAKiG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnClG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACb3F,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,KAAK,CAAC,CACnBJ,sBAAsB,CAAC,IAAI,CAAC,CAC5BkB,gBAAgB,CAAC,EAAE,CAAC,CACpBE,qBAAqB,CAAC,EAAE,CAAC,CACzBQ,oBAAoB,CAAC,EAAE,CAAC,CACxBE,yBAAyB,CAAC,EAAE,CAAC,CAC7BM,iBAAiB,CAAC,EAAE,CAAC,CACvB,CAAE,CACFiD,SAAS,CAAC,iEAAiE,CAAAD,QAAA,CAC5E,sBAED,CAAK,CAAC,CAELrF,mBAAmB,cAClBb,IAAA,QAAKmG,SAAS,CAAC,0FAA0F,CAAAD,QAAA,cACvGhG,KAAA,QAAKiG,SAAS,CAAC,0DAA0D,CAAAD,QAAA,eACvElG,IAAA,OAAImG,SAAS,CAAC,2CAA2C,CAAAD,QAAA,CACtDjF,SAAS,EAAI,KAAK,CACf,8BAA8B,CAC9B,mCAAmC,CACnCQ,WAAW,CACb,CAAC,cACLzB,IAAA,QAAK,CAAC,cACNA,IAAA,QAAKmG,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnBhG,KAAA,QAAAgG,QAAA,eACElG,IAAA,QAAKmG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,cAC/BlG,IAAA,CAACjB,UAAU,EACT6H,KAAK,CAAC,kBAAkB,CACxBC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEhF,aAAc,CACrBiF,QAAQ,CAAGC,CAAC,EACVjF,gBAAgB,CAACiF,CAAC,CAACC,MAAM,CAACH,KAAK,CAChC,CACDI,KAAK,CAAElF,kBAAmB,CAC3B,CAAC,CACC,CAAC,cACN/B,KAAA,QAAKiG,SAAS,CAAC,WAAW,CAAAD,QAAA,eACxBhG,KAAA,QAAKiG,SAAS,CAAC,0BAA0B,CAAAD,QAAA,EACtCjF,SAAS,GAAK,QAAQ,CACnBkC,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAE6C,GAAG,CAAC,CAACqB,GAAG,CAAEC,KAAK,gBACjCpH,KAAA,QACEyG,OAAO,CAAEA,CAAA,GAAM,CACbvD,qBAAqB,CACnBD,kBAAkB,CAACoE,MAAM,CACvB,CAACF,GAAG,CAAEG,CAAC,GAAKA,CAAC,GAAKF,KACpB,CACF,CAAC,CACH,CAAE,CACFnB,SAAS,CAAC,yFAAyF,CAAAD,QAAA,eAEnGlG,IAAA,QAAAkG,QAAA,CAAMmB,GAAG,CAACI,QAAQ,CAAM,CAAC,cACzBzH,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,EACH,CACN,CAAC,CACF,IAAI,CACPxD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE+C,GAAG,CAAC,CAACqB,GAAG,CAAEC,KAAK,gBAC9BpH,KAAA,QACEyG,OAAO,CAAEA,CAAA,GAAM,CACbzD,iBAAiB,CACfD,cAAc,CAACsE,MAAM,CACnB,CAACF,GAAG,CAAEG,CAAC,GAAKA,CAAC,GAAKF,KACpB,CACF,CAAC,CACH,CAAE,CACFnB,SAAS,CAAC,yFAAyF,CAAAD,QAAA,eAEnGlG,IAAA,QAAAkG,QAAA,CAAMmB,GAAG,CAAM,CAAC,cAChBrH,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBlG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNvG,KAAA,QAAKiG,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1ClG,IAAA,CAACjB,UAAU,EACT6H,KAAK,CAAC,gBAAgB,CACtBC,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,EAAE,CACdC,KAAK,CAAEtE,iBAAkB,CACzBuE,QAAQ,CAAGC,CAAC,EACVvE,oBAAoB,CAACuE,CAAC,CAACC,MAAM,CAACH,KAAK,CACpC,CACDI,KAAK,CAAExE,sBAAuB,CAC/B,CAAC,cACF3C,IAAA,QAAAkG,QAAA,cACElG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACbzE,qBAAqB,CAAC,EAAE,CAAC,CACzB,GAAIO,iBAAiB,GAAK,EAAE,CAAE,CAC5BG,yBAAyB,CACvB,sBACF,CAAC,CACH,CAAC,IAAM,CACL,GAAIH,iBAAiB,CAACiF,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACnCxE,iBAAiB,CAAC,CAChB,GAAGD,cAAc,CACjBR,iBAAiB,CAACiF,IAAI,CAAC,CAAC,CACzB,CAAC,CACFhF,oBAAoB,CAAC,EAAE,CAAC,CAC1B,CACF,CACF,CAAE,CACF2D,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,wBAAwB,CAAAD,QAAA,cAElClG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,6MAA6M,CAChN,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cACNvG,KAAA,QAAKiG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,eAC/BhG,KAAA,WACEiG,SAAS,CAAC,0EAA0E,CACpFQ,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAAgB,MAAM,CAAG,IAAI,CACjB,GAAI5F,aAAa,GAAK,EAAE,CAAE,CACxBG,qBAAqB,CAAC,sBAAsB,CAAC,CAC7CyF,MAAM,CAAG,KAAK,CAChB,CACA,GAAIA,MAAM,CAAE,CACV3G,YAAY,CAAC,IAAI,CAAC,CAClB,GAAIC,SAAS,GAAK,KAAK,CAAE,CACvB,KAAM,CAAAP,QAAQ,CACZxB,eAAe,CAAC,CACdwJ,cAAc,CAAE3G,aAAa,CAE7B+F,KAAK,CAAEC,IAAI,CAACC,SAAS,CAAC/E,cAAc,CACtC,CAAC,CACH,CAAC,CAACgF,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB,KAAM,CAAAvH,QAAQ,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAAC2I,IAAI,CACtC,IAAM,CAAC,CACT,CAAC,CACH,CAAC,IAAM,IACLhH,SAAS,GAAK,QAAQ,EACtBQ,WAAW,GAAK,EAAE,CAClB,CACA,KAAM,CAAAf,QAAQ,CACZlB,eAAe,CAACiC,WAAW,CAAE,CAC3BiH,cAAc,CAAE3G,aAAa,CAC7B+F,KAAK,CAAEC,IAAI,CAACC,SAAS,CAAC/E,cAAc,CAAC,CACrCiF,SAAS,CACPH,IAAI,CAACC,SAAS,CAAC7E,kBAAkB,CACrC,CAAC,CACH,CAAC,CAAC8E,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAChB,KAAM,CAAAvH,QAAQ,CAACpB,iBAAiB,CAAC,CAAC,CAAC,CAAC2I,IAAI,CACtC,IAAM,CAAC,CACT,CAAC,CACH,CACAjH,YAAY,CAAC,KAAK,CAAC,CACnBF,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CAAC,IAAM,CACLjB,KAAK,CAACsH,KAAK,CACT,qDACF,CAAC,CACH,CACF,CAAE,CACFgB,QAAQ,CAAEpH,SAAU,CAAAmF,QAAA,EAEnB,GAAG,CACHnF,SAAS,cACRb,KAAA,QAAKkI,IAAI,CAAC,QAAQ,CAAAlC,QAAA,eAChBhG,KAAA,QACE,cAAY,MAAM,CAClBiG,SAAS,CAAC,wEAAwE,CAClFI,OAAO,CAAC,aAAa,CACrBD,IAAI,CAAC,MAAM,CACXD,KAAK,CAAC,4BAA4B,CAAAH,QAAA,eAElClG,IAAA,SACEyG,CAAC,CAAC,8WAA8W,CAChXH,IAAI,CAAC,cAAc,CACpB,CAAC,cACFtG,IAAA,SACEyG,CAAC,CAAC,+kBAA+kB,CACjlBH,IAAI,CAAC,aAAa,CACnB,CAAC,EACC,CAAC,cACNtG,IAAA,SAAMmG,SAAS,CAAC,SAAS,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,EACxC,CAAC,CAEN,SACD,CAAE,GAAG,EACA,CAAC,cACTlG,IAAA,WACEmG,SAAS,CAAC,oEAAoE,CAC9EQ,OAAO,CAAEA,CAAA,GAAM,CACb7F,sBAAsB,CAAC,KAAK,CAAC,CAC7BE,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFmH,QAAQ,CAAEpH,SAAU,CAAAmF,QAAA,CACrB,SAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPpB,gBAAgB,cACf9E,IAAA,CAACP,MAAM,GAAE,CAAC,CACRsF,cAAc,cAChB/E,IAAA,CAACN,KAAK,EAACmH,IAAI,CAAC,OAAO,CAACwB,OAAO,CAAEtD,cAAe,CAAE,CAAC,cAE/C7E,KAAA,UAAOiG,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACvDlG,IAAA,UAAAkG,QAAA,cACEhG,KAAA,OAAIiG,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClClG,IAAA,OAAImG,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,WAE3E,CAAI,CAAC,cACLlG,IAAA,OAAImG,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,gBAE5E,CAAI,CAAC,cACLlG,IAAA,OAAImG,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,SAE3E,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cACRlG,IAAA,UAAAkG,QAAA,CACGrB,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEmB,GAAG,CAAC,CAAC2C,SAAS,CAAEJ,EAAE,QAAAK,gBAAA,oBAC7B1I,KAAA,OAAAgG,QAAA,eACElG,IAAA,OAAImG,SAAS,CAAC,gDAAgD,CAAAD,QAAA,cAC5DlG,IAAA,MAAGmG,SAAS,CAAC,kCAAkC,CAAAD,QAAA,CAC5CyC,SAAS,CAACD,cAAc,CACxB,CAAC,CACF,CAAC,cACL1I,IAAA,OAAImG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9DlG,IAAA,QAAKmG,SAAS,CAAC,kBAAkB,CAAAD,QAAA,EAAA0C,gBAAA,CAC9BD,SAAS,CAACF,KAAK,UAAAG,gBAAA,iBAAfA,gBAAA,CAAiB5C,GAAG,CAAC,CAACC,IAAI,CAAEqB,KAAK,gBAChCtH,IAAA,MAAGmG,SAAS,CAAC,+FAA+F,CAAAD,QAAA,CACzGD,IAAI,CAACwB,QAAQ,CACb,CACJ,CAAC,CACC,CAAC,CACJ,CAAC,cACLzH,IAAA,OAAImG,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC9DhG,KAAA,MAAGiG,SAAS,CAAC,2CAA2C,CAAAD,QAAA,eAEtDlG,IAAA,QACEmG,SAAS,CAAC,kCAAkC,CAC5CQ,OAAO,CAAEA,CAAA,GAAM,CACbjF,cAAc,CAACiH,SAAS,CAACJ,EAAE,CAAC,CAC5BrH,YAAY,CAAC,QAAQ,CAAC,CACtBc,gBAAgB,CACd2G,SAAS,CAACD,cACZ,CAAC,CACDtF,qBAAqB,CAACuF,SAAS,CAACF,KAAK,CAAC,CAEtC3H,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CAAE,CAAAoF,QAAA,cAEFlG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzElG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,kQAAkQ,CACrQ,CAAC,CACC,CAAC,CACH,CAAC,cACNzG,IAAA,QACE2G,OAAO,CAAEA,CAAA,GAAM,CACbjF,cAAc,CAACiH,SAAS,CAACJ,EAAE,CAAC,CAC5BrH,YAAY,CAAC,QAAQ,CAAC,CACtBI,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CAAE,CACF6E,SAAS,CAAC,kCAAkC,CAAAD,QAAA,cAE5ClG,IAAA,QACEqG,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExElG,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvByG,CAAC,CAAC,+ZAA+Z,CACla,CAAC,CACC,CAAC,CACH,CAAC,EACL,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,CACG,CAAC,EACH,CACR,EACE,CAAC,CACO,CAAC,CACb,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAENzG,IAAA,QAAKmG,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,cAENnG,IAAA,CAACF,iBAAiB,EAChB+I,MAAM,CAAE1H,eAAgB,CACxBkH,OAAO,CACLpH,SAAS,GAAK,QAAQ,CAClB,wDAAwD,CACxDA,SAAS,GAAK,QAAQ,CACtB,qDAAqD,CACrD,sDACL,CACD6H,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI7H,SAAS,GAAK,QAAQ,CAAE,CAC1B,GAAIM,QAAQ,GAAK,EAAE,CAAE,CACnBP,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAN,QAAQ,CAACvB,YAAY,CAACoC,QAAQ,CAAC,CAAC,CAAC0G,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CACrDjH,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CACF,CAAE,CACF2H,QAAQ,CAAEA,CAAA,GAAM,CACd3H,kBAAkB,CAAC,KAAK,CAAC,CACzBF,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,cAEFf,IAAA,CAACF,iBAAiB,EAChB+I,MAAM,CAAExH,kBAAmB,CAC3BgH,OAAO,CACLpH,SAAS,GAAK,QAAQ,CAClB,2DAA2D,CAC3DA,SAAS,GAAK,QAAQ,CACtB,wDAAwD,CACxD,yDACL,CACD6H,SAAS,CAAE,KAAAA,CAAA,GAAY,CACrB,GAAI7H,SAAS,GAAK,QAAQ,CAAE,CAC1B,GAAIQ,WAAW,GAAK,EAAE,CAAE,CACtBT,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAN,QAAQ,CAACtB,eAAe,CAACqC,WAAW,CAAC,CAAC,CAACwG,IAAI,CAAC,IAAM,CAAC,CAAC,CAAC,CAC3DjH,YAAY,CAAC,KAAK,CAAC,CACnBE,YAAY,CAAC,EAAE,CAAC,CAChBI,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CACF,CAAE,CACFyH,QAAQ,CAAEA,CAAA,GAAM,CACdzH,qBAAqB,CAAC,KAAK,CAAC,CAC5BJ,YAAY,CAAC,EAAE,CAAC,CAChBF,YAAY,CAAC,KAAK,CAAC,CACrB,CAAE,CACFD,SAAS,CAAEA,SAAU,CACtB,CAAC,EACW,CAAC,CAEpB,CAEA,cAAe,CAAAR,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}