{"ast": null, "code": "function addDomEvent(target, eventName, handler) {\n  let options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {\n    passive: true\n  };\n  target.addEventListener(eventName, handler, options);\n  return () => target.removeEventListener(eventName, handler);\n}\nexport { addDomEvent };", "map": {"version": 3, "names": ["addDomEvent", "target", "eventName", "handler", "options", "arguments", "length", "undefined", "passive", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/events/add-dom-event.mjs"], "sourcesContent": ["function addDomEvent(target, eventName, handler, options = { passive: true }) {\n    target.addEventListener(eventName, handler, options);\n    return () => target.removeEventListener(eventName, handler);\n}\n\nexport { addDomEvent };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAA+B;EAAA,IAA7BC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAAEG,OAAO,EAAE;EAAK,CAAC;EACxEP,MAAM,CAACQ,gBAAgB,CAACP,SAAS,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACpD,OAAO,MAAMH,MAAM,CAACS,mBAAmB,CAACR,SAAS,EAAEC,OAAO,CAAC;AAC/D;AAEA,SAASH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}