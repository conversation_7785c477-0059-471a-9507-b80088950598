{"ast": null, "code": "function getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isWebKit };", "map": {"version": 3, "names": ["getNodeName", "node", "isNode", "nodeName", "toLowerCase", "getWindow", "_node$ownerDocument", "ownerDocument", "defaultView", "window", "getDocumentElement", "_ref", "document", "documentElement", "value", "Node", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "isOverflowElement", "element", "overflow", "overflowX", "overflowY", "display", "getComputedStyle", "test", "includes", "isTableElement", "isContainingBlock", "webkit", "isWebKit", "css", "transform", "perspective", "containerType", "<PERSON><PERSON>ilter", "filter", "some", "<PERSON><PERSON><PERSON><PERSON>", "contain", "getContainingBlock", "currentNode", "getParentNode", "isLastTraversableNode", "CSS", "supports", "getNodeScroll", "scrollLeft", "scrollTop", "pageXOffset", "pageYOffset", "result", "assignedSlot", "parentNode", "host", "getNearestOverflowAncestor", "body", "getOverflowAncestors", "list", "traverseIframes", "_node$ownerDocument2", "scrollableAncestor", "isBody", "win", "concat", "visualViewport", "frameElement"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs"], "sourcesContent": ["function getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  // Browsers without `ShadowRoot` support.\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isContainingBlock(element) {\n  const webkit = isWebKit();\n  const css = getComputedStyle(element);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  return css.transform !== 'none' || css.perspective !== 'none' || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.pageXOffset,\n    scrollTop: element.pageYOffset\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], win.frameElement && traverseIframes ? getOverflowAncestors(win.frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isWebKit };\n"], "mappings": "AAAA,SAASA,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIC,MAAM,CAACD,IAAI,CAAC,EAAE;IAChB,OAAO,CAACA,IAAI,CAACE,QAAQ,IAAI,EAAE,EAAEC,WAAW,CAAC,CAAC;EAC5C;EACA;EACA;EACA;EACA,OAAO,WAAW;AACpB;AACA,SAASC,SAASA,CAACJ,IAAI,EAAE;EACvB,IAAIK,mBAAmB;EACvB,OAAO,CAACL,IAAI,IAAI,IAAI,IAAI,CAACK,mBAAmB,GAAGL,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,WAAW,KAAKC,MAAM;AAClI;AACA,SAASC,kBAAkBA,CAACT,IAAI,EAAE;EAChC,IAAIU,IAAI;EACR,OAAO,CAACA,IAAI,GAAG,CAACT,MAAM,CAACD,IAAI,CAAC,GAAGA,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACW,QAAQ,KAAKH,MAAM,CAACG,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,IAAI,CAACE,eAAe;AAChI;AACA,SAASX,MAAMA,CAACY,KAAK,EAAE;EACrB,OAAOA,KAAK,YAAYC,IAAI,IAAID,KAAK,YAAYT,SAAS,CAACS,KAAK,CAAC,CAACC,IAAI;AACxE;AACA,SAASC,SAASA,CAACF,KAAK,EAAE;EACxB,OAAOA,KAAK,YAAYG,OAAO,IAAIH,KAAK,YAAYT,SAAS,CAACS,KAAK,CAAC,CAACG,OAAO;AAC9E;AACA,SAASC,aAAaA,CAACJ,KAAK,EAAE;EAC5B,OAAOA,KAAK,YAAYK,WAAW,IAAIL,KAAK,YAAYT,SAAS,CAACS,KAAK,CAAC,CAACK,WAAW;AACtF;AACA,SAASC,YAAYA,CAACN,KAAK,EAAE;EAC3B;EACA,IAAI,OAAOO,UAAU,KAAK,WAAW,EAAE;IACrC,OAAO,KAAK;EACd;EACA,OAAOP,KAAK,YAAYO,UAAU,IAAIP,KAAK,YAAYT,SAAS,CAACS,KAAK,CAAC,CAACO,UAAU;AACpF;AACA,SAASC,iBAAiBA,CAACC,OAAO,EAAE;EAClC,MAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC;EACF,CAAC,GAAGC,gBAAgB,CAACL,OAAO,CAAC;EAC7B,OAAO,iCAAiC,CAACM,IAAI,CAACL,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAACK,QAAQ,CAACH,OAAO,CAAC;AAC9H;AACA,SAASI,cAAcA,CAACR,OAAO,EAAE;EAC/B,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAACO,QAAQ,CAAC9B,WAAW,CAACuB,OAAO,CAAC,CAAC;AAC7D;AACA,SAASS,iBAAiBA,CAACT,OAAO,EAAE;EAClC,MAAMU,MAAM,GAAGC,QAAQ,CAAC,CAAC;EACzB,MAAMC,GAAG,GAAGP,gBAAgB,CAACL,OAAO,CAAC;;EAErC;EACA,OAAOY,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,KAAKF,GAAG,CAACG,aAAa,GAAGH,GAAG,CAACG,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAACL,MAAM,KAAKE,GAAG,CAACI,cAAc,GAAGJ,GAAG,CAACI,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAACN,MAAM,KAAKE,GAAG,CAACK,MAAM,GAAGL,GAAG,CAACK,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,EAAE,QAAQ,CAAC,CAACC,IAAI,CAAC3B,KAAK,IAAI,CAACqB,GAAG,CAACO,UAAU,IAAI,EAAE,EAAEZ,QAAQ,CAAChB,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC2B,IAAI,CAAC3B,KAAK,IAAI,CAACqB,GAAG,CAACQ,OAAO,IAAI,EAAE,EAAEb,QAAQ,CAAChB,KAAK,CAAC,CAAC;AACpc;AACA,SAAS8B,kBAAkBA,CAACrB,OAAO,EAAE;EACnC,IAAIsB,WAAW,GAAGC,aAAa,CAACvB,OAAO,CAAC;EACxC,OAAOL,aAAa,CAAC2B,WAAW,CAAC,IAAI,CAACE,qBAAqB,CAACF,WAAW,CAAC,EAAE;IACxE,IAAIb,iBAAiB,CAACa,WAAW,CAAC,EAAE;MAClC,OAAOA,WAAW;IACpB;IACAA,WAAW,GAAGC,aAAa,CAACD,WAAW,CAAC;EAC1C;EACA,OAAO,IAAI;AACb;AACA,SAASX,QAAQA,CAAA,EAAG;EAClB,IAAI,OAAOc,GAAG,KAAK,WAAW,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE,OAAO,KAAK;EAC7D,OAAOD,GAAG,CAACC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,SAASF,qBAAqBA,CAAC9C,IAAI,EAAE;EACnC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC6B,QAAQ,CAAC9B,WAAW,CAACC,IAAI,CAAC,CAAC;AAClE;AACA,SAAS2B,gBAAgBA,CAACL,OAAO,EAAE;EACjC,OAAOlB,SAAS,CAACkB,OAAO,CAAC,CAACK,gBAAgB,CAACL,OAAO,CAAC;AACrD;AACA,SAAS2B,aAAaA,CAAC3B,OAAO,EAAE;EAC9B,IAAIP,SAAS,CAACO,OAAO,CAAC,EAAE;IACtB,OAAO;MACL4B,UAAU,EAAE5B,OAAO,CAAC4B,UAAU;MAC9BC,SAAS,EAAE7B,OAAO,CAAC6B;IACrB,CAAC;EACH;EACA,OAAO;IACLD,UAAU,EAAE5B,OAAO,CAAC8B,WAAW;IAC/BD,SAAS,EAAE7B,OAAO,CAAC+B;EACrB,CAAC;AACH;AACA,SAASR,aAAaA,CAAC7C,IAAI,EAAE;EAC3B,IAAID,WAAW,CAACC,IAAI,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOA,IAAI;EACb;EACA,MAAMsD,MAAM;EACZ;EACAtD,IAAI,CAACuD,YAAY;EACjB;EACAvD,IAAI,CAACwD,UAAU;EACf;EACArC,YAAY,CAACnB,IAAI,CAAC,IAAIA,IAAI,CAACyD,IAAI;EAC/B;EACAhD,kBAAkB,CAACT,IAAI,CAAC;EACxB,OAAOmB,YAAY,CAACmC,MAAM,CAAC,GAAGA,MAAM,CAACG,IAAI,GAAGH,MAAM;AACpD;AACA,SAASI,0BAA0BA,CAAC1D,IAAI,EAAE;EACxC,MAAMwD,UAAU,GAAGX,aAAa,CAAC7C,IAAI,CAAC;EACtC,IAAI8C,qBAAqB,CAACU,UAAU,CAAC,EAAE;IACrC,OAAOxD,IAAI,CAACM,aAAa,GAAGN,IAAI,CAACM,aAAa,CAACqD,IAAI,GAAG3D,IAAI,CAAC2D,IAAI;EACjE;EACA,IAAI1C,aAAa,CAACuC,UAAU,CAAC,IAAInC,iBAAiB,CAACmC,UAAU,CAAC,EAAE;IAC9D,OAAOA,UAAU;EACnB;EACA,OAAOE,0BAA0B,CAACF,UAAU,CAAC;AAC/C;AACA,SAASI,oBAAoBA,CAAC5D,IAAI,EAAE6D,IAAI,EAAEC,eAAe,EAAE;EACzD,IAAIC,oBAAoB;EACxB,IAAIF,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,EAAE;EACX;EACA,IAAIC,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAG,IAAI;EACxB;EACA,MAAME,kBAAkB,GAAGN,0BAA0B,CAAC1D,IAAI,CAAC;EAC3D,MAAMiE,MAAM,GAAGD,kBAAkB,MAAM,CAACD,oBAAoB,GAAG/D,IAAI,CAACM,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,oBAAoB,CAACJ,IAAI,CAAC;EAChI,MAAMO,GAAG,GAAG9D,SAAS,CAAC4D,kBAAkB,CAAC;EACzC,IAAIC,MAAM,EAAE;IACV,OAAOJ,IAAI,CAACM,MAAM,CAACD,GAAG,EAAEA,GAAG,CAACE,cAAc,IAAI,EAAE,EAAE/C,iBAAiB,CAAC2C,kBAAkB,CAAC,GAAGA,kBAAkB,GAAG,EAAE,EAAEE,GAAG,CAACG,YAAY,IAAIP,eAAe,GAAGF,oBAAoB,CAACM,GAAG,CAACG,YAAY,CAAC,GAAG,EAAE,CAAC;EACvM;EACA,OAAOR,IAAI,CAACM,MAAM,CAACH,kBAAkB,EAAEJ,oBAAoB,CAACI,kBAAkB,EAAE,EAAE,EAAEF,eAAe,CAAC,CAAC;AACvG;AAEA,SAASnC,gBAAgB,EAAEgB,kBAAkB,EAAElC,kBAAkB,EAAEiD,0BAA0B,EAAE3D,WAAW,EAAEkD,aAAa,EAAEW,oBAAoB,EAAEf,aAAa,EAAEzC,SAAS,EAAE2B,iBAAiB,EAAEhB,SAAS,EAAEE,aAAa,EAAE6B,qBAAqB,EAAE7C,MAAM,EAAEoB,iBAAiB,EAAEF,YAAY,EAAEW,cAAc,EAAEG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}