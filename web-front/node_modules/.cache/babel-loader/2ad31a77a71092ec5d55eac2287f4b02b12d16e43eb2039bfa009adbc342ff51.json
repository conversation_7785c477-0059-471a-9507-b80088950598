{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nexport var MonotonicInterpolant = /*#__PURE__*/function () {\n  function MonotonicInterpolant(xs, ys) {\n    _classCallCheck(this, MonotonicInterpolant);\n    _defineProperty(this, \"xs\", void 0);\n    _defineProperty(this, \"ys\", void 0);\n    _defineProperty(this, \"c1s\", void 0);\n    _defineProperty(this, \"c2s\", void 0);\n    _defineProperty(this, \"c3s\", void 0);\n    var length = xs.length; // Rearrange xs and ys so that xs is sorted\n\n    var indexes = [];\n    for (var i = 0; i < length; i++) {\n      indexes.push(i);\n    }\n    indexes.sort(function (a, b) {\n      return xs[a] < xs[b] ? -1 : 1;\n    }); // Get consecutive differences and slopes\n\n    var dys = [];\n    var dxs = [];\n    var ms = [];\n    var dx;\n    var dy;\n    for (var _i = 0; _i < length - 1; _i++) {\n      dx = xs[_i + 1] - xs[_i];\n      dy = ys[_i + 1] - ys[_i];\n      dxs.push(dx);\n      dys.push(dy);\n      ms.push(dy / dx);\n    } // Get degree-1 coefficients\n\n    var c1s = [ms[0]];\n    for (var _i2 = 0; _i2 < dxs.length - 1; _i2++) {\n      var m2 = ms[_i2];\n      var mNext = ms[_i2 + 1];\n      if (m2 * mNext <= 0) {\n        c1s.push(0);\n      } else {\n        dx = dxs[_i2];\n        var dxNext = dxs[_i2 + 1];\n        var common = dx + dxNext;\n        c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n      }\n    }\n    c1s.push(ms[ms.length - 1]); // Get degree-2 and degree-3 coefficients\n\n    var c2s = [];\n    var c3s = [];\n    var m;\n    for (var _i3 = 0; _i3 < c1s.length - 1; _i3++) {\n      m = ms[_i3];\n      var c1 = c1s[_i3];\n      var invDx = 1 / dxs[_i3];\n      var _common = c1 + c1s[_i3 + 1] - m - m;\n      c2s.push((m - c1 - _common) * invDx);\n      c3s.push(_common * invDx * invDx);\n    }\n    this.xs = xs;\n    this.ys = ys;\n    this.c1s = c1s;\n    this.c2s = c2s;\n    this.c3s = c3s;\n  }\n  _createClass(MonotonicInterpolant, [{\n    key: \"interpolate\",\n    value: function interpolate(x) {\n      var xs = this.xs,\n        ys = this.ys,\n        c1s = this.c1s,\n        c2s = this.c2s,\n        c3s = this.c3s; // The rightmost point in the dataset should give an exact result\n\n      var i = xs.length - 1;\n      if (x === xs[i]) {\n        return ys[i];\n      } // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\n      var low = 0;\n      var high = c3s.length - 1;\n      var mid;\n      while (low <= high) {\n        mid = Math.floor(0.5 * (low + high));\n        var xHere = xs[mid];\n        if (xHere < x) {\n          low = mid + 1;\n        } else if (xHere > x) {\n          high = mid - 1;\n        } else {\n          return ys[mid];\n        }\n      }\n      i = Math.max(0, high); // Interpolate\n\n      var diff = x - xs[i];\n      var diffSq = diff * diff;\n      return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n    }\n  }]);\n  return MonotonicInterpolant;\n}();", "map": {"version": 3, "names": ["_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "prototype", "_defineProperty", "obj", "value", "MonotonicInterpolant", "xs", "ys", "indexes", "push", "sort", "a", "b", "dys", "dxs", "ms", "dx", "dy", "_i", "c1s", "_i2", "m2", "mNext", "dxNext", "common", "c2s", "c3s", "m", "_i3", "c1", "invDx", "_common", "interpolate", "x", "low", "high", "mid", "Math", "floor", "xHere", "max", "diff", "diffSq"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-dnd-html5-backend/dist/esm/MonotonicInterpolant.js"], "sourcesContent": ["function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nexport var MonotonicInterpolant = /*#__PURE__*/function () {\n  function MonotonicInterpolant(xs, ys) {\n    _classCallCheck(this, MonotonicInterpolant);\n\n    _defineProperty(this, \"xs\", void 0);\n\n    _defineProperty(this, \"ys\", void 0);\n\n    _defineProperty(this, \"c1s\", void 0);\n\n    _defineProperty(this, \"c2s\", void 0);\n\n    _defineProperty(this, \"c3s\", void 0);\n\n    var length = xs.length; // Rearrange xs and ys so that xs is sorted\n\n    var indexes = [];\n\n    for (var i = 0; i < length; i++) {\n      indexes.push(i);\n    }\n\n    indexes.sort(function (a, b) {\n      return xs[a] < xs[b] ? -1 : 1;\n    }); // Get consecutive differences and slopes\n\n    var dys = [];\n    var dxs = [];\n    var ms = [];\n    var dx;\n    var dy;\n\n    for (var _i = 0; _i < length - 1; _i++) {\n      dx = xs[_i + 1] - xs[_i];\n      dy = ys[_i + 1] - ys[_i];\n      dxs.push(dx);\n      dys.push(dy);\n      ms.push(dy / dx);\n    } // Get degree-1 coefficients\n\n\n    var c1s = [ms[0]];\n\n    for (var _i2 = 0; _i2 < dxs.length - 1; _i2++) {\n      var m2 = ms[_i2];\n      var mNext = ms[_i2 + 1];\n\n      if (m2 * mNext <= 0) {\n        c1s.push(0);\n      } else {\n        dx = dxs[_i2];\n        var dxNext = dxs[_i2 + 1];\n        var common = dx + dxNext;\n        c1s.push(3 * common / ((common + dxNext) / m2 + (common + dx) / mNext));\n      }\n    }\n\n    c1s.push(ms[ms.length - 1]); // Get degree-2 and degree-3 coefficients\n\n    var c2s = [];\n    var c3s = [];\n    var m;\n\n    for (var _i3 = 0; _i3 < c1s.length - 1; _i3++) {\n      m = ms[_i3];\n      var c1 = c1s[_i3];\n      var invDx = 1 / dxs[_i3];\n\n      var _common = c1 + c1s[_i3 + 1] - m - m;\n\n      c2s.push((m - c1 - _common) * invDx);\n      c3s.push(_common * invDx * invDx);\n    }\n\n    this.xs = xs;\n    this.ys = ys;\n    this.c1s = c1s;\n    this.c2s = c2s;\n    this.c3s = c3s;\n  }\n\n  _createClass(MonotonicInterpolant, [{\n    key: \"interpolate\",\n    value: function interpolate(x) {\n      var xs = this.xs,\n          ys = this.ys,\n          c1s = this.c1s,\n          c2s = this.c2s,\n          c3s = this.c3s; // The rightmost point in the dataset should give an exact result\n\n      var i = xs.length - 1;\n\n      if (x === xs[i]) {\n        return ys[i];\n      } // Search for the interval x is in, returning the corresponding y if x is one of the original xs\n\n\n      var low = 0;\n      var high = c3s.length - 1;\n      var mid;\n\n      while (low <= high) {\n        mid = Math.floor(0.5 * (low + high));\n        var xHere = xs[mid];\n\n        if (xHere < x) {\n          low = mid + 1;\n        } else if (xHere > x) {\n          high = mid - 1;\n        } else {\n          return ys[mid];\n        }\n      }\n\n      i = Math.max(0, high); // Interpolate\n\n      var diff = x - xs[i];\n      var diffSq = diff * diff;\n      return ys[i] + c1s[i] * diff + c2s[i] * diffSq + c3s[i] * diff * diffSq;\n    }\n  }]);\n\n  return MonotonicInterpolant;\n}();"], "mappings": "AAAA,SAASA,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;EAAE;AAAE;AAE5T,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACiB,SAAS,EAAEF,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;EAAE,OAAOhB,WAAW;AAAE;AAEtN,SAASkB,eAAeA,CAACC,GAAG,EAAEN,GAAG,EAAEO,KAAK,EAAE;EAAE,IAAIP,GAAG,IAAIM,GAAG,EAAE;IAAER,MAAM,CAACC,cAAc,CAACO,GAAG,EAAEN,GAAG,EAAE;MAAEO,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAES,GAAG,CAACN,GAAG,CAAC,GAAGO,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAEhN,OAAO,IAAIE,oBAAoB,GAAG,aAAa,YAAY;EACzD,SAASA,oBAAoBA,CAACC,EAAE,EAAEC,EAAE,EAAE;IACpCzB,eAAe,CAAC,IAAI,EAAEuB,oBAAoB,CAAC;IAE3CH,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAEnCA,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAEnCA,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAEpCA,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAEpCA,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IAEpC,IAAIZ,MAAM,GAAGgB,EAAE,CAAChB,MAAM,CAAC,CAAC;;IAExB,IAAIkB,OAAO,GAAG,EAAE;IAEhB,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/BmB,OAAO,CAACC,IAAI,CAACpB,CAAC,CAAC;IACjB;IAEAmB,OAAO,CAACE,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MAC3B,OAAON,EAAE,CAACK,CAAC,CAAC,GAAGL,EAAE,CAACM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC/B,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,EAAE,GAAG,EAAE;IACX,IAAIC,EAAE;IACN,IAAIC,EAAE;IAEN,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG5B,MAAM,GAAG,CAAC,EAAE4B,EAAE,EAAE,EAAE;MACtCF,EAAE,GAAGV,EAAE,CAACY,EAAE,GAAG,CAAC,CAAC,GAAGZ,EAAE,CAACY,EAAE,CAAC;MACxBD,EAAE,GAAGV,EAAE,CAACW,EAAE,GAAG,CAAC,CAAC,GAAGX,EAAE,CAACW,EAAE,CAAC;MACxBJ,GAAG,CAACL,IAAI,CAACO,EAAE,CAAC;MACZH,GAAG,CAACJ,IAAI,CAACQ,EAAE,CAAC;MACZF,EAAE,CAACN,IAAI,CAACQ,EAAE,GAAGD,EAAE,CAAC;IAClB,CAAC,CAAC;;IAGF,IAAIG,GAAG,GAAG,CAACJ,EAAE,CAAC,CAAC,CAAC,CAAC;IAEjB,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,GAAG,CAACxB,MAAM,GAAG,CAAC,EAAE8B,GAAG,EAAE,EAAE;MAC7C,IAAIC,EAAE,GAAGN,EAAE,CAACK,GAAG,CAAC;MAChB,IAAIE,KAAK,GAAGP,EAAE,CAACK,GAAG,GAAG,CAAC,CAAC;MAEvB,IAAIC,EAAE,GAAGC,KAAK,IAAI,CAAC,EAAE;QACnBH,GAAG,CAACV,IAAI,CAAC,CAAC,CAAC;MACb,CAAC,MAAM;QACLO,EAAE,GAAGF,GAAG,CAACM,GAAG,CAAC;QACb,IAAIG,MAAM,GAAGT,GAAG,CAACM,GAAG,GAAG,CAAC,CAAC;QACzB,IAAII,MAAM,GAAGR,EAAE,GAAGO,MAAM;QACxBJ,GAAG,CAACV,IAAI,CAAC,CAAC,GAAGe,MAAM,IAAI,CAACA,MAAM,GAAGD,MAAM,IAAIF,EAAE,GAAG,CAACG,MAAM,GAAGR,EAAE,IAAIM,KAAK,CAAC,CAAC;MACzE;IACF;IAEAH,GAAG,CAACV,IAAI,CAACM,EAAE,CAACA,EAAE,CAACzB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,IAAImC,GAAG,GAAG,EAAE;IACZ,IAAIC,GAAG,GAAG,EAAE;IACZ,IAAIC,CAAC;IAEL,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,GAAG,CAAC7B,MAAM,GAAG,CAAC,EAAEsC,GAAG,EAAE,EAAE;MAC7CD,CAAC,GAAGZ,EAAE,CAACa,GAAG,CAAC;MACX,IAAIC,EAAE,GAAGV,GAAG,CAACS,GAAG,CAAC;MACjB,IAAIE,KAAK,GAAG,CAAC,GAAGhB,GAAG,CAACc,GAAG,CAAC;MAExB,IAAIG,OAAO,GAAGF,EAAE,GAAGV,GAAG,CAACS,GAAG,GAAG,CAAC,CAAC,GAAGD,CAAC,GAAGA,CAAC;MAEvCF,GAAG,CAAChB,IAAI,CAAC,CAACkB,CAAC,GAAGE,EAAE,GAAGE,OAAO,IAAID,KAAK,CAAC;MACpCJ,GAAG,CAACjB,IAAI,CAACsB,OAAO,GAAGD,KAAK,GAAGA,KAAK,CAAC;IACnC;IAEA,IAAI,CAACxB,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACY,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;EAChB;EAEA5B,YAAY,CAACO,oBAAoB,EAAE,CAAC;IAClCR,GAAG,EAAE,aAAa;IAClBO,KAAK,EAAE,SAAS4B,WAAWA,CAACC,CAAC,EAAE;MAC7B,IAAI3B,EAAE,GAAG,IAAI,CAACA,EAAE;QACZC,EAAE,GAAG,IAAI,CAACA,EAAE;QACZY,GAAG,GAAG,IAAI,CAACA,GAAG;QACdM,GAAG,GAAG,IAAI,CAACA,GAAG;QACdC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;;MAEpB,IAAIrC,CAAC,GAAGiB,EAAE,CAAChB,MAAM,GAAG,CAAC;MAErB,IAAI2C,CAAC,KAAK3B,EAAE,CAACjB,CAAC,CAAC,EAAE;QACf,OAAOkB,EAAE,CAAClB,CAAC,CAAC;MACd,CAAC,CAAC;;MAGF,IAAI6C,GAAG,GAAG,CAAC;MACX,IAAIC,IAAI,GAAGT,GAAG,CAACpC,MAAM,GAAG,CAAC;MACzB,IAAI8C,GAAG;MAEP,OAAOF,GAAG,IAAIC,IAAI,EAAE;QAClBC,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,IAAIJ,GAAG,GAAGC,IAAI,CAAC,CAAC;QACpC,IAAII,KAAK,GAAGjC,EAAE,CAAC8B,GAAG,CAAC;QAEnB,IAAIG,KAAK,GAAGN,CAAC,EAAE;UACbC,GAAG,GAAGE,GAAG,GAAG,CAAC;QACf,CAAC,MAAM,IAAIG,KAAK,GAAGN,CAAC,EAAE;UACpBE,IAAI,GAAGC,GAAG,GAAG,CAAC;QAChB,CAAC,MAAM;UACL,OAAO7B,EAAE,CAAC6B,GAAG,CAAC;QAChB;MACF;MAEA/C,CAAC,GAAGgD,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEL,IAAI,CAAC,CAAC,CAAC;;MAEvB,IAAIM,IAAI,GAAGR,CAAC,GAAG3B,EAAE,CAACjB,CAAC,CAAC;MACpB,IAAIqD,MAAM,GAAGD,IAAI,GAAGA,IAAI;MACxB,OAAOlC,EAAE,CAAClB,CAAC,CAAC,GAAG8B,GAAG,CAAC9B,CAAC,CAAC,GAAGoD,IAAI,GAAGhB,GAAG,CAACpC,CAAC,CAAC,GAAGqD,MAAM,GAAGhB,GAAG,CAACrC,CAAC,CAAC,GAAGoD,IAAI,GAAGC,MAAM;IACzE;EACF,CAAC,CAAC,CAAC;EAEH,OAAOrC,oBAAoB;AAC7B,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}