{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { GeoJSON as LeafletGeoJSON } from 'leaflet';\nexport const GeoJSON = createPathComponent(function createGeoJSON(_ref, ctx) {\n  let {\n    data,\n    ...options\n  } = _ref;\n  const geoJSON = new LeafletGeoJSON(data, options);\n  return createElementObject(geoJSON, extendContext(ctx, {\n    overlayContainer: geoJSON\n  }));\n}, function updateGeoJSON(layer, props, prevProps) {\n  if (props.style !== prevProps.style) {\n    if (props.style == null) {\n      layer.resetStyle();\n    } else {\n      layer.setStyle(props.style);\n    }\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "GeoJSON", "LeafletGeoJSON", "createGeoJSON", "_ref", "ctx", "data", "options", "geoJSON", "overlayContainer", "updateGeoJSON", "layer", "props", "prevProps", "style", "resetStyle", "setStyle"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/GeoJSON.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { GeoJSON as LeafletGeoJSON } from 'leaflet';\nexport const GeoJSON = createPathComponent(function createGeoJSON({ data , ...options }, ctx) {\n    const geoJSON = new LeafletGeoJSON(data, options);\n    return createElementObject(geoJSON, extendContext(ctx, {\n        overlayContainer: geoJSON\n    }));\n}, function updateGeoJSON(layer, props, prevProps) {\n    if (props.style !== prevProps.style) {\n        if (props.style == null) {\n            layer.resetStyle();\n        } else {\n            layer.setStyle(props.style);\n        }\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,OAAO,IAAIC,cAAc,QAAQ,SAAS;AACnD,OAAO,MAAMD,OAAO,GAAGF,mBAAmB,CAAC,SAASI,aAAaA,CAAAC,IAAA,EAAwBC,GAAG,EAAE;EAAA,IAA5B;IAAEC,IAAI;IAAG,GAAGC;EAAQ,CAAC,GAAAH,IAAA;EACnF,MAAMI,OAAO,GAAG,IAAIN,cAAc,CAACI,IAAI,EAAEC,OAAO,CAAC;EACjD,OAAOT,mBAAmB,CAACU,OAAO,EAAER,aAAa,CAACK,GAAG,EAAE;IACnDI,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,aAAaA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC/C,IAAID,KAAK,CAACE,KAAK,KAAKD,SAAS,CAACC,KAAK,EAAE;IACjC,IAAIF,KAAK,CAACE,KAAK,IAAI,IAAI,EAAE;MACrBH,KAAK,CAACI,UAAU,CAAC,CAAC;IACtB,CAAC,MAAM;MACHJ,KAAK,CAACK,QAAQ,CAACJ,KAAK,CAACE,KAAK,CAAC;IAC/B;EACJ;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}