{"ast": null, "code": "import axios from \"../../axios\";\nimport { MARQUE_LIST_REQUEST, MARQUE_LIST_SUCCESS, MARQUE_LIST_FAIL,\n//\nMARQUE_ADD_REQUEST, MARQUE_ADD_SUCCESS, MARQUE_ADD_FAIL,\n//\nMARQUE_DELETE_REQUEST, MARQUE_DELETE_SUCCESS, MARQUE_DELETE_FAIL\n//\n} from \"../constants/marqueConstants\";\n\n// delete marque\nexport const deleteMarque = id => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_DELETE_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.delete(`/marques/${id}/delete/`, config);\n    dispatch({\n      type: MARQUE_DELETE_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_DELETE_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// add new Marque\nexport const addNewMarque = marqueCar => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_ADD_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.post(`/marques/add/`, {\n      marque_car: marqueCar\n    }, config);\n    dispatch({\n      type: MARQUE_ADD_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_ADD_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};\n\n// get list client\nexport const getMarqueList = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_LIST_REQUEST\n    });\n    var {\n      userLogin: {\n        userInfo\n      }\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`\n      }\n    };\n    const {\n      data\n    } = await axios.get(`/marques/`, config);\n    dispatch({\n      type: MARQUE_LIST_SUCCESS,\n      payload: data\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_LIST_FAIL,\n      payload: error.response && error.response.data.detail ? error.response.data.detail : error.detail\n    });\n  }\n};", "map": {"version": 3, "names": ["axios", "MARQUE_LIST_REQUEST", "MARQUE_LIST_SUCCESS", "MARQUE_LIST_FAIL", "MARQUE_ADD_REQUEST", "MARQUE_ADD_SUCCESS", "MARQUE_ADD_FAIL", "MARQUE_DELETE_REQUEST", "MARQUE_DELETE_SUCCESS", "MARQUE_DELETE_FAIL", "deleteMarque", "id", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "access", "data", "delete", "payload", "error", "response", "detail", "addNewMarque", "marqueCar", "post", "marque_car", "getMarqueList", "get"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/marqueActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  MARQUE_LIST_REQUEST,\n  MARQUE_LIST_SUCCESS,\n  MARQUE_LIST_FAIL,\n  //\n  MARQUE_ADD_REQUEST,\n  MARQUE_ADD_SUCCESS,\n  MARQUE_ADD_FAIL,\n  //\n  MARQUE_DELETE_REQUEST,\n  MARQUE_DELETE_SUCCESS,\n  MARQUE_DELETE_FAIL,\n  //\n} from \"../constants/marqueConstants\";\n\n// delete marque\nexport const deleteMarque = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_DELETE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.delete(`/marques/${id}/delete/`, config);\n    dispatch({\n      type: MARQUE_DELETE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_DELETE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// add new Marque\nexport const addNewMarque = (marqueCar) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(\n      `/marques/add/`,\n      { marque_car: marqueCar },\n      config\n    );\n\n    dispatch({\n      type: MARQUE_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list client\nexport const getMarqueList = () => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: MARQUE_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/marques/`, config);\n\n    dispatch({\n      type: MARQUE_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: MARQUE_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SACEC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB;AAChB;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,eAAe;AACf;AACAC,qBAAqB,EACrBC,qBAAqB,EACrBC;AACA;AAAA,OACK,8BAA8B;;AAErC;AACA,OAAO,MAAMC,YAAY,GAAIC,EAAE,IAAK,OAAOC,QAAQ,EAAEC,QAAQ,KAAK;EAChE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEP;IACR,CAAC,CAAC;IACF,IAAI;MACFQ,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMrB,KAAK,CAACsB,MAAM,CAAE,YAAWX,EAAG,UAAS,EAAEM,MAAM,CAAC;IACrEL,QAAQ,CAAC;MACPE,IAAI,EAAEN,qBAAqB;MAC3Be,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdZ,QAAQ,CAAC;MACPE,IAAI,EAAEL,kBAAkB;MACxBc,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAIC,SAAS,IAAK,OAAOhB,QAAQ,EAAEC,QAAQ,KAAK;EACvE,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEV;IACR,CAAC,CAAC;IACF,IAAI;MACFW,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMrB,KAAK,CAAC6B,IAAI,CAC9B,eAAc,EACf;MAAEC,UAAU,EAAEF;IAAU,CAAC,EACzBX,MACF,CAAC;IAEDL,QAAQ,CAAC;MACPE,IAAI,EAAET,kBAAkB;MACxBkB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdZ,QAAQ,CAAC;MACPE,IAAI,EAAER,eAAe;MACrBiB,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMK,aAAa,GAAGA,CAAA,KAAM,OAAOnB,QAAQ,EAAEC,QAAQ,KAAK;EAC/D,IAAI;IACFD,QAAQ,CAAC;MACPE,IAAI,EAAEb;IACR,CAAC,CAAC;IACF,IAAI;MACFc,SAAS,EAAE;QAAEC;MAAS;IACxB,CAAC,GAAGH,QAAQ,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG;MACbC,OAAO,EAAE;QACP,cAAc,EAAE,kBAAkB;QAClCC,aAAa,EAAG,UAASH,QAAQ,CAACI,MAAO;MAC3C;IACF,CAAC;IACD,MAAM;MAAEC;IAAK,CAAC,GAAG,MAAMrB,KAAK,CAACgC,GAAG,CAAE,WAAU,EAAEf,MAAM,CAAC;IAErDL,QAAQ,CAAC;MACPE,IAAI,EAAEZ,mBAAmB;MACzBqB,OAAO,EAAEF;IACX,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdZ,QAAQ,CAAC;MACPE,IAAI,EAAEX,gBAAgB;MACtBoB,OAAO,EACLC,KAAK,CAACC,QAAQ,IAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,GAC1BF,KAAK,CAACE;IACd,CAAC,CAAC;EACJ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}