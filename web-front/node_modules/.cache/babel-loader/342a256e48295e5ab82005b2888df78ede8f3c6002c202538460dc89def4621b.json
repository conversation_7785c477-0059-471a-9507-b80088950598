{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>uan/UNIMEDCARE/web-front/src/screens/insurances/EditInsuranceScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailInsurance } from \"../../redux/actions/insuranceActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction EditInsuranceScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const insuranceDetail = useSelector(state => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo\n  } = insuranceDetail;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"EditInsuranceScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 10\n  }, this);\n}\n_s(EditInsuranceScreen, \"XmN/XWKihr3f3hZxk1BqNfbBxfg=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector];\n});\n_c = EditInsuranceScreen;\nexport default EditInsuranceScreen;\nvar _c;\n$RefreshReg$(_c, \"EditInsuranceScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "detailInsurance", "jsxDEV", "_jsxDEV", "EditInsuranceScreen", "_s", "navigate", "location", "dispatch", "id", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "insuranceName", "setInsuranceName", "insuranceNameError", "setInsuranceNameError", "insuranceCountry", "setInsuranceCountry", "insuranceCountryError", "setInsuranceCountryError", "insuranceEmail", "setInsuranceEmail", "insuranceEmailError", "setInsuranceEmailError", "insurancePhone", "setInsurancePhone", "insurancePhoneError", "setInsurancePhoneError", "insuranceLogo", "setInsuranceLogo", "insuranceLogoValue", "setInsuranceLogoValue", "insuranceLogoError", "setInsuranceLogoError", "userLogin", "state", "userInfo", "insuranceDetail", "loadingInsuranceInfo", "errorInsuranceInfo", "successInsuranceInfo", "insuranceInfo", "redirect", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/EditInsuranceScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailInsurance } from \"../../redux/actions/insuranceActions\";\n\nfunction EditInsuranceScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [insuranceName, setInsuranceName] = useState(\"\");\n  const [insuranceNameError, setInsuranceNameError] = useState(\"\");\n\n  const [insuranceCountry, setInsuranceCountry] = useState(\"\");\n  const [insuranceCountryError, setInsuranceCountryError] = useState(\"\");\n\n  const [insuranceEmail, setInsuranceEmail] = useState(\"\");\n  const [insuranceEmailError, setInsuranceEmailError] = useState(\"\");\n\n  const [insurancePhone, setInsurancePhone] = useState(\"\");\n  const [insurancePhoneError, setInsurancePhoneError] = useState(\"\");\n\n  const [insuranceLogo, setInsuranceLogo] = useState(\"\");\n  const [insuranceLogoValue, setInsuranceLogoValue] = useState(\"\");\n  const [insuranceLogoError, setInsuranceLogoError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const insuranceDetail = useSelector((state) => state.detailInsurance);\n  const {\n    loadingInsuranceInfo,\n    errorInsuranceInfo,\n    successInsuranceInfo,\n    insuranceInfo,\n  } = insuranceDetail;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailInsurance(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  return <div>EditInsuranceScreen</div>;\n}\n\nexport default EditInsuranceScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,eAAe,QAAQ,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEa;EAAG,CAAC,GAAGT,SAAS,CAAC,CAAC;EAExB,MAAM,CAACU,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAEtE,MAAM,CAAC2B,cAAc,EAAEC,iBAAiB,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAACuC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAMyC,SAAS,GAAGvC,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,eAAe,GAAG1C,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACpC,eAAe,CAAC;EACrE,MAAM;IACJuC,oBAAoB;IACpBC,kBAAkB;IAClBC,oBAAoB;IACpBC;EACF,CAAC,GAAGJ,eAAe;EAEnB,MAAMK,QAAQ,GAAG,GAAG;EACpBlD,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,QAAQ,EAAE;MACbhC,QAAQ,CAACsC,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLpC,QAAQ,CAACP,eAAe,CAACQ,EAAE,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEgC,QAAQ,EAAE9B,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtC,oBAAON,OAAA;IAAA0C,QAAA,EAAK;EAAmB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACvC;AAAC5C,EAAA,CA9CQD,mBAAmB;EAAA,QACTL,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EAqBJH,WAAW,EAGLA,WAAW;AAAA;AAAAqD,EAAA,GA5B5B9C,mBAAmB;AAgD5B,eAAeA,mBAAmB;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}