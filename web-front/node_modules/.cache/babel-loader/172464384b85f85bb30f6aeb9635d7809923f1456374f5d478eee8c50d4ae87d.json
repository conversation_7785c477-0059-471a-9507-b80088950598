{"ast": null, "code": "import { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useRef, useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n  getSnapshotBeforeUpdate(prevProps) {\n    const element = this.props.childRef.current;\n    if (element && prevProps.isPresent && !this.props.isPresent) {\n      const size = this.props.sizeRef.current;\n      size.height = element.offsetHeight || 0;\n      size.width = element.offsetWidth || 0;\n      size.top = element.offsetTop;\n      size.left = element.offsetLeft;\n    }\n    return null;\n  }\n  /**\n   * Required with getSnapshotBeforeUpdate to stop React complaining.\n   */\n  componentDidUpdate() {}\n  render() {\n    return this.props.children;\n  }\n}\nfunction PopChild({\n  children,\n  isPresent\n}) {\n  const id = useId();\n  const ref = useRef(null);\n  const size = useRef({\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  });\n  const {\n    nonce\n  } = useContext(MotionConfigContext);\n  /**\n   * We create and inject a style block so we can apply this explicit\n   * sizing in a non-destructive manner by just deleting the style block.\n   *\n   * We can't apply size via render as the measurement happens\n   * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n   * styles directly on the DOM node, we might be overwriting\n   * styles set via the style prop.\n   */\n  useInsertionEffect(() => {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = size.current;\n    if (isPresent || !ref.current || !width || !height) return;\n    ref.current.dataset.motionPopId = id;\n    const style = document.createElement(\"style\");\n    if (nonce) style.nonce = nonce;\n    document.head.appendChild(style);\n    if (style.sheet) {\n      style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n    }\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, [isPresent]);\n  return jsx(PopChildMeasure, {\n    isPresent: isPresent,\n    childRef: ref,\n    sizeRef: size,\n    children: React.cloneElement(children, {\n      ref\n    })\n  });\n}\nexport { PopChild };", "map": {"version": 3, "names": ["jsx", "React", "useId", "useRef", "useContext", "useInsertionEffect", "MotionConfigContext", "PopChildMeasure", "Component", "getSnapshotBeforeUpdate", "prevProps", "element", "props", "childRef", "current", "isPresent", "size", "sizeRef", "height", "offsetHeight", "width", "offsetWidth", "top", "offsetTop", "left", "offsetLeft", "componentDidUpdate", "render", "children", "PopChild", "id", "ref", "nonce", "dataset", "motionPopId", "style", "document", "createElement", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs"], "sourcesContent": ["import { jsx } from 'react/jsx-runtime';\nimport * as React from 'react';\nimport { useId, useRef, useContext, useInsertionEffect } from 'react';\nimport { MotionConfigContext } from '../../context/MotionConfigContext.mjs';\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends React.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = useId();\n    const ref = useRef(null);\n    const size = useRef({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    const { nonce } = useContext(MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    useInsertionEffect(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        if (nonce)\n            style.nonce = nonce;\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (jsx(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size, children: React.cloneElement(children, { ref }) }));\n}\n\nexport { PopChild };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,kBAAkB,QAAQ,OAAO;AACrE,SAASC,mBAAmB,QAAQ,uCAAuC;;AAE3E;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASN,KAAK,CAACO,SAAS,CAAC;EAC1CC,uBAAuBA,CAACC,SAAS,EAAE;IAC/B,MAAMC,OAAO,GAAG,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACC,OAAO;IAC3C,IAAIH,OAAO,IAAID,SAAS,CAACK,SAAS,IAAI,CAAC,IAAI,CAACH,KAAK,CAACG,SAAS,EAAE;MACzD,MAAMC,IAAI,GAAG,IAAI,CAACJ,KAAK,CAACK,OAAO,CAACH,OAAO;MACvCE,IAAI,CAACE,MAAM,GAAGP,OAAO,CAACQ,YAAY,IAAI,CAAC;MACvCH,IAAI,CAACI,KAAK,GAAGT,OAAO,CAACU,WAAW,IAAI,CAAC;MACrCL,IAAI,CAACM,GAAG,GAAGX,OAAO,CAACY,SAAS;MAC5BP,IAAI,CAACQ,IAAI,GAAGb,OAAO,CAACc,UAAU;IAClC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIC,kBAAkBA,CAAA,EAAG,CAAE;EACvBC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACf,KAAK,CAACgB,QAAQ;EAC9B;AACJ;AACA,SAASC,QAAQA,CAAC;EAAED,QAAQ;EAAEb;AAAU,CAAC,EAAE;EACvC,MAAMe,EAAE,GAAG5B,KAAK,CAAC,CAAC;EAClB,MAAM6B,GAAG,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMa,IAAI,GAAGb,MAAM,CAAC;IAChBiB,KAAK,EAAE,CAAC;IACRF,MAAM,EAAE,CAAC;IACTI,GAAG,EAAE,CAAC;IACNE,IAAI,EAAE;EACV,CAAC,CAAC;EACF,MAAM;IAAEQ;EAAM,CAAC,GAAG5B,UAAU,CAACE,mBAAmB,CAAC;EACjD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,kBAAkB,CAAC,MAAM;IACrB,MAAM;MAAEe,KAAK;MAAEF,MAAM;MAAEI,GAAG;MAAEE;IAAK,CAAC,GAAGR,IAAI,CAACF,OAAO;IACjD,IAAIC,SAAS,IAAI,CAACgB,GAAG,CAACjB,OAAO,IAAI,CAACM,KAAK,IAAI,CAACF,MAAM,EAC9C;IACJa,GAAG,CAACjB,OAAO,CAACmB,OAAO,CAACC,WAAW,GAAGJ,EAAE;IACpC,MAAMK,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7C,IAAIL,KAAK,EACLG,KAAK,CAACH,KAAK,GAAGA,KAAK;IACvBI,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC;IAChC,IAAIA,KAAK,CAACK,KAAK,EAAE;MACbL,KAAK,CAACK,KAAK,CAACC,UAAU,CAAE;AACpC,iCAAiCX,EAAG;AACpC;AACA,qBAAqBV,KAAM;AAC3B,sBAAsBF,MAAO;AAC7B,mBAAmBI,GAAI;AACvB,oBAAoBE,IAAK;AACzB;AACA,SAAS,CAAC;IACF;IACA,OAAO,MAAM;MACTY,QAAQ,CAACE,IAAI,CAACI,WAAW,CAACP,KAAK,CAAC;IACpC,CAAC;EACL,CAAC,EAAE,CAACpB,SAAS,CAAC,CAAC;EACf,OAAQf,GAAG,CAACO,eAAe,EAAE;IAAEQ,SAAS,EAAEA,SAAS;IAAEF,QAAQ,EAAEkB,GAAG;IAAEd,OAAO,EAAED,IAAI;IAAEY,QAAQ,EAAE3B,KAAK,CAAC0C,YAAY,CAACf,QAAQ,EAAE;MAAEG;IAAI,CAAC;EAAE,CAAC,CAAC;AACzI;AAEA,SAASF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}