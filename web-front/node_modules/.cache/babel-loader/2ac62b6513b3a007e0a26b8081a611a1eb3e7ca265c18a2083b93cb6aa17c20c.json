{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js\";\nimport React from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ResetPasswordScreen() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10\",\n    children: /*#__PURE__*/_jsxDEV(\"img\", {\n      src: logoProjet,\n      className: \"size-20\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n}\n_c = ResetPasswordScreen;\nexport default ResetPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"ResetPasswordScreen\");", "map": {"version": 3, "names": ["React", "logoProjet", "jsxDEV", "_jsxDEV", "ResetPasswordScreen", "className", "children", "src", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/ResetPasswordScreen.js"], "sourcesContent": ["import React from \"react\";\nimport logoProjet from \"../../images/logo-project.png\";\n\nfunction ResetPasswordScreen() {\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10\">\n      <img src={logoProjet} className=\"size-20\" />\n    </div>\n  );\n}\n\nexport default ResetPasswordScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,mBAAmBA,CAAA,EAAG;EAC7B,oBACED,OAAA;IAAKE,SAAS,EAAC,gDAAgD;IAAAC,QAAA,eAC7DH,OAAA;MAAKI,GAAG,EAAEN,UAAW;MAACI,SAAS,EAAC;IAAS;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzC,CAAC;AAEV;AAACC,EAAA,GANQR,mBAAmB;AAQ5B,eAAeA,mBAAmB;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}