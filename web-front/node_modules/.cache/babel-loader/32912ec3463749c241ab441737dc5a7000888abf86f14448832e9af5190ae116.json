{"ast": null, "code": "import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOveraly(_ref, ctx) {\n  let {\n    bounds,\n    url,\n    ...options\n  } = _ref;\n  const overlay = new LeafletImageOverlay(url, bounds, options);\n  return createElementObject(overlay, extendContext(ctx, {\n    overlayContainer: overlay\n  }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n  updateMediaOverlay(overlay, props, prevProps);\n  if (props.bounds !== prevProps.bounds) {\n    const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n    overlay.setBounds(bounds);\n  }\n  if (props.url !== prevProps.url) {\n    overlay.setUrl(props.url);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createLayerComponent", "extendContext", "updateMediaOverlay", "LatLngBounds", "ImageOverlay", "LeafletImageOverlay", "createImageOveraly", "_ref", "ctx", "bounds", "url", "options", "overlay", "overlayContainer", "updateImageOverlay", "props", "prevProps", "setBounds", "setUrl"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/ImageOverlay.js"], "sourcesContent": ["import { createElementObject, createLayerComponent, extendContext, updateMediaOverlay } from '@react-leaflet/core';\nimport { LatLngBounds, ImageOverlay as LeafletImageOverlay } from 'leaflet';\nexport const ImageOverlay = createLayerComponent(function createImageOveraly({ bounds , url , ...options }, ctx) {\n    const overlay = new LeafletImageOverlay(url, bounds, options);\n    return createElementObject(overlay, extendContext(ctx, {\n        overlayContainer: overlay\n    }));\n}, function updateImageOverlay(overlay, props, prevProps) {\n    updateMediaOverlay(overlay, props, prevProps);\n    if (props.bounds !== prevProps.bounds) {\n        const bounds = props.bounds instanceof LatLngBounds ? props.bounds : new LatLngBounds(props.bounds);\n        overlay.setBounds(bounds);\n    }\n    if (props.url !== prevProps.url) {\n        overlay.setUrl(props.url);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,qBAAqB;AAClH,SAASC,YAAY,EAAEC,YAAY,IAAIC,mBAAmB,QAAQ,SAAS;AAC3E,OAAO,MAAMD,YAAY,GAAGJ,oBAAoB,CAAC,SAASM,kBAAkBA,CAAAC,IAAA,EAAgCC,GAAG,EAAE;EAAA,IAApC;IAAEC,MAAM;IAAGC,GAAG;IAAG,GAAGC;EAAQ,CAAC,GAAAJ,IAAA;EACtG,MAAMK,OAAO,GAAG,IAAIP,mBAAmB,CAACK,GAAG,EAAED,MAAM,EAAEE,OAAO,CAAC;EAC7D,OAAOZ,mBAAmB,CAACa,OAAO,EAAEX,aAAa,CAACO,GAAG,EAAE;IACnDK,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,kBAAkBA,CAACF,OAAO,EAAEG,KAAK,EAAEC,SAAS,EAAE;EACtDd,kBAAkB,CAACU,OAAO,EAAEG,KAAK,EAAEC,SAAS,CAAC;EAC7C,IAAID,KAAK,CAACN,MAAM,KAAKO,SAAS,CAACP,MAAM,EAAE;IACnC,MAAMA,MAAM,GAAGM,KAAK,CAACN,MAAM,YAAYN,YAAY,GAAGY,KAAK,CAACN,MAAM,GAAG,IAAIN,YAAY,CAACY,KAAK,CAACN,MAAM,CAAC;IACnGG,OAAO,CAACK,SAAS,CAACR,MAAM,CAAC;EAC7B;EACA,IAAIM,KAAK,CAACL,GAAG,KAAKM,SAAS,CAACN,GAAG,EAAE;IAC7BE,OAAO,CAACM,MAAM,CAACH,KAAK,CAACL,GAAG,CAAC;EAC7B;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}