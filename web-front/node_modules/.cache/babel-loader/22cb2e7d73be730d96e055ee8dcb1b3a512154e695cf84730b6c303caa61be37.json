{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction InsurancesScreen() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listInsurances = useSelector(state => state.insuranceList);\n  const {\n    insurances,\n    loadingInsurances,\n    errorInsurances,\n    pages\n  } = listInsurances;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: \"InsurancesScreen\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 10\n  }, this);\n}\n_s(InsurancesScreen, \"abmluD1s3AOI76XMdwWxvYXenfI=\", false, function () {\n  return [useLocation, useNavigate, useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = InsurancesScreen;\nexport default InsurancesScreen;\nvar _c;\n$RefreshReg$(_c, \"InsurancesScreen\");", "map": {"version": 3, "names": ["React", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "jsxDEV", "_jsxDEV", "InsurancesScreen", "_s", "location", "navigate", "searchParams", "page", "get", "dispatch", "userLogin", "state", "userInfo", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "pages", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/insurances/InsurancesScreen.js"], "sourcesContent": ["import React from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\n\nfunction InsurancesScreen() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances, pages } =\n    listInsurances;\n\n  return <div>InsurancesScreen</div>;\n}\n\nexport default InsurancesScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACQ,YAAY,CAAC,GAAGP,eAAe,CAAC,CAAC;EACxC,MAAMQ,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,SAAS,GAAGd,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,cAAc,GAAGjB,WAAW,CAAEe,KAAK,IAAKA,KAAK,CAACG,aAAa,CAAC;EAClE,MAAM;IAAEC,UAAU;IAAEC,iBAAiB;IAAEC,eAAe;IAAEC;EAAM,CAAC,GAC7DL,cAAc;EAEhB,oBAAOZ,OAAA;IAAAkB,QAAA,EAAK;EAAgB;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AACpC;AAACpB,EAAA,CAfQD,gBAAgB;EAAA,QACNL,WAAW,EACXC,WAAW,EACLC,eAAe,EAErBJ,WAAW,EAEVC,WAAW,EAGNA,WAAW;AAAA;AAAA4B,EAAA,GAV3BtB,gBAAgB;AAiBzB,eAAeA,gBAAgB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}