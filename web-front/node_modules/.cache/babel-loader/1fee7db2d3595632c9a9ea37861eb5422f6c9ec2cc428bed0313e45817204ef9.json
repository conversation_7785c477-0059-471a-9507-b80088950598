{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewProvider } from \"../../redux/actions/providerActions\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport { COUNTRIES, SERVICESPECIALIST, SERVICETYPE, validateEmail, validateLocationX, validateLocationY, validatePhone } from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction AddProviderScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n  const [emailSecond, setEmailSecond] = useState(\"\");\n  const [emailSecondError, setEmailSecondError] = useState(\"\");\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n  const [phoneSecond, setPhoneSecond] = useState(\"\");\n  const [phoneSecondError, setPhoneSecondError] = useState(\"\");\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n  const [locationX, setLocationX] = useState(0);\n  const [locationXError, setLocationXError] = useState(\"\");\n  const [locationY, setLocationY] = useState(0);\n  const [locationYError, setLocationYError] = useState(\"\");\n  const [services, setServices] = useState([]);\n  const [infoProvider, setInfoProvider] = useState([]);\n  const [infoType, setInfoType] = useState(\"\");\n  const [infoTypeError, setInfoTypeError] = useState(\"\");\n  const [infoValue, setInfoValue] = useState(\"\");\n  const [infoValueError, setInfoValueError] = useState(\"\");\n  const [paymentMethod, setPaymentMethod] = useState(\"\");\n  const [paymentMethodError, setPaymentMethodError] = useState(\"\");\n  const [providerNote, setProviderNote] = useState(\"\");\n  const [providerNoteError, setProviderNoteError] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const providerAdd = useSelector(state => state.addNewProvider);\n  const {\n    loadingProviderAdd,\n    errorProviderAdd,\n    successProviderAdd\n  } = providerAdd;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n  useEffect(() => {\n    if (successProviderAdd) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setEmailSecond(\"\");\n      setPhoneSecond(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setLocationX(0);\n      setLocationY(0);\n      setServiceType(\"\");\n      setServices([]);\n      setServiceSpecialist(\"\");\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setEmailSecondError(\"\");\n      setPhoneSecondError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n    }\n  }, [successProviderAdd]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/providers-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                className: \"w-4 h-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"\",\n              children: \"Providers List\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Create New Provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-5 px-4 flex justify-between\",\n        children: /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \" uppercase font-semibold text-black dark:text-white\",\n          children: \"New Provider\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white py-4 px-2 rounded-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"First Name \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"First Name\",\n                  value: firstName,\n                  onChange: v => setFirstName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: firstNameError ? firstNameError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: \"Last Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  className: \" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",\n                  type: \"text\",\n                  placeholder: \"Last Name\",\n                  value: lastName,\n                  onChange: v => setLastName(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Payment Method\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: paymentMethod,\n                  onChange: v => {\n                    setPaymentMethod(v.target.value);\n                  },\n                  className: ` outline-none border ${paymentMethodError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Payment Method \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Credit Card (Over the phone)\",\n                    children: \"Credit Card (Over the phone)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Credit Card (via Fax)\",\n                    children: \"Credit Card (via Fax)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Credit Card (via Email)\",\n                    children: \"Credit Card (via Email)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Bank Transfer\",\n                    children: \"Bank Transfer\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Online Payment\",\n                    children: \"Online Payment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"PayPal\",\n                    children: \"PayPal\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Pix\",\n                    children: \"Pix\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Western Union\",\n                    children: \"Western Union\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"No Payment Accepted\",\n                    children: \"No Payment Accepted\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: paymentMethodError ? paymentMethodError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Contact Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 365,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: infoType,\n                  onChange: v => {\n                    setInfoType(v.target.value);\n                  },\n                  className: ` outline-none border ${infoTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Type \"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Main Phone\",\n                    children: \"Main Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Whatsapp\",\n                    children: \"Whatsapp\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Billing Phone\",\n                    children: \"Billing Phone\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Main email\",\n                    children: \"Main email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 382,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Administration email\",\n                    children: \"Administration email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Billing email\",\n                    children: \"Billing email\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 386,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: infoTypeError ? infoTypeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Contact Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: infoValue,\n                  onChange: v => {\n                    setInfoValue(v.target.value);\n                  },\n                  className: ` outline-none border ${infoValueError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: infoTypeError ? infoTypeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var check = true;\n                setInfoTypeError(\"\");\n                setInfoValueError(\"\");\n                if (infoType === \"\") {\n                  setInfoTypeError(\"These fields are required.\");\n                  check = false;\n                }\n                if (infoValue === \"\") {\n                  setInfoValueError(\"These fields are required.\");\n                  check = false;\n                }\n                if (check) {\n                  // Add the new item if it doesn't exist\n                  setInfoProvider([...infoProvider, {\n                    info_type: infoType !== null && infoType !== void 0 ? infoType : \"\",\n                    info_value: infoValue !== null && infoValue !== void 0 ? infoValue : \"\"\n                  }]);\n                  setInfoType(\"\");\n                  setInfoValue(\"\");\n                } else {\n                  toast.error(\"Some fields are empty or invalid. please try again\");\n                }\n              },\n              className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \" Add Contact Info \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Contacts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-black text-sm\",\n                children: infoProvider === null || infoProvider === void 0 ? void 0 : infoProvider.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-row items-center my-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"min-w-6 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        const updatedInfoProvider = infoProvider.filter((_, indexF) => indexF !== index);\n                        setInfoProvider(updatedInfoProvider);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-6\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 493,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1 border-l px-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Type:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 503,\n                        columnNumber: 27\n                      }, this), \" \", item.info_type]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 502,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Value:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 506,\n                        columnNumber: 27\n                      }, this), \" \", item.info_value]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Service Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: serviceType,\n                  onChange: option => {\n                    setServiceType(option);\n                    setServiceSpecialist(\"\");\n                  },\n                  className: \"text-sm\",\n                  options: SERVICETYPE.map(item => ({\n                    value: item,\n                    label: item\n                  })),\n                  placeholder: \"Select a Service Type...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: serviceTypeError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: serviceTypeError ? serviceTypeError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), serviceType !== \"\" && serviceType.value === \"Specialists\" ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Service Specialist\", \" \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 570,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: serviceSpecialist,\n                  onChange: option => {\n                    setServiceSpecialist(option);\n                  },\n                  className: \"text-sm\",\n                  options: SERVICESPECIALIST.map(item => ({\n                    value: item,\n                    label: item\n                  })),\n                  placeholder: \"Select a Specialist...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: serviceSpecialistError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 573,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: serviceSpecialistError ? serviceSpecialistError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this) : null]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                var check = true;\n                setServiceTypeError(\"\");\n                setServiceSpecialistError(\"\");\n                if (serviceType === \"\" || serviceType.value === \"\") {\n                  setServiceTypeError(\"These fields are required.\");\n                  toast.error(\" Service is required\");\n                  check = false;\n                } else if (serviceType.value === \"Specialists\" && (serviceSpecialist === \"\" || serviceSpecialist.value === \"\")) {\n                  setServiceSpecialistError(\"These fields are required.\");\n                  toast.error(\" Specialist is required\");\n                  check = false;\n                }\n                if (check) {\n                  var serviceSpecialistValue = \"\";\n                  if (serviceType.value === \"Specialists\" && serviceSpecialist !== \"\" && serviceSpecialist.value !== \"\") {\n                    var _serviceSpecialist$va;\n                    serviceSpecialistValue = (_serviceSpecialist$va = serviceSpecialist.value) !== null && _serviceSpecialist$va !== void 0 ? _serviceSpecialist$va : \"\";\n                  }\n                  const exists = services.some(service => service.service_type === serviceType.value && service.service_specialist === serviceSpecialistValue);\n                  if (!exists) {\n                    var _serviceType$value;\n                    // Add the new item if it doesn't exist\n                    setServices([...services, {\n                      service_type: (_serviceType$value = serviceType.value) !== null && _serviceType$value !== void 0 ? _serviceType$value : \"\",\n                      service_specialist: serviceSpecialistValue\n                    }]);\n                    setServiceType(\"\");\n                    setServiceSpecialist(\"\");\n                  } else {\n                    setServiceTypeError(\"This service is already added!\");\n                    toast.error(\"This service is already added!\");\n                  }\n                }\n              },\n              className: \"text-primary  flex flex-row items-center my-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                \"stroke-width\": \"1.5\",\n                stroke: \"currentColor\",\n                class: \"size-4\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  \"stroke-linecap\": \"round\",\n                  \"stroke-linejoin\": \"round\",\n                  d: \"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \" Add Service \"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 682,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-black text-sm\",\n                children: services === null || services === void 0 ? void 0 : services.map((itemService, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-row items-center my-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"min-w-6 text-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        const updatedServices = services.filter((_, indexF) => indexF !== index);\n                        setServices(updatedServices);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        \"stroke-width\": \"1.5\",\n                        stroke: \"currentColor\",\n                        class: \"size-6\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          \"stroke-linecap\": \"round\",\n                          \"stroke-linejoin\": \"round\",\n                          d: \"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 711,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 703,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 mx-1 border-l px-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Service:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 721,\n                        columnNumber: 27\n                      }, this), \" \", itemService.service_type]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 720,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Speciality:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 724,\n                        columnNumber: 27\n                      }, this), \" \", itemService.service_specialist]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 723,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 719,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 684,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Address \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 27\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${addressError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"text\",\n                  placeholder: \"Address\",\n                  value: address,\n                  onChange: v => setAddress(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: addressError ? addressError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 749,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Country\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Select, {\n                  value: country,\n                  onChange: option => {\n                    setCountry(option);\n                  },\n                  className: \"text-sm\",\n                  options: COUNTRIES.map(country => ({\n                    value: country.title,\n                    label: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${country.title === \"\" ? \"py-2\" : \"\"} flex flex-row items-center`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-2\",\n                        children: country.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 776,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: country.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 777,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 771,\n                      columnNumber: 25\n                    }, this)\n                  })),\n                  placeholder: \"Select a country...\",\n                  isSearchable: true,\n                  styles: {\n                    control: (base, state) => ({\n                      ...base,\n                      background: \"#fff\",\n                      border: countryError ? \"1px solid #d34053\" : \"1px solid #F1F3FF\",\n                      boxShadow: state.isFocused ? \"none\" : \"none\",\n                      \"&:hover\": {\n                        border: \"1px solid #F1F3FF\"\n                      }\n                    }),\n                    option: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    }),\n                    singleValue: base => ({\n                      ...base,\n                      display: \"flex\",\n                      alignItems: \"center\"\n                    })\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 762,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: countryError ? countryError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 807,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: [\"City \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 24\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 814,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(GoogleComponent, {\n                  apiKey: \"AIzaSyCozE2Q3aj449xsY28qeQ4-C5_IBOg21Ng\",\n                  className: ` outline-none border ${cityError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  onChange: v => {\n                    setCity(v.target.value);\n                  },\n                  onPlaceSelected: place => {\n                    if (place && place.geometry) {\n                      var _place$formatted_addr, _place$formatted_addr2;\n                      setCity((_place$formatted_addr = place.formatted_address) !== null && _place$formatted_addr !== void 0 ? _place$formatted_addr : \"\");\n                      setCityVl((_place$formatted_addr2 = place.formatted_address) !== null && _place$formatted_addr2 !== void 0 ? _place$formatted_addr2 : \"\");\n                      //   const latitude = place.geometry.location.lat();\n                      //   const longitude = place.geometry.location.lng();\n                      //   setLocationX(latitude ?? \"\");\n                      //   setLocationY(longitude ?? \"\");\n                    }\n                  },\n                  defaultValue: city,\n                  types: [\"city\"],\n                  language: \"en\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 818,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: cityError ? cityError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 813,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: [\"Location X \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 851,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 850,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"number\",\n                  step: 0.01,\n                  placeholder: \"Location X\",\n                  value: locationX,\n                  onChange: v => setLocationX(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: locationXError ? locationXError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 849,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  md:pl-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs mb-1\",\n                children: [\"Location Y \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-danger\",\n                  children: \"*\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 30\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  className: ` outline-none border ${locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`,\n                  type: \"number\",\n                  step: 0.01,\n                  placeholder: \"Location Y\",\n                  value: locationY,\n                  onChange: v => setLocationY(v.target.value)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: locationYError ? locationYError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 848,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex md:flex-row flex-col  \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \" w-full  md:pr-1 my-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#000000bf] font-bold text-xs  mb-1\",\n                children: \"Provider Note\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 894,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                  rows: 5,\n                  value: providerNote,\n                  onChange: v => {\n                    setProviderNote(v.target.value);\n                  },\n                  className: ` outline-none border ${providerNoteError ? \"border-danger\" : \"border-[#F1F3FF]\"} px-3 py-2 w-full rounded text-sm`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 898,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \" text-[8px] text-danger\",\n                  children: providerNoteError ? providerNoteError : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 897,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 893,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 892,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 \",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center justify-end my-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                href: \"/providers-list\",\n                className: \"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",\n                children: \"Back\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: async () => {\n                  var check = true;\n                  setFirstNameError(\"\");\n                  setAddressError(\"\");\n                  setServiceTypeError(\"\");\n                  setServiceSpecialistError(\"\");\n                  setLocationXError(\"\");\n                  setLocationYError(\"\");\n                  setPhoneError(\"\");\n                  setEmailError(\"\");\n                  setCityError(\"\");\n                  setPaymentMethodError(\"\");\n                  setProviderNoteError(\"\");\n                  if (firstName === \"\") {\n                    setFirstNameError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (email !== \"\" && !validateEmail(email)) {\n                    setEmailError(\"Invalid email address. Please correct it.\");\n                    check = false;\n                  }\n                  if (phone !== \"\" && !validatePhone(phone)) {\n                    setPhoneError(\"Invalid phone number. Please correct it.\");\n                    check = false;\n                  }\n                  if (services.length === 0) {\n                    setServiceTypeError(\"Please select this and click Add Service.\");\n                    check = false;\n                  }\n                  if (address === \"\") {\n                    setAddressError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (city === \"\") {\n                    setCityError(\"These fields are required.\");\n                    check = false;\n                  }\n                  if (locationX === \"\") {\n                    setLocationXError(\"These fields are required.\");\n                    check = false;\n                  } else if (!validateLocationX(locationX)) {\n                    setLocationXError(\"Please enter a valid longitude (-180 to 180).\");\n                    check = false;\n                  }\n                  if (locationY === \"\") {\n                    setLocationYError(\"These fields are required.\");\n                    check = false;\n                  } else if (!validateLocationY(locationY)) {\n                    setLocationYError(\"Please enter a valid latitude (-180 to 180).\");\n                    check = false;\n                  }\n                  if (check) {\n                    var _country$value;\n                    setLoadEvent(true);\n                    await dispatch(createNewProvider({\n                      first_name: firstName,\n                      last_name: lastName !== null && lastName !== void 0 ? lastName : \"\",\n                      full_name: firstName + \" \" + lastName,\n                      // service_type: serviceType.value ?? \"\",\n                      // service_specialist: serviceSpecialist.value ?? \"\",\n                      email: email !== null && email !== void 0 ? email : \"\",\n                      second_email: emailSecond !== null && emailSecond !== void 0 ? emailSecond : \"\",\n                      phone: phone !== null && phone !== void 0 ? phone : \"\",\n                      second_phone: phoneSecond !== null && phoneSecond !== void 0 ? phoneSecond : \"\",\n                      address: address,\n                      country: (_country$value = country.value) !== null && _country$value !== void 0 ? _country$value : \"\",\n                      city: city !== null && city !== void 0 ? city : \"\",\n                      location_x: locationX,\n                      location_y: locationY,\n                      services: services,\n                      contacts_info: infoProvider,\n                      provider_note: providerNote,\n                      payment_method: paymentMethod\n                    })).then(() => {});\n                    setLoadEvent(false);\n                  } else {\n                    toast.error(\"Some fields are empty or invalid. please try again\");\n                  }\n                },\n                className: \"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",\n                children: loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 915,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n}\n_s(AddProviderScreen, \"IOYjt4cnWRwBjITH6Gn0aHZGwHQ=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useSelector, useSelector];\n});\n_c = AddProviderScreen;\nexport default AddProviderScreen;\nvar _c;\n$RefreshReg$(_c, \"AddProviderScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "toast", "createNewProvider", "axios", "Select", "COUNTRIES", "SERVICESPECIALIST", "SERVICETYPE", "validateEmail", "validateLocationX", "validateLocationY", "validatePhone", "GoogleComponent", "jsxDEV", "_jsxDEV", "AddProviderScreen", "_s", "navigate", "location", "dispatch", "isOpen", "setIsOpen", "loadEvent", "setLoadEvent", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "emailSecond", "setEmailSecond", "emailSecondError", "setEmailSecondError", "serviceType", "setServiceType", "serviceTypeError", "setServiceTypeError", "serviceSpecialist", "setServiceSpecialist", "serviceSpecialistError", "setServiceSpecialistError", "phone", "setPhone", "phoneError", "setPhoneError", "phoneSecond", "setPhoneSecond", "phoneSecondError", "setPhoneSecondError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "country", "setCountry", "countryError", "setCountryError", "cityVl", "setCityVl", "city", "setCity", "cityError", "setCityError", "locationX", "setLocationX", "locationXError", "setLocationXError", "locationY", "setLocationY", "locationYError", "setLocationYError", "services", "setServices", "infoProvider", "setInfoProvider", "infoType", "setInfoType", "infoTypeError", "setInfoTypeError", "infoValue", "setInfoValue", "infoValueError", "setInfoValueError", "paymentMethod", "setPaymentMethod", "paymentMethodError", "setPaymentMethodError", "providerNote", "setProviderNote", "providerNoteError", "setProviderNoteError", "userLogin", "state", "userInfo", "loading", "error", "providerAdd", "addNewProvider", "loadingProviderAdd", "errorProviderAdd", "successProviderAdd", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "v", "target", "onClick", "check", "info_type", "info_value", "class", "map", "item", "index", "updatedInfoProvider", "filter", "_", "indexF", "option", "options", "label", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "display", "alignItems", "singleValue", "serviceSpecialistValue", "_serviceSpecialist$va", "exists", "some", "service", "service_type", "service_specialist", "_serviceType$value", "itemService", "updatedServices", "title", "icon", "<PERSON><PERSON><PERSON><PERSON>", "onPlaceSelected", "place", "geometry", "_place$formatted_addr", "_place$formatted_addr2", "formatted_address", "defaultValue", "types", "language", "step", "rows", "length", "_country$value", "first_name", "last_name", "full_name", "second_email", "second_phone", "location_x", "location_y", "contacts_info", "provider_note", "payment_method", "then", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/proveedors/AddProviderScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { toast } from \"react-toastify\";\nimport { createNewProvider } from \"../../redux/actions/providerActions\";\nimport axios from \"axios\";\nimport Select from \"react-select\";\nimport {\n  COUNTRIES,\n  SERVICESPECIALIST,\n  SERVICETYPE,\n  validateEmail,\n  validateLocationX,\n  validateLocationY,\n  validatePhone,\n} from \"../../constants\";\nimport GoogleComponent from \"react-google-autocomplete\";\n\nfunction AddProviderScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  const [isOpen, setIsOpen] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [emailSecond, setEmailSecond] = useState(\"\");\n  const [emailSecondError, setEmailSecondError] = useState(\"\");\n\n  const [serviceType, setServiceType] = useState(\"\");\n  const [serviceTypeError, setServiceTypeError] = useState(\"\");\n\n  const [serviceSpecialist, setServiceSpecialist] = useState(\"\");\n  const [serviceSpecialistError, setServiceSpecialistError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [phoneSecond, setPhoneSecond] = useState(\"\");\n  const [phoneSecondError, setPhoneSecondError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n\n  const [cityVl, setCityVl] = useState(\"\");\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [locationX, setLocationX] = useState(0);\n  const [locationXError, setLocationXError] = useState(\"\");\n\n  const [locationY, setLocationY] = useState(0);\n  const [locationYError, setLocationYError] = useState(\"\");\n\n  const [services, setServices] = useState([]);\n\n  const [infoProvider, setInfoProvider] = useState([]);\n\n  const [infoType, setInfoType] = useState(\"\");\n  const [infoTypeError, setInfoTypeError] = useState(\"\");\n\n  const [infoValue, setInfoValue] = useState(\"\");\n  const [infoValueError, setInfoValueError] = useState(\"\");\n\n  const [paymentMethod, setPaymentMethod] = useState(\"\");\n  const [paymentMethodError, setPaymentMethodError] = useState(\"\");\n\n  const [providerNote, setProviderNote] = useState(\"\");\n  const [providerNoteError, setProviderNoteError] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const providerAdd = useSelector((state) => state.addNewProvider);\n  const { loadingProviderAdd, errorProviderAdd, successProviderAdd } =\n    providerAdd;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successProviderAdd) {\n      setFirstName(\"\");\n      setLastName(\"\");\n      setEmail(\"\");\n      setPhone(\"\");\n      setEmailSecond(\"\");\n      setPhoneSecond(\"\");\n      setAddress(\"\");\n      setCountry(\"\");\n      setCity(\"\");\n      setLocationX(0);\n      setLocationY(0);\n      setServiceType(\"\");\n      setServices([]);\n      setServiceSpecialist(\"\");\n\n      setFirstNameError(\"\");\n      setLastNameError(\"\");\n      setEmailError(\"\");\n      setPhoneError(\"\");\n      setEmailSecondError(\"\");\n      setPhoneSecondError(\"\");\n      setAddressError(\"\");\n      setCountryError(\"\");\n      setCityError(\"\");\n      setLocationXError(\"\");\n      setLocationYError(\"\");\n      setServiceTypeError(\"\");\n      setServiceSpecialistError(\"\");\n    }\n  }, [successProviderAdd]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <a href=\"/providers-list\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <span>\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  className=\"w-4 h-4\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n                  />\n                </svg>\n              </span>\n              <div className=\"\">Providers List</div>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Provider</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Provider\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  First Name <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      firstNameError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"First Name\"\n                    value={firstName}\n                    onChange={(v) => setFirstName(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {firstNameError ? firstNameError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Last Name\n                </div>\n                <div>\n                  <input\n                    className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                    type=\"text\"\n                    placeholder=\"Last Name\"\n                    value={lastName}\n                    onChange={(v) => setLastName(v.target.value)}\n                  />\n                </div>\n              </div>\n            </div>\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Payment Method\n                </div>\n                <div>\n                  <select\n                    value={paymentMethod}\n                    onChange={(v) => {\n                      setPaymentMethod(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      paymentMethodError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  >\n                    <option value={\"\"}>Select Payment Method </option>\n                    <option value={\"Credit Card (Over the phone)\"}>\n                      Credit Card (Over the phone)\n                    </option>\n                    <option value={\"Credit Card (via Fax)\"}>\n                      Credit Card (via Fax)\n                    </option>\n                    <option value={\"Credit Card (via Email)\"}>\n                      Credit Card (via Email)\n                    </option>\n                    <option value={\"Bank Transfer\"}>Bank Transfer</option>\n                    <option value={\"Online Payment\"}>Online Payment</option>\n                    <option value={\"PayPal\"}>PayPal</option>\n                    <option value={\"Pix\"}>Pix</option>\n                    <option value={\"Western Union\"}>Western Union</option>\n                    <option value={\"No Payment Accepted\"}>\n                      No Payment Accepted\n                    </option>\n                  </select>\n                  <div className=\" text-[8px] text-danger\">\n                    {paymentMethodError ? paymentMethodError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            {/* <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Email 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 1\"\n                    value={email}\n                    onChange={(v) => setEmail(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailError ? emailError : \"\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Email 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      emailSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"email\"\n                    placeholder=\"Email 2\"\n                    value={emailSecond}\n                    onChange={(v) => setEmailSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {emailSecondError ? emailSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n          \n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Phone 1\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 1\"\n                    value={phone}\n                    onChange={(v) => setPhone(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneError ? phoneError : \"\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Phone 2\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      phoneSecondError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"phone\"\n                    placeholder=\"Phone 2\"\n                    value={phoneSecond}\n                    onChange={(v) => setPhoneSecond(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {phoneSecondError ? phoneSecondError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            */}\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Contact Type\n                </div>\n                <div>\n                  <select\n                    value={infoType}\n                    onChange={(v) => {\n                      setInfoType(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      infoTypeError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  >\n                    <option value={\"\"}>Select Type </option>\n                    <option value={\"Main Phone\"}>Main Phone</option>\n                    <option value={\"Whatsapp\"}>Whatsapp</option>\n                    <option value={\"Billing Phone\"}>Billing Phone</option>\n                    <option value={\"Main email\"}>Main email</option>\n                    <option value={\"Administration email\"}>\n                      Administration email\n                    </option>\n                    <option value={\"Billing email\"}>Billing email</option>\n                  </select>\n                  <div className=\" text-[8px] text-danger\">\n                    {infoTypeError ? infoTypeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Contact Value\n                </div>\n                <div>\n                  <input\n                    type=\"text\"\n                    value={infoValue}\n                    onChange={(v) => {\n                      setInfoValue(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      infoValueError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {infoTypeError ? infoTypeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n            </div>\n            <div className=\"flex flex-col  \">\n              <button\n                onClick={() => {\n                  var check = true;\n                  setInfoTypeError(\"\");\n                  setInfoValueError(\"\");\n                  if (infoType === \"\") {\n                    setInfoTypeError(\"These fields are required.\");\n                    check = false;\n                  }\n\n                  if (infoValue === \"\") {\n                    setInfoValueError(\"These fields are required.\");\n                    check = false;\n                  }\n\n                  if (check) {\n                    // Add the new item if it doesn't exist\n                    setInfoProvider([\n                      ...infoProvider,\n                      {\n                        info_type: infoType ?? \"\",\n                        info_value: infoValue ?? \"\",\n                      },\n                    ]);\n                    setInfoType(\"\");\n                    setInfoValue(\"\");\n                  } else {\n                    toast.error(\n                      \"Some fields are empty or invalid. please try again\"\n                    );\n                  }\n                }}\n                className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  />\n                </svg>\n                <span> Add Contact Info </span>\n              </button>\n              <div className=\" w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Contacts\n                </div>\n                <div className=\"my-2 text-black text-sm\">\n                  {infoProvider?.map((item, index) => (\n                    <div\n                      key={index}\n                      className=\"flex flex-row items-center my-1\"\n                    >\n                      <div className=\"min-w-6 text-center\">\n                        <button\n                          onClick={() => {\n                            const updatedInfoProvider = infoProvider.filter(\n                              (_, indexF) => indexF !== index\n                            );\n                            setInfoProvider(updatedInfoProvider);\n                          }}\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-6\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"flex-1 mx-1 border-l px-1\">\n                        <div>\n                          <b>Type:</b> {item.info_type}\n                        </div>\n                        <div>\n                          <b>Value:</b> {item.info_value}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Service Type\n                </div>\n\n                <div>\n                  <Select\n                    value={serviceType}\n                    onChange={(option) => {\n                      setServiceType(option);\n                      setServiceSpecialist(\"\");\n                    }}\n                    className=\"text-sm\"\n                    options={SERVICETYPE.map((item) => ({\n                      value: item,\n                      label: item,\n                    }))}\n                    placeholder=\"Select a Service Type...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: serviceTypeError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {serviceTypeError ? serviceTypeError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              {serviceType !== \"\" && serviceType.value === \"Specialists\" ? (\n                <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                  <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                    Service Specialist{\" \"}\n                    <strong className=\"text-danger\">*</strong>\n                  </div>\n                  <div>\n                    <Select\n                      value={serviceSpecialist}\n                      onChange={(option) => {\n                        setServiceSpecialist(option);\n                      }}\n                      className=\"text-sm\"\n                      options={SERVICESPECIALIST.map((item) => ({\n                        value: item,\n                        label: item,\n                      }))}\n                      placeholder=\"Select a Specialist...\"\n                      isSearchable\n                      styles={{\n                        control: (base, state) => ({\n                          ...base,\n                          background: \"#fff\",\n                          border: serviceSpecialistError\n                            ? \"1px solid #d34053\"\n                            : \"1px solid #F1F3FF\",\n                          boxShadow: state.isFocused ? \"none\" : \"none\",\n                          \"&:hover\": {\n                            border: \"1px solid #F1F3FF\",\n                          },\n                        }),\n                        option: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                        singleValue: (base) => ({\n                          ...base,\n                          display: \"flex\",\n                          alignItems: \"center\",\n                        }),\n                      }}\n                    />\n                    <div className=\" text-[8px] text-danger\">\n                      {serviceSpecialistError ? serviceSpecialistError : \"\"}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n            <div className=\"flex flex-col  \">\n              <button\n                onClick={() => {\n                  var check = true;\n                  setServiceTypeError(\"\");\n                  setServiceSpecialistError(\"\");\n                  if (serviceType === \"\" || serviceType.value === \"\") {\n                    setServiceTypeError(\"These fields are required.\");\n                    toast.error(\" Service is required\");\n                    check = false;\n                  } else if (\n                    serviceType.value === \"Specialists\" &&\n                    (serviceSpecialist === \"\" || serviceSpecialist.value === \"\")\n                  ) {\n                    setServiceSpecialistError(\"These fields are required.\");\n                    toast.error(\" Specialist is required\");\n                    check = false;\n                  }\n                  if (check) {\n                    var serviceSpecialistValue = \"\";\n                    if (\n                      serviceType.value === \"Specialists\" &&\n                      serviceSpecialist !== \"\" &&\n                      serviceSpecialist.value !== \"\"\n                    ) {\n                      serviceSpecialistValue = serviceSpecialist.value ?? \"\";\n                    }\n                    const exists = services.some(\n                      (service) =>\n                        service.service_type === serviceType.value &&\n                        service.service_specialist === serviceSpecialistValue\n                    );\n\n                    if (!exists) {\n                      // Add the new item if it doesn't exist\n                      setServices([\n                        ...services,\n                        {\n                          service_type: serviceType.value ?? \"\",\n                          service_specialist: serviceSpecialistValue,\n                        },\n                      ]);\n                      setServiceType(\"\");\n                      setServiceSpecialist(\"\");\n                    } else {\n                      setServiceTypeError(\"This service is already added!\");\n                      toast.error(\"This service is already added!\");\n                    }\n                  }\n                }}\n                className=\"text-primary  flex flex-row items-center my-2 text-sm\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke-width=\"1.5\"\n                  stroke=\"currentColor\"\n                  class=\"size-4\"\n                >\n                  <path\n                    stroke-linecap=\"round\"\n                    stroke-linejoin=\"round\"\n                    d=\"M12 9v6m3-3H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                  />\n                </svg>\n                <span> Add Service </span>\n              </button>\n              <div className=\" w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Services\n                </div>\n                <div className=\"my-2 text-black text-sm\">\n                  {services?.map((itemService, index) => (\n                    <div\n                      key={index}\n                      className=\"flex flex-row items-center my-1\"\n                    >\n                      <div className=\"min-w-6 text-center\">\n                        <button\n                          onClick={() => {\n                            const updatedServices = services.filter(\n                              (_, indexF) => indexF !== index\n                            );\n                            setServices(updatedServices);\n                          }}\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-6\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                      <div className=\"flex-1 mx-1 border-l px-1\">\n                        <div>\n                          <b>Service:</b> {itemService.service_type}\n                        </div>\n                        <div>\n                          <b>Speciality:</b> {itemService.service_specialist}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Address <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      addressError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"text\"\n                    placeholder=\"Address\"\n                    value={address}\n                    onChange={(v) => setAddress(v.target.value)}\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {addressError ? addressError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Country\n                </div>\n                <div>\n                  <Select\n                    value={country}\n                    onChange={(option) => {\n                      setCountry(option);\n                    }}\n                    className=\"text-sm\"\n                    options={COUNTRIES.map((country) => ({\n                      value: country.title,\n                      label: (\n                        <div\n                          className={`${\n                            country.title === \"\" ? \"py-2\" : \"\"\n                          } flex flex-row items-center`}\n                        >\n                          <span className=\"mr-2\">{country.icon}</span>\n                          <span>{country.title}</span>\n                        </div>\n                      ),\n                    }))}\n                    placeholder=\"Select a country...\"\n                    isSearchable\n                    styles={{\n                      control: (base, state) => ({\n                        ...base,\n                        background: \"#fff\",\n                        border: countryError\n                          ? \"1px solid #d34053\"\n                          : \"1px solid #F1F3FF\",\n                        boxShadow: state.isFocused ? \"none\" : \"none\",\n                        \"&:hover\": {\n                          border: \"1px solid #F1F3FF\",\n                        },\n                      }),\n                      option: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                      singleValue: (base) => ({\n                        ...base,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                      }),\n                    }}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {countryError ? countryError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  City <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <GoogleComponent\n                    apiKey=\"AIzaSyCozE2Q3aj449xsY28qeQ4-C5_IBOg21Ng\"\n                    className={` outline-none border ${\n                      cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    onChange={(v) => {\n                      setCity(v.target.value);\n                    }}\n                    onPlaceSelected={(place) => {\n                      if (place && place.geometry) {\n                        setCity(place.formatted_address ?? \"\");\n                        setCityVl(place.formatted_address ?? \"\");\n                        //   const latitude = place.geometry.location.lat();\n                        //   const longitude = place.geometry.location.lng();\n                        //   setLocationX(latitude ?? \"\");\n                        //   setLocationY(longitude ?? \"\");\n                      }\n                    }}\n                    defaultValue={city}\n                    types={[\"city\"]}\n                    language=\"en\"\n                  />\n\n                  <div className=\" text-[8px] text-danger\">\n                    {cityError ? cityError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Location X <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationXError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location X\"\n                    value={locationX}\n                    onChange={(v) => setLocationX(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationXError ? locationXError : \"\"}\n                  </div>\n                </div>\n              </div>\n              {/*  */}\n              <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs mb-1\">\n                  Location Y <strong className=\"text-danger\">*</strong>\n                </div>\n                <div>\n                  <input\n                    className={` outline-none border ${\n                      locationYError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                    type=\"number\"\n                    step={0.01}\n                    placeholder=\"Location Y\"\n                    value={locationY}\n                    onChange={(v) => setLocationY(v.target.value)}\n                  />\n                  <div className=\" text-[8px] text-danger\">\n                    {locationYError ? locationYError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"flex md:flex-row flex-col  \">\n              <div className=\" w-full  md:pr-1 my-1\">\n                <div className=\"text-[#000000bf] font-bold text-xs  mb-1\">\n                  Provider Note\n                </div>\n                <div>\n                  <textarea\n                    rows={5}\n                    value={providerNote}\n                    onChange={(v) => {\n                      setProviderNote(v.target.value);\n                    }}\n                    className={` outline-none border ${\n                      providerNoteError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                    } px-3 py-2 w-full rounded text-sm`}\n                  ></textarea>\n                  <div className=\" text-[8px] text-danger\">\n                    {providerNoteError ? providerNoteError : \"\"}\n                  </div>\n                </div>\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"my-3 \">\n              <div className=\"flex flex-row items-center justify-end my-3\">\n                <a\n                  href=\"/providers-list\"\n                  className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                >\n                  Back\n                </a>\n                <button\n                  onClick={async () => {\n                    var check = true;\n                    setFirstNameError(\"\");\n                    setAddressError(\"\");\n                    setServiceTypeError(\"\");\n                    setServiceSpecialistError(\"\");\n                    setLocationXError(\"\");\n                    setLocationYError(\"\");\n                    setPhoneError(\"\");\n                    setEmailError(\"\");\n                    setCityError(\"\");\n\n                    setPaymentMethodError(\"\");\n                    setProviderNoteError(\"\");\n\n                    if (firstName === \"\") {\n                      setFirstNameError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (email !== \"\" && !validateEmail(email)) {\n                      setEmailError(\n                        \"Invalid email address. Please correct it.\"\n                      );\n                      check = false;\n                    }\n                    if (phone !== \"\" && !validatePhone(phone)) {\n                      setPhoneError(\"Invalid phone number. Please correct it.\");\n                      check = false;\n                    }\n\n                    if (services.length === 0) {\n                      setServiceTypeError(\n                        \"Please select this and click Add Service.\"\n                      );\n                      check = false;\n                    }\n\n                    if (address === \"\") {\n                      setAddressError(\"These fields are required.\");\n                      check = false;\n                    }\n                    if (city === \"\") {\n                      setCityError(\"These fields are required.\");\n                      check = false;\n                    }\n\n                    if (locationX === \"\") {\n                      setLocationXError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationX(locationX)) {\n                      setLocationXError(\n                        \"Please enter a valid longitude (-180 to 180).\"\n                      );\n                      check = false;\n                    }\n                    if (locationY === \"\") {\n                      setLocationYError(\"These fields are required.\");\n                      check = false;\n                    } else if (!validateLocationY(locationY)) {\n                      setLocationYError(\n                        \"Please enter a valid latitude (-180 to 180).\"\n                      );\n                      check = false;\n                    }\n\n                    if (check) {\n                      setLoadEvent(true);\n                      await dispatch(\n                        createNewProvider({\n                          first_name: firstName,\n                          last_name: lastName ?? \"\",\n                          full_name: firstName + \" \" + lastName,\n                          // service_type: serviceType.value ?? \"\",\n                          // service_specialist: serviceSpecialist.value ?? \"\",\n                          email: email ?? \"\",\n                          second_email: emailSecond ?? \"\",\n                          phone: phone ?? \"\",\n                          second_phone: phoneSecond ?? \"\",\n                          address: address,\n                          country: country.value ?? \"\",\n                          city: city ?? \"\",\n                          location_x: locationX,\n                          location_y: locationY,\n                          services: services,\n                          contacts_info: infoProvider,\n                          provider_note: providerNote,\n                          payment_method: paymentMethod,\n                        })\n                      ).then(() => {});\n                      setLoadEvent(false);\n                    } else {\n                      toast.error(\n                        \"Some fields are empty or invalid. please try again\"\n                      );\n                    }\n                  }}\n                  className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                >\n                  {loadingProviderAdd ? \"Loading ...\" : \"Create Provider\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddProviderScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,SACEC,SAAS,EACTC,iBAAiB,EACjBC,WAAW,EACXC,aAAa,EACbC,iBAAiB,EACjBC,iBAAiB,EACjBC,aAAa,QACR,iBAAiB;AACxB,OAAOC,eAAe,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,cAAc,EAAEC,iBAAiB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAACqC,KAAK,EAAEC,QAAQ,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC+C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmD,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAExE,MAAM,CAACqD,KAAK,EAAEC,QAAQ,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAE5D,MAAM,CAAC6D,OAAO,EAAEC,UAAU,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACqE,MAAM,EAAEC,SAAS,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuE,IAAI,EAAEC,OAAO,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACyE,SAAS,EAAEC,YAAY,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAE9C,MAAM,CAAC2E,SAAS,EAAEC,YAAY,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC+E,SAAS,EAAEC,YAAY,CAAC,GAAGhF,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACiF,cAAc,EAAEC,iBAAiB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAACmF,QAAQ,EAAEC,WAAW,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM,CAACqF,YAAY,EAAEC,eAAe,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAEpD,MAAM,CAACuF,QAAQ,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyF,aAAa,EAAEC,gBAAgB,CAAC,GAAG1F,QAAQ,CAAC,EAAE,CAAC;EAEtD,MAAM,CAAC2F,SAAS,EAAEC,YAAY,CAAC,GAAG5F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC6F,cAAc,EAAEC,iBAAiB,CAAC,GAAG9F,QAAQ,CAAC,EAAE,CAAC;EAExD,MAAM,CAAC+F,aAAa,EAAEC,gBAAgB,CAAC,GAAGhG,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiG,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAEhE,MAAM,CAACmG,YAAY,EAAEC,eAAe,CAAC,GAAGpG,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACqG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAE9D,MAAMuG,SAAS,GAAGrG,WAAW,CAAEsG,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,WAAW,GAAG1G,WAAW,CAAEsG,KAAK,IAAKA,KAAK,CAACK,cAAc,CAAC;EAChE,MAAM;IAAEC,kBAAkB;IAAEC,gBAAgB;IAAEC;EAAmB,CAAC,GAChEJ,WAAW;EAEb,MAAMK,QAAQ,GAAG,GAAG;EACpBlH,SAAS,CAAC,MAAM;IACd,IAAI,CAAC0G,QAAQ,EAAE;MACbnF,QAAQ,CAAC2F,QAAQ,CAAC;IACpB;EACF,CAAC,EAAE,CAAC3F,QAAQ,EAAEmF,QAAQ,EAAEjF,QAAQ,CAAC,CAAC;EAElCzB,SAAS,CAAC,MAAM;IACd,IAAIiH,kBAAkB,EAAE;MACtBlF,YAAY,CAAC,EAAE,CAAC;MAChBI,WAAW,CAAC,EAAE,CAAC;MACfI,QAAQ,CAAC,EAAE,CAAC;MACZgB,QAAQ,CAAC,EAAE,CAAC;MACZZ,cAAc,CAAC,EAAE,CAAC;MAClBgB,cAAc,CAAC,EAAE,CAAC;MAClBI,UAAU,CAAC,EAAE,CAAC;MACdI,UAAU,CAAC,EAAE,CAAC;MACdM,OAAO,CAAC,EAAE,CAAC;MACXI,YAAY,CAAC,CAAC,CAAC;MACfI,YAAY,CAAC,CAAC,CAAC;MACflC,cAAc,CAAC,EAAE,CAAC;MAClBsC,WAAW,CAAC,EAAE,CAAC;MACflC,oBAAoB,CAAC,EAAE,CAAC;MAExBlB,iBAAiB,CAAC,EAAE,CAAC;MACrBI,gBAAgB,CAAC,EAAE,CAAC;MACpBI,aAAa,CAAC,EAAE,CAAC;MACjBgB,aAAa,CAAC,EAAE,CAAC;MACjBZ,mBAAmB,CAAC,EAAE,CAAC;MACvBgB,mBAAmB,CAAC,EAAE,CAAC;MACvBI,eAAe,CAAC,EAAE,CAAC;MACnBI,eAAe,CAAC,EAAE,CAAC;MACnBM,YAAY,CAAC,EAAE,CAAC;MAChBI,iBAAiB,CAAC,EAAE,CAAC;MACrBI,iBAAiB,CAAC,EAAE,CAAC;MACrBlC,mBAAmB,CAAC,EAAE,CAAC;MACvBI,yBAAyB,CAAC,EAAE,CAAC;IAC/B;EACF,CAAC,EAAE,CAAC4D,kBAAkB,CAAC,CAAC;EAExB,oBACE7F,OAAA,CAACd,aAAa;IAAA6G,QAAA,eACZ/F,OAAA;MAAA+F,QAAA,gBACE/F,OAAA;QAAKgG,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD/F,OAAA;UAAGiG,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB/F,OAAA;YAAKgG,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/F,OAAA;cACEkG,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB/F,OAAA;gBACEsG,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN5G,OAAA;cAAMgG,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ5G,OAAA;UAAGiG,IAAI,EAAC,iBAAiB;UAAAF,QAAA,eACvB/F,OAAA;YAAKgG,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D/F,OAAA;cAAA+F,QAAA,eACE/F,OAAA;gBACEkG,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBL,SAAS,EAAC,SAAS;gBAAAD,QAAA,eAEnB/F,OAAA;kBACEsG,aAAa,EAAC,OAAO;kBACrBC,cAAc,EAAC,OAAO;kBACtBC,CAAC,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACP5G,OAAA;cAAKgG,SAAS,EAAC,EAAE;cAAAD,QAAA,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJ5G,OAAA;UAAA+F,QAAA,eACE/F,OAAA;YACEkG,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB/F,OAAA;cACEsG,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACP5G,OAAA;UAAKgG,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAmB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eAEN5G,OAAA;QAAKgG,SAAS,EAAC,gCAAgC;QAAAD,QAAA,eAC7C/F,OAAA;UAAIgG,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAEpE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN5G,OAAA;QAAKgG,SAAS,EAAC,mIAAmI;QAAAD,QAAA,eAChJ/F,OAAA;UAAKgG,SAAS,EAAC,oCAAoC;UAAAD,QAAA,gBACjD/F,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAA/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACEgG,SAAS,EAAG,wBACVpF,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpCiG,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAErG,SAAU;kBACjBsG,QAAQ,EAAGC,CAAC,IAAKtG,YAAY,CAACsG,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCnF,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5G,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,EAAC;cAEzD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,eACE/F,OAAA;kBACEgG,SAAS,EAAC,wEAAwE;kBAClFa,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,WAAW;kBACvBC,KAAK,EAAEjG,QAAS;kBAChBkG,QAAQ,EAAGC,CAAC,IAAKlG,WAAW,CAACkG,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACE+G,KAAK,EAAEnC,aAAc;kBACrBoC,QAAQ,EAAGC,CAAC,IAAK;oBACfpC,gBAAgB,CAACoC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAClC,CAAE;kBACFf,SAAS,EAAG,wBACVlB,kBAAkB,GAAG,eAAe,GAAG,kBACxC,mCAAmC;kBAAAiB,QAAA,gBAEpC/F,OAAA;oBAAQ+G,KAAK,EAAE,EAAG;oBAAAhB,QAAA,EAAC;kBAAsB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5G,OAAA;oBAAQ+G,KAAK,EAAE,8BAA+B;oBAAAhB,QAAA,EAAC;kBAE/C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA;oBAAQ+G,KAAK,EAAE,uBAAwB;oBAAAhB,QAAA,EAAC;kBAExC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA;oBAAQ+G,KAAK,EAAE,yBAA0B;oBAAAhB,QAAA,EAAC;kBAE1C;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA;oBAAQ+G,KAAK,EAAE,eAAgB;oBAAAhB,QAAA,EAAC;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtD5G,OAAA;oBAAQ+G,KAAK,EAAE,gBAAiB;oBAAAhB,QAAA,EAAC;kBAAc;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxD5G,OAAA;oBAAQ+G,KAAK,EAAE,QAAS;oBAAAhB,QAAA,EAAC;kBAAM;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC5G,OAAA;oBAAQ+G,KAAK,EAAE,KAAM;oBAAAhB,QAAA,EAAC;kBAAG;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC5G,OAAA;oBAAQ+G,KAAK,EAAE,eAAgB;oBAAAhB,QAAA,EAAC;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtD5G,OAAA;oBAAQ+G,KAAK,EAAE,qBAAsB;oBAAAhB,QAAA,EAAC;kBAEtC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACT5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjB,kBAAkB,GAAGA,kBAAkB,GAAG;gBAAE;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAqFN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACE+G,KAAK,EAAE3C,QAAS;kBAChB4C,QAAQ,EAAGC,CAAC,IAAK;oBACf5C,WAAW,CAAC4C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAC7B,CAAE;kBACFf,SAAS,EAAG,wBACV1B,aAAa,GAAG,eAAe,GAAG,kBACnC,mCAAmC;kBAAAyB,QAAA,gBAEpC/F,OAAA;oBAAQ+G,KAAK,EAAE,EAAG;oBAAAhB,QAAA,EAAC;kBAAY;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACxC5G,OAAA;oBAAQ+G,KAAK,EAAE,YAAa;oBAAAhB,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD5G,OAAA;oBAAQ+G,KAAK,EAAE,UAAW;oBAAAhB,QAAA,EAAC;kBAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5C5G,OAAA;oBAAQ+G,KAAK,EAAE,eAAgB;oBAAAhB,QAAA,EAAC;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtD5G,OAAA;oBAAQ+G,KAAK,EAAE,YAAa;oBAAAhB,QAAA,EAAC;kBAAU;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD5G,OAAA;oBAAQ+G,KAAK,EAAE,sBAAuB;oBAAAhB,QAAA,EAAC;kBAEvC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT5G,OAAA;oBAAQ+G,KAAK,EAAE,eAAgB;oBAAAhB,QAAA,EAAC;kBAAa;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACT5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzB,aAAa,GAAGA,aAAa,GAAG;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN5G,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACE6G,IAAI,EAAC,MAAM;kBACXE,KAAK,EAAEvC,SAAU;kBACjBwC,QAAQ,EAAGC,CAAC,IAAK;oBACfxC,YAAY,CAACwC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBAC9B,CAAE;kBACFf,SAAS,EAAG,wBACVtB,cAAc,GAAG,eAAe,GAAG,kBACpC;gBAAmC;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzB,aAAa,GAAGA,aAAa,GAAG;gBAAE;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEH,CAAC,eACN5G,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9B/F,OAAA;cACEmH,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIC,KAAK,GAAG,IAAI;gBAChB7C,gBAAgB,CAAC,EAAE,CAAC;gBACpBI,iBAAiB,CAAC,EAAE,CAAC;gBACrB,IAAIP,QAAQ,KAAK,EAAE,EAAE;kBACnBG,gBAAgB,CAAC,4BAA4B,CAAC;kBAC9C6C,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAI5C,SAAS,KAAK,EAAE,EAAE;kBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;kBAC/CyC,KAAK,GAAG,KAAK;gBACf;gBAEA,IAAIA,KAAK,EAAE;kBACT;kBACAjD,eAAe,CAAC,CACd,GAAGD,YAAY,EACf;oBACEmD,SAAS,EAAEjD,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,EAAE;oBACzBkD,UAAU,EAAE9C,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI;kBAC3B,CAAC,CACF,CAAC;kBACFH,WAAW,CAAC,EAAE,CAAC;kBACfI,YAAY,CAAC,EAAE,CAAC;gBAClB,CAAC,MAAM;kBACLtF,KAAK,CAACqG,KAAK,CACT,oDACF,CAAC;gBACH;cACF,CAAE;cACFQ,SAAS,EAAC,uDAAuD;cAAAD,QAAA,gBAEjE/F,OAAA;gBACEkG,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBkB,KAAK,EAAC,QAAQ;gBAAAxB,QAAA,eAEd/F,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBwG,CAAC,EAAC;gBAAmD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,EAAM;cAAkB;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACT5G,OAAA;cAAKgG,SAAS,EAAC,uBAAuB;cAAAD,QAAA,gBACpC/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAKgG,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,EACrC7B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEsD,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7B1H,OAAA;kBAEEgG,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAE3C/F,OAAA;oBAAKgG,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,eAClC/F,OAAA;sBACEmH,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAMQ,mBAAmB,GAAGzD,YAAY,CAAC0D,MAAM,CAC7C,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAKJ,KAC5B,CAAC;wBACDvD,eAAe,CAACwD,mBAAmB,CAAC;sBACtC,CAAE;sBAAA5B,QAAA,eAEF/F,OAAA;wBACEkG,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBkB,KAAK,EAAC,QAAQ;wBAAAxB,QAAA,eAEd/F,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBwG,CAAC,EAAC;wBAAuE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACN5G,OAAA;oBAAKgG,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxC/F,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAA+F,QAAA,EAAG;sBAAK;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,KAAC,EAACa,IAAI,CAACJ,SAAS;oBAAA;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACN5G,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAA+F,QAAA,EAAG;sBAAM;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,KAAC,EAACa,IAAI,CAACH,UAAU;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAnCDc,KAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoCP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA,CAACV,MAAM;kBACLyH,KAAK,EAAErF,WAAY;kBACnBsF,QAAQ,EAAGe,MAAM,IAAK;oBACpBpG,cAAc,CAACoG,MAAM,CAAC;oBACtBhG,oBAAoB,CAAC,EAAE,CAAC;kBAC1B,CAAE;kBACFiE,SAAS,EAAC,SAAS;kBACnBgC,OAAO,EAAEvI,WAAW,CAAC+H,GAAG,CAAEC,IAAI,KAAM;oBAClCV,KAAK,EAAEU,IAAI;oBACXQ,KAAK,EAAER;kBACT,CAAC,CAAC,CAAE;kBACJX,WAAW,EAAC,0BAA0B;kBACtCoB,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEhD,KAAK,MAAM;sBACzB,GAAGgD,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAE3G,gBAAgB,GACpB,mBAAmB,GACnB,mBAAmB;sBACvB4G,SAAS,EAAEnD,KAAK,CAACoD,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFR,MAAM,EAAGM,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCnE,gBAAgB,GAAGA,gBAAgB,GAAG;gBAAE;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELlF,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACqF,KAAK,KAAK,aAAa,gBACxD/G,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,oBACtC,EAAC,GAAG,eACtB/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA,CAACV,MAAM;kBACLyH,KAAK,EAAEjF,iBAAkB;kBACzBkF,QAAQ,EAAGe,MAAM,IAAK;oBACpBhG,oBAAoB,CAACgG,MAAM,CAAC;kBAC9B,CAAE;kBACF/B,SAAS,EAAC,SAAS;kBACnBgC,OAAO,EAAExI,iBAAiB,CAACgI,GAAG,CAAEC,IAAI,KAAM;oBACxCV,KAAK,EAAEU,IAAI;oBACXQ,KAAK,EAAER;kBACT,CAAC,CAAC,CAAE;kBACJX,WAAW,EAAC,wBAAwB;kBACpCoB,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEhD,KAAK,MAAM;sBACzB,GAAGgD,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAEvG,sBAAsB,GAC1B,mBAAmB,GACnB,mBAAmB;sBACvBwG,SAAS,EAAEnD,KAAK,CAACoD,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFR,MAAM,EAAGM,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC/D,sBAAsB,GAAGA,sBAAsB,GAAG;gBAAE;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GACJ,IAAI;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN5G,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAAAD,QAAA,gBAC9B/F,OAAA;cACEmH,OAAO,EAAEA,CAAA,KAAM;gBACb,IAAIC,KAAK,GAAG,IAAI;gBAChBvF,mBAAmB,CAAC,EAAE,CAAC;gBACvBI,yBAAyB,CAAC,EAAE,CAAC;gBAC7B,IAAIP,WAAW,KAAK,EAAE,IAAIA,WAAW,CAACqF,KAAK,KAAK,EAAE,EAAE;kBAClDlF,mBAAmB,CAAC,4BAA4B,CAAC;kBACjD1C,KAAK,CAACqG,KAAK,CAAC,sBAAsB,CAAC;kBACnC4B,KAAK,GAAG,KAAK;gBACf,CAAC,MAAM,IACL1F,WAAW,CAACqF,KAAK,KAAK,aAAa,KAClCjF,iBAAiB,KAAK,EAAE,IAAIA,iBAAiB,CAACiF,KAAK,KAAK,EAAE,CAAC,EAC5D;kBACA9E,yBAAyB,CAAC,4BAA4B,CAAC;kBACvD9C,KAAK,CAACqG,KAAK,CAAC,yBAAyB,CAAC;kBACtC4B,KAAK,GAAG,KAAK;gBACf;gBACA,IAAIA,KAAK,EAAE;kBACT,IAAIyB,sBAAsB,GAAG,EAAE;kBAC/B,IACEnH,WAAW,CAACqF,KAAK,KAAK,aAAa,IACnCjF,iBAAiB,KAAK,EAAE,IACxBA,iBAAiB,CAACiF,KAAK,KAAK,EAAE,EAC9B;oBAAA,IAAA+B,qBAAA;oBACAD,sBAAsB,IAAAC,qBAAA,GAAGhH,iBAAiB,CAACiF,KAAK,cAAA+B,qBAAA,cAAAA,qBAAA,GAAI,EAAE;kBACxD;kBACA,MAAMC,MAAM,GAAG/E,QAAQ,CAACgF,IAAI,CACzBC,OAAO,IACNA,OAAO,CAACC,YAAY,KAAKxH,WAAW,CAACqF,KAAK,IAC1CkC,OAAO,CAACE,kBAAkB,KAAKN,sBACnC,CAAC;kBAED,IAAI,CAACE,MAAM,EAAE;oBAAA,IAAAK,kBAAA;oBACX;oBACAnF,WAAW,CAAC,CACV,GAAGD,QAAQ,EACX;sBACEkF,YAAY,GAAAE,kBAAA,GAAE1H,WAAW,CAACqF,KAAK,cAAAqC,kBAAA,cAAAA,kBAAA,GAAI,EAAE;sBACrCD,kBAAkB,EAAEN;oBACtB,CAAC,CACF,CAAC;oBACFlH,cAAc,CAAC,EAAE,CAAC;oBAClBI,oBAAoB,CAAC,EAAE,CAAC;kBAC1B,CAAC,MAAM;oBACLF,mBAAmB,CAAC,gCAAgC,CAAC;oBACrD1C,KAAK,CAACqG,KAAK,CAAC,gCAAgC,CAAC;kBAC/C;gBACF;cACF,CAAE;cACFQ,SAAS,EAAC,uDAAuD;cAAAD,QAAA,gBAEjE/F,OAAA;gBACEkG,KAAK,EAAC,4BAA4B;gBAClCC,IAAI,EAAC,MAAM;gBACXC,OAAO,EAAC,WAAW;gBACnB,gBAAa,KAAK;gBAClBC,MAAM,EAAC,cAAc;gBACrBkB,KAAK,EAAC,QAAQ;gBAAAxB,QAAA,eAEd/F,OAAA;kBACE,kBAAe,OAAO;kBACtB,mBAAgB,OAAO;kBACvBwG,CAAC,EAAC;gBAAmD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,EAAM;cAAa;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACT5G,OAAA;cAAKgG,SAAS,EAAC,uBAAuB;cAAAD,QAAA,gBACpC/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAKgG,SAAS,EAAC,yBAAyB;gBAAAD,QAAA,EACrC/B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,GAAG,CAAC,CAAC6B,WAAW,EAAE3B,KAAK,kBAChC1H,OAAA;kBAEEgG,SAAS,EAAC,iCAAiC;kBAAAD,QAAA,gBAE3C/F,OAAA;oBAAKgG,SAAS,EAAC,qBAAqB;oBAAAD,QAAA,eAClC/F,OAAA;sBACEmH,OAAO,EAAEA,CAAA,KAAM;wBACb,MAAMmC,eAAe,GAAGtF,QAAQ,CAAC4D,MAAM,CACrC,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,KAAKJ,KAC5B,CAAC;wBACDzD,WAAW,CAACqF,eAAe,CAAC;sBAC9B,CAAE;sBAAAvD,QAAA,eAEF/F,OAAA;wBACEkG,KAAK,EAAC,4BAA4B;wBAClCC,IAAI,EAAC,MAAM;wBACXC,OAAO,EAAC,WAAW;wBACnB,gBAAa,KAAK;wBAClBC,MAAM,EAAC,cAAc;wBACrBkB,KAAK,EAAC,QAAQ;wBAAAxB,QAAA,eAEd/F,OAAA;0BACE,kBAAe,OAAO;0BACtB,mBAAgB,OAAO;0BACvBwG,CAAC,EAAC;wBAAuE;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1E;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACN5G,OAAA;oBAAKgG,SAAS,EAAC,2BAA2B;oBAAAD,QAAA,gBACxC/F,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAA+F,QAAA,EAAG;sBAAQ;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,KAAC,EAACyC,WAAW,CAACH,YAAY;oBAAA;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACN5G,OAAA;sBAAA+F,QAAA,gBACE/F,OAAA;wBAAA+F,QAAA,EAAG;sBAAW;wBAAAU,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,KAAC,EAACyC,WAAW,CAACF,kBAAkB;oBAAA;sBAAA1C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAnCDc,KAAK;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoCP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C/F,OAAA;cAAKgG,SAAS,EAAC,sBAAsB;cAAAD,QAAA,gBACnC/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,UAChD,eAAA/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACEgG,SAAS,EAAG,wBACVpD,YAAY,GAAG,eAAe,GAAG,kBAClC,mCAAmC;kBACpCiE,IAAI,EAAC,MAAM;kBACXC,WAAW,EAAC,SAAS;kBACrBC,KAAK,EAAErE,OAAQ;kBACfsE,QAAQ,EAAGC,CAAC,IAAKtE,UAAU,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eAEF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCnD,YAAY,GAAGA,YAAY,GAAG;gBAAE;kBAAA6D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA,CAACV,MAAM;kBACLyH,KAAK,EAAEjE,OAAQ;kBACfkE,QAAQ,EAAGe,MAAM,IAAK;oBACpBhF,UAAU,CAACgF,MAAM,CAAC;kBACpB,CAAE;kBACF/B,SAAS,EAAC,SAAS;kBACnBgC,OAAO,EAAEzI,SAAS,CAACiI,GAAG,CAAE1E,OAAO,KAAM;oBACnCiE,KAAK,EAAEjE,OAAO,CAACyG,KAAK;oBACpBtB,KAAK,eACHjI,OAAA;sBACEgG,SAAS,EAAG,GACVlD,OAAO,CAACyG,KAAK,KAAK,EAAE,GAAG,MAAM,GAAG,EACjC,6BAA6B;sBAAAxD,QAAA,gBAE9B/F,OAAA;wBAAMgG,SAAS,EAAC,MAAM;wBAAAD,QAAA,EAAEjD,OAAO,CAAC0G;sBAAI;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eAC5C5G,OAAA;wBAAA+F,QAAA,EAAOjD,OAAO,CAACyG;sBAAK;wBAAA9C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAET,CAAC,CAAC,CAAE;kBACJE,WAAW,EAAC,qBAAqB;kBACjCoB,YAAY;kBACZC,MAAM,EAAE;oBACNC,OAAO,EAAEA,CAACC,IAAI,EAAEhD,KAAK,MAAM;sBACzB,GAAGgD,IAAI;sBACPC,UAAU,EAAE,MAAM;sBAClBC,MAAM,EAAEvF,YAAY,GAChB,mBAAmB,GACnB,mBAAmB;sBACvBwF,SAAS,EAAEnD,KAAK,CAACoD,SAAS,GAAG,MAAM,GAAG,MAAM;sBAC5C,SAAS,EAAE;wBACTF,MAAM,EAAE;sBACV;oBACF,CAAC,CAAC;oBACFR,MAAM,EAAGM,IAAI,KAAM;sBACjB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC,CAAC;oBACFC,WAAW,EAAGP,IAAI,KAAM;sBACtB,GAAGA,IAAI;sBACPK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAC;kBACH;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrC/C,YAAY,GAAGA,YAAY,GAAG;gBAAE;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5G,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,GAAC,OAClD,eAAA/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA,CAACF,eAAe;kBACd2J,MAAM,EAAC,yCAAyC;kBAChDzD,SAAS,EAAG,wBACV1C,SAAS,GAAG,eAAe,GAAG,kBAC/B,mCAAmC;kBACpC0D,QAAQ,EAAGC,CAAC,IAAK;oBACf5D,OAAO,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBACzB,CAAE;kBACF2C,eAAe,EAAGC,KAAK,IAAK;oBAC1B,IAAIA,KAAK,IAAIA,KAAK,CAACC,QAAQ,EAAE;sBAAA,IAAAC,qBAAA,EAAAC,sBAAA;sBAC3BzG,OAAO,EAAAwG,qBAAA,GAACF,KAAK,CAACI,iBAAiB,cAAAF,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;sBACtC1G,SAAS,EAAA2G,sBAAA,GAACH,KAAK,CAACI,iBAAiB,cAAAD,sBAAA,cAAAA,sBAAA,GAAI,EAAE,CAAC;sBACxC;sBACA;sBACA;sBACA;oBACF;kBACF,CAAE;kBACFE,YAAY,EAAE5G,IAAK;kBACnB6G,KAAK,EAAE,CAAC,MAAM,CAAE;kBAChBC,QAAQ,EAAC;gBAAI;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eAEF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCzC,SAAS,GAAGA,SAAS,GAAG;gBAAE;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAC1C/F,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,GAAC,aAC7C,eAAA/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACEgG,SAAS,EAAG,wBACVtC,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpCmD,IAAI,EAAC,QAAQ;kBACbsD,IAAI,EAAE,IAAK;kBACXrD,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEvD,SAAU;kBACjBwD,QAAQ,EAAGC,CAAC,IAAKxD,YAAY,CAACwD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCrC,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5G,OAAA;cAAKgG,SAAS,EAAC,+BAA+B;cAAAD,QAAA,gBAC5C/F,OAAA;gBAAKgG,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,GAAC,aAC5C,eAAA/F,OAAA;kBAAQgG,SAAS,EAAC,aAAa;kBAAAD,QAAA,EAAC;gBAAC;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACEgG,SAAS,EAAG,wBACVlC,cAAc,GAAG,eAAe,GAAG,kBACpC,mCAAmC;kBACpC+C,IAAI,EAAC,QAAQ;kBACbsD,IAAI,EAAE,IAAK;kBACXrD,WAAW,EAAC,YAAY;kBACxBC,KAAK,EAAEnD,SAAU;kBACjBoD,QAAQ,EAAGC,CAAC,IAAKpD,YAAY,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACF5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCjC,cAAc,GAAGA,cAAc,GAAG;gBAAE;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,6BAA6B;YAAAD,QAAA,eAC1C/F,OAAA;cAAKgG,SAAS,EAAC,uBAAuB;cAAAD,QAAA,gBACpC/F,OAAA;gBAAKgG,SAAS,EAAC,0CAA0C;gBAAAD,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACN5G,OAAA;gBAAA+F,QAAA,gBACE/F,OAAA;kBACEoK,IAAI,EAAE,CAAE;kBACRrD,KAAK,EAAE/B,YAAa;kBACpBgC,QAAQ,EAAGC,CAAC,IAAK;oBACfhC,eAAe,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAC;kBACjC,CAAE;kBACFf,SAAS,EAAG,wBACVd,iBAAiB,GAAG,eAAe,GAAG,kBACvC;gBAAmC;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACZ5G,OAAA;kBAAKgG,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,EACrCb,iBAAiB,GAAGA,iBAAiB,GAAG;gBAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5G,OAAA;YAAKgG,SAAS,EAAC,OAAO;YAAAD,QAAA,eACpB/F,OAAA;cAAKgG,SAAS,EAAC,6CAA6C;cAAAD,QAAA,gBAC1D/F,OAAA;gBACEiG,IAAI,EAAC,iBAAiB;gBACtBD,SAAS,EAAC,6DAA6D;gBAAAD,QAAA,EACxE;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJ5G,OAAA;gBACEmH,OAAO,EAAE,MAAAA,CAAA,KAAY;kBACnB,IAAIC,KAAK,GAAG,IAAI;kBAChBvG,iBAAiB,CAAC,EAAE,CAAC;kBACrBgC,eAAe,CAAC,EAAE,CAAC;kBACnBhB,mBAAmB,CAAC,EAAE,CAAC;kBACvBI,yBAAyB,CAAC,EAAE,CAAC;kBAC7B0B,iBAAiB,CAAC,EAAE,CAAC;kBACrBI,iBAAiB,CAAC,EAAE,CAAC;kBACrB1B,aAAa,CAAC,EAAE,CAAC;kBACjBhB,aAAa,CAAC,EAAE,CAAC;kBACjBkC,YAAY,CAAC,EAAE,CAAC;kBAEhBwB,qBAAqB,CAAC,EAAE,CAAC;kBACzBI,oBAAoB,CAAC,EAAE,CAAC;kBAExB,IAAIzE,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CuG,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIlG,KAAK,KAAK,EAAE,IAAI,CAACxB,aAAa,CAACwB,KAAK,CAAC,EAAE;oBACzCG,aAAa,CACX,2CACF,CAAC;oBACD+F,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIlF,KAAK,KAAK,EAAE,IAAI,CAACrC,aAAa,CAACqC,KAAK,CAAC,EAAE;oBACzCG,aAAa,CAAC,0CAA0C,CAAC;oBACzD+E,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIpD,QAAQ,CAACqG,MAAM,KAAK,CAAC,EAAE;oBACzBxI,mBAAmB,CACjB,2CACF,CAAC;oBACDuF,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAI1E,OAAO,KAAK,EAAE,EAAE;oBAClBG,eAAe,CAAC,4BAA4B,CAAC;oBAC7CuE,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIhE,IAAI,KAAK,EAAE,EAAE;oBACfG,YAAY,CAAC,4BAA4B,CAAC;oBAC1C6D,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAI5D,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CyD,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IAAI,CAACzH,iBAAiB,CAAC6D,SAAS,CAAC,EAAE;oBACxCG,iBAAiB,CACf,+CACF,CAAC;oBACDyD,KAAK,GAAG,KAAK;kBACf;kBACA,IAAIxD,SAAS,KAAK,EAAE,EAAE;oBACpBG,iBAAiB,CAAC,4BAA4B,CAAC;oBAC/CqD,KAAK,GAAG,KAAK;kBACf,CAAC,MAAM,IAAI,CAACxH,iBAAiB,CAACgE,SAAS,CAAC,EAAE;oBACxCG,iBAAiB,CACf,8CACF,CAAC;oBACDqD,KAAK,GAAG,KAAK;kBACf;kBAEA,IAAIA,KAAK,EAAE;oBAAA,IAAAkD,cAAA;oBACT7J,YAAY,CAAC,IAAI,CAAC;oBAClB,MAAMJ,QAAQ,CACZjB,iBAAiB,CAAC;sBAChBmL,UAAU,EAAE7J,SAAS;sBACrB8J,SAAS,EAAE1J,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,EAAE;sBACzB2J,SAAS,EAAE/J,SAAS,GAAG,GAAG,GAAGI,QAAQ;sBACrC;sBACA;sBACAI,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE;sBAClBwJ,YAAY,EAAEpJ,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE;sBAC/BY,KAAK,EAAEA,KAAK,aAALA,KAAK,cAALA,KAAK,GAAI,EAAE;sBAClByI,YAAY,EAAErI,WAAW,aAAXA,WAAW,cAAXA,WAAW,GAAI,EAAE;sBAC/BI,OAAO,EAAEA,OAAO;sBAChBI,OAAO,GAAAwH,cAAA,GAAExH,OAAO,CAACiE,KAAK,cAAAuD,cAAA,cAAAA,cAAA,GAAI,EAAE;sBAC5BlH,IAAI,EAAEA,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE;sBAChBwH,UAAU,EAAEpH,SAAS;sBACrBqH,UAAU,EAAEjH,SAAS;sBACrBI,QAAQ,EAAEA,QAAQ;sBAClB8G,aAAa,EAAE5G,YAAY;sBAC3B6G,aAAa,EAAE/F,YAAY;sBAC3BgG,cAAc,EAAEpG;oBAClB,CAAC,CACH,CAAC,CAACqG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAChBxK,YAAY,CAAC,KAAK,CAAC;kBACrB,CAAC,MAAM;oBACLtB,KAAK,CAACqG,KAAK,CACT,oDACF,CAAC;kBACH;gBACF,CAAE;gBACFQ,SAAS,EAAC,wDAAwD;gBAAAD,QAAA,EAEjEJ,kBAAkB,GAAG,aAAa,GAAG;cAAiB;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAAC1G,EAAA,CAp/BQD,iBAAiB;EAAA,QACPhB,WAAW,EACXD,WAAW,EACXF,WAAW,EA6DVC,WAAW,EAGTA,WAAW;AAAA;AAAAmM,EAAA,GAnExBjL,iBAAiB;AAs/B1B,eAAeA,iBAAiB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}