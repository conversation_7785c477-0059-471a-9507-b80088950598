{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/clients/ClientScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport { Link, useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { clientList, deleteClient } from \"../../redux/actions/clientActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport InputModel from \"../../components/InputModel\";\nimport { Tooltip } from \"react-tooltip\";\nimport \"react-tooltip/dist/react-tooltip.css\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { baseURLFile } from \"../../constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ClientScreen() {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [clientId, setClientId] = useState(\"\");\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listClient = useSelector(state => state.clientList);\n  const {\n    clients,\n    loading,\n    error,\n    pages\n  } = listClient;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(clientList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Clients\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \" uppercase font-semibold text-black  text-xs w-max\",\n            children: \"Gestion des clients\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/clients/add\",\n            className: \"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-6 h-6\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"M12 4.5v15m7.5-7.5h-15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), \"Ajouter\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex md:flex-row flex-col\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full px-1 py-1 flex flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"N\\xB0\",\n                type: \"text\",\n                placeholder: \"\",\n                value: code,\n                onChange: value => {\n                  setCode(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"Nom\",\n                type: \"text\",\n                placeholder: \"\",\n                value: firstName,\n                onChange: value => {\n                  setFirstName(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full px-1 py-1 flex flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"Pr\\xE9nom\",\n                type: \"text\",\n                placeholder: \"\",\n                value: lastName,\n                onChange: value => {\n                  setLastName(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"GSM\",\n                type: \"text\",\n                placeholder: \"\",\n                value: gsmPhone,\n                onChange: value => {\n                  setGsmPhone(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"md:w-1/3 w-full px-1 py-1 flex flex-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"N\\xB0 CIN\",\n                type: \"text\",\n                placeholder: \"\",\n                value: cinNumber,\n                onChange: value => {\n                  setCinNunber(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:py-2 md:flex mx-1 w-full\",\n              children: /*#__PURE__*/_jsxDEV(InputModel, {\n                label: \"N\\xB0 Permis\",\n                type: \"text\",\n                placeholder: \"\",\n                value: permiNumber,\n                onChange: value => {\n                  setPermiNumber(value.target.value);\n                },\n                error: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n          type: \"error\",\n          message: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-full overflow-x-auto mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full table-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-2 text-left  \",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" flex flex-row items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"N\\xB0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" flex flex-row items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Nom\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" flex flex-row items-center cursor-pointer\",\n                    children: [orderBy.includes(\"last_name\") ? orderBy.includes(\"-\") ? /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"size-5  mr-2\",\n                      src: sortDesc\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 226,\n                      columnNumber: 29\n                    }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"size-5   mr-2\",\n                      src: sortAsc\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 29\n                    }, this) : null, /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Pr\\xE9nom\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 231,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max  \",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \" flex flex-row items-center cursor-pointer\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"CIN\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: \"Permis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: \"Contrat\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: \"GSM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \",\n                  children: \"N\\xE9 le\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"py-4 px-4 font-bold text-black  text-xs w-max\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [clients === null || clients === void 0 ? void 0 : clients.map((client, id) => {\n                var _client$first_name, _client$last_name, _client$cin_number, _client$permi_number, _client$gsm_phone, _client$email, _client$date_birth;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[30px] border-b border-[#eee] py-2 px-4    \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: client.id\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$first_name = client.first_name) !== null && _client$first_name !== void 0 ? _client$first_name : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 267,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$last_name = client.last_name) !== null && _client$last_name !== void 0 ? _client$last_name : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$cin_number = client.cin_number) !== null && _client$cin_number !== void 0 ? _client$cin_number : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$permi_number = client.permi_number) !== null && _client$permi_number !== void 0 ? _client$permi_number : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: \"0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 287,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$gsm_phone = client.gsm_phone) !== null && _client$gsm_phone !== void 0 ? _client$gsm_phone : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$email = client.email) !== null && _client$email !== void 0 ? _client$email : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 295,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max\",\n                      children: (_client$date_birth = client.date_birth) !== null && _client$date_birth !== void 0 ? _client$date_birth : \"---\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 300,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"min-w-[120px] border-b border-[#eee] py-2 px-4  \",\n                    children: /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-black  text-xs w-max flex flex-row\",\n                      children: [/*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 update-class\",\n                        to: \"/clients/edit/\" + client.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 319,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 311,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"mx-1 delete-class\",\n                        onClick: () => {\n                          setEventType(\"delete\");\n                          setClientId(client.id);\n                          setIsDelete(true);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 343,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 335,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 327,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        className: \"mx-1 contrat-class\",\n                        to: \"/clients/contrat/\" + client.id,\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 363,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 355,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 351,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Link, {\n                        rel: \"noopener\",\n                        target: \"_blank\",\n                        className: \"mx-1 declaration-class\",\n                        to: baseURLFile + \"/api/clients/4/declaration/\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          xmlns: \"http://www.w3.org/2000/svg\",\n                          fill: \"none\",\n                          viewBox: \"0 0 24 24\",\n                          \"stroke-width\": \"1.5\",\n                          stroke: \"currentColor\",\n                          className: \"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            d: \"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 385,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 377,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 371,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 21\n                }, this);\n              }), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"h-11\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/_jsxDEV(Paginate, {\n              route: \"/clients?\",\n              search: \"\",\n              page: page,\n              pages: pages\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n}\n_s(ClientScreen, \"5jcGIIkFsFSp1rai84/e/IOak8U=\", false, function () {\n  return [useNavigate, useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = ClientScreen;\nexport default ClientScreen;\nvar _c;\n$RefreshReg$(_c, \"ClientScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "DefaultLayout", "Link", "useLocation", "useNavigate", "useSearchParams", "useDispatch", "useSelector", "clientList", "deleteClient", "Loader", "<PERSON><PERSON>", "Paginate", "InputModel", "<PERSON><PERSON><PERSON>", "ConfirmationModal", "baseURLFile", "jsxDEV", "_jsxDEV", "ClientScreen", "_s", "navigate", "searchParams", "page", "get", "dispatch", "isDelete", "setIsDelete", "loadEvent", "setLoadEvent", "eventType", "setEventType", "clientId", "setClientId", "userLogin", "state", "userInfo", "listClient", "clients", "loading", "error", "pages", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "label", "type", "placeholder", "value", "code", "onChange", "setCode", "target", "firstName", "setFirstName", "lastName", "setLastName", "gsmPhone", "setGsmPhone", "cinNumber", "setCinNunber", "permiNumber", "setPermiNumber", "message", "orderBy", "includes", "src", "sortDesc", "sortAsc", "map", "client", "id", "_client$first_name", "_client$last_name", "_client$cin_number", "_client$permi_number", "_client$gsm_phone", "_client$email", "_client$date_birth", "first_name", "last_name", "cin_number", "permi_number", "gsm_phone", "email", "date_birth", "onClick", "rel", "route", "search", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/clients/ClientScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport {\n  Link,\n  useLocation,\n  useNavigate,\n  useSearchParams,\n} from \"react-router-dom\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { clientList, deleteClient } from \"../../redux/actions/clientActions\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport InputModel from \"../../components/InputModel\";\n\nimport { Tooltip } from \"react-tooltip\";\nimport \"react-tooltip/dist/react-tooltip.css\";\nimport ConfirmationModal from \"../../components/ConfirmationModal\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction ClientScreen() {\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const [isDelete, setIsDelete] = useState(false);\n  const [loadEvent, setLoadEvent] = useState(false);\n  const [eventType, setEventType] = useState(\"\");\n  const [clientId, setClientId] = useState(\"\");\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listClient = useSelector((state) => state.clientList);\n  const { clients, loading, error, pages } = listClient;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(clientList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Clients</div>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default   dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Gestion des clients\n            </h4>\n            <Link\n              to={\"/clients/add\"}\n              className=\"rounded bg-primary text-white px-5 py-1 flex flex-row text-sm\"\n            >\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-6 h-6\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"M12 4.5v15m7.5-7.5h-15\"\n                />\n              </svg>\n              Ajouter\n            </Link>\n          </div>\n          {/* search */}\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N°\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={code}\n                  onChange={(value) => {\n                    setCode(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n              {/*  */}\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"Nom\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={firstName}\n                  onChange={(value) => {\n                    setFirstName(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"Prénom\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={lastName}\n                  onChange={(value) => {\n                    setLastName(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"GSM\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={gsmPhone}\n                  onChange={(value) => {\n                    setGsmPhone(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n            {/*  */}\n            <div className=\"md:w-1/3 w-full px-1 py-1 flex flex-row\">\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N° CIN\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={cinNumber}\n                  onChange={(value) => {\n                    setCinNunber(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n              <div className=\"md:py-2 md:flex mx-1 w-full\">\n                <InputModel\n                  label=\"N° Permis\"\n                  type=\"text\"\n                  placeholder=\"\"\n                  value={permiNumber}\n                  onChange={(value) => {\n                    setPermiNumber(value.target.value);\n                  }}\n                  error={\"\"}\n                />\n              </div>\n            </div>\n          </div>\n          {/* list */}\n          {loading ? (\n            <Loader />\n          ) : error ? (\n            <Alert type=\"error\" message={error} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left  \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      <div className=\" flex flex-row items-center cursor-pointer\">\n                        <span>N°</span>\n                      </div>\n                    </th>\n                    <th className=\"min-w-[100px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      <div className=\" flex flex-row items-center cursor-pointer\">\n                        <span>Nom</span>\n                      </div>\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max  \">\n                      <div className=\" flex flex-row items-center cursor-pointer\">\n                        {orderBy.includes(\"last_name\") ? (\n                          orderBy.includes(\"-\") ? (\n                            <img className=\"size-5  mr-2\" src={sortDesc} />\n                          ) : (\n                            <img className=\"size-5   mr-2\" src={sortAsc} />\n                          )\n                        ) : null}\n                        <span>Prénom</span>\n                      </div>\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max  \">\n                      <div className=\" flex flex-row items-center cursor-pointer\">\n                        <span>CIN</span>\n                      </div>\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Permis\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Contrat\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      GSM\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Email\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max \">\n                      Né le\n                    </th>\n                    <th className=\"py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {clients?.map((client, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4    \">\n                        <p className=\"text-black  text-xs w-max\">{client.id}</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.first_name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.last_name ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.cin_number ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.permi_number ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">0</p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.gsm_phone ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.email ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {client.date_birth ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* edit */}\n                          <Link\n                            className=\"mx-1 update-class\"\n                            to={\"/clients/edit/\" + client.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-primary rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* delete */}\n                          <button\n                            className=\"mx-1 delete-class\"\n                            onClick={() => {\n                              setEventType(\"delete\");\n                              setClientId(client.id);\n                              setIsDelete(true);\n                            }}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0\"\n                              />\n                            </svg>\n                          </button>\n                          {/* list contrat */}\n                          <Link\n                            className=\"mx-1 contrat-class\"\n                            to={\"/clients/contrat/\" + client.id}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* declaration */}\n                          <Link\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            className=\"mx-1 declaration-class\"\n                            to={baseURLFile + \"/api/clients/4/declaration/\"}\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-body rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                strokeLinecap=\"round\"\n                                strokeLinejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/clients?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default ClientScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,aAAa,MAAM,6BAA6B;AACvD,SACEC,IAAI,EACJC,WAAW,EACXC,WAAW,EACXC,eAAe,QACV,kBAAkB;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,UAAU,EAAEC,YAAY,QAAQ,mCAAmC;AAC5E,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,UAAU,MAAM,6BAA6B;AAEpD,SAASC,OAAO,QAAQ,eAAe;AACvC,OAAO,sCAAsC;AAC7C,OAAOC,iBAAiB,MAAM,oCAAoC;AAClE,SAASC,WAAW,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EACtB,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkB,YAAY,CAAC,GAAGjB,eAAe,CAAC,CAAC;EACxC,MAAMkB,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,QAAQ,EAAEC,WAAW,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAMkC,SAAS,GAAG3B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,UAAU,GAAG9B,WAAW,CAAE4B,KAAK,IAAKA,KAAK,CAAC3B,UAAU,CAAC;EAC3D,MAAM;IAAE8B,OAAO;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAM,CAAC,GAAGJ,UAAU;EAErD,MAAMK,QAAQ,GAAG,GAAG;EAEpB3C,SAAS,CAAC,MAAM;IACd,IAAI,CAACqC,QAAQ,EAAE;MACbf,QAAQ,CAACqB,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLjB,QAAQ,CAACjB,UAAU,CAACe,IAAI,CAAC,CAAC;IAC5B;EACF,CAAC,EAAE,CAACF,QAAQ,EAAEe,QAAQ,EAAEX,QAAQ,EAAEF,IAAI,CAAC,CAAC;EAExC,oBACEL,OAAA,CAACjB,aAAa;IAAA0C,QAAA,eACZzB,OAAA;MAAAyB,QAAA,gBACEzB,OAAA;QAAK0B,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtDzB,OAAA;UAAG2B,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClBzB,OAAA;YAAK0B,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5DzB,OAAA;cACE4B,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBzB,OAAA;gBACEgC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAM0B,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJtC,OAAA;UAAAyB,QAAA,eACEzB,OAAA;YACE4B,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnBzB,OAAA;cACEgC,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPtC,OAAA;UAAK0B,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5B,CAAC,eAENtC,OAAA;QAAK0B,SAAS,EAAC,8GAA8G;QAAAD,QAAA,gBAC3HzB,OAAA;UAAK0B,SAAS,EAAC,kDAAkD;UAAAD,QAAA,gBAC/DzB,OAAA;YAAI0B,SAAS,EAAC,oDAAoD;YAAAD,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtC,OAAA,CAAChB,IAAI;YACHuD,EAAE,EAAE,cAAe;YACnBb,SAAS,EAAC,+DAA+D;YAAAD,QAAA,gBAEzEzB,OAAA;cACE4B,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnBzB,OAAA;gBACEgC,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,WAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENtC,OAAA;UAAK0B,SAAS,EAAC,2BAA2B;UAAAD,QAAA,gBACxCzB,OAAA;YAAK0B,SAAS,EAAC,yCAAyC;YAAAD,QAAA,gBACtDzB,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,OAAI;gBACVC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAEC,IAAK;gBACZC,QAAQ,EAAGF,KAAK,IAAK;kBACnBG,OAAO,CAACH,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBAC7B,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtC,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,KAAK;gBACXC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAEK,SAAU;gBACjBH,QAAQ,EAAGF,KAAK,IAAK;kBACnBM,YAAY,CAACN,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBAClC,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAK0B,SAAS,EAAC,yCAAyC;YAAAD,QAAA,gBACtDzB,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,WAAQ;gBACdC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAEO,QAAS;gBAChBL,QAAQ,EAAGF,KAAK,IAAK;kBACnBQ,WAAW,CAACR,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBACjC,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENtC,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,KAAK;gBACXC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAES,QAAS;gBAChBP,QAAQ,EAAGF,KAAK,IAAK;kBACnBU,WAAW,CAACV,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBACjC,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtC,OAAA;YAAK0B,SAAS,EAAC,yCAAyC;YAAAD,QAAA,gBACtDzB,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,WAAQ;gBACdC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAEW,SAAU;gBACjBT,QAAQ,EAAGF,KAAK,IAAK;kBACnBY,YAAY,CAACZ,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBAClC,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtC,OAAA;cAAK0B,SAAS,EAAC,6BAA6B;cAAAD,QAAA,eAC1CzB,OAAA,CAACL,UAAU;gBACT6C,KAAK,EAAC,cAAW;gBACjBC,IAAI,EAAC,MAAM;gBACXC,WAAW,EAAC,EAAE;gBACdC,KAAK,EAAEa,WAAY;gBACnBX,QAAQ,EAAGF,KAAK,IAAK;kBACnBc,cAAc,CAACd,KAAK,CAACI,MAAM,CAACJ,KAAK,CAAC;gBACpC,CAAE;gBACFrB,KAAK,EAAE;cAAG;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELjB,OAAO,gBACNrB,OAAA,CAACR,MAAM;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,GACRhB,KAAK,gBACPtB,OAAA,CAACP,KAAK;UAACgD,IAAI,EAAC,OAAO;UAACiB,OAAO,EAAEpC;QAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEtCtC,OAAA;UAAK0B,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC9CzB,OAAA;YAAO0B,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAClCzB,OAAA;cAAAyB,QAAA,eACEzB,OAAA;gBAAI0B,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,gBACnCzB,OAAA;kBAAI0B,SAAS,EAAC,4DAA4D;kBAAAD,QAAA,eACxEzB,OAAA;oBAAK0B,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,eACzDzB,OAAA;sBAAAyB,QAAA,EAAM;oBAAE;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,eAC1EzB,OAAA;oBAAK0B,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,eACzDzB,OAAA;sBAAAyB,QAAA,EAAM;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,+DAA+D;kBAAAD,QAAA,eAC3EzB,OAAA;oBAAK0B,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,GACxDkC,OAAO,CAACC,QAAQ,CAAC,WAAW,CAAC,GAC5BD,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,gBACnB5D,OAAA;sBAAK0B,SAAS,EAAC,cAAc;sBAACmC,GAAG,EAAEC;oBAAS;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE/CtC,OAAA;sBAAK0B,SAAS,EAAC,eAAe;sBAACmC,GAAG,EAAEE;oBAAQ;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC/C,GACC,IAAI,eACRtC,OAAA;sBAAAyB,QAAA,EAAM;oBAAM;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,+DAA+D;kBAAAD,QAAA,eAC3EzB,OAAA;oBAAK0B,SAAS,EAAC,4CAA4C;oBAAAD,QAAA,eACzDzB,OAAA;sBAAAyB,QAAA,EAAM;oBAAG;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EAAC;gBAE7E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EAAC;gBAE7E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EAAC;gBAE7E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EAAC;gBAE7E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,8DAA8D;kBAAAD,QAAA,EAAC;gBAE7E;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtC,OAAA;kBAAI0B,SAAS,EAAC,+CAA+C;kBAAAD,QAAA,EAAC;gBAE9D;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAERtC,OAAA;cAAAyB,QAAA,GACGL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4C,GAAG,CAAC,CAACC,MAAM,EAAEC,EAAE;gBAAA,IAAAC,kBAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,iBAAA,EAAAC,aAAA,EAAAC,kBAAA;gBAAA,oBACvBzE,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA;oBAAI0B,SAAS,EAAC,mDAAmD;oBAAAD,QAAA,eAC/DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EAAEwC,MAAM,CAACC;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA0C,kBAAA,GACrCF,MAAM,CAACS,UAAU,cAAAP,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA2C,iBAAA,GACrCH,MAAM,CAACU,SAAS,cAAAP,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA4C,kBAAA,GACrCJ,MAAM,CAACW,UAAU,cAAAP,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA6C,oBAAA,GACrCL,MAAM,CAACY,YAAY,cAAAP,oBAAA,cAAAA,oBAAA,GAAI;oBAAK;sBAAAnC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,EAAC;oBAAC;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA8C,iBAAA,GACrCN,MAAM,CAACa,SAAS,cAAAP,iBAAA,cAAAA,iBAAA,GAAI;oBAAK;sBAAApC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAA+C,aAAA,GACrCP,MAAM,CAACc,KAAK,cAAAP,aAAA,cAAAA,aAAA,GAAI;oBAAK;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,2BAA2B;sBAAAD,QAAA,GAAAgD,kBAAA,GACrCR,MAAM,CAACe,UAAU,cAAAP,kBAAA,cAAAA,kBAAA,GAAI;oBAAK;sBAAAtC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eACLtC,OAAA;oBAAI0B,SAAS,EAAC,kDAAkD;oBAAAD,QAAA,eAC9DzB,OAAA;sBAAG0B,SAAS,EAAC,yCAAyC;sBAAAD,QAAA,gBAEpDzB,OAAA,CAAChB,IAAI;wBACH0C,SAAS,EAAC,mBAAmB;wBAC7Ba,EAAE,EAAE,gBAAgB,GAAG0B,MAAM,CAACC,EAAG;wBAAAzC,QAAA,eAEjCzB,OAAA;0BACE4B,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,+DAA+D;0BAAAD,QAAA,eAEzEzB,OAAA;4BACEgC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAAkQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPtC,OAAA;wBACE0B,SAAS,EAAC,mBAAmB;wBAC7BuD,OAAO,EAAEA,CAAA,KAAM;0BACbpE,YAAY,CAAC,QAAQ,CAAC;0BACtBE,WAAW,CAACkD,MAAM,CAACC,EAAE,CAAC;0BACtBzD,WAAW,CAAC,IAAI,CAAC;wBACnB,CAAE;wBAAAgB,QAAA,eAEFzB,OAAA;0BACE4B,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,8DAA8D;0BAAAD,QAAA,eAExEzB,OAAA;4BACEgC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAA+Z;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACla;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eAETtC,OAAA,CAAChB,IAAI;wBACH0C,SAAS,EAAC,oBAAoB;wBAC9Ba,EAAE,EAAE,mBAAmB,GAAG0B,MAAM,CAACC,EAAG;wBAAAzC,QAAA,eAEpCzB,OAAA;0BACE4B,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,4DAA4D;0BAAAD,QAAA,eAEtEzB,OAAA;4BACEgC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAAslB;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzlB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEPtC,OAAA,CAAChB,IAAI;wBACHkG,GAAG,EAAC,UAAU;wBACdnC,MAAM,EAAC,QAAQ;wBACfrB,SAAS,EAAC,wBAAwB;wBAClCa,EAAE,EAAEzC,WAAW,GAAG,6BAA8B;wBAAA2B,QAAA,eAEhDzB,OAAA;0BACE4B,KAAK,EAAC,4BAA4B;0BAClCC,IAAI,EAAC,MAAM;0BACXC,OAAO,EAAC,WAAW;0BACnB,gBAAa,KAAK;0BAClBC,MAAM,EAAC,cAAc;0BACrBL,SAAS,EAAC,4DAA4D;0BAAAD,QAAA,eAEtEzB,OAAA;4BACEgC,aAAa,EAAC,OAAO;4BACrBC,cAAc,EAAC,OAAO;4BACtBC,CAAC,EAAC;0BAAmQ;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA,CACN,CAAC,eACFtC,OAAA;gBAAI0B,SAAS,EAAC;cAAM;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACRtC,OAAA;YAAK0B,SAAS,EAAC,EAAE;YAAAD,QAAA,eACfzB,OAAA,CAACN,QAAQ;cACPyF,KAAK,EAAE,WAAY;cACnBC,MAAM,EAAE,EAAG;cACX/E,IAAI,EAAEA,IAAK;cACXkB,KAAK,EAAEA;YAAM;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACpC,EAAA,CAxYQD,YAAY;EAAA,QACFf,WAAW,EACLC,eAAe,EAErBC,WAAW,EAOVC,WAAW,EAGVA,WAAW;AAAA;AAAAgG,EAAA,GAdvBpF,YAAY;AA0YrB,eAAeA,YAAY;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}