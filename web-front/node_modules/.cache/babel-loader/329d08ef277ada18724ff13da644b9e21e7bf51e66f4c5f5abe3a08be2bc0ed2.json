{"ast": null, "code": "import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline(_ref, ctx) {\n  let {\n    positions,\n    ...options\n  } = _ref;\n  const polyline = new LeafletPolyline(positions, options);\n  return createElementObject(polyline, extendContext(ctx, {\n    overlayContainer: polyline\n  }));\n}, function updatePolyline(layer, props, prevProps) {\n  if (props.positions !== prevProps.positions) {\n    layer.setLatLngs(props.positions);\n  }\n});", "map": {"version": 3, "names": ["createElementObject", "createPathComponent", "extendContext", "Polyline", "LeafletPolyline", "createPolyline", "_ref", "ctx", "positions", "options", "polyline", "overlayContainer", "updatePolyline", "layer", "props", "prevProps", "setLatLngs"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/react-leaflet/lib/Polyline.js"], "sourcesContent": ["import { createElementObject, createPathComponent, extendContext } from '@react-leaflet/core';\nimport { Polyline as LeafletPolyline } from 'leaflet';\nexport const Polyline = createPathComponent(function createPolyline({ positions , ...options }, ctx) {\n    const polyline = new LeafletPolyline(positions, options);\n    return createElementObject(polyline, extendContext(ctx, {\n        overlayContainer: polyline\n    }));\n}, function updatePolyline(layer, props, prevProps) {\n    if (props.positions !== prevProps.positions) {\n        layer.setLatLngs(props.positions);\n    }\n});\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,mBAAmB,EAAEC,aAAa,QAAQ,qBAAqB;AAC7F,SAASC,QAAQ,IAAIC,eAAe,QAAQ,SAAS;AACrD,OAAO,MAAMD,QAAQ,GAAGF,mBAAmB,CAAC,SAASI,cAAcA,CAAAC,IAAA,EAA6BC,GAAG,EAAE;EAAA,IAAjC;IAAEC,SAAS;IAAG,GAAGC;EAAQ,CAAC,GAAAH,IAAA;EAC1F,MAAMI,QAAQ,GAAG,IAAIN,eAAe,CAACI,SAAS,EAAEC,OAAO,CAAC;EACxD,OAAOT,mBAAmB,CAACU,QAAQ,EAAER,aAAa,CAACK,GAAG,EAAE;IACpDI,gBAAgB,EAAED;EACtB,CAAC,CAAC,CAAC;AACP,CAAC,EAAE,SAASE,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAChD,IAAID,KAAK,CAACN,SAAS,KAAKO,SAAS,CAACP,SAAS,EAAE;IACzCK,KAAK,CAACG,UAAU,CAACF,KAAK,CAACN,SAAS,CAAC;EACrC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}