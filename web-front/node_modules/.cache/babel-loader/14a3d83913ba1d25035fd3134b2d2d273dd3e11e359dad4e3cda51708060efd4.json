{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar crossStr = String.fromCharCode(215);\nvar RemoveComponent = function RemoveComponent(props) {\n  var readOnly = props.readOnly,\n    removeComponent = props.removeComponent,\n    onRemove = props.onRemove,\n    className = props.className,\n    tag = props.tag,\n    index = props.index;\n  var onKeydown = function onKeydown(event) {\n    if (_constants.KEYS.ENTER.includes(event.keyCode) || event.keyCode === _constants.KEYS.SPACE) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    if (event.keyCode === _constants.KEYS.BACKSPACE) {\n      onRemove(event);\n    }\n  };\n  if (readOnly) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"span\", null);\n  }\n  var ariaLabel = \"Tag at index \".concat(index, \" with value \").concat(tag.id, \" focussed. Press backspace to remove\");\n  if (removeComponent) {\n    var Component = removeComponent;\n    return /*#__PURE__*/_react[\"default\"].createElement(Component, {\n      onRemove: onRemove,\n      onKeyDown: onKeydown,\n      className: className,\n      \"aria-label\": ariaLabel,\n      tag: tag,\n      index: index\n    });\n  }\n  return /*#__PURE__*/_react[\"default\"].createElement(\"button\", {\n    onClick: onRemove,\n    onKeyDown: onKeydown,\n    className: className,\n    type: \"button\",\n    \"aria-label\": ariaLabel\n  }, crossStr);\n};\nRemoveComponent.propTypes = {\n  className: _propTypes[\"default\"].string,\n  onRemove: _propTypes[\"default\"].func.isRequired,\n  readOnly: _propTypes[\"default\"].bool,\n  removeComponent: _propTypes[\"default\"].func,\n  tag: _propTypes[\"default\"].shape({\n    id: _propTypes[\"default\"].string.isRequired,\n    className: _propTypes[\"default\"].string,\n    key: _propTypes[\"default\"].string\n  }),\n  index: _propTypes[\"default\"].number.isRequired\n};\nvar _default = exports[\"default\"] = RemoveComponent;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_react", "_interopRequireDefault", "require", "_propTypes", "_constants", "obj", "__esModule", "crossStr", "String", "fromCharCode", "RemoveComponent", "props", "readOnly", "removeComponent", "onRemove", "className", "tag", "index", "onKeydown", "event", "KEYS", "ENTER", "includes", "keyCode", "SPACE", "preventDefault", "stopPropagation", "BACKSPACE", "createElement", "aria<PERSON><PERSON><PERSON>", "concat", "id", "Component", "onKeyDown", "onClick", "type", "propTypes", "string", "func", "isRequired", "bool", "shape", "key", "number", "_default"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/react-tag-input/dist-modules/components/RemoveComponent.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _constants = require(\"./constants\");\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { \"default\": obj }; }\nvar crossStr = String.fromCharCode(215);\nvar RemoveComponent = function RemoveComponent(props) {\n  var readOnly = props.readOnly,\n    removeComponent = props.removeComponent,\n    onRemove = props.onRemove,\n    className = props.className,\n    tag = props.tag,\n    index = props.index;\n  var onKeydown = function onKeydown(event) {\n    if (_constants.KEYS.ENTER.includes(event.keyCode) || event.keyCode === _constants.KEYS.SPACE) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    if (event.keyCode === _constants.KEYS.BACKSPACE) {\n      onRemove(event);\n    }\n  };\n  if (readOnly) {\n    return /*#__PURE__*/_react[\"default\"].createElement(\"span\", null);\n  }\n  var ariaLabel = \"Tag at index \".concat(index, \" with value \").concat(tag.id, \" focussed. Press backspace to remove\");\n  if (removeComponent) {\n    var Component = removeComponent;\n    return /*#__PURE__*/_react[\"default\"].createElement(Component, {\n      onRemove: onRemove,\n      onKeyDown: onKeydown,\n      className: className,\n      \"aria-label\": ariaLabel,\n      tag: tag,\n      index: index\n    });\n  }\n  return /*#__PURE__*/_react[\"default\"].createElement(\"button\", {\n    onClick: onRemove,\n    onKeyDown: onKeydown,\n    className: className,\n    type: \"button\",\n    \"aria-label\": ariaLabel\n  }, crossStr);\n};\nRemoveComponent.propTypes = {\n  className: _propTypes[\"default\"].string,\n  onRemove: _propTypes[\"default\"].func.isRequired,\n  readOnly: _propTypes[\"default\"].bool,\n  removeComponent: _propTypes[\"default\"].func,\n  tag: _propTypes[\"default\"].shape({\n    id: _propTypes[\"default\"].string.isRequired,\n    className: _propTypes[\"default\"].string,\n    key: _propTypes[\"default\"].string\n  }),\n  index: _propTypes[\"default\"].number.isRequired\n};\nvar _default = exports[\"default\"] = RemoveComponent;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC;AAC3B,IAAIE,MAAM,GAAGC,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIC,UAAU,GAAGF,sBAAsB,CAACC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC9D,IAAIE,UAAU,GAAGF,OAAO,CAAC,aAAa,CAAC;AACvC,SAASD,sBAAsBA,CAACI,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAAE;AAChG,IAAIE,QAAQ,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC;AACvC,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAE;EACpD,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAC3BC,eAAe,GAAGF,KAAK,CAACE,eAAe;IACvCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,GAAG,GAAGL,KAAK,CAACK,GAAG;IACfC,KAAK,GAAGN,KAAK,CAACM,KAAK;EACrB,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACC,KAAK,EAAE;IACxC,IAAIf,UAAU,CAACgB,IAAI,CAACC,KAAK,CAACC,QAAQ,CAACH,KAAK,CAACI,OAAO,CAAC,IAAIJ,KAAK,CAACI,OAAO,KAAKnB,UAAU,CAACgB,IAAI,CAACI,KAAK,EAAE;MAC5FL,KAAK,CAACM,cAAc,CAAC,CAAC;MACtBN,KAAK,CAACO,eAAe,CAAC,CAAC;MACvB;IACF;IACA,IAAIP,KAAK,CAACI,OAAO,KAAKnB,UAAU,CAACgB,IAAI,CAACO,SAAS,EAAE;MAC/Cb,QAAQ,CAACK,KAAK,CAAC;IACjB;EACF,CAAC;EACD,IAAIP,QAAQ,EAAE;IACZ,OAAO,aAAaZ,MAAM,CAAC,SAAS,CAAC,CAAC4B,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;EACnE;EACA,IAAIC,SAAS,GAAG,eAAe,CAACC,MAAM,CAACb,KAAK,EAAE,cAAc,CAAC,CAACa,MAAM,CAACd,GAAG,CAACe,EAAE,EAAE,sCAAsC,CAAC;EACpH,IAAIlB,eAAe,EAAE;IACnB,IAAImB,SAAS,GAAGnB,eAAe;IAC/B,OAAO,aAAab,MAAM,CAAC,SAAS,CAAC,CAAC4B,aAAa,CAACI,SAAS,EAAE;MAC7DlB,QAAQ,EAAEA,QAAQ;MAClBmB,SAAS,EAAEf,SAAS;MACpBH,SAAS,EAAEA,SAAS;MACpB,YAAY,EAAEc,SAAS;MACvBb,GAAG,EAAEA,GAAG;MACRC,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,OAAO,aAAajB,MAAM,CAAC,SAAS,CAAC,CAAC4B,aAAa,CAAC,QAAQ,EAAE;IAC5DM,OAAO,EAAEpB,QAAQ;IACjBmB,SAAS,EAAEf,SAAS;IACpBH,SAAS,EAAEA,SAAS;IACpBoB,IAAI,EAAE,QAAQ;IACd,YAAY,EAAEN;EAChB,CAAC,EAAEtB,QAAQ,CAAC;AACd,CAAC;AACDG,eAAe,CAAC0B,SAAS,GAAG;EAC1BrB,SAAS,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACkC,MAAM;EACvCvB,QAAQ,EAAEX,UAAU,CAAC,SAAS,CAAC,CAACmC,IAAI,CAACC,UAAU;EAC/C3B,QAAQ,EAAET,UAAU,CAAC,SAAS,CAAC,CAACqC,IAAI;EACpC3B,eAAe,EAAEV,UAAU,CAAC,SAAS,CAAC,CAACmC,IAAI;EAC3CtB,GAAG,EAAEb,UAAU,CAAC,SAAS,CAAC,CAACsC,KAAK,CAAC;IAC/BV,EAAE,EAAE5B,UAAU,CAAC,SAAS,CAAC,CAACkC,MAAM,CAACE,UAAU;IAC3CxB,SAAS,EAAEZ,UAAU,CAAC,SAAS,CAAC,CAACkC,MAAM;IACvCK,GAAG,EAAEvC,UAAU,CAAC,SAAS,CAAC,CAACkC;EAC7B,CAAC,CAAC;EACFpB,KAAK,EAAEd,UAAU,CAAC,SAAS,CAAC,CAACwC,MAAM,CAACJ;AACtC,CAAC;AACD,IAAIK,QAAQ,GAAG9C,OAAO,CAAC,SAAS,CAAC,GAAGY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}