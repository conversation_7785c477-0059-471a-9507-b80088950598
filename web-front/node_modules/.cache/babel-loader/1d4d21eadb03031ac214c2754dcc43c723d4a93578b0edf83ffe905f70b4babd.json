{"ast": null, "code": "import { scale, alpha } from '../../../value/types/numbers/index.mjs';\nimport { px, degrees, progressPercentage } from '../../../value/types/numbers/units.mjs';\nimport { int } from './type-int.mjs';\nconst numberValueTypes = {\n  // Border props\n  borderWidth: px,\n  borderTopWidth: px,\n  borderRightWidth: px,\n  borderBottomWidth: px,\n  borderLeftWidth: px,\n  borderRadius: px,\n  radius: px,\n  borderTopLeftRadius: px,\n  borderTopRightRadius: px,\n  borderBottomRightRadius: px,\n  borderBottomLeftRadius: px,\n  // Positioning props\n  width: px,\n  maxWidth: px,\n  height: px,\n  maxHeight: px,\n  size: px,\n  top: px,\n  right: px,\n  bottom: px,\n  left: px,\n  // Spacing props\n  padding: px,\n  paddingTop: px,\n  paddingRight: px,\n  paddingBottom: px,\n  paddingLeft: px,\n  margin: px,\n  marginTop: px,\n  marginRight: px,\n  marginBottom: px,\n  marginLeft: px,\n  // Transform props\n  rotate: degrees,\n  rotateX: degrees,\n  rotateY: degrees,\n  rotateZ: degrees,\n  scale,\n  scaleX: scale,\n  scaleY: scale,\n  scaleZ: scale,\n  skew: degrees,\n  skewX: degrees,\n  skewY: degrees,\n  distance: px,\n  translateX: px,\n  translateY: px,\n  translateZ: px,\n  x: px,\n  y: px,\n  z: px,\n  perspective: px,\n  transformPerspective: px,\n  opacity: alpha,\n  originX: progressPercentage,\n  originY: progressPercentage,\n  originZ: px,\n  // Misc\n  zIndex: int,\n  backgroundPositionX: px,\n  backgroundPositionY: px,\n  // SVG\n  fillOpacity: alpha,\n  strokeOpacity: alpha,\n  numOctaves: int\n};\nexport { numberValueTypes };", "map": {"version": 3, "names": ["scale", "alpha", "px", "degrees", "progressPercentage", "int", "numberValueTypes", "borderWidth", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "borderRadius", "radius", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "width", "max<PERSON><PERSON><PERSON>", "height", "maxHeight", "size", "top", "right", "bottom", "left", "padding", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "margin", "marginTop", "marginRight", "marginBottom", "marginLeft", "rotate", "rotateX", "rotateY", "rotateZ", "scaleX", "scaleY", "scaleZ", "skew", "skewX", "skewY", "distance", "translateX", "translateY", "translateZ", "x", "y", "z", "perspective", "transformPerspective", "opacity", "originX", "originY", "originZ", "zIndex", "backgroundPositionX", "backgroundPositionY", "fillOpacity", "strokeOpacity", "numOctaves"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/framer-motion/dist/es/render/dom/value-types/number.mjs"], "sourcesContent": ["import { scale, alpha } from '../../../value/types/numbers/index.mjs';\nimport { px, degrees, progressPercentage } from '../../../value/types/numbers/units.mjs';\nimport { int } from './type-int.mjs';\n\nconst numberValueTypes = {\n    // Border props\n    borderWidth: px,\n    borderTopWidth: px,\n    borderRightWidth: px,\n    borderBottomWidth: px,\n    borderLeftWidth: px,\n    borderRadius: px,\n    radius: px,\n    borderTopLeftRadius: px,\n    borderTopRightRadius: px,\n    borderBottomRightRadius: px,\n    borderBottomLeftRadius: px,\n    // Positioning props\n    width: px,\n    maxWidth: px,\n    height: px,\n    maxHeight: px,\n    size: px,\n    top: px,\n    right: px,\n    bottom: px,\n    left: px,\n    // Spacing props\n    padding: px,\n    paddingTop: px,\n    paddingRight: px,\n    paddingBottom: px,\n    paddingLeft: px,\n    margin: px,\n    marginTop: px,\n    marginRight: px,\n    marginBottom: px,\n    marginLeft: px,\n    // Transform props\n    rotate: degrees,\n    rotateX: degrees,\n    rotateY: degrees,\n    rotateZ: degrees,\n    scale,\n    scaleX: scale,\n    scaleY: scale,\n    scaleZ: scale,\n    skew: degrees,\n    skewX: degrees,\n    skewY: degrees,\n    distance: px,\n    translateX: px,\n    translateY: px,\n    translateZ: px,\n    x: px,\n    y: px,\n    z: px,\n    perspective: px,\n    transformPerspective: px,\n    opacity: alpha,\n    originX: progressPercentage,\n    originY: progressPercentage,\n    originZ: px,\n    // Misc\n    zIndex: int,\n    backgroundPositionX: px,\n    backgroundPositionY: px,\n    // SVG\n    fillOpacity: alpha,\n    strokeOpacity: alpha,\n    numOctaves: int,\n};\n\nexport { numberValueTypes };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,KAAK,QAAQ,wCAAwC;AACrE,SAASC,EAAE,EAAEC,OAAO,EAAEC,kBAAkB,QAAQ,wCAAwC;AACxF,SAASC,GAAG,QAAQ,gBAAgB;AAEpC,MAAMC,gBAAgB,GAAG;EACrB;EACAC,WAAW,EAAEL,EAAE;EACfM,cAAc,EAAEN,EAAE;EAClBO,gBAAgB,EAAEP,EAAE;EACpBQ,iBAAiB,EAAER,EAAE;EACrBS,eAAe,EAAET,EAAE;EACnBU,YAAY,EAAEV,EAAE;EAChBW,MAAM,EAAEX,EAAE;EACVY,mBAAmB,EAAEZ,EAAE;EACvBa,oBAAoB,EAAEb,EAAE;EACxBc,uBAAuB,EAAEd,EAAE;EAC3Be,sBAAsB,EAAEf,EAAE;EAC1B;EACAgB,KAAK,EAAEhB,EAAE;EACTiB,QAAQ,EAAEjB,EAAE;EACZkB,MAAM,EAAElB,EAAE;EACVmB,SAAS,EAAEnB,EAAE;EACboB,IAAI,EAAEpB,EAAE;EACRqB,GAAG,EAAErB,EAAE;EACPsB,KAAK,EAAEtB,EAAE;EACTuB,MAAM,EAAEvB,EAAE;EACVwB,IAAI,EAAExB,EAAE;EACR;EACAyB,OAAO,EAAEzB,EAAE;EACX0B,UAAU,EAAE1B,EAAE;EACd2B,YAAY,EAAE3B,EAAE;EAChB4B,aAAa,EAAE5B,EAAE;EACjB6B,WAAW,EAAE7B,EAAE;EACf8B,MAAM,EAAE9B,EAAE;EACV+B,SAAS,EAAE/B,EAAE;EACbgC,WAAW,EAAEhC,EAAE;EACfiC,YAAY,EAAEjC,EAAE;EAChBkC,UAAU,EAAElC,EAAE;EACd;EACAmC,MAAM,EAAElC,OAAO;EACfmC,OAAO,EAAEnC,OAAO;EAChBoC,OAAO,EAAEpC,OAAO;EAChBqC,OAAO,EAAErC,OAAO;EAChBH,KAAK;EACLyC,MAAM,EAAEzC,KAAK;EACb0C,MAAM,EAAE1C,KAAK;EACb2C,MAAM,EAAE3C,KAAK;EACb4C,IAAI,EAAEzC,OAAO;EACb0C,KAAK,EAAE1C,OAAO;EACd2C,KAAK,EAAE3C,OAAO;EACd4C,QAAQ,EAAE7C,EAAE;EACZ8C,UAAU,EAAE9C,EAAE;EACd+C,UAAU,EAAE/C,EAAE;EACdgD,UAAU,EAAEhD,EAAE;EACdiD,CAAC,EAAEjD,EAAE;EACLkD,CAAC,EAAElD,EAAE;EACLmD,CAAC,EAAEnD,EAAE;EACLoD,WAAW,EAAEpD,EAAE;EACfqD,oBAAoB,EAAErD,EAAE;EACxBsD,OAAO,EAAEvD,KAAK;EACdwD,OAAO,EAAErD,kBAAkB;EAC3BsD,OAAO,EAAEtD,kBAAkB;EAC3BuD,OAAO,EAAEzD,EAAE;EACX;EACA0D,MAAM,EAAEvD,GAAG;EACXwD,mBAAmB,EAAE3D,EAAE;EACvB4D,mBAAmB,EAAE5D,EAAE;EACvB;EACA6D,WAAW,EAAE9D,KAAK;EAClB+D,aAAa,EAAE/D,KAAK;EACpBgE,UAAU,EAAE5D;AAChB,CAAC;AAED,SAASC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}