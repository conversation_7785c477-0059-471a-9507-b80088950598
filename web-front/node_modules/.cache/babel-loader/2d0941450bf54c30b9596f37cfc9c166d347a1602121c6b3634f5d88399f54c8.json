{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/Mohssin <PERSON>/UNIMEDCARE/web-front/src/components/CaseHistory.js\";\nimport React from \"react\";\nimport Loader from \"./Loader\";\nimport Alert from \"./Alert\";\nimport Paginate from \"./Paginate\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CaseHistory({\n  historyData,\n  loading,\n  error\n}) {\n  const history = (historyData === null || historyData === void 0 ? void 0 : historyData.history) || [];\n  const page = (historyData === null || historyData === void 0 ? void 0 : historyData.page) || 1;\n  const pages = (historyData === null || historyData === void 0 ? void 0 : historyData.pages) || 1;\n  const count = (historyData === null || historyData === void 0 ? void 0 : historyData.count) || 0;\n\n  // Function to format date\n  const formatDate = dateString => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  // Function to get action color and icon\n  const getActionStyles = action => {\n    switch (action) {\n      case \"Created\":\n        return {\n          color: \"bg-green-100 text-green-800\",\n          icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5 text-green-600\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)\n        };\n      case \"Modified\":\n        return {\n          color: \"bg-blue-100 text-blue-800\",\n          icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5 text-blue-600\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        };\n      case \"Deleted\":\n        return {\n          color: \"bg-red-100 text-red-800\",\n          icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5 text-red-600\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        };\n      default:\n        return {\n          color: \"bg-gray-100 text-gray-800\",\n          icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            className: \"h-5 w-5 text-gray-600\",\n            viewBox: \"0 0 20 20\",\n            fill: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              fillRule: \"evenodd\",\n              d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n              clipRule: \"evenodd\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        };\n    }\n  };\n\n  // Function to render changes\n  const renderChanges = changes => {\n    if (!changes || Object.keys(changes).length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-500\",\n        children: \"No changes detected\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 14\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3 space-y-3\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto rounded-lg border\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Field\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Previous Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                scope: \"col\",\n                className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"New Value\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: Object.keys(changes).map(key => {\n              const change = changes[key];\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"hover:bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-3 text-sm font-medium text-gray-900 break-words\",\n                  children: change.field_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-3 text-sm text-gray-500 break-words\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-red-600\",\n                    children: renderValue(change.old)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 102,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-4 py-3 text-sm text-gray-500 break-words\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-green-600\",\n                    children: renderValue(change.new)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 21\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Function to render values (handling objects)\n  const renderValue = value => {\n    if (value === null || value === undefined) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-400 italic\",\n        children: \"None\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 14\n      }, this);\n    }\n    if (typeof value === \"object\" && value !== null) {\n      if (value.name) {\n        return value.name;\n      } else if (value.full_name) {\n        return value.full_name;\n      } else if (value.id) {\n        return `ID: ${value.id}`;\n      } else {\n        return JSON.stringify(value);\n      }\n    }\n    return String(value);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"my-3 mx-2 bg-white shadow-2 py-3 sm:py-5 px-3 sm:px-5 rounded\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-0\",\n        children: \"Case History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 9\n      }, this), pages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs sm:text-sm text-gray-500\",\n        children: [\"Showing \", history.length, \" of \", count, \" records\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      type: \"error\",\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this) : history && history.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute left-4 md:left-8 top-0 bottom-0 w-0.5 bg-gray-200 hidden sm:block\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: history.map(record => {\n            var _record$user, _record$user2;\n            const actionStyle = getActionStyles(record.action);\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative pl-6 sm:pl-16\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute left-0 sm:left-6 top-0 sm:-translate-x-1/2 w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 sm:border-4 border-white bg-[#0388A6] shadow\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow p-3 sm:p-5\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex flex-col sm:flex-row justify-between mb-3 sm:mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex flex-col sm:flex-row sm:items-center mb-2 sm:mb-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${actionStyle.color} mr-2 mb-1 sm:mb-0 w-fit`,\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"mr-1\",\n                        children: actionStyle.icon\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 29\n                      }, this), record.action]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-500 text-xs sm:text-sm\",\n                      children: formatDate(record.date)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center text-xs sm:text-sm text-gray-700\",\n                      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                        xmlns: \"http://www.w3.org/2000/svg\",\n                        className: \"h-3 w-3 sm:h-4 sm:w-4 mr-1 text-gray-500\",\n                        viewBox: \"0 0 20 20\",\n                        fill: \"currentColor\",\n                        children: /*#__PURE__*/_jsxDEV(\"path\", {\n                          fillRule: \"evenodd\",\n                          d: \"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\",\n                          clipRule: \"evenodd\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 187,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 186,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"font-medium\",\n                        children: ((_record$user = record.user) === null || _record$user === void 0 ? void 0 : _record$user.full_name) || ((_record$user2 = record.user) === null || _record$user2 === void 0 ? void 0 : _record$user2.email) || \"System\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 189,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 185,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 23\n                }, this), record.action === \"Modified\" && record.changes && Object.keys(record.changes).length > 0 && renderChanges(record.changes), record.action === \"Created\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-green-50 border border-green-100 rounded-md p-2 sm:p-3 text-xs sm:text-sm text-green-800\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-1 sm:mr-2 flex-shrink-0\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Initial case creation\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 207,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 25\n                }, this), record.action === \"Deleted\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-red-50 border border-red-100 rounded-md p-2 sm:p-3 text-xs sm:text-sm text-red-800\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      xmlns: \"http://www.w3.org/2000/svg\",\n                      className: \"h-4 w-4 sm:h-5 sm:w-5 text-red-600 mr-1 sm:mr-2 flex-shrink-0\",\n                      viewBox: \"0 0 20 20\",\n                      fill: \"currentColor\",\n                      children: /*#__PURE__*/_jsxDEV(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 216,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 215,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Case was deleted\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 218,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 21\n              }, this)]\n            }, record.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: /*#__PURE__*/_jsxDEV(Paginate, {\n          pages: pages,\n          page: page,\n          route: `?tab=History&`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-6 sm:py-10 bg-gray-50 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        className: \"h-8 w-8 sm:h-12 sm:w-12 mx-auto text-gray-400 mb-2 sm:mb-3\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        stroke: \"currentColor\",\n        children: /*#__PURE__*/_jsxDEV(\"path\", {\n          strokeLinecap: \"round\",\n          strokeLinejoin: \"round\",\n          strokeWidth: 1,\n          d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-500 text-base sm:text-lg\",\n        children: \"No history records found for this case\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-400 text-xs sm:text-sm mt-1\",\n        children: \"Any changes made to this case will appear here\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 139,\n    columnNumber: 5\n  }, this);\n}\n_c = CaseHistory;\nexport default CaseHistory;\nvar _c;\n$RefreshReg$(_c, \"CaseHistory\");", "map": {"version": 3, "names": ["React", "Loader", "<PERSON><PERSON>", "Paginate", "jsxDEV", "_jsxDEV", "CaseHistory", "historyData", "loading", "error", "history", "page", "pages", "count", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getActionStyles", "action", "color", "icon", "xmlns", "className", "viewBox", "fill", "children", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "renderChanges", "changes", "Object", "keys", "length", "scope", "map", "key", "change", "field_name", "renderValue", "old", "new", "value", "undefined", "name", "full_name", "id", "JSON", "stringify", "String", "type", "message", "record", "_record$user", "_record$user2", "actionStyle", "user", "email", "route", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/CaseHistory.js"], "sourcesContent": ["import React from \"react\";\nimport Loader from \"./Loader\";\nimport Alert from \"./Alert\";\nimport Paginate from \"./Paginate\";\n\nfunction CaseHistory({ historyData, loading, error }) {\n  const history = historyData?.history || [];\n  const page = historyData?.page || 1;\n  const pages = historyData?.pages || 1;\n  const count = historyData?.count || 0;\n\n  // Function to format date\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  // Function to get action color and icon\n  const getActionStyles = (action) => {\n    switch (action) {\n      case \"Created\":\n        return {\n          color: \"bg-green-100 text-green-800\",\n          icon: (\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-600\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n          )\n        };\n      case \"Modified\":\n        return {\n          color: \"bg-blue-100 text-blue-800\",\n          icon: (\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-600\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path d=\"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z\" />\n            </svg>\n          )\n        };\n      case \"Deleted\":\n        return {\n          color: \"bg-red-100 text-red-800\",\n          icon: (\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-red-600\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n          )\n        };\n      default:\n        return {\n          color: \"bg-gray-100 text-gray-800\",\n          icon: (\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-gray-600\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n            </svg>\n          )\n        };\n    }\n  };\n\n  // Function to render changes\n  const renderChanges = (changes) => {\n    if (!changes || Object.keys(changes).length === 0) {\n      return <div className=\"text-gray-500\">No changes detected</div>;\n    }\n\n    return (\n      <div className=\"mt-3 space-y-3\">\n        <div className=\"overflow-x-auto rounded-lg border\">\n          <table className=\"min-w-full divide-y divide-gray-200\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Field\n                </th>\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Previous Value\n                </th>\n                <th scope=\"col\" className=\"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  New Value\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {Object.keys(changes).map((key) => {\n                const change = changes[key];\n                return (\n                  <tr key={key} className=\"hover:bg-gray-50\">\n                    <td className=\"px-4 py-3 text-sm font-medium text-gray-900 break-words\">\n                      {change.field_name}\n                    </td>\n                    <td className=\"px-4 py-3 text-sm text-gray-500 break-words\">\n                      <span className=\"text-red-600\">{renderValue(change.old)}</span>\n                    </td>\n                    <td className=\"px-4 py-3 text-sm text-gray-500 break-words\">\n                      <span className=\"text-green-600\">{renderValue(change.new)}</span>\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n    );\n  };\n\n  // Function to render values (handling objects)\n  const renderValue = (value) => {\n    if (value === null || value === undefined) {\n      return <span className=\"text-gray-400 italic\">None</span>;\n    }\n\n    if (typeof value === \"object\" && value !== null) {\n      if (value.name) {\n        return value.name;\n      } else if (value.full_name) {\n        return value.full_name;\n      } else if (value.id) {\n        return `ID: ${value.id}`;\n      } else {\n        return JSON.stringify(value);\n      }\n    }\n\n    return String(value);\n  };\n\n  return (\n    <div className=\"my-3 mx-2 bg-white shadow-2 py-3 sm:py-5 px-3 sm:px-5 rounded\">\n      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6\">\n        <h2 className=\"text-base sm:text-lg font-semibold text-gray-800 mb-2 sm:mb-0\">\n          Case History\n        </h2>\n        {pages > 1 && (\n          <div className=\"text-xs sm:text-sm text-gray-500\">\n            Showing {history.length} of {count} records\n          </div>\n        )}\n      </div>\n\n      {loading ? (\n        <Loader />\n      ) : error ? (\n        <Alert type=\"error\" message={error} />\n      ) : history && history.length > 0 ? (\n        <div>\n          <div className=\"relative\">\n            {/* Timeline line - hidden on small screens */}\n            <div className=\"absolute left-4 md:left-8 top-0 bottom-0 w-0.5 bg-gray-200 hidden sm:block\"></div>\n\n            {/* Timeline items */}\n            <div className=\"space-y-6\">\n              {history.map((record) => {\n                const actionStyle = getActionStyles(record.action);\n\n                return (\n                  <div key={record.id} className=\"relative pl-6 sm:pl-16\">\n                    {/* Timeline dot - smaller on mobile */}\n                    <div className=\"absolute left-0 sm:left-6 top-0 sm:-translate-x-1/2 w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 sm:border-4 border-white bg-[#0388A6] shadow\"></div>\n\n                    {/* Content card */}\n                    <div className=\"bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow p-3 sm:p-5\">\n                      {/* Header */}\n                      <div className=\"flex flex-col sm:flex-row justify-between mb-3 sm:mb-4\">\n                        <div className=\"flex flex-col sm:flex-row sm:items-center mb-2 sm:mb-0\">\n                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${actionStyle.color} mr-2 mb-1 sm:mb-0 w-fit`}>\n                            <span className=\"mr-1\">{actionStyle.icon}</span>\n                            {record.action}\n                          </span>\n                          <span className=\"text-gray-500 text-xs sm:text-sm\">\n                            {formatDate(record.date)}\n                          </span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"flex items-center text-xs sm:text-sm text-gray-700\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-3 w-3 sm:h-4 sm:w-4 mr-1 text-gray-500\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\" />\n                            </svg>\n                            <span className=\"font-medium\">\n                              {record.user?.full_name || record.user?.email || \"System\"}\n                            </span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Content */}\n                      {record.action === \"Modified\" && record.changes && Object.keys(record.changes).length > 0 && (\n                        renderChanges(record.changes)\n                      )}\n\n                      {record.action === \"Created\" && (\n                        <div className=\"bg-green-50 border border-green-100 rounded-md p-2 sm:p-3 text-xs sm:text-sm text-green-800\">\n                          <div className=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 text-green-600 mr-1 sm:mr-2 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                            </svg>\n                            <p>Initial case creation</p>\n                          </div>\n                        </div>\n                      )}\n\n                      {record.action === \"Deleted\" && (\n                        <div className=\"bg-red-50 border border-red-100 rounded-md p-2 sm:p-3 text-xs sm:text-sm text-red-800\">\n                          <div className=\"flex items-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 sm:h-5 sm:w-5 text-red-600 mr-1 sm:mr-2 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                            </svg>\n                            <p>Case was deleted</p>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Pagination */}\n          <div className=\"mt-8\">\n            <Paginate\n              pages={pages}\n              page={page}\n              route={`?tab=History&`}\n            />\n          </div>\n        </div>\n      ) : (\n        <div className=\"text-center py-6 sm:py-10 bg-gray-50 rounded-lg\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 sm:h-12 sm:w-12 mx-auto text-gray-400 mb-2 sm:mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          <p className=\"text-gray-500 text-base sm:text-lg\">No history records found for this case</p>\n          <p className=\"text-gray-400 text-xs sm:text-sm mt-1\">Any changes made to this case will appear here</p>\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default CaseHistory;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,SAASC,WAAWA,CAAC;EAAEC,WAAW;EAAEC,OAAO;EAAEC;AAAM,CAAC,EAAE;EACpD,MAAMC,OAAO,GAAG,CAAAH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEG,OAAO,KAAI,EAAE;EAC1C,MAAMC,IAAI,GAAG,CAAAJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,IAAI,KAAI,CAAC;EACnC,MAAMC,KAAK,GAAG,CAAAL,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,KAAK,KAAI,CAAC;EACrC,MAAMC,KAAK,GAAG,CAAAN,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,KAAK,KAAI,CAAC;;EAErC;EACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAIA,UAAU,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;QACtCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAOR,UAAU;IACnB;EACF,CAAC;;EAED;EACA,MAAMS,eAAe,GAAIC,MAAM,IAAK;IAClC,QAAQA,MAAM;MACZ,KAAK,SAAS;QACZ,OAAO;UACLC,KAAK,EAAE,6BAA6B;UACpCC,IAAI,eACFtB,OAAA;YAAKuB,KAAK,EAAC,4BAA4B;YAACC,SAAS,EAAC,wBAAwB;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAC,QAAA,eAChH3B,OAAA;cAAM4B,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,uIAAuI;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrL;QAET,CAAC;MACH,KAAK,UAAU;QACb,OAAO;UACLb,KAAK,EAAE,2BAA2B;UAClCC,IAAI,eACFtB,OAAA;YAAKuB,KAAK,EAAC,4BAA4B;YAACC,SAAS,EAAC,uBAAuB;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAC,QAAA,eAC/G3B,OAAA;cAAM6B,CAAC,EAAC;YAAyH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI;QAET,CAAC;MACH,KAAK,SAAS;QACZ,OAAO;UACLb,KAAK,EAAE,yBAAyB;UAChCC,IAAI,eACFtB,OAAA;YAAKuB,KAAK,EAAC,4BAA4B;YAACC,SAAS,EAAC,sBAAsB;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAC,QAAA,eAC9G3B,OAAA;cAAM4B,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,6MAA6M;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3P;QAET,CAAC;MACH;QACE,OAAO;UACLb,KAAK,EAAE,2BAA2B;UAClCC,IAAI,eACFtB,OAAA;YAAKuB,KAAK,EAAC,4BAA4B;YAACC,SAAS,EAAC,uBAAuB;YAACC,OAAO,EAAC,WAAW;YAACC,IAAI,EAAC,cAAc;YAAAC,QAAA,eAC/G3B,OAAA;cAAM4B,QAAQ,EAAC,SAAS;cAACC,CAAC,EAAC,kIAAkI;cAACC,QAAQ,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChL;QAET,CAAC;IACL;EACF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAIC,OAAO,IAAK;IACjC,IAAI,CAACA,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;MACjD,oBAAOvC,OAAA;QAAKwB,SAAS,EAAC,eAAe;QAAAG,QAAA,EAAC;MAAmB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IACjE;IAEA,oBACElC,OAAA;MAAKwB,SAAS,EAAC,gBAAgB;MAAAG,QAAA,eAC7B3B,OAAA;QAAKwB,SAAS,EAAC,mCAAmC;QAAAG,QAAA,eAChD3B,OAAA;UAAOwB,SAAS,EAAC,qCAAqC;UAAAG,QAAA,gBACpD3B,OAAA;YAAOwB,SAAS,EAAC,YAAY;YAAAG,QAAA,eAC3B3B,OAAA;cAAA2B,QAAA,gBACE3B,OAAA;gBAAIwC,KAAK,EAAC,KAAK;gBAAChB,SAAS,EAAC,gFAAgF;gBAAAG,QAAA,EAAC;cAE3G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlC,OAAA;gBAAIwC,KAAK,EAAC,KAAK;gBAAChB,SAAS,EAAC,gFAAgF;gBAAAG,QAAA,EAAC;cAE3G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLlC,OAAA;gBAAIwC,KAAK,EAAC,KAAK;gBAAChB,SAAS,EAAC,gFAAgF;gBAAAG,QAAA,EAAC;cAE3G;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRlC,OAAA;YAAOwB,SAAS,EAAC,mCAAmC;YAAAG,QAAA,EACjDU,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACK,GAAG,CAAEC,GAAG,IAAK;cACjC,MAAMC,MAAM,GAAGP,OAAO,CAACM,GAAG,CAAC;cAC3B,oBACE1C,OAAA;gBAAcwB,SAAS,EAAC,kBAAkB;gBAAAG,QAAA,gBACxC3B,OAAA;kBAAIwB,SAAS,EAAC,yDAAyD;kBAAAG,QAAA,EACpEgB,MAAM,CAACC;gBAAU;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACLlC,OAAA;kBAAIwB,SAAS,EAAC,6CAA6C;kBAAAG,QAAA,eACzD3B,OAAA;oBAAMwB,SAAS,EAAC,cAAc;oBAAAG,QAAA,EAAEkB,WAAW,CAACF,MAAM,CAACG,GAAG;kBAAC;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACLlC,OAAA;kBAAIwB,SAAS,EAAC,6CAA6C;kBAAAG,QAAA,eACzD3B,OAAA;oBAAMwB,SAAS,EAAC,gBAAgB;oBAAAG,QAAA,EAAEkB,WAAW,CAACF,MAAM,CAACI,GAAG;kBAAC;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA,GATEQ,GAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUR,CAAC;YAET,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMW,WAAW,GAAIG,KAAK,IAAK;IAC7B,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACzC,oBAAOjD,OAAA;QAAMwB,SAAS,EAAC,sBAAsB;QAAAG,QAAA,EAAC;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAC3D;IAEA,IAAI,OAAOc,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;MAC/C,IAAIA,KAAK,CAACE,IAAI,EAAE;QACd,OAAOF,KAAK,CAACE,IAAI;MACnB,CAAC,MAAM,IAAIF,KAAK,CAACG,SAAS,EAAE;QAC1B,OAAOH,KAAK,CAACG,SAAS;MACxB,CAAC,MAAM,IAAIH,KAAK,CAACI,EAAE,EAAE;QACnB,OAAQ,OAAMJ,KAAK,CAACI,EAAG,EAAC;MAC1B,CAAC,MAAM;QACL,OAAOC,IAAI,CAACC,SAAS,CAACN,KAAK,CAAC;MAC9B;IACF;IAEA,OAAOO,MAAM,CAACP,KAAK,CAAC;EACtB,CAAC;EAED,oBACEhD,OAAA;IAAKwB,SAAS,EAAC,+DAA+D;IAAAG,QAAA,gBAC5E3B,OAAA;MAAKwB,SAAS,EAAC,2EAA2E;MAAAG,QAAA,gBACxF3B,OAAA;QAAIwB,SAAS,EAAC,+DAA+D;QAAAG,QAAA,EAAC;MAE9E;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACJ3B,KAAK,GAAG,CAAC,iBACRP,OAAA;QAAKwB,SAAS,EAAC,kCAAkC;QAAAG,QAAA,GAAC,UACxC,EAACtB,OAAO,CAACkC,MAAM,EAAC,MAAI,EAAC/B,KAAK,EAAC,UACrC;MAAA;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL/B,OAAO,gBACNH,OAAA,CAACJ,MAAM;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACR9B,KAAK,gBACPJ,OAAA,CAACH,KAAK;MAAC2D,IAAI,EAAC,OAAO;MAACC,OAAO,EAAErD;IAAM;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACpC7B,OAAO,IAAIA,OAAO,CAACkC,MAAM,GAAG,CAAC,gBAC/BvC,OAAA;MAAA2B,QAAA,gBACE3B,OAAA;QAAKwB,SAAS,EAAC,UAAU;QAAAG,QAAA,gBAEvB3B,OAAA;UAAKwB,SAAS,EAAC;QAA4E;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGlGlC,OAAA;UAAKwB,SAAS,EAAC,WAAW;UAAAG,QAAA,EACvBtB,OAAO,CAACoC,GAAG,CAAEiB,MAAM,IAAK;YAAA,IAAAC,YAAA,EAAAC,aAAA;YACvB,MAAMC,WAAW,GAAG1C,eAAe,CAACuC,MAAM,CAACtC,MAAM,CAAC;YAElD,oBACEpB,OAAA;cAAqBwB,SAAS,EAAC,wBAAwB;cAAAG,QAAA,gBAErD3B,OAAA;gBAAKwB,SAAS,EAAC;cAA8I;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAGpKlC,OAAA;gBAAKwB,SAAS,EAAC,mGAAmG;gBAAAG,QAAA,gBAEhH3B,OAAA;kBAAKwB,SAAS,EAAC,wDAAwD;kBAAAG,QAAA,gBACrE3B,OAAA;oBAAKwB,SAAS,EAAC,wDAAwD;oBAAAG,QAAA,gBACrE3B,OAAA;sBAAMwB,SAAS,EAAG,yEAAwEqC,WAAW,CAACxC,KAAM,0BAA0B;sBAAAM,QAAA,gBACpI3B,OAAA;wBAAMwB,SAAS,EAAC,MAAM;wBAAAG,QAAA,EAAEkC,WAAW,CAACvC;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,EAC/CwB,MAAM,CAACtC,MAAM;oBAAA;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACPlC,OAAA;sBAAMwB,SAAS,EAAC,kCAAkC;sBAAAG,QAAA,EAC/ClB,UAAU,CAACiD,MAAM,CAAC/C,IAAI;oBAAC;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNlC,OAAA;oBAAKwB,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,eAChC3B,OAAA;sBAAKwB,SAAS,EAAC,oDAAoD;sBAAAG,QAAA,gBACjE3B,OAAA;wBAAKuB,KAAK,EAAC,4BAA4B;wBAACC,SAAS,EAAC,0CAA0C;wBAACC,OAAO,EAAC,WAAW;wBAACC,IAAI,EAAC,cAAc;wBAAAC,QAAA,eAClI3B,OAAA;0BAAM4B,QAAQ,EAAC,SAAS;0BAACC,CAAC,EAAC,qDAAqD;0BAACC,QAAQ,EAAC;wBAAS;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnG,CAAC,eACNlC,OAAA;wBAAMwB,SAAS,EAAC,aAAa;wBAAAG,QAAA,EAC1B,EAAAgC,YAAA,GAAAD,MAAM,CAACI,IAAI,cAAAH,YAAA,uBAAXA,YAAA,CAAaR,SAAS,OAAAS,aAAA,GAAIF,MAAM,CAACI,IAAI,cAAAF,aAAA,uBAAXA,aAAA,CAAaG,KAAK,KAAI;sBAAQ;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLwB,MAAM,CAACtC,MAAM,KAAK,UAAU,IAAIsC,MAAM,CAACtB,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACoB,MAAM,CAACtB,OAAO,CAAC,CAACG,MAAM,GAAG,CAAC,IACvFJ,aAAa,CAACuB,MAAM,CAACtB,OAAO,CAC7B,EAEAsB,MAAM,CAACtC,MAAM,KAAK,SAAS,iBAC1BpB,OAAA;kBAAKwB,SAAS,EAAC,6FAA6F;kBAAAG,QAAA,eAC1G3B,OAAA;oBAAKwB,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,gBAChC3B,OAAA;sBAAKuB,KAAK,EAAC,4BAA4B;sBAACC,SAAS,EAAC,iEAAiE;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAC,QAAA,eACzJ3B,OAAA;wBAAM4B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,uIAAuI;wBAACC,QAAQ,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrL,CAAC,eACNlC,OAAA;sBAAA2B,QAAA,EAAG;oBAAqB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEAwB,MAAM,CAACtC,MAAM,KAAK,SAAS,iBAC1BpB,OAAA;kBAAKwB,SAAS,EAAC,uFAAuF;kBAAAG,QAAA,eACpG3B,OAAA;oBAAKwB,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,gBAChC3B,OAAA;sBAAKuB,KAAK,EAAC,4BAA4B;sBAACC,SAAS,EAAC,+DAA+D;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,cAAc;sBAAAC,QAAA,eACvJ3B,OAAA;wBAAM4B,QAAQ,EAAC,SAAS;wBAACC,CAAC,EAAC,mNAAmN;wBAACC,QAAQ,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjQ,CAAC,eACNlC,OAAA;sBAAA2B,QAAA,EAAG;oBAAgB;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAvDEwB,MAAM,CAACN,EAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAwDd,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlC,OAAA;QAAKwB,SAAS,EAAC,MAAM;QAAAG,QAAA,eACnB3B,OAAA,CAACF,QAAQ;UACPS,KAAK,EAAEA,KAAM;UACbD,IAAI,EAAEA,IAAK;UACX0D,KAAK,EAAG;QAAe;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENlC,OAAA;MAAKwB,SAAS,EAAC,iDAAiD;MAAAG,QAAA,gBAC9D3B,OAAA;QAAKuB,KAAK,EAAC,4BAA4B;QAACC,SAAS,EAAC,4DAA4D;QAACE,IAAI,EAAC,MAAM;QAACD,OAAO,EAAC,WAAW;QAACwC,MAAM,EAAC,cAAc;QAAAtC,QAAA,eAClK3B,OAAA;UAAMkE,aAAa,EAAC,OAAO;UAACC,cAAc,EAAC,OAAO;UAACC,WAAW,EAAE,CAAE;UAACvC,CAAC,EAAC;QAA6C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClH,CAAC,eACNlC,OAAA;QAAGwB,SAAS,EAAC,oCAAoC;QAAAG,QAAA,EAAC;MAAsC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAC5FlC,OAAA;QAAGwB,SAAS,EAAC,uCAAuC;QAAAG,QAAA,EAAC;MAA8C;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpG,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACmC,EAAA,GAnPQpE,WAAW;AAqPpB,eAAeA,WAAW;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}