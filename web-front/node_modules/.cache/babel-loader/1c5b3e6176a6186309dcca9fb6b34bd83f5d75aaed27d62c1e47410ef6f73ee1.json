{"ast": null, "code": "import React,{useEffect}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{Link,useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import Paginate from\"../../components/Paginate\";import{getFacturesListContrats,getListContrats}from\"../../redux/actions/contratActions\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function FactureScreen(){const navigate=useNavigate();const location=useLocation();const page=location.search.split(\"&\")[1]?location.search.split(\"&\")[1].split(\"=\")[1]:1;const dispatch=useDispatch();const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listContrat=useSelector(state=>state.facturesContratList);const{factureContrats,loadingFactureContrat,errorFactureContrat,pages}=listContrat;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(getFacturesListContrats(page));}},[navigate,userInfo,dispatch,page]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Accueil\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Factures\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black  text-xs w-max\",children:\"Gestion des Factures\"})}),loadingFactureContrat?/*#__PURE__*/_jsx(Loader,{}):errorFactureContrat?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorFactureContrat}):/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-full overflow-x-auto mt-3\",children:[/*#__PURE__*/_jsxs(\"table\",{className:\"w-full table-auto\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-2 text-left \",children:[/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"NF\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"NC\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Client\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Voiture\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Matricule\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"D\\xE9but\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Fin\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"NJ\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Prix/jour\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Montant\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Avance\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Reste\"}),/*#__PURE__*/_jsx(\"th\",{className:\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\",children:\"Actions\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[factureContrats===null||factureContrats===void 0?void 0:factureContrats.map((contrat,id)=>{var _contrat$client$first,_contrat$client,_contrat$client$last_,_contrat$client2,_ref,_contrat$car$marque$m,_contrat$car,_contrat$car2,_contrat$model_car,_contrat$car$matricul,_contrat$car3,_contrat$start_date,_contrat$end_date,_contrat$nbr_day,_parseFloat$toFixed,_parseFloat$toFixed2,_parseFloat$toFixed3,_parseFloat$toFixed4;return/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:contrat.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[30px] border-b border-[#eee] py-2 px-4  \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:contrat.id})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max\",children:[(_contrat$client$first=(_contrat$client=contrat.client)===null||_contrat$client===void 0?void 0:_contrat$client.first_name)!==null&&_contrat$client$first!==void 0?_contrat$client$first:\"---\",\" \",(_contrat$client$last_=(_contrat$client2=contrat.client)===null||_contrat$client2===void 0?void 0:_contrat$client2.last_name)!==null&&_contrat$client$last_!==void 0?_contrat$client$last_:\"\"]})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:contrat.is_withcar?(_ref=((_contrat$car$marque$m=(_contrat$car=contrat.car)===null||_contrat$car===void 0?void 0:_contrat$car.marque.marque_car)!==null&&_contrat$car$marque$m!==void 0?_contrat$car$marque$m:\"---\")+\" \"+((_contrat$car2=contrat.car)===null||_contrat$car2===void 0?void 0:_contrat$car2.model.model_car))!==null&&_ref!==void 0?_ref:\"\":((_contrat$model_car=contrat.model_car)!==null&&_contrat$model_car!==void 0?_contrat$model_car:\"---\")+\" (Sans voiture)\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$car$matricul=(_contrat$car3=contrat.car)===null||_contrat$car3===void 0?void 0:_contrat$car3.matricule)!==null&&_contrat$car$matricul!==void 0?_contrat$car$matricul:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$start_date=contrat.start_date)!==null&&_contrat$start_date!==void 0?_contrat$start_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$end_date=contrat.end_date)!==null&&_contrat$end_date!==void 0?_contrat$end_date:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_contrat$nbr_day=contrat.nbr_day)!==null&&_contrat$nbr_day!==void 0?_contrat$nbr_day:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed=parseFloat(contrat.price_day).toFixed(2))!==null&&_parseFloat$toFixed!==void 0?_parseFloat$toFixed:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed2=parseFloat(contrat.price_total).toFixed(2))!==null&&_parseFloat$toFixed2!==void 0?_parseFloat$toFixed2:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed3=parseFloat(contrat.price_avance).toFixed(2))!==null&&_parseFloat$toFixed3!==void 0?_parseFloat$toFixed3:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-black  text-xs w-max\",children:(_parseFloat$toFixed4=parseFloat(parseFloat(contrat.price_total)-parseFloat(contrat.price_avance)).toFixed(2))!==null&&_parseFloat$toFixed4!==void 0?_parseFloat$toFixed4:\"---\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"min-w-[120px] border-b border-[#eee] py-2 px-4 \",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-black  text-xs w-max flex flex-row\",children:[/*#__PURE__*/_jsx(Link,{className:\"mx-1 imprimer-class\",rel:\"noopener\",target:\"_blank\",to:baseURLFile+\"/api/contrats/print_pdf/\"+contrat.id+\"/\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"})})}),/*#__PURE__*/_jsx(Link,{className:\"mx-1 factures-class\",to:baseURLFile+\"/api/contrats/facture_pdf/\"+contrat.id+\"/\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z\"})})})]})})]});}),/*#__PURE__*/_jsx(\"tr\",{className:\"h-11\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(Paginate,{route:\"/factures?\",search:\"\",page:page,pages:pages})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid md:grid-cols-2 w-full container mt-5\"})]})});}export default FactureScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "Link", "useLocation", "useNavigate", "DefaultLayout", "Loader", "<PERSON><PERSON>", "Paginate", "getFacturesListContrats", "getListContrats", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "FactureScreen", "navigate", "location", "page", "search", "split", "dispatch", "userLogin", "state", "userInfo", "listContrat", "facturesContratList", "factureContrats", "loadingFactureContrat", "errorFactureContrat", "pages", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "d", "type", "message", "map", "contrat", "id", "_contrat$client$first", "_contrat$client", "_contrat$client$last_", "_contrat$client2", "_ref", "_contrat$car$marque$m", "_contrat$car", "_contrat$car2", "_contrat$model_car", "_contrat$car$matricul", "_contrat$car3", "_contrat$start_date", "_contrat$end_date", "_contrat$nbr_day", "_parseFloat$toFixed", "_parseFloat$toFixed2", "_parseFloat$toFixed3", "_parseFloat$toFixed4", "client", "first_name", "last_name", "is_withcar", "car", "marque", "marque_car", "model", "model_car", "matricule", "start_date", "end_date", "nbr_day", "parseFloat", "price_day", "toFixed", "price_total", "price_avance", "rel", "target", "to", "route"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/screens/factures/FactureScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { Link, useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport Paginate from \"../../components/Paginate\";\nimport {\n  getFacturesListContrats,\n  getListContrats,\n} from \"../../redux/actions/contratActions\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction FactureScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const page = location.search.split(\"&\")[1]\n    ? location.search.split(\"&\")[1].split(\"=\")[1]\n    : 1;\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listContrat = useSelector((state) => state.facturesContratList);\n  const { factureContrats, loadingFactureContrat, errorFactureContrat, pages } =\n    listContrat;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(getFacturesListContrats(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n\n  return (\n    <DefaultLayout>\n      <div>\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  stroke-linecap=\"round\"\n                  stroke-linejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Accueil</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                stroke-linecap=\"round\"\n                stroke-linejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Factures</div>\n        </div>\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default  dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"py-2 px-4 md:px-6 xl:px-7.5 flex justify-between\">\n            <h4 className=\" uppercase font-semibold text-black  text-xs w-max\">\n              Gestion des Factures\n            </h4>\n          </div>\n\n          {/* list */}\n          {loadingFactureContrat ? (\n            <Loader />\n          ) : errorFactureContrat ? (\n            <Alert type=\"error\" message={errorFactureContrat} />\n          ) : (\n            <div className=\"max-w-full overflow-x-auto mt-3\">\n              <table className=\"w-full table-auto\">\n                <thead>\n                  <tr className=\"bg-gray-2 text-left \">\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NF\n                    </th>\n                    <th className=\"min-w-[30px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NC\n                    </th>\n\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Client\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Voiture\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Matricule\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Début\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Fin\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      NJ\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Prix/jour\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Montant\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Avance\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Reste\n                    </th>\n                    <th className=\"min-w-[120px] py-4 px-4 font-bold text-black  text-xs w-max\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                {/*  */}\n                <tbody>\n                  {factureContrats?.map((contrat, id) => (\n                    <tr>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[30px] border-b border-[#eee] py-2 px-4  \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.id}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.client?.first_name ?? \"---\"}{\" \"}\n                          {contrat.client?.last_name ?? \"\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.is_withcar\n                            ? (contrat.car?.marque.marque_car ?? \"---\") +\n                                \" \" +\n                                contrat.car?.model.model_car ?? \"\"\n                            : (contrat.model_car ?? \"---\") + \" (Sans voiture)\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.car?.matricule ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.start_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.end_date ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {contrat.nbr_day ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_day).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_total).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(contrat.price_avance).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max\">\n                          {parseFloat(\n                            parseFloat(contrat.price_total) -\n                              parseFloat(contrat.price_avance)\n                          ).toFixed(2) ?? \"---\"}\n                        </p>\n                      </td>\n\n                      <td className=\"min-w-[120px] border-b border-[#eee] py-2 px-4 \">\n                        <p className=\"text-black  text-xs w-max flex flex-row\">\n                          {/* pdf */}\n                          <Link\n                            className=\"mx-1 imprimer-class\"\n                            rel=\"noopener\"\n                            target=\"_blank\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/print_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-warning rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z\"\n                              />\n                            </svg>\n                          </Link>\n                          {/* facture */}\n                          <Link\n                            className=\"mx-1 factures-class\"\n                            to={\n                              baseURLFile +\n                              \"/api/contrats/facture_pdf/\" +\n                              contrat.id +\n                              \"/\"\n                            }\n                          >\n                            <svg\n                              xmlns=\"http://www.w3.org/2000/svg\"\n                              fill=\"none\"\n                              viewBox=\"0 0 24 24\"\n                              stroke-width=\"1.5\"\n                              stroke=\"currentColor\"\n                              className=\"w-5 h-5 bg-danger rounded p-1 text-white text-center text-xs\"\n                            >\n                              <path\n                                stroke-linecap=\"round\"\n                                stroke-linejoin=\"round\"\n                                d=\"M6.72 13.829c-.24.03-.48.062-.72.096m.72-.096a42.415 42.415 0 0 1 10.56 0m-10.56 0L6.34 18m10.94-4.171c.24.03.48.062.72.096m-.72-.096L17.66 18m0 0 .229 2.523a1.125 1.125 0 0 1-1.12 1.227H7.231c-.662 0-1.18-.568-1.12-1.227L6.34 18m11.318 0h1.091A2.25 2.25 0 0 0 21 15.75V9.456c0-1.081-.768-2.015-1.837-2.175a48.055 48.055 0 0 0-1.913-.247M6.34 18H5.25A2.25 2.25 0 0 1 3 15.75V9.456c0-1.081.768-2.015 1.837-2.175a48.041 48.041 0 0 1 1.913-.247m10.5 0a48.536 48.536 0 0 0-10.5 0m10.5 0V3.375c0-.621-.504-1.125-1.125-1.125h-8.25c-.621 0-1.125.504-1.125 1.125v3.659M18 10.5h.008v.008H18V10.5Zm-3 0h.008v.008H15V10.5Z\"\n                              />\n                            </svg>\n                          </Link>\n                        </p>\n                      </td>\n                    </tr>\n                  ))}\n                  <tr className=\"h-11\"></tr>\n                </tbody>\n              </table>\n              <div className=\"\">\n                <Paginate\n                  route={\"/factures?\"}\n                  search={\"\"}\n                  page={page}\n                  pages={pages}\n                />\n              </div>\n            </div>\n          )}\n        </div>\n        {/* buttom dash */}\n        <div className=\"grid md:grid-cols-2 w-full container mt-5\"></div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default FactureScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CACjE,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,MAAO,CAAAC,QAAQ,KAAM,2BAA2B,CAChD,OACEC,uBAAuB,CACvBC,eAAe,KACV,oCAAoC,CAC3C,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAc,QAAQ,CAAGf,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAgB,IAAI,CAAGD,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtCH,QAAQ,CAACE,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC3C,CAAC,CACL,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAuB,SAAS,CAAGtB,WAAW,CAAEuB,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,WAAW,CAAGzB,WAAW,CAAEuB,KAAK,EAAKA,KAAK,CAACG,mBAAmB,CAAC,CACrE,KAAM,CAAEC,eAAe,CAAEC,qBAAqB,CAAEC,mBAAmB,CAAEC,KAAM,CAAC,CAC1EL,WAAW,CAEb,KAAM,CAAAM,QAAQ,CAAG,GAAG,CAEpBjC,SAAS,CAAC,IAAM,CACd,GAAI,CAAC0B,QAAQ,CAAE,CACbR,QAAQ,CAACe,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLV,QAAQ,CAACb,uBAAuB,CAACU,IAAI,CAAC,CAAC,CACzC,CACF,CAAC,CAAE,CAACF,QAAQ,CAAEQ,QAAQ,CAAEH,QAAQ,CAAEH,IAAI,CAAC,CAAC,CAExC,mBACEN,IAAA,CAACR,aAAa,EAAA4B,QAAA,cACZlB,KAAA,QAAAkB,QAAA,eACElB,KAAA,QAAKmB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtDpB,IAAA,MAAGsB,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBlB,KAAA,QAAKmB,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2B,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACN3B,IAAA,SAAMqB,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,EAClC,CAAC,CACL,CAAC,cACJpB,IAAA,SAAAoB,QAAA,cACEpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnBpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2B,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACP3B,IAAA,QAAKqB,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,EAC7B,CAAC,cACNlB,KAAA,QAAKmB,SAAS,CAAC,6GAA6G,CAAAD,QAAA,eAC1HpB,IAAA,QAAKqB,SAAS,CAAC,kDAAkD,CAAAD,QAAA,cAC/DpB,IAAA,OAAIqB,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,sBAEnE,CAAI,CAAC,CACF,CAAC,CAGLJ,qBAAqB,cACpBhB,IAAA,CAACP,MAAM,GAAE,CAAC,CACRwB,mBAAmB,cACrBjB,IAAA,CAACN,KAAK,EAACkC,IAAI,CAAC,OAAO,CAACC,OAAO,CAAEZ,mBAAoB,CAAE,CAAC,cAEpDf,KAAA,QAAKmB,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9ClB,KAAA,UAAOmB,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAClCpB,IAAA,UAAAoB,QAAA,cACElB,KAAA,OAAImB,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eAClCpB,IAAA,OAAIqB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,IAE3E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,4DAA4D,CAAAD,QAAA,CAAC,IAE3E,CAAI,CAAC,cAELpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,QAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,WAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,UAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,KAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,IAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,WAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,QAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,OAE5E,CAAI,CAAC,cACLpB,IAAA,OAAIqB,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CAAC,SAE5E,CAAI,CAAC,EACH,CAAC,CACA,CAAC,cAERlB,KAAA,UAAAkB,QAAA,EACGL,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEe,GAAG,CAAC,CAACC,OAAO,CAAEC,EAAE,QAAAC,qBAAA,CAAAC,eAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,IAAA,CAAAC,qBAAA,CAAAC,YAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,aAAA,CAAAC,mBAAA,CAAAC,iBAAA,CAAAC,gBAAA,CAAAC,mBAAA,CAAAC,oBAAA,CAAAC,oBAAA,CAAAC,oBAAA,oBAChChD,KAAA,OAAAkB,QAAA,eACEpB,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCW,OAAO,CAACC,EAAE,CACV,CAAC,CACF,CAAC,cACLhC,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCW,OAAO,CAACC,EAAE,CACV,CAAC,CACF,CAAC,cACLhC,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DlB,KAAA,MAAGmB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,GAAAa,qBAAA,EAAAC,eAAA,CACrCH,OAAO,CAACoB,MAAM,UAAAjB,eAAA,iBAAdA,eAAA,CAAgBkB,UAAU,UAAAnB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAAE,GAAG,EAAAE,qBAAA,EAAAC,gBAAA,CACxCL,OAAO,CAACoB,MAAM,UAAAf,gBAAA,iBAAdA,gBAAA,CAAgBiB,SAAS,UAAAlB,qBAAA,UAAAA,qBAAA,CAAI,EAAE,EAC/B,CAAC,CACF,CAAC,cACLnC,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,CACrCW,OAAO,CAACuB,UAAU,EAAAjB,IAAA,CACf,EAAAC,qBAAA,EAAAC,YAAA,CAACR,OAAO,CAACwB,GAAG,UAAAhB,YAAA,iBAAXA,YAAA,CAAaiB,MAAM,CAACC,UAAU,UAAAnB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EACtC,GAAG,GAAAE,aAAA,CACHT,OAAO,CAACwB,GAAG,UAAAf,aAAA,iBAAXA,aAAA,CAAakB,KAAK,CAACC,SAAS,WAAAtB,IAAA,UAAAA,IAAA,CAAI,EAAE,CACpC,EAAAI,kBAAA,CAACV,OAAO,CAAC4B,SAAS,UAAAlB,kBAAA,UAAAA,kBAAA,CAAI,KAAK,EAAI,iBAAiB,CACnD,CAAC,CACF,CAAC,cACLzC,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAsB,qBAAA,EAAAC,aAAA,CACrCZ,OAAO,CAACwB,GAAG,UAAAZ,aAAA,iBAAXA,aAAA,CAAaiB,SAAS,UAAAlB,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,CACF,CAAC,cACL1C,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAwB,mBAAA,CACrCb,OAAO,CAAC8B,UAAU,UAAAjB,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CAC3B,CAAC,CACF,CAAC,cACL5C,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAAyB,iBAAA,CACrCd,OAAO,CAAC+B,QAAQ,UAAAjB,iBAAA,UAAAA,iBAAA,CAAI,KAAK,CACzB,CAAC,CACF,CAAC,cACL7C,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA0B,gBAAA,CACrCf,OAAO,CAACgC,OAAO,UAAAjB,gBAAA,UAAAA,gBAAA,CAAI,KAAK,CACxB,CAAC,CACF,CAAC,cACL9C,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA2B,mBAAA,CACrCiB,UAAU,CAACjC,OAAO,CAACkC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,UAAAnB,mBAAA,UAAAA,mBAAA,CAAI,KAAK,CACjD,CAAC,CACF,CAAC,cACL/C,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA4B,oBAAA,CACrCgB,UAAU,CAACjC,OAAO,CAACoC,WAAW,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC,UAAAlB,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACnD,CAAC,CACF,CAAC,cACLhD,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA6B,oBAAA,CACrCe,UAAU,CAACjC,OAAO,CAACqC,YAAY,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,UAAAjB,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACpD,CAAC,CACF,CAAC,cACLjD,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DpB,IAAA,MAAGqB,SAAS,CAAC,2BAA2B,CAAAD,QAAA,EAAA8B,oBAAA,CACrCc,UAAU,CACTA,UAAU,CAACjC,OAAO,CAACoC,WAAW,CAAC,CAC7BH,UAAU,CAACjC,OAAO,CAACqC,YAAY,CACnC,CAAC,CAACF,OAAO,CAAC,CAAC,CAAC,UAAAhB,oBAAA,UAAAA,oBAAA,CAAI,KAAK,CACpB,CAAC,CACF,CAAC,cAELlD,IAAA,OAAIqB,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAC7DlB,KAAA,MAAGmB,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEpDpB,IAAA,CAACX,IAAI,EACHgC,SAAS,CAAC,qBAAqB,CAC/BgD,GAAG,CAAC,UAAU,CACdC,MAAM,CAAC,QAAQ,CACfC,EAAE,CACAzE,WAAW,CACX,0BAA0B,CAC1BiC,OAAO,CAACC,EAAE,CACV,GACD,CAAAZ,QAAA,cAEDpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAEzEpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2B,CAAC,CAAC,mQAAmQ,CACtQ,CAAC,CACC,CAAC,CACF,CAAC,cAEP3B,IAAA,CAACX,IAAI,EACHgC,SAAS,CAAC,qBAAqB,CAC/BkD,EAAE,CACAzE,WAAW,CACX,4BAA4B,CAC5BiC,OAAO,CAACC,EAAE,CACV,GACD,CAAAZ,QAAA,cAEDpB,IAAA,QACEuB,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,8DAA8D,CAAAD,QAAA,cAExEpB,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvB2B,CAAC,CAAC,qmBAAqmB,CACxmB,CAAC,CACC,CAAC,CACF,CAAC,EACN,CAAC,CACF,CAAC,EACH,CAAC,EACN,CAAC,cACF3B,IAAA,OAAIqB,SAAS,CAAC,MAAM,CAAK,CAAC,EACrB,CAAC,EACH,CAAC,cACRrB,IAAA,QAAKqB,SAAS,CAAC,EAAE,CAAAD,QAAA,cACfpB,IAAA,CAACL,QAAQ,EACP6E,KAAK,CAAE,YAAa,CACpBjE,MAAM,CAAE,EAAG,CACXD,IAAI,CAAEA,IAAK,CACXY,KAAK,CAAEA,KAAM,CACd,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,cAENlB,IAAA,QAAKqB,SAAS,CAAC,2CAA2C,CAAM,CAAC,EAC9D,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAlB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}