{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList } from \"../../redux/actions/caseActions\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DashboardScreen() {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo\n  } = userLogin;\n  const listCases = useSelector(state => state.caseList);\n  const {\n    cases,\n    loadingCases,\n    errorCases,\n    pages\n  } = listCases;\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"UNIMEDCARE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex md:flex-row items-center justify-around flex-col min-h-screen \",\n      children: [/*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/cases\",\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          src: \"https://img.icons8.com/clouds/100/view-file--v2.png\",\n          alt: \"view-file--v2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"BUSQUEDA DE CASOS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/proveedors\",\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          src: \"https://img.icons8.com/clouds/100/view-file--v2.png\",\n          alt: \"view-file--v2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"BUSCADOR DE PROVEEDORES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: \"/kps-informations\",\n        className: \"flex flex-col justify-center items-center mx-1 \",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"my-1 w-25\",\n          src: \"https://img.icons8.com/clouds/100/view-file--v2.png\",\n          alt: \"view-file--v2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-graydark px-4 py-2 my-1 rounded-full text-white\",\n          children: \"KPI \\u0301S / INFORMES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n}\n_s(DashboardScreen, \"nSzF0MaDMu8qgz7M90bXedJs+/E=\", false, function () {\n  return [useNavigate, useLocation, useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = DashboardScreen;\nexport default DashboardScreen;\nvar _c;\n$RefreshReg$(_c, \"DashboardScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useLocation", "useNavigate", "useSearchParams", "casesList", "jsxDEV", "_jsxDEV", "DashboardScreen", "_s", "navigate", "location", "searchParams", "page", "get", "dispatch", "userLogin", "state", "userInfo", "listCases", "caseList", "cases", "loadingCases", "errorCases", "pages", "redirect", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "src", "alt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/dashboard/DashboardScreen.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { casesList } from \"../../redux/actions/caseActions\";\n\nfunction DashboardScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n  const dispatch = useDispatch();\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listCases = useSelector((state) => state.caseList);\n  const { cases, loadingCases, errorCases, pages } = listCases;\n\n  const redirect = \"/\";\n\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(casesList(page));\n    }\n  }, [navigate, userInfo, dispatch, page]);\n  return (\n    <div className=\"container mx-auto flex flex-col\">\n      <div>UNIMEDCARE</div>\n      <div className=\"flex-1 flex md:flex-row items-center justify-around flex-col min-h-screen \">\n        <a\n          href=\"/cases\"\n          className=\"flex flex-col justify-center items-center mx-1 \"\n        >\n          <img\n            className=\"my-1 w-25\"\n            src=\"https://img.icons8.com/clouds/100/view-file--v2.png\"\n            alt=\"view-file--v2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            BUSQUEDA DE CASOS\n          </div>\n        </a>\n        {/*  */}\n        <a\n          href=\"/proveedors\"\n          className=\"flex flex-col justify-center items-center mx-1 \"\n        >\n          <img\n            className=\"my-1 w-25\"\n            src=\"https://img.icons8.com/clouds/100/view-file--v2.png\"\n            alt=\"view-file--v2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            BUSCADOR DE PROVEEDORES\n          </div>\n        </a>\n        {/*  */}\n        <a\n          href=\"/kps-informations\"\n          className=\"flex flex-col justify-center items-center mx-1 \"\n        >\n          <img\n            className=\"my-1 w-25\"\n            src=\"https://img.icons8.com/clouds/100/view-file--v2.png\"\n            alt=\"view-file--v2\"\n          />\n          <div className=\"bg-graydark px-4 py-2 my-1 rounded-full text-white\">\n            KPI ́S / INFORMES\n          </div>\n        </a>\n      </div>\n    </div>\n  );\n}\n\nexport default DashboardScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC5E,SAASC,SAAS,QAAQ,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,eAAeA,CAAA,EAAG;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACU,YAAY,CAAC,GAAGR,eAAe,CAAC,CAAC;EACxC,MAAMS,IAAI,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG;EAC5C,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAE9B,MAAMgB,SAAS,GAAGf,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE;EAAS,CAAC,GAAGF,SAAS;EAE9B,MAAMG,SAAS,GAAGlB,WAAW,CAAEgB,KAAK,IAAKA,KAAK,CAACG,QAAQ,CAAC;EACxD,MAAM;IAAEC,KAAK;IAAEC,YAAY;IAAEC,UAAU;IAAEC;EAAM,CAAC,GAAGL,SAAS;EAE5D,MAAMM,QAAQ,GAAG,GAAG;EAEpB1B,SAAS,CAAC,MAAM;IACd,IAAI,CAACmB,QAAQ,EAAE;MACbR,QAAQ,CAACe,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLV,QAAQ,CAACV,SAAS,CAACQ,IAAI,CAAC,CAAC;IAC3B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEQ,QAAQ,EAAEH,QAAQ,EAAEF,IAAI,CAAC,CAAC;EACxC,oBACEN,OAAA;IAAKmB,SAAS,EAAC,iCAAiC;IAAAC,QAAA,gBAC9CpB,OAAA;MAAAoB,QAAA,EAAK;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACrBxB,OAAA;MAAKmB,SAAS,EAAC,4EAA4E;MAAAC,QAAA,gBACzFpB,OAAA;QACEyB,IAAI,EAAC,QAAQ;QACbN,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DpB,OAAA;UACEmB,SAAS,EAAC,WAAW;UACrBO,GAAG,EAAC,qDAAqD;UACzDC,GAAG,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFxB,OAAA;UAAKmB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEJxB,OAAA;QACEyB,IAAI,EAAC,aAAa;QAClBN,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DpB,OAAA;UACEmB,SAAS,EAAC,WAAW;UACrBO,GAAG,EAAC,qDAAqD;UACzDC,GAAG,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFxB,OAAA;UAAKmB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEJxB,OAAA;QACEyB,IAAI,EAAC,mBAAmB;QACxBN,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAE3DpB,OAAA;UACEmB,SAAS,EAAC,WAAW;UACrBO,GAAG,EAAC,qDAAqD;UACzDC,GAAG,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eACFxB,OAAA;UAAKmB,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAEpE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACtB,EAAA,CAtEQD,eAAe;EAAA,QACLL,WAAW,EACXD,WAAW,EACLE,eAAe,EAErBJ,WAAW,EAEVC,WAAW,EAGXA,WAAW;AAAA;AAAAkC,EAAA,GAVtB3B,eAAe;AAwExB,eAAeA,eAAe;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}