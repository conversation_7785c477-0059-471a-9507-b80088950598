{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON>sin <PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction DetailCaseScreen() {\n  _s();\n  var _caseInfo$patient$ful, _caseInfo$patient, _caseInfo$patient$ful2, _caseInfo$patient2, _caseInfo$patient$bir, _caseInfo$patient3, _caseInfo$patient$pat, _caseInfo$patient4, _caseInfo$patient$pat2, _caseInfo$patient5, _caseInfo$patient$pat3, _caseInfo$patient6, _caseInfo$patient$ful3, _caseInfo$patient7, _caseInfo$patient$bir2, _caseInfo$patient8;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let {\n    id\n  } = useParams();\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n\n  //\n  const userLogin = useSelector(state => state.userLogin);\n  const {\n    userInfo,\n    loading,\n    error\n  } = userLogin;\n  const caseDetail = useSelector(state => state.detailCase);\n  const {\n    loadingCaseInfo,\n    errorCaseInfo,\n    successCaseInfo,\n    caseInfo\n  } = caseDetail;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  };\n\n  //\n  return /*#__PURE__*/_jsxDEV(DefaultLayout, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-row text-sm items-center my-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/dashboard\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row  items-center hover:text-black \",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              xmlns: \"http://www.w3.org/2000/svg\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              \"stroke-width\": \"1.5\",\n              stroke: \"currentColor\",\n              className: \"w-4 h-4\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                d: \"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mx-1\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"/cases-list\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"\",\n            children: \"Cases List\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            \"stroke-width\": \"1.5\",\n            stroke: \"currentColor\",\n            className: \"w-4 h-4\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"\",\n          children: \"Case Page\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), loadingCaseInfo ? /*#__PURE__*/_jsxDEV(Loader, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 11\n      }, this) : errorCaseInfo ? /*#__PURE__*/_jsxDEV(Alert, {\n        type: \"error\",\n        message: errorCaseInfo\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this) : caseInfo ? /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \" text-[#32475C] text-md font-medium opacity-85\",\n            children: [\"#\", caseInfo.id]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-row items-center my-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center mr-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-60 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-60 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Full Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 21\n                }, this), \" \", (_caseInfo$patient$ful = (_caseInfo$patient = caseInfo.patient) === null || _caseInfo$patient === void 0 ? void 0 : _caseInfo$patient.full_name) !== null && _caseInfo$patient$ful !== void 0 ? _caseInfo$patient$ful : \"---\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-row items-center ml-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  xmlns: \"http://www.w3.org/2000/svg\",\n                  fill: \"none\",\n                  viewBox: \"0 0 24 24\",\n                  \"stroke-width\": \"1.5\",\n                  stroke: \"currentColor\",\n                  class: \"size-4 mx-1 text-[#303030]  opacity-60 \",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    \"stroke-linecap\": \"round\",\n                    \"stroke-linejoin\": \"round\",\n                    d: \"m4.5 12.75 6 6 9-13.5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mx-1 text-[#303030] text-sm opacity-60 \",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: \"Status:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 21\n                }, this), \" Active\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-2 bg-white shadow-1 px-3 py-4 rounded\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",\n            children: [\"General Information\", \"Coordination Details\", \"Medical Reports\", \"Invoices\", \"Insurance Authorization\"].map((select, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectPage(select),\n              className: `px-4 py-1 md:my-0 my-1  text-sm ${selectPage === select ? \"rounded-full bg-[#0388A6] text-white font-medium \" : \"font-normal text-[#838383]\"}`,\n              children: select\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Patient Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Name:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful2 = (_caseInfo$patient2 = caseInfo.patient) === null || _caseInfo$patient2 === void 0 ? void 0 : _caseInfo$patient2.full_name) !== null && _caseInfo$patient$ful2 !== void 0 ? _caseInfo$patient$ful2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Date of Birth:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir = (_caseInfo$patient3 = caseInfo.patient) === null || _caseInfo$patient3 === void 0 ? void 0 : _caseInfo$patient3.birth_day) !== null && _caseInfo$patient$bir !== void 0 ? _caseInfo$patient$bir : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Phone:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat = (_caseInfo$patient4 = caseInfo.patient) === null || _caseInfo$patient4 === void 0 ? void 0 : _caseInfo$patient4.patient_phone) !== null && _caseInfo$patient$pat !== void 0 ? _caseInfo$patient$pat : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Email:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat2 = (_caseInfo$patient5 = caseInfo.patient) === null || _caseInfo$patient5 === void 0 ? void 0 : _caseInfo$patient5.patient_email) !== null && _caseInfo$patient$pat2 !== void 0 ? _caseInfo$patient$pat2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Address:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$pat3 = (_caseInfo$patient6 = caseInfo.patient) === null || _caseInfo$patient6 === void 0 ? void 0 : _caseInfo$patient6.patient_address) !== null && _caseInfo$patient$pat3 !== void 0 ? _caseInfo$patient$pat3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"md:w-1/2 w-full  px-2 y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"my-2 text-xs text-[#303030] opacity-60\",\n                children: \"Case Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Case Creation Date:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: formatDate(item.created_at)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Assigned Coordinator:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$ful3 = (_caseInfo$patient7 = caseInfo.patient) === null || _caseInfo$patient7 === void 0 ? void 0 : _caseInfo$patient7.full_name) !== null && _caseInfo$patient$ful3 !== void 0 ? _caseInfo$patient$ful3 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-semibold\",\n                  children: \"Description:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 mx-1\",\n                  children: (_caseInfo$patient$bir2 = (_caseInfo$patient8 = caseInfo.patient) === null || _caseInfo$patient8 === void 0 ? void 0 : _caseInfo$patient8.birth_day) !== null && _caseInfo$patient$bir2 !== void 0 ? _caseInfo$patient$bir2 : \"---\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 11\n      }, this) : null]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_s(DetailCaseScreen, \"u57wJLJbfM/Qcxlb6a4Azu8gwSc=\", false, function () {\n  return [useNavigate, useLocation, useDispatch, useParams, useSelector, useSelector];\n});\n_c = DetailCaseScreen;\nexport default DetailCaseScreen;\nvar _c;\n$RefreshReg$(_c, \"DetailCaseScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "detailCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DetailCaseScreen", "_s", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$ful2", "_caseInfo$patient2", "_caseInfo$patient$bir", "_caseInfo$patient3", "_caseInfo$patient$pat", "_caseInfo$patient4", "_caseInfo$patient$pat2", "_caseInfo$patient5", "_caseInfo$patient$pat3", "_caseInfo$patient6", "_caseInfo$patient$ful3", "_caseInfo$patient7", "_caseInfo$patient$bir2", "_caseInfo$patient8", "navigate", "location", "dispatch", "id", "selectPage", "setSelectPage", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "class", "patient", "full_name", "map", "select", "index", "onClick", "birth_day", "patient_phone", "patient_email", "patient_address", "item", "created_at", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\",\n    });\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\" text-[#32475C] text-md font-medium opacity-85\">\n                #{caseInfo.id}\n              </div>\n              <div className=\"flex flex-row items-center my-2\">\n                <div className=\"flex flex-row items-center mr-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Status:</span> Active\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/*  */}\n\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                <div className=\"md:w-1/2 w-full px-2 y-2\">\n                  <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                    Patient Details\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Name:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.full_name ?? \"---\"}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Date of Birth:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.birth_day ?? \"---\"}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Phone:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.patient_phone ?? \"---\"}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Email:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.patient_email ?? \"---\"}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Address:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.patient_address ?? \"---\"}\n                    </div>\n                  </div>\n                </div>\n                <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                  <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                    Case Details\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Case Creation Date:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {formatDate(item.created_at)}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Assigned Coordinator:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.full_name ?? \"---\"}\n                    </div>\n                  </div>\n                  <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                    <div className=\"font-semibold\">Description:</div>\n                    <div className=\"flex-1 mx-1\">\n                      {caseInfo.patient?.birth_day ?? \"---\"}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,EAAEC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACtE,SAASC,UAAU,QAAQ,iCAAiC;AAC5D,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,KAAK,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,sBAAA,EAAAC,kBAAA;EAC1B,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,IAAI;IAAEgC;EAAG,CAAC,GAAG5B,SAAS,CAAC,CAAC;EAExB,MAAM,CAAC6B,UAAU,EAAEC,aAAa,CAAC,GAAGnC,QAAQ,CAAC,qBAAqB,CAAC;;EAEnE;EACA,MAAMoC,SAAS,GAAGlC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAACD,SAAS,CAAC;EACzD,MAAM;IAAEE,QAAQ;IAAEC,OAAO;IAAEC;EAAM,CAAC,GAAGJ,SAAS;EAE9C,MAAMK,UAAU,GAAGvC,WAAW,CAAEmC,KAAK,IAAKA,KAAK,CAAC/B,UAAU,CAAC;EAC3D,MAAM;IAAEoC,eAAe;IAAEC,aAAa;IAAEC,eAAe;IAAEC;EAAS,CAAC,GACjEJ,UAAU;EACZ;EACA,MAAMK,QAAQ,GAAG,GAAG;EACpB/C,SAAS,CAAC,MAAM;IACd,IAAI,CAACuC,QAAQ,EAAE;MACbR,QAAQ,CAACgB,QAAQ,CAAC;IACpB,CAAC,MAAM;MACLd,QAAQ,CAAC1B,UAAU,CAAC2B,EAAE,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,QAAQ,EAAEQ,QAAQ,EAAEN,QAAQ,EAAEC,EAAE,CAAC,CAAC;EAEtC,MAAMc,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACtCC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,oBACE3C,OAAA,CAACJ,aAAa;IAAAgD,QAAA,eACZ5C,OAAA;MAAK6C,SAAS,EAAC,EAAE;MAAAD,QAAA,gBACf5C,OAAA;QAAK6C,SAAS,EAAC,yCAAyC;QAAAD,QAAA,gBAEtD5C,OAAA;UAAG8C,IAAI,EAAC,YAAY;UAAAF,QAAA,eAClB5C,OAAA;YAAK6C,SAAS,EAAC,+CAA+C;YAAAD,QAAA,gBAC5D5C,OAAA;cACE+C,KAAK,EAAC,4BAA4B;cAClCC,IAAI,EAAC,MAAM;cACXC,OAAO,EAAC,WAAW;cACnB,gBAAa,KAAK;cAClBC,MAAM,EAAC,cAAc;cACrBL,SAAS,EAAC,SAAS;cAAAD,QAAA,eAEnB5C,OAAA;gBACEmD,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,CAAC,EAAC;cAA4O;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/O;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNzD,OAAA;cAAM6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAC;YAAS;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACJzD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACE+C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5C,OAAA;cACEmD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzD,OAAA;UAAG8C,IAAI,EAAC,aAAa;UAAAF,QAAA,eACnB5C,OAAA;YAAK6C,SAAS,EAAC,EAAE;YAAAD,QAAA,EAAC;UAAU;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACJzD,OAAA;UAAA4C,QAAA,eACE5C,OAAA;YACE+C,KAAK,EAAC,4BAA4B;YAClCC,IAAI,EAAC,MAAM;YACXC,OAAO,EAAC,WAAW;YACnB,gBAAa,KAAK;YAClBC,MAAM,EAAC,cAAc;YACrBL,SAAS,EAAC,SAAS;YAAAD,QAAA,eAEnB5C,OAAA;cACEmD,aAAa,EAAC,OAAO;cACrBC,cAAc,EAAC,OAAO;cACtBC,CAAC,EAAC;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACPzD,OAAA;UAAK6C,SAAS,EAAC,EAAE;UAAAD,QAAA,EAAC;QAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGL1B,eAAe,gBACd/B,OAAA,CAACH,MAAM;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GACRzB,aAAa,gBACfhC,OAAA,CAACF,KAAK;QAAC4D,IAAI,EAAE,OAAQ;QAACC,OAAO,EAAE3B;MAAc;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAC9CvB,QAAQ,gBACVlC,OAAA;QAAA4C,QAAA,gBAEE5C,OAAA;UAAK6C,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD5C,OAAA;YAAK6C,SAAS,EAAC,gDAAgD;YAAAD,QAAA,GAAC,GAC7D,EAACV,QAAQ,CAACZ,EAAE;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNzD,OAAA;YAAK6C,SAAS,EAAC,iCAAiC;YAAAD,QAAA,gBAC9C5C,OAAA;cAAK6C,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9C5C,OAAA;gBAAA4C,QAAA,eACE5C,OAAA;kBACE+C,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C5C,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBqD,CAAC,EAAC;kBAAyJ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5J;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD5C,OAAA;kBAAM6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAU;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,EAAC,GAAG,GAAAtD,qBAAA,IAAAC,iBAAA,GACpD8B,QAAQ,CAAC2B,OAAO,cAAAzD,iBAAA,uBAAhBA,iBAAA,CAAkB0D,SAAS,cAAA3D,qBAAA,cAAAA,qBAAA,GAAI,KAAK;cAAA;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzD,OAAA;cAAK6C,SAAS,EAAC,iCAAiC;cAAAD,QAAA,gBAC9C5C,OAAA;gBAAA4C,QAAA,eACE5C,OAAA;kBACE+C,KAAK,EAAC,4BAA4B;kBAClCC,IAAI,EAAC,MAAM;kBACXC,OAAO,EAAC,WAAW;kBACnB,gBAAa,KAAK;kBAClBC,MAAM,EAAC,cAAc;kBACrBU,KAAK,EAAC,yCAAyC;kBAAAhB,QAAA,eAE/C5C,OAAA;oBACE,kBAAe,OAAO;oBACtB,mBAAgB,OAAO;oBACvBqD,CAAC,EAAC;kBAAuB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,yCAAyC;gBAAAD,QAAA,gBACtD5C,OAAA;kBAAM6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAO;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,WAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC;cAAE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzD,OAAA;UAAK6C,SAAS,EAAC,0CAA0C;UAAAD,QAAA,gBACvD5C,OAAA;YAAK6C,SAAS,EAAC,iGAAiG;YAAAD,QAAA,EAC7G,CACC,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,EACjB,UAAU,EACV,yBAAyB,CAC1B,CAACmB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAClBjE,OAAA;cACEkE,OAAO,EAAEA,CAAA,KAAM1C,aAAa,CAACwC,MAAM,CAAE;cACrCnB,SAAS,EAAG,mCACVtB,UAAU,KAAKyC,MAAM,GACjB,mDAAmD,GACnD,4BACL,EAAE;cAAApB,QAAA,EAEFoB;YAAM;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNzD,OAAA;YAAK6C,SAAS,EAAC,0EAA0E;YAAAD,QAAA,gBACvF5C,OAAA;cAAK6C,SAAS,EAAC,0BAA0B;cAAAD,QAAA,gBACvC5C,OAAA;gBAAK6C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1CzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAvC,sBAAA,IAAAC,kBAAA,GACzB4B,QAAQ,CAAC2B,OAAO,cAAAvD,kBAAA,uBAAhBA,kBAAA,CAAkBwD,SAAS,cAAAzD,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAc;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAArC,qBAAA,IAAAC,kBAAA,GACzB0B,QAAQ,CAAC2B,OAAO,cAAArD,kBAAA,uBAAhBA,kBAAA,CAAkB2D,SAAS,cAAA5D,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAnC,qBAAA,IAAAC,kBAAA,GACzBwB,QAAQ,CAAC2B,OAAO,cAAAnD,kBAAA,uBAAhBA,kBAAA,CAAkB0D,aAAa,cAAA3D,qBAAA,cAAAA,qBAAA,GAAI;gBAAK;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAM;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3CzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAAjC,sBAAA,IAAAC,kBAAA,GACzBsB,QAAQ,CAAC2B,OAAO,cAAAjD,kBAAA,uBAAhBA,kBAAA,CAAkByD,aAAa,cAAA1D,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAQ;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7CzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA/B,sBAAA,IAAAC,kBAAA,GACzBoB,QAAQ,CAAC2B,OAAO,cAAA/C,kBAAA,uBAAhBA,kBAAA,CAAkBwD,eAAe,cAAAzD,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzD,OAAA;cAAK6C,SAAS,EAAC,2BAA2B;cAAAD,QAAA,gBACxC5C,OAAA;gBAAK6C,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,EAAC;cAExD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,EACzBR,UAAU,CAACmC,IAAI,CAACC,UAAU;gBAAC;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAqB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1DzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA7B,sBAAA,IAAAC,kBAAA,GACzBkB,QAAQ,CAAC2B,OAAO,cAAA7C,kBAAA,uBAAhBA,kBAAA,CAAkB8C,SAAS,cAAA/C,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAK6C,SAAS,EAAC,sDAAsD;gBAAAD,QAAA,gBACnE5C,OAAA;kBAAK6C,SAAS,EAAC,eAAe;kBAAAD,QAAA,EAAC;gBAAY;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDzD,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,GAAA3B,sBAAA,IAAAC,kBAAA,GACzBgB,QAAQ,CAAC2B,OAAO,cAAA3C,kBAAA,uBAAhBA,kBAAA,CAAkBiD,SAAS,cAAAlD,sBAAA,cAAAA,sBAAA,GAAI;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACJ,IAAI;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACvD,EAAA,CAtPQD,gBAAgB;EAAA,QACNR,WAAW,EACXD,WAAW,EACXF,WAAW,EACfI,SAAS,EAKJH,WAAW,EAGVA,WAAW;AAAA;AAAAkF,EAAA,GAZvBxE,gBAAgB;AAwPzB,eAAeA,gBAAgB;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}