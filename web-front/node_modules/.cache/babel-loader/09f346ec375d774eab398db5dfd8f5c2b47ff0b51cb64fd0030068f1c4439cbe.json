{"ast": null, "code": "import{createStore,combineReducers,applyMiddleware}from\"redux\";import thunk from\"redux-thunk\";import{composeWithDevTools}from\"redux-devtools-extension\";import{createNewUserReducer,deleteUserReducer,getProfileUserReducer,updateProfileUserReducer,userLoginReducer,usersListReducer}from\"./reducers/userReducers\";import{clientListReducer,createNewClientReducer,deleteClientReducer,detailClientReducer,updateClientReducer}from\"./reducers/clientReducers\";import{caseListReducer,createNewCaseReducer,deleteCaseReducer,detailCaseReducer,updateCaseReducer}from\"./reducers/caseReducers\";import{detailProviderReducer,providerListReducer}from\"./reducers/providerReducers\";const reducer=combineReducers({userLogin:userLoginReducer,// cases\ncaseList:caseListReducer,detailCase:detailCaseReducer,createNewCase:createNewCaseReducer,deleteCase:deleteCaseReducer,updateCase:updateCaseReducer,// providers\nproviderList:providerListReducer,detailProvider:detailProviderReducer,//\nclientList:clientListReducer,createNewClient:createNewClientReducer,detailClient:detailClientReducer,updateClient:updateClientReducer,deleteClient:deleteClientReducer,//\n//\nusersList:usersListReducer,createNewUser:createNewUserReducer,getProfileUser:getProfileUserReducer,updateProfileUser:updateProfileUserReducer,deleteUser:deleteUserReducer//\n});const userInfoFromStorage=localStorage.getItem(\"userInfoUnimedCare\")?JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")):null;const initialState={userLogin:{userInfo:userInfoFromStorage}};const middleware=[thunk];const store=createStore(reducer,initialState,composeWithDevTools(applyMiddleware(...middleware)));export default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "createNewUserReducer", "deleteUserReducer", "getProfileUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListReducer", "createNewCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseReducer", "detailProviderReducer", "providerListReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "providerList", "detail<PERSON>rovider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  createNewUserReducer,\n  deleteUserReducer,\n  getProfileUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListReducer,\n  createNewCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  detailProviderReducer,\n  providerListReducer,\n} from \"./reducers/providerReducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  composeWithDevTools(applyMiddleware(...middleware))\n);\n\nexport default store;\n"], "mappings": "AAAA,OAASA,WAAW,CAAEC,eAAe,CAAEC,eAAe,KAAQ,OAAO,CACrE,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,OAASC,mBAAmB,KAAQ,0BAA0B,CAE9D,OACEC,oBAAoB,CACpBC,iBAAiB,CACjBC,qBAAqB,CACrBC,wBAAwB,CACxBC,gBAAgB,CAChBC,gBAAgB,KACX,yBAAyB,CAChC,OACEC,iBAAiB,CACjBC,sBAAsB,CACtBC,mBAAmB,CACnBC,mBAAmB,CACnBC,mBAAmB,KACd,2BAA2B,CAElC,OACEC,eAAe,CACfC,oBAAoB,CACpBC,iBAAiB,CACjBC,iBAAiB,CACjBC,iBAAiB,KACZ,yBAAyB,CAChC,OACEC,qBAAqB,CACrBC,mBAAmB,KACd,6BAA6B,CAEpC,KAAM,CAAAC,OAAO,CAAGtB,eAAe,CAAC,CAC9BuB,SAAS,CAAEf,gBAAgB,CAE3B;AACAgB,QAAQ,CAAET,eAAe,CACzBU,UAAU,CAAEP,iBAAiB,CAC7BQ,aAAa,CAAEV,oBAAoB,CACnCW,UAAU,CAAEV,iBAAiB,CAC7BW,UAAU,CAAET,iBAAiB,CAC7B;AACAU,YAAY,CAAER,mBAAmB,CACjCS,cAAc,CAAEV,qBAAqB,CACrC;AACAW,UAAU,CAAErB,iBAAiB,CAC7BsB,eAAe,CAAErB,sBAAsB,CACvCsB,YAAY,CAAEpB,mBAAmB,CACjCqB,YAAY,CAAEpB,mBAAmB,CACjCqB,YAAY,CAAEvB,mBAAmB,CACjC;AAEA;AACAwB,SAAS,CAAE3B,gBAAgB,CAC3B4B,aAAa,CAAEjC,oBAAoB,CACnCkC,cAAc,CAAEhC,qBAAqB,CACrCiC,iBAAiB,CAAEhC,wBAAwB,CAC3CiC,UAAU,CAAEnC,iBACZ;AACF,CAAC,CAAC,CAEF,KAAM,CAAAoC,mBAAmB,CAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CACtD,IAAI,CAER,KAAM,CAAAG,YAAY,CAAG,CACnBvB,SAAS,CAAE,CAAEwB,QAAQ,CAAEN,mBAAoB,CAC7C,CAAC,CAED,KAAM,CAAAO,UAAU,CAAG,CAAC9C,KAAK,CAAC,CAE1B,KAAM,CAAA+C,KAAK,CAAGlD,WAAW,CACvBuB,OAAO,CACPwB,YAAY,CACZ3C,mBAAmB,CAACF,eAAe,CAAC,GAAG+C,UAAU,CAAC,CACpD,CAAC,CAED,cAAe,CAAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}