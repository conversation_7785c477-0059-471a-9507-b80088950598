{"ast": null, "code": "import React from\"react\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const InputModel=_ref=>{let{label,type,placeholder,value,onChange,error,options,isPrice,disabled=false,refr=null,accept=null,ismultiple=false,min=0,isMax=false,max=0}=_ref;const inputElement=type===\"select\"?/*#__PURE__*/_jsxs(\"select\",{ref:refr,disabled:disabled,multiple:ismultiple,className:`${disabled?\"bg-gray\":\"\"} font-bold text-[14px] block w-full p-2 ${ismultiple?\"\":\"h-[34px]\"}  py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,value:value,style:{boxShadow:\"inset 0 1px 1px rgba(0,0,0,.075)\",transition:\"border-color ease-in-out .15s, box-shadow ease-in-out .15s\"},onChange:onChange,children:[/*#__PURE__*/_jsx(\"option\",{value:\"\"}),options===null||options===void 0?void 0:options.map(option=>/*#__PURE__*/_jsx(\"option\",{value:option.value,children:option.label},option.value))]}):type===\"textarea\"?/*#__PURE__*/_jsx(\"textarea\",{disabled:disabled,ref:refr,placeholder:placeholder,style:{boxShadow:\"inset 0 1px 1px rgba(0,0,0,.075)\",transition:\"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",resize:\"vertical\"},className:`${disabled?\"bg-gray\":\"\"} font-bold text-[14px] block w-full p-2 py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,value:value,onChange:onChange}):/*#__PURE__*/_jsx(\"input\",{disabled:disabled,type:type,ref:refr,min:min,max:isMax?max:undefined,accept:accept,step:isPrice?0.01:1,placeholder:placeholder,style:{boxShadow:\"inset 0 1px 1px rgba(0,0,0,.075)\",transition:\"border-color ease-in-out .15s, box-shadow ease-in-out .15s\"},className:`${disabled?\"bg-gray\":\"\"}  font-bold text-[14px] block w-full p-2 h-[34px] py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`,value:value,onChange:onChange});return/*#__PURE__*/_jsxs(\"div\",{className:\"md:flex-1 md:mr-1 md:mb-0 mb-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`mt-1 relative`,children:[/*#__PURE__*/_jsx(\"label\",{className:\"absolute text-[#898989] text-[14px] mt-[-16px] ml-[7px] px-[5px] bg-[#FFFFFF] line-clamp-1\\t\",children:label}),inputElement]}),error&&/*#__PURE__*/_jsx(\"p\",{className:\"text-[9px] italic text-danger leading-none mt-1\",children:error})]});};export default InputModel;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "InputModel", "_ref", "label", "type", "placeholder", "value", "onChange", "error", "options", "isPrice", "disabled", "refr", "accept", "ismultiple", "min", "isMax", "max", "inputElement", "ref", "multiple", "className", "style", "boxShadow", "transition", "children", "map", "option", "resize", "undefined", "step"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/components/InputModel.js"], "sourcesContent": ["import React from \"react\";\n\nconst InputModel = ({\n  label,\n  type,\n  placeholder,\n  value,\n  onChange,\n  error,\n  options,\n  isPrice,\n  disabled = false,\n  refr = null,\n  accept = null,\n  ismultiple = false,\n  min = 0,\n  isMax = false,\n  max = 0,\n}) => {\n  const inputElement =\n    type === \"select\" ? (\n      <select\n        ref={refr}\n        disabled={disabled}\n        multiple={ismultiple}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        } font-bold text-[14px] block w-full p-2 ${\n          ismultiple ? \"\" : \"h-[34px]\"\n        }  py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n        }}\n        onChange={onChange}\n      >\n        <option value={\"\"}></option>\n        {options?.map((option) => (\n          <option key={option.value} value={option.value}>\n            {option.label}\n          </option>\n        ))}\n      </select>\n    ) : type === \"textarea\" ? (\n      <textarea\n        disabled={disabled}\n        ref={refr}\n        placeholder={placeholder}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n          resize: \"vertical\",\n        }}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        } font-bold text-[14px] block w-full p-2 py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        onChange={onChange}\n      />\n    ) : (\n      <input\n        disabled={disabled}\n        type={type}\n        ref={refr}\n        min={min}\n        max={isMax ? max : undefined}\n        accept={accept}\n        step={isPrice ? 0.01 : 1}\n        placeholder={placeholder}\n        style={{\n          boxShadow: \"inset 0 1px 1px rgba(0,0,0,.075)\",\n          transition:\n            \"border-color ease-in-out .15s, box-shadow ease-in-out .15s\",\n        }}\n        className={`${\n          disabled ? \"bg-gray\" : \"\"\n        }  font-bold text-[14px] block w-full p-2 h-[34px] py-[6px] px-3 text-[#555] border-[#ccc] focus:border rounded border`}\n        value={value}\n        onChange={onChange}\n      />\n    );\n\n  return (\n    <div className=\"md:flex-1 md:mr-1 md:mb-0 mb-5\">\n      <div className={`mt-1 relative`}>\n        <label className=\"absolute text-[#898989] text-[14px] mt-[-16px] ml-[7px] px-[5px] bg-[#FFFFFF] line-clamp-1\t\">\n          {label}\n        </label>\n        {inputElement}\n      </div>\n      {error && (\n        <p className=\"text-[9px] italic text-danger leading-none mt-1\">\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default InputModel;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAgBb,IAhBc,CAClBC,KAAK,CACLC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,QAAQ,CACRC,KAAK,CACLC,OAAO,CACPC,OAAO,CACPC,QAAQ,CAAG,KAAK,CAChBC,IAAI,CAAG,IAAI,CACXC,MAAM,CAAG,IAAI,CACbC,UAAU,CAAG,KAAK,CAClBC,GAAG,CAAG,CAAC,CACPC,KAAK,CAAG,KAAK,CACbC,GAAG,CAAG,CACR,CAAC,CAAAf,IAAA,CACC,KAAM,CAAAgB,YAAY,CAChBd,IAAI,GAAK,QAAQ,cACfJ,KAAA,WACEmB,GAAG,CAAEP,IAAK,CACVD,QAAQ,CAAEA,QAAS,CACnBS,QAAQ,CAAEN,UAAW,CACrBO,SAAS,CAAG,GACVV,QAAQ,CAAG,SAAS,CAAG,EACxB,2CACCG,UAAU,CAAG,EAAE,CAAG,UACnB,uEAAuE,CACxER,KAAK,CAAEA,KAAM,CACbgB,KAAK,CAAE,CACLC,SAAS,CAAE,kCAAkC,CAC7CC,UAAU,CACR,4DACJ,CAAE,CACFjB,QAAQ,CAAEA,QAAS,CAAAkB,QAAA,eAEnB3B,IAAA,WAAQQ,KAAK,CAAE,EAAG,CAAS,CAAC,CAC3BG,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEiB,GAAG,CAAEC,MAAM,eACnB7B,IAAA,WAA2BQ,KAAK,CAAEqB,MAAM,CAACrB,KAAM,CAAAmB,QAAA,CAC5CE,MAAM,CAACxB,KAAK,EADFwB,MAAM,CAACrB,KAEZ,CACT,CAAC,EACI,CAAC,CACPF,IAAI,GAAK,UAAU,cACrBN,IAAA,aACEa,QAAQ,CAAEA,QAAS,CACnBQ,GAAG,CAAEP,IAAK,CACVP,WAAW,CAAEA,WAAY,CACzBiB,KAAK,CAAE,CACLC,SAAS,CAAE,kCAAkC,CAC7CC,UAAU,CACR,4DAA4D,CAC9DI,MAAM,CAAE,UACV,CAAE,CACFP,SAAS,CAAG,GACVV,QAAQ,CAAG,SAAS,CAAG,EACxB,6GAA6G,CAC9GL,KAAK,CAAEA,KAAM,CACbC,QAAQ,CAAEA,QAAS,CACpB,CAAC,cAEFT,IAAA,UACEa,QAAQ,CAAEA,QAAS,CACnBP,IAAI,CAAEA,IAAK,CACXe,GAAG,CAAEP,IAAK,CACVG,GAAG,CAAEA,GAAI,CACTE,GAAG,CAAED,KAAK,CAAGC,GAAG,CAAGY,SAAU,CAC7BhB,MAAM,CAAEA,MAAO,CACfiB,IAAI,CAAEpB,OAAO,CAAG,IAAI,CAAG,CAAE,CACzBL,WAAW,CAAEA,WAAY,CACzBiB,KAAK,CAAE,CACLC,SAAS,CAAE,kCAAkC,CAC7CC,UAAU,CACR,4DACJ,CAAE,CACFH,SAAS,CAAG,GACVV,QAAQ,CAAG,SAAS,CAAG,EACxB,uHAAuH,CACxHL,KAAK,CAAEA,KAAM,CACbC,QAAQ,CAAEA,QAAS,CACpB,CACF,CAEH,mBACEP,KAAA,QAAKqB,SAAS,CAAC,gCAAgC,CAAAI,QAAA,eAC7CzB,KAAA,QAAKqB,SAAS,CAAG,eAAe,CAAAI,QAAA,eAC9B3B,IAAA,UAAOuB,SAAS,CAAC,8FAA6F,CAAAI,QAAA,CAC3GtB,KAAK,CACD,CAAC,CACPe,YAAY,EACV,CAAC,CACLV,KAAK,eACJV,IAAA,MAAGuB,SAAS,CAAC,iDAAiD,CAAAI,QAAA,CAC3DjB,KAAK,CACL,CACJ,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}