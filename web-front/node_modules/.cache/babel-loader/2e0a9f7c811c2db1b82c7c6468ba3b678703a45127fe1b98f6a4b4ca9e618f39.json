{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams,useSearchParams}from\"react-router-dom\";import{addNewCommentCase,detailCase,getListCommentCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile}from\"../../constants\";import{useDropzone}from\"react-dropzone\";import{toast}from\"react-toastify\";import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function DetailCaseScreen(){var _caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$ful2,_caseInfo$patient2,_caseInfo$patient$bir,_caseInfo$patient3,_caseInfo$patient$pat,_caseInfo$patient4,_caseInfo$patient$pat2,_caseInfo$patient5,_caseInfo$patient$pat3,_caseInfo$patient6,_caseInfo$coordinator,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$service_loc,_caseInfo$provider$fu,_caseInfo$provider,_caseInfo$provider$ph,_caseInfo$provider2,_caseInfo$provider$em,_caseInfo$provider3,_caseInfo$provider$ad,_caseInfo$provider4,_caseInfo$medical_rep,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$assurance_s,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$policy_numb,_caseInfo$upload_auth;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[searchParams]=useSearchParams();const page=searchParams.get(\"page\")||\"1\";const[selectPage,setSelectPage]=useState(\"General Information\");const[commentInput,setCommentInput]=useState(\"\");const[commentInputError,setCommentInputError]=useState(\"\");// files comment\n// initialMedicalReports\nconst[filesComments,setFilesComments]=useState([]);const{getRootProps:getRootComments,getInputProps:getInputComments}=useDropzone({accept:{\"image/*\":[]},onDrop:acceptedFiles=>{setFilesComments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesComments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;const listCommentCase=useSelector(state=>state.commentCaseList);const{comments,loadingCommentCase,errorCommentCase,pages}=listCommentCase;const createCommentCase=useSelector(state=>state.createNewCommentCase);const{loadingCommentCaseAdd,successCommentCaseAdd,errorCommentCaseAdd}=createCommentCase;//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailCase(id));dispatch(getListCommentCase(\"0\",id));}},[navigate,userInfo,dispatch,id,page]);useEffect(()=>{if(successCommentCaseAdd){setCommentInput(\"\");setCommentInputError(\"\");setFilesComments([]);dispatch(getListCommentCase(\"0\",id));}},[successCommentCaseAdd]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};//\nreturn/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-md font-medium opacity-85\",children:[\"#\",caseInfo.id]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center mr-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-60 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-60 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Full Name:\"}),\" \",(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-60 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-60 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Status:\"}),\" \",caseStatus(caseInfo.status_coordination)]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectPage(select),className:\"px-4 py-1 md:my-0 my-1  text-sm \".concat(selectPage===select?\"rounded-full bg-[#0388A6] text-white font-medium \":\"font-normal text-[#838383]\"),children:select}))}),selectPage===\"General Information\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Patient Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$ful2=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date of Birth:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$bir=(_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat2=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Address:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat3=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Case Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Case Creation Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Assigned Coordinator:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$coordinator=caseInfo.coordinator)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Description:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Coordination Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Current Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Last Updated Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.updated_at)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Appointment Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Scheduled Appointment Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.appointment_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Provider Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Provider Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$fu=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.full_name)!==null&&_caseInfo$provider$fu!==void 0?_caseInfo$provider$fu:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ph=(_caseInfo$provider2=caseInfo.provider)===null||_caseInfo$provider2===void 0?void 0:_caseInfo$provider2.phone)!==null&&_caseInfo$provider$ph!==void 0?_caseInfo$provider$ph:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$em=(_caseInfo$provider3=caseInfo.provider)===null||_caseInfo$provider3===void 0?void 0:_caseInfo$provider3.email)!==null&&_caseInfo$provider$em!==void 0?_caseInfo$provider$em:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Address:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ad=(_caseInfo$provider4=caseInfo.provider)===null||_caseInfo$provider4===void 0?void 0:_caseInfo$provider4.address)!==null&&_caseInfo$provider$ad!==void 0?_caseInfo$provider$ad:\"---\"})]})]})]})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Invoice Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date Issued:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.date_issued)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Amount:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[\"$\",parseFloat(caseInfo.invoice_amount).toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Due Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Insurance Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Insurance Company Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Policy Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 b py-3  px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1  py-1 px-2\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Comment\"}),/*#__PURE__*/_jsx(\"textarea\",{value:commentInput,onChange:v=>setCommentInput(v.target.value),className:\"  \".concat(commentInputError?\"border-danger\":\"border-[#F1F3FF]\",\" min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3\")}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:commentInputError?commentInputError:\"\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"md:w-1/2 w-full\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"my-1 bg-white py-1 px-2 rounded-md\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Images\"}),/*#__PURE__*/_jsxs(\"div\",{...getRootComments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputComments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-7 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-sm\",children:\"Drag & Drop Images or BROWSE\"})]})]})})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesComments===null||filesComments===void 0?void 0:filesComments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" text-[#81838E] text-center  shadow-1 \",children:/*#__PURE__*/_jsx(\"img\",{src:file.preview,className:\"size-8\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesComments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{disabled:loadingCommentCaseAdd,onClick:async()=>{var check=true;setCommentInputError(\"\");if(commentInput===\"\"&&filesComments.length===0){setCommentInputError(\"This field is required.\");check=false;}if(check){await dispatch(addNewCommentCase({content:commentInput,// files\nfiles_commet:filesComments},id));}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\",children:loadingCommentCaseAdd?\"Loading ..\":\"Save\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5\",children:loadingCommentCase?/*#__PURE__*/_jsx(Loader,{}):errorCommentCase?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCommentCase}):comments?/*#__PURE__*/_jsx(_Fragment,{children:comments===null||comments===void 0?void 0:comments.map((comment,index)=>{var _comment$coordinator,_comment$coordinator2,_comment$coordinator$,_comment$coordinator3,_comment$coordinator$2,_comment$coordinator4,_comment$coordinator$3,_comment$coordinator5,_comment$content,_comment$files;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-start\",children:[/*#__PURE__*/_jsx(\"div\",{children:comment.coordinator?(_comment$coordinator=comment.coordinator)!==null&&_comment$coordinator!==void 0&&_comment$coordinator.photo?/*#__PURE__*/_jsx(\"img\",{className:\" size-12 rounded-full\",src:baseURLFile+((_comment$coordinator2=comment.coordinator)===null||_comment$coordinator2===void 0?void 0:_comment$coordinator2.photo)}):/*#__PURE__*/_jsx(\"div\",{className:\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\" uppercase\",children:[(_comment$coordinator$=(_comment$coordinator3=comment.coordinator)===null||_comment$coordinator3===void 0?void 0:_comment$coordinator3.first_name[0])!==null&&_comment$coordinator$!==void 0?_comment$coordinator$:\"\",(_comment$coordinator$2=(_comment$coordinator4=comment.coordinator)===null||_comment$coordinator4===void 0?void 0:_comment$coordinator4.last_name[0])!==null&&_comment$coordinator$2!==void 0?_comment$coordinator$2:\"\"]})}):null}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row mb-1 items-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1 text-xs\",children:formatDate(comment.created_at)})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1 font-semibold\",children:(_comment$coordinator$3=(_comment$coordinator5=comment.coordinator)===null||_comment$coordinator5===void 0?void 0:_comment$coordinator5.full_name)!==null&&_comment$coordinator$3!==void 0?_comment$coordinator$3:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm my-1\",children:(_comment$content=comment.content)!==null&&_comment$content!==void 0?_comment$content:\"\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap items-center  my-1\",children:(_comment$files=comment.files)===null||_comment$files===void 0?void 0:_comment$files.map((file,index)=>/*#__PURE__*/_jsx(\"a\",{target:\"_blank\",rel:\"noopener noreferrer\",href:baseURLFile+file.file,children:/*#__PURE__*/_jsx(\"img\",{src:baseURLFile+file.file,className:\"size-30 shadow-1 rounded m-1\"})}))}),/*#__PURE__*/_jsx(\"hr\",{className:\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\"})]})]});})}):null})]})})]}):null]})});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "useSearchParams", "addNewCommentCase", "detailCase", "getListCommentCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "useDropzone", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "DetailCaseScreen", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$ful2", "_caseInfo$patient2", "_caseInfo$patient$bir", "_caseInfo$patient3", "_caseInfo$patient$pat", "_caseInfo$patient4", "_caseInfo$patient$pat2", "_caseInfo$patient5", "_caseInfo$patient$pat3", "_caseInfo$patient6", "_caseInfo$coordinator", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider$fu", "_caseInfo$provider", "_caseInfo$provider$ph", "_caseInfo$provider2", "_caseInfo$provider$em", "_caseInfo$provider3", "_caseInfo$provider$ad", "_caseInfo$provider4", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "searchParams", "page", "get", "selectPage", "setSelectPage", "commentInput", "setCommentInput", "commentInputError", "setCommentInputError", "filesComments", "setFilesComments", "getRootProps", "getRootComments", "getInputProps", "getInputComments", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "listCommentCase", "commentCaseList", "comments", "loadingCommentCase", "errorCommentCase", "pages", "createCommentCase", "createNewCommentCase", "loadingCommentCaseAdd", "successCommentCaseAdd", "errorCommentCaseAdd", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "class", "patient", "full_name", "status_coordination", "select", "index", "onClick", "concat", "birth_day", "patient_phone", "patient_email", "patient_address", "case_date", "coordinator", "case_description", "updated_at", "appointment_date", "service_location", "provider", "phone", "email", "address", "medical_reports", "item", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "parseFloat", "invoice_amount", "toFixed", "upload_invoices", "assurance_status", "assurance", "assurance_name", "policy_number", "upload_authorization", "value", "onChange", "v", "style", "src", "name", "size", "filter", "_", "indexToRemove", "disabled", "check", "length", "content", "files_commet", "comment", "_comment$coordinator", "_comment$coordinator2", "_comment$coordinator$", "_comment$coordinator3", "_comment$coordinator$2", "_comment$coordinator4", "_comment$coordinator$3", "_comment$coordinator5", "_comment$content", "_comment$files", "photo", "first_name", "last_name", "created_at", "files"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport {\n  useLocation,\n  useNavigate,\n  useParams,\n  useSearchParams,\n} from \"react-router-dom\";\nimport {\n  addNewCommentCase,\n  detailCase,\n  getListCommentCase,\n} from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { toast } from \"react-toastify\";\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n  const [searchParams] = useSearchParams();\n  const page = searchParams.get(\"page\") || \"1\";\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n  const [commentInput, setCommentInput] = useState(\"\");\n  const [commentInputError, setCommentInputError] = useState(\"\");\n\n  // files comment\n  // initialMedicalReports\n  const [filesComments, setFilesComments] = useState([]);\n  const { getRootProps: getRootComments, getInputProps: getInputComments } =\n    useDropzone({\n      accept: {\n        \"image/*\": [],\n      },\n      onDrop: (acceptedFiles) => {\n        setFilesComments((prevFiles) => [\n          ...prevFiles,\n          ...acceptedFiles.map((file) =>\n            Object.assign(file, {\n              preview: URL.createObjectURL(file),\n            })\n          ),\n        ]);\n      },\n    });\n\n  useEffect(() => {\n    return () =>\n      filesComments.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n\n  const listCommentCase = useSelector((state) => state.commentCaseList);\n  const { comments, loadingCommentCase, errorCommentCase, pages } =\n    listCommentCase;\n\n  const createCommentCase = useSelector((state) => state.createNewCommentCase);\n  const { loadingCommentCaseAdd, successCommentCaseAdd, errorCommentCaseAdd } =\n    createCommentCase;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [navigate, userInfo, dispatch, id, page]);\n\n  useEffect(() => {\n    if (successCommentCaseAdd) {\n      setCommentInput(\"\");\n      setCommentInputError(\"\");\n      setFilesComments([]);\n      dispatch(getListCommentCase(\"0\", id));\n    }\n  }, [successCommentCaseAdd]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\" text-[#32475C] text-md font-medium opacity-85\">\n                #{caseInfo.id}\n              </div>\n              <div className=\"flex flex-row items-center my-2\">\n                <div className=\"flex flex-row items-center mr-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseStatus(caseInfo.status_coordination)}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Provider Information\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n\n              {/*  */}\n            </div>\n            {/* comment */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 b py-3  px-2\">\n                <div className=\"flex md:flex-row flex-col \">\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1  py-1 px-2\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Comment\n                      </label>\n                      <textarea\n                        value={commentInput}\n                        onChange={(v) => setCommentInput(v.target.value)}\n                        className={`  ${\n                          commentInputError\n                            ? \"border-danger\"\n                            : \"border-[#F1F3FF]\"\n                        } min-h-30  outline-none border border-[#F1F3FF]  w-full rounded text-sm p-3`}\n                      ></textarea>\n                      <div className=\" text-[8px] text-danger\">\n                        {commentInputError ? commentInputError : \"\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full\">\n                    <div className=\"my-1 bg-white py-1 px-2 rounded-md\">\n                      <label className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                        Images\n                      </label>\n                      <div\n                        {...getRootComments({\n                          className: \"dropzone\",\n                        })}\n                        // style={dropzoneStyle}\n                        className=\"bg-[#F5F6FF] w-full min-h-30 flex flex-col items-center justify-center cursor-pointer\"\n                      >\n                        <input {...getInputComments()} />\n                        <div className=\"my-2\">\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            className=\"size-7 p-2 bg-[#0388A6] rounded-full text-white\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                            />\n                          </svg>\n                        </div>\n                        <div className=\"my-2 text-sm\">\n                          Drag & Drop Images or BROWSE\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <aside style={thumbsContainer}>\n                  <div className=\"w-full flex flex-col \">\n                    {filesComments?.map((file, index) => (\n                      <div\n                        className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                        key={file.name}\n                      >\n                        <div className=\" text-[#81838E] text-center  shadow-1 \">\n                          <img src={file.preview} className=\"size-8\" />\n                        </div>\n                        <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                          <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                            {file.name}\n                          </div>\n                          <div>{(file.size / (1024 * 1024)).toFixed(2)} mb</div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            setFilesComments((prevFiles) =>\n                              prevFiles.filter(\n                                (_, indexToRemove) => index !== indexToRemove\n                              )\n                            );\n                          }}\n                          className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                        >\n                          <svg\n                            xmlns=\"http://www.w3.org/2000/svg\"\n                            fill=\"none\"\n                            viewBox=\"0 0 24 24\"\n                            stroke-width=\"1.5\"\n                            stroke=\"currentColor\"\n                            class=\"size-5\"\n                          >\n                            <path\n                              stroke-linecap=\"round\"\n                              stroke-linejoin=\"round\"\n                              d=\"M6 18 18 6M6 6l12 12\"\n                            />\n                          </svg>\n                        </button>\n                      </div>\n                    ))}\n                  </div>\n                </aside>\n                <div>\n                  <button\n                    disabled={loadingCommentCaseAdd}\n                    onClick={async () => {\n                      var check = true;\n                      setCommentInputError(\"\");\n\n                      if (commentInput === \"\" && filesComments.length === 0) {\n                        setCommentInputError(\"This field is required.\");\n                        check = false;\n                      }\n\n                      if (check) {\n                        await dispatch(\n                          addNewCommentCase(\n                            {\n                              content: commentInput,\n                              // files\n                              files_commet: filesComments,\n                            },\n                            id\n                          )\n                        );\n                      } else {\n                        toast.error(\n                          \"Some fields are empty or invalid. please try again\"\n                        );\n                      }\n                    }}\n                    className=\"text-white  bg-[#0388A6] text-sm px-10 py-2 rounded-2xl\"\n                  >\n                    {loadingCommentCaseAdd ? \"Loading ..\" : \"Save\"}\n                  </button>\n                </div>\n                <div className=\"my-5\">\n                  {loadingCommentCase ? (\n                    <Loader />\n                  ) : errorCommentCase ? (\n                    <Alert type={\"error\"} message={errorCommentCase} />\n                  ) : comments ? (\n                    <>\n                      {comments?.map((comment, index) => (\n                        <div className=\"flex flex-row items-start\">\n                          <div>\n                            {comment.coordinator ? (\n                              comment.coordinator?.photo ? (\n                                <img\n                                  className=\" size-12 rounded-full\"\n                                  src={baseURLFile + comment.coordinator?.photo}\n                                />\n                              ) : (\n                                <div className=\"size-12  rounded-full shadow-1 bg-[#0388A6] text-white flex flex-row items-center justify-center\">\n                                  <div className=\" uppercase\">\n                                    {comment.coordinator?.first_name[0] ?? \"\"}\n                                    {comment.coordinator?.last_name[0] ?? \"\"}\n                                  </div>\n                                </div>\n                              )\n                            ) : null}\n                          </div>\n                          <div className=\"flex-1 px-2\">\n                            <div className=\"flex flex-row mb-1 items-center\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z\"\n                                />\n                              </svg>\n\n                              <div className=\"flex-1 mx-1 text-xs\">\n                                {formatDate(comment.created_at)}\n                              </div>\n                            </div>\n                            <div className=\"text-sm my-1 font-semibold\">\n                              {comment.coordinator?.full_name ?? \"\"}\n                            </div>\n                            <div className=\"text-sm my-1\">\n                              {comment.content ?? \"\"}\n                            </div>\n                            <div className=\"flex flex-wrap items-center  my-1\">\n                              {comment.files?.map((file, index) => (\n                                <a\n                                  target=\"_blank\"\n                                  rel=\"noopener noreferrer\"\n                                  href={baseURLFile + file.file}\n                                >\n                                  <img\n                                    src={baseURLFile + file.file}\n                                    className=\"size-30 shadow-1 rounded m-1\"\n                                  />\n                                </a>\n                              ))}\n                            </div>\n                            <hr className=\"text-opacity-10 bg-opacity-20 bg-[#0388A6]  text-[#0388A6] mb-3 mt-2\" />\n                          </div>\n                        </div>\n                      ))}\n                    </>\n                  ) : null}\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OACEC,WAAW,CACXC,WAAW,CACXC,SAAS,CACTC,eAAe,KACV,kBAAkB,CACzB,OACEC,iBAAiB,CACjBC,UAAU,CACVC,kBAAkB,KACb,iCAAiC,CACxC,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,iBAAiB,CAE7C,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,KAAK,KAAQ,gBAAgB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEvC,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,iBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAGxD,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAyD,QAAQ,CAAG1D,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA2D,QAAQ,CAAG7D,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAE8D,EAAG,CAAC,CAAG1D,SAAS,CAAC,CAAC,CACxB,KAAM,CAAC2D,YAAY,CAAC,CAAG1D,eAAe,CAAC,CAAC,CACxC,KAAM,CAAA2D,IAAI,CAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,EAAI,GAAG,CAE5C,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGpE,QAAQ,CAAC,qBAAqB,CAAC,CACnE,KAAM,CAACqE,YAAY,CAAEC,eAAe,CAAC,CAAGtE,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACuE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxE,QAAQ,CAAC,EAAE,CAAC,CAE9D;AACA;AACA,KAAM,CAACyE,aAAa,CAAEC,gBAAgB,CAAC,CAAG1E,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAE2E,YAAY,CAAEC,eAAe,CAAEC,aAAa,CAAEC,gBAAiB,CAAC,CACtEhE,WAAW,CAAC,CACViE,MAAM,CAAE,CACN,SAAS,CAAE,EACb,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,gBAAgB,CAAEQ,SAAS,EAAK,CAC9B,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEJrF,SAAS,CAAC,IAAM,CACd,MAAO,IACL0E,aAAa,CAACiB,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CACtE,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAK,SAAS,CAAG1F,WAAW,CAAE2F,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAG/F,WAAW,CAAE2F,KAAK,EAAKA,KAAK,CAACrF,UAAU,CAAC,CAC3D,KAAM,CAAE0F,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CAEZ,KAAM,CAAAK,eAAe,CAAGpG,WAAW,CAAE2F,KAAK,EAAKA,KAAK,CAACU,eAAe,CAAC,CACrE,KAAM,CAAEC,QAAQ,CAAEC,kBAAkB,CAAEC,gBAAgB,CAAEC,KAAM,CAAC,CAC7DL,eAAe,CAEjB,KAAM,CAAAM,iBAAiB,CAAG1G,WAAW,CAAE2F,KAAK,EAAKA,KAAK,CAACgB,oBAAoB,CAAC,CAC5E,KAAM,CAAEC,qBAAqB,CAAEC,qBAAqB,CAAEC,mBAAoB,CAAC,CACzEJ,iBAAiB,CACnB;AACA,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBlH,SAAS,CAAC,IAAM,CACd,GAAI,CAAC+F,QAAQ,CAAE,CACblC,QAAQ,CAACqD,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLnD,QAAQ,CAACtD,UAAU,CAACuD,EAAE,CAAC,CAAC,CACxBD,QAAQ,CAACrD,kBAAkB,CAAC,GAAG,CAAEsD,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEkC,QAAQ,CAAEhC,QAAQ,CAAEC,EAAE,CAAEE,IAAI,CAAC,CAAC,CAE5ClE,SAAS,CAAC,IAAM,CACd,GAAIgH,qBAAqB,CAAE,CACzBzC,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,EAAE,CAAC,CACxBE,gBAAgB,CAAC,EAAE,CAAC,CACpBZ,QAAQ,CAACrD,kBAAkB,CAAC,GAAG,CAAEsD,EAAE,CAAC,CAAC,CACvC,CACF,CAAC,CAAE,CAACgD,qBAAqB,CAAC,CAAC,CAE3B,KAAM,CAAAG,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED;AACA,mBACE1G,IAAA,CAACP,aAAa,EAAAkH,QAAA,cACZzG,KAAA,QAAK0G,SAAS,CAAC,EAAE,CAAAD,QAAA,eACfzG,KAAA,QAAK0G,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD3G,IAAA,MAAG6G,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClBzG,KAAA,QAAK0G,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3G,IAAA,SACEkH,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNpH,IAAA,SAAM4G,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ3G,IAAA,SAAA2G,QAAA,cACE3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3G,IAAA,SACEkH,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpH,IAAA,MAAG6G,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB3G,IAAA,QAAK4G,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ3G,IAAA,SAAA2G,QAAA,cACE3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB3G,IAAA,SACEkH,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPpH,IAAA,QAAK4G,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGL1B,eAAe,cACdjF,IAAA,CAACN,MAAM,GAAE,CAAC,CACRwF,aAAa,cACflF,IAAA,CAACL,KAAK,EAAC0H,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEpC,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACVlF,KAAA,QAAAyG,QAAA,eAEEzG,KAAA,QAAK0G,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvDzG,KAAA,QAAK0G,SAAS,CAAC,gDAAgD,CAAAD,QAAA,EAAC,GAC7D,CAACvB,QAAQ,CAACtC,EAAE,EACV,CAAC,cACN5C,KAAA,QAAK0G,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9CzG,KAAA,QAAK0G,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3G,IAAA,QAAA2G,QAAA,cACE3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C3G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoH,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNlH,KAAA,QAAK0G,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3G,IAAA,SAAM4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,CAAC,GAAG,EAAAhG,qBAAA,EAAAC,iBAAA,CACpDwE,QAAQ,CAACoC,OAAO,UAAA5G,iBAAA,iBAAhBA,iBAAA,CAAkB6G,SAAS,UAAA9G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAClC,CAAC,EACH,CAAC,cACNT,KAAA,QAAK0G,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3G,IAAA,QAAA2G,QAAA,cACE3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C3G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoH,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CAAC,cACNlH,KAAA,QAAK0G,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD3G,IAAA,SAAM4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAC,GAAG,CACjDF,UAAU,CAACrB,QAAQ,CAACsC,mBAAmB,CAAC,EACtC,CAAC,cACN1H,IAAA,QAAK4G,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,EACH,CAAC,EACH,CAAC,cAEN1G,KAAA,QAAK0G,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD3G,IAAA,QAAK4G,SAAS,CAAC,iGAAiG,CAAAD,QAAA,CAC7G,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CAC1B,CAACzC,GAAG,CAAC,CAACyD,MAAM,CAAEC,KAAK,gBAClB5H,IAAA,WACE6H,OAAO,CAAEA,CAAA,GAAM1E,aAAa,CAACwE,MAAM,CAAE,CACrCf,SAAS,oCAAAkB,MAAA,CACP5E,UAAU,GAAKyE,MAAM,CACjB,mDAAmD,CACnD,4BAA4B,CAC/B,CAAAhB,QAAA,CAEFgB,MAAM,CACD,CACT,CAAC,CACC,CAAC,CAELzE,UAAU,GAAK,qBAAqB,cACnChD,KAAA,QAAK0G,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFzG,KAAA,QAAK0G,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9F,sBAAA,EAAAC,kBAAA,CACzBsE,QAAQ,CAACoC,OAAO,UAAA1G,kBAAA,iBAAhBA,kBAAA,CAAkB2G,SAAS,UAAA5G,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNX,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5F,qBAAA,EAAAC,kBAAA,CACzBoE,QAAQ,CAACoC,OAAO,UAAAxG,kBAAA,iBAAhBA,kBAAA,CAAkB+G,SAAS,UAAAhH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNb,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA1F,qBAAA,EAAAC,kBAAA,CACzBkE,QAAQ,CAACoC,OAAO,UAAAtG,kBAAA,iBAAhBA,kBAAA,CAAkB8G,aAAa,UAAA/G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNf,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAxF,sBAAA,EAAAC,kBAAA,CACzBgE,QAAQ,CAACoC,OAAO,UAAApG,kBAAA,iBAAhBA,kBAAA,CAAkB6G,aAAa,UAAA9G,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNjB,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAtF,sBAAA,EAAAC,kBAAA,CACzB8D,QAAQ,CAACoC,OAAO,UAAAlG,kBAAA,iBAAhBA,kBAAA,CAAkB4G,eAAe,UAAA7G,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNnB,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,cAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,cACxD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACb,QAAQ,CAAC+C,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cACNjI,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC1D3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAApF,qBAAA,CACzB6D,QAAQ,CAACgD,WAAW,UAAA7G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC3B,CAAC,EACH,CAAC,cACNrB,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAnF,qBAAA,CACzB4D,QAAQ,CAACiD,gBAAgB,UAAA7G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEP0B,UAAU,GAAK,sBAAsB,cACpChD,KAAA,QAAAyG,QAAA,eACEzG,KAAA,QAAK0G,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFzG,KAAA,QAAK0G,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlF,qBAAA,CACzB2D,QAAQ,CAACsC,mBAAmB,UAAAjG,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNvB,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACb,QAAQ,CAACkD,UAAU,CAAC,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,cACNpI,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6BAE/B,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACb,QAAQ,CAACmD,gBAAgB,CAAC,CACnC,CAAC,EACH,CAAC,cACNrI,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cACtD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjF,qBAAA,CACzB0D,QAAQ,CAACoD,gBAAgB,UAAA9G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAK0G,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvFzG,KAAA,QAAK0G,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,sBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAhF,qBAAA,EAAAC,kBAAA,CACzBwD,QAAQ,CAACqD,QAAQ,UAAA7G,kBAAA,iBAAjBA,kBAAA,CAAmB6F,SAAS,UAAA9F,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNzB,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9E,qBAAA,EAAAC,mBAAA,CACzBsD,QAAQ,CAACqD,QAAQ,UAAA3G,mBAAA,iBAAjBA,mBAAA,CAAmB4G,KAAK,UAAA7G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5E,qBAAA,EAAAC,mBAAA,CACzBoD,QAAQ,CAACqD,QAAQ,UAAAzG,mBAAA,iBAAjBA,mBAAA,CAAmB2G,KAAK,UAAA5G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,cACN7B,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA1E,qBAAA,EAAAC,mBAAA,CACzBkD,QAAQ,CAACqD,QAAQ,UAAAvG,mBAAA,iBAAjBA,mBAAA,CAAmB0G,OAAO,UAAA3G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPiB,UAAU,GAAK,iBAAiB,cAC/BlD,IAAA,QAAK4G,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvFzG,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAxE,qBAAA,CAC5BiD,QAAQ,CAACyD,eAAe,UAAA1G,qBAAA,iBAAxBA,qBAAA,CAA0B+B,GAAG,CAAC,CAAC4E,IAAI,CAAElB,KAAK,gBACzC5H,IAAA,MACE6G,IAAI,CAAEjH,WAAW,CAAGkJ,IAAI,CAAC3E,IAAK,CAC9B4E,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBpC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzG,KAAA,QAAK0G,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3G,IAAA,QAAK4G,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzG,KAAA,QACE4G,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3G,IAAA,SAAMoH,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpH,IAAA,SAAMoH,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlH,KAAA,QAAK0G,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3G,IAAA,QAAK4G,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FmC,IAAI,CAACG,SAAS,CACZ,CAAC,cACN/I,KAAA,QAAAyG,QAAA,EAAMmC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPhG,UAAU,GAAK,UAAU,cACxBhD,KAAA,QAAK0G,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7DzG,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCzG,KAAA,QAAK0G,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvE,qBAAA,CACzBgD,QAAQ,CAAC+D,cAAc,UAAA/G,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cACNlC,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACb,QAAQ,CAACgE,WAAW,CAAC,CAC9B,CAAC,EACH,CAAC,cACNlJ,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cAC5CzG,KAAA,QAAK0G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,GAC1B,CAAC0C,UAAU,CAACjE,QAAQ,CAACkE,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,cACNrJ,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9C3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,EACH,CAAC,EACH,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAtE,qBAAA,CAC5B+C,QAAQ,CAACoE,eAAe,UAAAnH,qBAAA,iBAAxBA,qBAAA,CAA0B6B,GAAG,CAAC,CAAC4E,IAAI,CAAElB,KAAK,gBACzC5H,IAAA,MACE6G,IAAI,CAAEjH,WAAW,CAAGkJ,IAAI,CAAC3E,IAAK,CAC9B4E,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBpC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzG,KAAA,QAAK0G,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3G,IAAA,QAAK4G,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzG,KAAA,QACE4G,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3G,IAAA,SAAMoH,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpH,IAAA,SAAMoH,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlH,KAAA,QAAK0G,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3G,IAAA,QAAK4G,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FmC,IAAI,CAACG,SAAS,CACZ,CAAC,cACN/I,KAAA,QAAAyG,QAAA,EAAMmC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhG,UAAU,GAAK,yBAAyB,cACvChD,KAAA,QAAK0G,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9DzG,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxCzG,KAAA,QAAK0G,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,mBAExD,CAAK,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAE/B,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArE,qBAAA,CACzB8C,QAAQ,CAACqE,gBAAgB,UAAAnH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACNpC,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yBAE/B,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAApE,qBAAA,EAAAC,mBAAA,CACzB4C,QAAQ,CAACsE,SAAS,UAAAlH,mBAAA,iBAAlBA,mBAAA,CAAoBmH,cAAc,UAAApH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,EACH,CAAC,cACNrC,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACNzG,KAAA,QAAK0G,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE3G,IAAA,QAAK4G,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD3G,IAAA,QAAK4G,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlE,qBAAA,CACzB2C,QAAQ,CAACwE,aAAa,UAAAnH,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNvC,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN3G,IAAA,QAAK4G,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAjE,qBAAA,CAC5B0C,QAAQ,CAACyE,oBAAoB,UAAAnH,qBAAA,iBAA7BA,qBAAA,CAA+BwB,GAAG,CAAC,CAAC4E,IAAI,CAAElB,KAAK,gBAC9C5H,IAAA,MACE6G,IAAI,CAAEjH,WAAW,CAAGkJ,IAAI,CAAC3E,IAAK,CAC9B4E,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBpC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3CzG,KAAA,QAAK0G,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF3G,IAAA,QAAK4G,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5EzG,KAAA,QACE4G,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB3G,IAAA,SAAMoH,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOpH,IAAA,SAAMoH,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNlH,KAAA,QAAK0G,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE3G,IAAA,QAAK4G,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FmC,IAAI,CAACG,SAAS,CACZ,CAAC,cACN/I,KAAA,QAAAyG,QAAA,EAAMmC,IAAI,CAACI,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EAGL,CAAC,cAENlJ,IAAA,QAAK4G,SAAS,CAAC,0CAA0C,CAAAD,QAAA,cACvDzG,KAAA,QAAK0G,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrCzG,KAAA,QAAK0G,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC3G,IAAA,QAAK4G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BzG,KAAA,QAAK0G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B3G,IAAA,UAAO4G,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,SAE5D,CAAO,CAAC,cACR3G,IAAA,aACE8J,KAAK,CAAE1G,YAAa,CACpB2G,QAAQ,CAAGC,CAAC,EAAK3G,eAAe,CAAC2G,CAAC,CAACjB,MAAM,CAACe,KAAK,CAAE,CACjDlD,SAAS,MAAAkB,MAAA,CACPxE,iBAAiB,CACb,eAAe,CACf,kBAAkB,+EACsD,CACrE,CAAC,cACZtD,IAAA,QAAK4G,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCrD,iBAAiB,CAAGA,iBAAiB,CAAG,EAAE,CACxC,CAAC,EACH,CAAC,CACH,CAAC,cACNtD,IAAA,QAAK4G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,cAC9BzG,KAAA,QAAK0G,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3G,IAAA,UAAO4G,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,QAE5D,CAAO,CAAC,cACRzG,KAAA,WACMyD,eAAe,CAAC,CAClBiD,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,uFAAuF,CAAAD,QAAA,eAEjG3G,IAAA,aAAW6D,gBAAgB,CAAC,CAAC,CAAG,CAAC,cACjC7D,IAAA,QAAK4G,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D3G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoH,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNpH,IAAA,QAAK4G,SAAS,CAAC,cAAc,CAAAD,QAAA,CAAC,8BAE9B,CAAK,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACN3G,IAAA,UAAOiK,KAAK,CAAE5J,eAAgB,CAAAsG,QAAA,cAC5B3G,IAAA,QAAK4G,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCnD,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAEyD,KAAK,gBAC9B1H,KAAA,QACE0G,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF3G,IAAA,QAAK4G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD3G,IAAA,QAAKkK,GAAG,CAAE/F,IAAI,CAACG,OAAQ,CAACsC,SAAS,CAAC,QAAQ,CAAE,CAAC,CAC1C,CAAC,cACN1G,KAAA,QAAK0G,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3G,IAAA,QAAK4G,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FxC,IAAI,CAACgG,IAAI,CACP,CAAC,cACNjK,KAAA,QAAAyG,QAAA,EAAM,CAACxC,IAAI,CAACiG,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEb,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAK,CAAC,EACnD,CAAC,cACNvJ,IAAA,WACE6H,OAAO,CAAEA,CAAA,GAAM,CACbpE,gBAAgB,CAAEQ,SAAS,EACzBA,SAAS,CAACoG,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GAAK3C,KAAK,GAAK2C,aAClC,CACF,CAAC,CACH,CAAE,CACF3D,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd3G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoH,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GAnCJjD,IAAI,CAACgG,IAoCP,CACN,CAAC,CACC,CAAC,CACD,CAAC,cACRnK,IAAA,QAAA2G,QAAA,cACE3G,IAAA,WACEwK,QAAQ,CAAE3E,qBAAsB,CAChCgC,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,GAAI,CAAA4C,KAAK,CAAG,IAAI,CAChBlH,oBAAoB,CAAC,EAAE,CAAC,CAExB,GAAIH,YAAY,GAAK,EAAE,EAAII,aAAa,CAACkH,MAAM,GAAK,CAAC,CAAE,CACrDnH,oBAAoB,CAAC,yBAAyB,CAAC,CAC/CkH,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACT,KAAM,CAAA5H,QAAQ,CACZvD,iBAAiB,CACf,CACEqL,OAAO,CAAEvH,YAAY,CACrB;AACAwH,YAAY,CAAEpH,aAChB,CAAC,CACDV,EACF,CACF,CAAC,CACH,CAAC,IAAM,CACLhD,KAAK,CAACiF,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF6B,SAAS,CAAC,yDAAyD,CAAAD,QAAA,CAElEd,qBAAqB,CAAG,YAAY,CAAG,MAAM,CACxC,CAAC,CACN,CAAC,cACN7F,IAAA,QAAK4G,SAAS,CAAC,MAAM,CAAAD,QAAA,CAClBnB,kBAAkB,cACjBxF,IAAA,CAACN,MAAM,GAAE,CAAC,CACR+F,gBAAgB,cAClBzF,IAAA,CAACL,KAAK,EAAC0H,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAE7B,gBAAiB,CAAE,CAAC,CACjDF,QAAQ,cACVvF,IAAA,CAAAI,SAAA,EAAAuG,QAAA,CACGpB,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAErB,GAAG,CAAC,CAAC2G,OAAO,CAAEjD,KAAK,QAAAkD,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,gBAAA,CAAAC,cAAA,oBAC5BrL,KAAA,QAAK0G,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3G,IAAA,QAAA2G,QAAA,CACGkE,OAAO,CAACzC,WAAW,CAClB,CAAA0C,oBAAA,CAAAD,OAAO,CAACzC,WAAW,UAAA0C,oBAAA,WAAnBA,oBAAA,CAAqBU,KAAK,cACxBxL,IAAA,QACE4G,SAAS,CAAC,uBAAuB,CACjCsD,GAAG,CAAEtK,WAAW,GAAAmL,qBAAA,CAAGF,OAAO,CAACzC,WAAW,UAAA2C,qBAAA,iBAAnBA,qBAAA,CAAqBS,KAAK,CAAC,CAC/C,CAAC,cAEFxL,IAAA,QAAK4G,SAAS,CAAC,kGAAkG,CAAAD,QAAA,cAC/GzG,KAAA,QAAK0G,SAAS,CAAC,YAAY,CAAAD,QAAA,GAAAqE,qBAAA,EAAAC,qBAAA,CACxBJ,OAAO,CAACzC,WAAW,UAAA6C,qBAAA,iBAAnBA,qBAAA,CAAqBQ,UAAU,CAAC,CAAC,CAAC,UAAAT,qBAAA,UAAAA,qBAAA,CAAI,EAAE,EAAAE,sBAAA,EAAAC,qBAAA,CACxCN,OAAO,CAACzC,WAAW,UAAA+C,qBAAA,iBAAnBA,qBAAA,CAAqBO,SAAS,CAAC,CAAC,CAAC,UAAAR,sBAAA,UAAAA,sBAAA,CAAI,EAAE,EACrC,CAAC,CACH,CACN,CACC,IAAI,CACL,CAAC,cACNhL,KAAA,QAAK0G,SAAS,CAAC,aAAa,CAAAD,QAAA,eAC1BzG,KAAA,QAAK0G,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C3G,IAAA,QACE8G,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,QAAQ,CAAAZ,QAAA,cAEd3G,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBoH,CAAC,CAAC,6iBAA6iB,CAChjB,CAAC,CACC,CAAC,cAENpH,IAAA,QAAK4G,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CACjCV,UAAU,CAAC4E,OAAO,CAACc,UAAU,CAAC,CAC5B,CAAC,EACH,CAAC,cACN3L,IAAA,QAAK4G,SAAS,CAAC,4BAA4B,CAAAD,QAAA,EAAAyE,sBAAA,EAAAC,qBAAA,CACxCR,OAAO,CAACzC,WAAW,UAAAiD,qBAAA,iBAAnBA,qBAAA,CAAqB5D,SAAS,UAAA2D,sBAAA,UAAAA,sBAAA,CAAI,EAAE,CAClC,CAAC,cACNpL,IAAA,QAAK4G,SAAS,CAAC,cAAc,CAAAD,QAAA,EAAA2E,gBAAA,CAC1BT,OAAO,CAACF,OAAO,UAAAW,gBAAA,UAAAA,gBAAA,CAAI,EAAE,CACnB,CAAC,cACNtL,IAAA,QAAK4G,SAAS,CAAC,mCAAmC,CAAAD,QAAA,EAAA4E,cAAA,CAC/CV,OAAO,CAACe,KAAK,UAAAL,cAAA,iBAAbA,cAAA,CAAerH,GAAG,CAAC,CAACC,IAAI,CAAEyD,KAAK,gBAC9B5H,IAAA,MACE+I,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBnC,IAAI,CAAEjH,WAAW,CAAGuE,IAAI,CAACA,IAAK,CAAAwC,QAAA,cAE9B3G,IAAA,QACEkK,GAAG,CAAEtK,WAAW,CAAGuE,IAAI,CAACA,IAAK,CAC7ByC,SAAS,CAAC,8BAA8B,CACzC,CAAC,CACD,CACJ,CAAC,CACC,CAAC,cACN5G,IAAA,OAAI4G,SAAS,CAAC,sEAAsE,CAAE,CAAC,EACpF,CAAC,EACH,CAAC,EACP,CAAC,CACF,CAAC,CACD,IAAI,CACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAlG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}