{"ast": null, "code": "import axios from \"axios\";\nimport { baseURL } from \"./constants\";\n\n// this online\n// const baseURL = \"http://15.188.52.23/api\";\n// const baseURLFile = \"http://15.188.52.23/\";\n\n// const baseURL = \"http://backend.ctitechnologie.ma\";\n\nconst instance = axios.create({\n  timeout: 60000,\n  // زيادة مهلة الانتظار إلى 60 ثانية\n  baseURL: baseURL\n});\nexport default instance;", "map": {"version": 3, "names": ["axios", "baseURL", "instance", "create", "timeout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/axios.js"], "sourcesContent": ["import axios from \"axios\";\nimport { baseURL } from \"./constants\";\n\n// this online\n// const baseURL = \"http://15.188.52.23/api\";\n// const baseURLFile = \"http://15.188.52.23/\";\n\n// const baseURL = \"http://backend.ctitechnologie.ma\";\n\nconst instance = axios.create({\n  timeout: 60000, // زيادة مهلة الانتظار إلى 60 ثانية\n  baseURL: baseURL,\n});\nexport default instance;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,aAAa;;AAErC;AACA;AACA;;AAEA;;AAEA,MAAMC,QAAQ,GAAGF,KAAK,CAACG,MAAM,CAAC;EAC5BC,OAAO,EAAE,KAAK;EAAE;EAChBH,OAAO,EAAEA;AACX,CAAC,CAAC;AACF,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}