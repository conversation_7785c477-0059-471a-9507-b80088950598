{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate}from\"react-router-dom\";import DefaultLayout from\"../../layouts/DefaultLayout\";import addreactionface from\"../../images/icon/add_reaction.png\";import{toast}from\"react-toastify\";import{providersList}from\"../../redux/actions/providerActions\";import{addNewCase}from\"../../redux/actions/caseActions\";import Select from\"react-select\";import{useDropzone}from\"react-dropzone\";import{getInsuranesList}from\"../../redux/actions/insuranceActions\";import{getListCoordinators}from\"../../redux/actions/userActions\";import{COUNTRIES}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STEPSLIST=[{index:0,title:\"General Information\",description:\"Please enter the general information about the patient and the case.\"},{index:1,title:\"Coordination Details\",description:\"Provide information about the initial coordination & appointment details for this case.\"},{index:2,title:\"Medical Reports\",description:\"Upload any initial medical reports related to the case.\"},{index:3,title:\"Invoices\",description:\"If there are any initial invoices related to the case, please provide the details and upload the documents.\"},{index:4,title:\"Insurance Authorization\",description:\"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\"},{index:5,title:\"Finish\",description:\"You can go back to any step to make changes.\"}];const thumbsContainer={display:\"flex\",flexDirection:\"row\",flexWrap:\"wrap\",marginTop:16};function AddCaseScreen(){const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();//\nconst[firstName,setFirstName]=useState(\"\");const[firstNameError,setFirstNameError]=useState(\"\");const[lastName,setLastName]=useState(\"\");const[lastNameError,setLastNameError]=useState(\"\");const[email,setEmail]=useState(\"\");const[emailError,setEmailError]=useState(\"\");const[birthDate,setBirthDate]=useState(\"\");const[birthDateError,setBirthDateError]=useState(\"\");const[phone,setPhone]=useState(\"\");const[phoneError,setPhoneError]=useState(\"\");const[address,setAddress]=useState(\"\");const[addressError,setAddressError]=useState(\"\");const[city,setCity]=useState(\"\");const[cityError,setCityError]=useState(\"\");const[country,setCountry]=useState(\"\");const[countryError,setCountryError]=useState(\"\");//\nconst[coordinator,setCoordinator]=useState(\"\");const[coordinatorError,setCoordinatorError]=useState(\"\");const[caseDate,setCaseDate]=useState(\"\");const[caseDateError,setCaseDateError]=useState(\"\");const[caseType,setCaseType]=useState(\"\");const[caseTypeError,setCaseTypeError]=useState(\"\");const[caseDescription,setCaseDescription]=useState(\"\");const[caseDescriptionError,setCaseDescriptionError]=useState(\"\");//\nconst[coordinatStatus,setCoordinatStatus]=useState(\"\");const[coordinatStatusError,setCoordinatStatusError]=useState(\"\");const[appointmentDate,setAppointmentDate]=useState(\"\");const[appointmentDateError,setAppointmentDateError]=useState(\"\");const[serviceLocation,setServiceLocation]=useState(\"\");const[serviceLocationError,setServiceLocationError]=useState(\"\");//\nconst[providerName,setProviderName]=useState(\"\");const[providerNameError,setProviderNameError]=useState(\"\");const[providerPhone,setProviderPhone]=useState(\"\");const[providerPhoneError,setProviderPhoneError]=useState(\"\");const[providerEmail,setProviderEmail]=useState(\"\");const[providerEmailError,setProviderEmailError]=useState(\"\");const[providerAddress,setProviderAddress]=useState(\"\");const[providerAddressError,setProviderAddressError]=useState(\"\");//\nconst[invoiceNumber,setInvoiceNumber]=useState(\"\");const[invoiceNumberError,setInvoiceNumberError]=useState(\"\");const[dateIssued,setDateIssued]=useState(\"\");const[dateIssuedError,setDateIssuedError]=useState(\"\");const[amount,setAmount]=useState(0);const[amountError,setAmountError]=useState(\"\");//\nconst[insuranceCompany,setInsuranceCompany]=useState(\"\");const[insuranceCompanyError,setInsuranceCompanyError]=useState(\"\");const[insuranceNumber,setInsuranceNumber]=useState(\"\");const[insuranceNumberError,setInsuranceNumberError]=useState(\"\");const[policyNumber,setPolicyNumber]=useState(\"\");const[policyNumberError,setPolicyNumberError]=useState(\"\");const[initialStatus,setInitialStatus]=useState(\"\");const[initialStatusError,setInitialStatusError]=useState(\"\");// fils\n// initialMedicalReports\nconst[filesInitialMedicalReports,setFilesInitialMedicalReports]=useState([]);const{getRootProps:getRootPropsInitialMedical,getInputProps:getInputPropsInitialMedical}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesInitialMedicalReports(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesInitialMedicalReports.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Invoice\nconst[filesUploadInvoice,setFilesUploadInvoice]=useState([]);const{getRootProps:getRootPropsUploadInvoice,getInputProps:getInputPropsUploadInvoice}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadInvoice(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadInvoice.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Upload Authorization Documents\nconst[filesUploadAuthorizationDocuments,setFilesUploadAuthorizationDocuments]=useState([]);const{getRootProps:getRootPropsUploadAuthorizationDocuments,getInputProps:getInputPropsUploadAuthorizationDocuments}=useDropzone({accept:{\"*\":[]},onDrop:acceptedFiles=>{setFilesUploadAuthorizationDocuments(prevFiles=>[...prevFiles,...acceptedFiles.map(file=>Object.assign(file,{preview:URL.createObjectURL(file)}))]);}});useEffect(()=>{return()=>filesUploadAuthorizationDocuments.forEach(file=>URL.revokeObjectURL(file.preview));},[]);// Configure react-dropzone\n//\nconst[stepSelect,setStepSelect]=useState(0);const userLogin=useSelector(state=>state.userLogin);const{userInfo}=userLogin;const listProviders=useSelector(state=>state.providerList);const{providers,loadingProviders,errorProviders}=listProviders;const createCase=useSelector(state=>state.createNewCase);const{loadingCaseAdd,successCaseAdd,errorCaseAdd}=createCase;const listInsurances=useSelector(state=>state.insuranceList);const{insurances,loadingInsurances,errorInsurances}=listInsurances;const listCoordinators=useSelector(state=>state.coordinatorsList);const{coordinators,loadingCoordinators,errorCoordinators}=listCoordinators;const redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{setStepSelect(0);dispatch(getListCoordinators(\"0\"));dispatch(providersList(\"0\"));dispatch(getInsuranesList(\"0\"));//   dispatch(clientList(\"0\"));\n}},[navigate,userInfo,dispatch]);useEffect(()=>{if(successCaseAdd){setStepSelect(5);}},[successCaseAdd]);return/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Create New Case\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"py-5 px-4 flex justify-between\",children:/*#__PURE__*/_jsx(\"h4\",{className:\" uppercase font-semibold text-black dark:text-white\",children:\"New Case\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"}),STEPSLIST===null||STEPSLIST===void 0?void 0:STEPSLIST.map((step,index)=>/*#__PURE__*/_jsxs(\"div\",{// onClick={() => setStepSelect(step.index)}\nclassName:\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\",children:[stepSelect<step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"img\",{src:addreactionface,className:\"size-5\",onError:e=>{e.target.onerror=null;e.target.src=\"/assets/placeholder.png\";}})}):stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-white z-10  border-[11px] rounded-full\"}):/*#__PURE__*/_jsx(\"div\",{className:\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-black flex-1 px-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-medium text-sm\",children:step.title}),stepSelect===step.index?/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-light md:block hidden\",children:step.description}):null]})]}))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\",children:[stepSelect===0?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"General Information\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Patient Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"First Name \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(firstNameError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"First Name\",value:firstName,onChange:v=>setFirstName(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:firstNameError?firstNameError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:\"Last Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Last Name\",value:lastName,onChange:v=>setLastName(v.target.value)})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Email\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(emailError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"email\",placeholder:\"Email Address\",value:email,onChange:v=>setEmail(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:emailError?emailError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"phone \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\"outline-none border \".concat(phoneError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"Phone no\",value:phone,onChange:v=>setPhone(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:phoneError?phoneError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Country \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:country,onChange:option=>{setCountry(option);},options:COUNTRIES.map(country=>({value:country.title,label:/*#__PURE__*/_jsxs(\"div\",{className:\"\".concat(country.title===\"\"?\"py-2\":\"\",\" flex flex-row items-center\"),children:[/*#__PURE__*/_jsx(\"span\",{className:\"mr-2\",children:country.icon}),/*#__PURE__*/_jsx(\"span\",{children:country.title})]})})),className:\"text-sm\",placeholder:\"Select a country...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:countryError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:countryError?countryError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"City\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(cityError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"City\",value:city,onChange:v=>setCity(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:cityError?cityError:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceCompanyError?insuranceCompanyError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"CIA ID\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(insuranceNumberError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),type:\"text\",placeholder:\"CIA ID\",value:insuranceNumber,onChange:v=>setInsuranceNumber(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:insuranceNumberError?insuranceNumberError:\"\"})]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Case Details:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Assigned Coordinator\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(Select,{value:coordinator,onChange:option=>{setCoordinator(option);},className:\"text-sm\",options:coordinators===null||coordinators===void 0?void 0:coordinators.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Coordinator...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:coordinatorError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatorError?coordinatorError:\"\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pl-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs mb-1\",children:[\"Case Creation Date\",\" \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border \".concat(caseDateError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),type:\"date\",placeholder:\"Case Creation Date\",value:caseDate,onChange:v=>setCaseDate(v.target.value)}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseDateError?caseDateError:\"\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Type \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:caseType,onChange:v=>setCaseType(v.target.value),className:\" outline-none border \".concat(caseTypeError?\"border-danger\":\"border-[#F1F3FF]\",\" px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Type\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Medical\",children:\"Medical\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Technical\",children:\"Technical\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:caseTypeError?caseTypeError:\"\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Description\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"textarea\",{value:caseDescription,rows:5,onChange:v=>setCaseDescription(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setFirstNameError(\"\");setLastNameError(\"\");setBirthDateError(\"\");setPhoneError(\"\");setEmailError(\"\");setAddressError(\"\");setCaseTypeError(\"\");setCaseDateError(\"\");setCoordinatorError(\"\");setCityError(\"\");setCountryError(\"\");if(firstName===\"\"){setFirstNameError(\"This field is required.\");check=false;}if(phone===\"\"){setPhoneError(\"This field is required.\");check=false;}if(country===\"\"||country.value===\"\"){setCountryError(\"This field is required.\");check=false;}if(coordinator===\"\"||coordinator.value===\"\"){setCoordinatorError(\"This field is required.\");check=false;}if(caseDate===\"\"){setCaseDateError(\"This field is required.\");check=false;}if(caseType===\"\"){setCaseTypeError(\"This field is required.\");check=false;}if(check){setStepSelect(1);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})})]}):null,stepSelect===1?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Coordination Details\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Coordination Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:[\"Status \",/*#__PURE__*/_jsx(\"strong\",{className:\"text-danger\",children:\"*\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"select\",{value:coordinatStatus,onChange:v=>setCoordinatStatus(v.target.value),className:\"outline-none border \".concat(coordinatStatusError?\"border-danger\":\"border-[#F1F3FF]\",\"  px-3 py-2 w-full rounded text-sm\"),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pending-coordination\",children:\"Pending Coordination\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-m-r\",children:\"Coordinated, Missing M.R.\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-missing-invoice\",children:\"Coordinated, Missing Invoice\"}),/*#__PURE__*/_jsx(\"option\",{value:\"waiting-for-insurance-authorization\",children:\"Waiting for Insurance Authorization\"}),/*#__PURE__*/_jsx(\"option\",{value:\"coordinated-patient-not-seen-yet\",children:\"Coordinated, Patient not seen yet\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fully-coordinated\",children:\"Fully Coordinated\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\" text-[8px] text-danger\",children:coordinatStatusError?coordinatStatusError:\"\"})]})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Appointment Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Appointment Date\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Appointment Date\",value:appointmentDate,onChange:v=>setAppointmentDate(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Service Location\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\" Service Location\",value:serviceLocation,onChange:v=>setServiceLocation(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Provider Information:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\" w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Provider Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:providerName,onChange:option=>{setProviderName(option);},className:\"text-sm\",options:providers===null||providers===void 0?void 0:providers.map(item=>({value:item.id,label:item.full_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),placeholder:\"Select Provider...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:providerNameError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(0),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{var check=true;setCoordinatStatusError(\"\");if(coordinatStatus===\"\"){setCoordinatStatusError(\"This field is required.\");check=false;}if(check){setStepSelect(2);}else{toast.error(\"Some fields are empty or invalid. please try again\");}},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===2?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Medical Reports\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Initial Medical Reports:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsInitialMedical({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsInitialMedical()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesInitialMedicalReports===null||filesInitialMedicalReports===void 0?void 0:filesInitialMedicalReports.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesInitialMedicalReports(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(1),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===3?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Invoices\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Invoice Information:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Invoice Number (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Invoice Number (Optional)\",value:invoiceNumber,onChange:v=>setInvoiceNumber(v.target.value)})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Date Issued (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"date\",placeholder:\"Date Issued (Optional)\",value:dateIssued,onChange:v=>setDateIssued(v.target.value)})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Amount (Optional)\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"number\",placeholder:\"Amount (Optional)\",value:amount,onChange:v=>setAmount(v.target.value)})})]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Invoice\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadInvoice({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadInvoice()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadInvoice===null||filesUploadInvoice===void 0?void 0:filesUploadInvoice.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadInvoice(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(2),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(4),className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Save & Continue\"})]})]}):null,stepSelect===4?/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#0388A6] font-semibold text-xl\",children:\"Insurance Authorization\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Insurance Details:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col  \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Insurance Company Name\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(Select,{value:insuranceCompany,onChange:option=>{setInsuranceCompany(option);},options:insurances===null||insurances===void 0?void 0:insurances.map(assurance=>({value:assurance.id,label:assurance.assurance_name||\"\"})),filterOption:(option,inputValue)=>option.label.toLowerCase().includes(inputValue.toLowerCase()),className:\"text-sm\",placeholder:\"Select Insurance...\",isSearchable:true,styles:{control:(base,state)=>({...base,background:\"#fff\",border:insuranceCompanyError?\"1px solid #d34053\":\"1px solid #F1F3FF\",boxShadow:state.isFocused?\"none\":\"none\",\"&:hover\":{border:\"1px solid #F1F3FF\"}}),option:base=>({...base,display:\"flex\",alignItems:\"center\"}),singleValue:base=>({...base,display:\"flex\",alignItems:\"center\"})}})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Policy Number\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",type:\"text\",placeholder:\"Policy Number\",value:policyNumber,onChange:v=>setPolicyNumber(v.target.value)})})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"flex md:flex-row flex-col  \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full  md:pr-1 my-1\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-[#B4B4B4] text-xs  mb-1\",children:\"Initial Status\"}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"select\",{value:initialStatus,onChange:v=>setInitialStatus(v.target.value),className:\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"\",children:\"Select Status\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Pending\",children:\"Pending\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Approved\",children:\"Approved\"}),/*#__PURE__*/_jsx(\"option\",{value:\"Denied\",children:\"Denied\"})]})})]})})}),/*#__PURE__*/_jsx(\"div\",{className:\"text-xs font-medium mt-5 mb-2 text-black\",children:\"Upload Authorization Documents\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:[/*#__PURE__*/_jsxs(\"div\",{...getRootPropsUploadAuthorizationDocuments({className:\"dropzone\"}),// style={dropzoneStyle}\nclassName:\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\",children:[/*#__PURE__*/_jsx(\"input\",{...getInputPropsUploadAuthorizationDocuments()}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-8 p-2 bg-[#0388A6] rounded-full text-white\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-2\",children:\"Drag & Drop Image File or BROWSE\"})]}),/*#__PURE__*/_jsx(\"aside\",{style:thumbsContainer,children:/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col \",children:filesUploadAuthorizationDocuments===null||filesUploadAuthorizationDocuments===void 0?void 0:filesUploadAuthorizationDocuments.map((file,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",class:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-5 text-[#303030] text-sm\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:file.name}),/*#__PURE__*/_jsxs(\"div\",{children:[(file.size/(1024*1024)).toFixed(2),\" mb\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{setFilesUploadAuthorizationDocuments(prevFiles=>prevFiles.filter((_,indexToRemove)=>index!==indexToRemove));},className:\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\",children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-5\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M6 18 18 6M6 6l12 12\"})})})]},file.name))})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setStepSelect(3),className:\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\",children:\"Back\"}),/*#__PURE__*/_jsx(\"button\",{disabled:loadingCaseAdd,onClick:async()=>{await dispatch(addNewCase({first_name:firstName,last_name:lastName,full_name:firstName+\" \"+lastName,birth_day:birthDate,patient_phone:phone,patient_email:email,patient_address:address,patient_city:city,patient_country:country.value,//\ncoordinator:coordinator.value,case_date:caseDate,case_type:caseType,case_description:caseDescription,//\nstatus_coordination:coordinatStatus,appointment_date:appointmentDate,service_location:serviceLocation,provider:providerName.value,//\ninvoice_number:invoiceNumber,date_issued:dateIssued,invoice_amount:amount,assurance:insuranceCompany.value,assurance_number:insuranceNumber,policy_number:policyNumber,assurance_status:initialStatus,// files\ninitial_medical_reports:filesInitialMedicalReports,upload_invoice:filesUploadInvoice,upload_authorization_documents:filesUploadAuthorizationDocuments}));},className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:loadingCaseAdd?\"Loading..\":\"Submit\"})]})]}):null,stepSelect===5?/*#__PURE__*/_jsx(\"div\",{className:\"\",children:/*#__PURE__*/_jsx(\"div\",{className:\"my-2 bg-white py-4 px-2 rounded-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-30 flex flex-col items-center justify-center\",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"my-5 font-semibold text-2xl text-black\",children:\"Case Created Successfully!\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-base text-center md:w-2/3 mx-auto w-full px-3\",children:\"Your case has been successfully created and saved. You can now view the case details or create another case.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-row items-center justify-end my-3\",children:/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",className:\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\",children:\"Go to Dahboard\"})})]})})}):null]})]})})]})});}export default AddCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "DefaultLayout", "addreactionface", "toast", "providersList", "addNewCase", "Select", "useDropzone", "getInsuranesList", "getListCoordinators", "COUNTRIES", "jsx", "_jsx", "jsxs", "_jsxs", "STEPSLIST", "index", "title", "description", "thumbsContainer", "display", "flexDirection", "flexWrap", "marginTop", "AddCaseScreen", "navigate", "location", "dispatch", "firstName", "setFirstName", "firstNameError", "setFirstNameError", "lastName", "setLastName", "lastNameError", "setLastNameError", "email", "setEmail", "emailError", "setEmailError", "birthDate", "setBirthDate", "birthDateE<PERSON>r", "setBirthDateError", "phone", "setPhone", "phoneError", "setPhoneError", "address", "<PERSON><PERSON><PERSON><PERSON>", "addressError", "setAddressError", "city", "setCity", "cityError", "setCityError", "country", "setCountry", "countryError", "setCountryError", "coordinator", "setCoordinator", "coordinator<PERSON><PERSON><PERSON>", "setCoordinatorError", "caseDate", "setCaseDate", "caseDateError", "setCaseDateError", "caseType", "setCaseType", "caseTypeError", "setCaseTypeError", "caseDescription", "setCaseDescription", "caseDescriptionError", "setCaseDescriptionError", "coordinatStatus", "setCoordinatStatus", "coordinatStatusError", "setCoordinatStatusError", "appointmentDate", "setAppointmentDate", "appointmentDateError", "setAppointmentDateError", "serviceLocation", "setServiceLocation", "serviceLocationError", "setServiceLocationError", "providerName", "setProviderName", "providerNameError", "setProviderNameError", "providerPhone", "setProviderPhone", "providerPhoneError", "setProviderPhoneError", "providerEmail", "setProviderEmail", "providerEmailError", "setProviderEmailError", "providerAddress", "set<PERSON>roviderAddress", "providerAddressError", "setProviderAddressError", "invoiceNumber", "setInvoiceNumber", "invoiceNumberError", "setInvoiceNumberError", "dateIssued", "setDateIssued", "dateIssuedError", "setDateIssuedError", "amount", "setAmount", "amountError", "setAmountError", "insuranceCompany", "setInsuranceCompany", "insuranceCompanyError", "setInsuranceCompanyError", "insuranceNumber", "setInsuranceNumber", "insuranceNumberError", "setInsuranceNumberError", "policyNumber", "setPolicyNumber", "policyNumberError", "setPolicyNumberError", "initialStatus", "setInitialStatus", "initialStatusError", "setInitialStatusError", "filesInitialMedicalReports", "setFilesInitialMedicalReports", "getRootProps", "getRootPropsInitialMedical", "getInputProps", "getInputPropsInitialMedical", "accept", "onDrop", "acceptedFiles", "prevFiles", "map", "file", "Object", "assign", "preview", "URL", "createObjectURL", "for<PERSON>ach", "revokeObjectURL", "filesUploadInvoice", "setFilesUploadInvoice", "getRootPropsUploadInvoice", "getInputPropsUploadInvoice", "filesUploadAuthorizationDocuments", "setFilesUploadAuthorizationDocuments", "getRootPropsUploadAuthorizationDocuments", "getInputPropsUploadAuthorizationDocuments", "stepSelect", "setStepSelect", "userLogin", "state", "userInfo", "listProviders", "providerList", "providers", "loadingProviders", "errorProviders", "createCase", "createNewCase", "loadingCaseAdd", "successCaseAdd", "errorCaseAdd", "listInsurances", "insuranceList", "insurances", "loadingInsurances", "errorInsurances", "listCoordinators", "coordinatorsList", "coordinators", "loadingCoordinators", "errorCoordinators", "redirect", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "step", "src", "onError", "e", "target", "onerror", "concat", "type", "placeholder", "value", "onChange", "v", "option", "options", "label", "icon", "isSearchable", "styles", "control", "base", "background", "border", "boxShadow", "isFocused", "alignItems", "singleValue", "assurance", "id", "assurance_name", "filterOption", "inputValue", "toLowerCase", "includes", "item", "full_name", "rows", "onClick", "check", "error", "style", "class", "name", "size", "toFixed", "filter", "_", "indexToRemove", "disabled", "first_name", "last_name", "birth_day", "patient_phone", "patient_email", "patient_address", "patient_city", "patient_country", "case_date", "case_type", "case_description", "status_coordination", "appointment_date", "service_location", "provider", "invoice_number", "date_issued", "invoice_amount", "assurance_number", "policy_number", "assurance_status", "initial_medical_reports", "upload_invoice", "upload_authorization_documents"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/AddCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate } from \"react-router-dom\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport addreactionface from \"../../images/icon/add_reaction.png\";\nimport { toast } from \"react-toastify\";\nimport { providersList } from \"../../redux/actions/providerActions\";\nimport { addNewCase } from \"../../redux/actions/caseActions\";\n\nimport Select from \"react-select\";\n\nimport { useDropzone } from \"react-dropzone\";\nimport { getInsuranesList } from \"../../redux/actions/insuranceActions\";\nimport { getListCoordinators } from \"../../redux/actions/userActions\";\nimport { COUNTRIES } from \"../../constants\";\n\nconst STEPSLIST = [\n  {\n    index: 0,\n    title: \"General Information\",\n    description:\n      \"Please enter the general information about the patient and the case.\",\n  },\n  {\n    index: 1,\n    title: \"Coordination Details\",\n    description:\n      \"Provide information about the initial coordination & appointment details for this case.\",\n  },\n  {\n    index: 2,\n    title: \"Medical Reports\",\n    description: \"Upload any initial medical reports related to the case.\",\n  },\n  {\n    index: 3,\n    title: \"Invoices\",\n    description:\n      \"If there are any initial invoices related to the case, please provide the details and upload the documents.\",\n  },\n  {\n    index: 4,\n    title: \"Insurance Authorization\",\n    description:\n      \"Please provide the details of the insurance authorization for this case, and upload any relevant documents.\",\n  },\n  {\n    index: 5,\n    title: \"Finish\",\n    description: \"You can go back to any step to make changes.\",\n  },\n];\n\nconst thumbsContainer = {\n  display: \"flex\",\n  flexDirection: \"row\",\n  flexWrap: \"wrap\",\n  marginTop: 16,\n};\n\nfunction AddCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n\n  //\n  const [firstName, setFirstName] = useState(\"\");\n  const [firstNameError, setFirstNameError] = useState(\"\");\n\n  const [lastName, setLastName] = useState(\"\");\n  const [lastNameError, setLastNameError] = useState(\"\");\n\n  const [email, setEmail] = useState(\"\");\n  const [emailError, setEmailError] = useState(\"\");\n\n  const [birthDate, setBirthDate] = useState(\"\");\n  const [birthDateError, setBirthDateError] = useState(\"\");\n\n  const [phone, setPhone] = useState(\"\");\n  const [phoneError, setPhoneError] = useState(\"\");\n\n  const [address, setAddress] = useState(\"\");\n  const [addressError, setAddressError] = useState(\"\");\n\n  const [city, setCity] = useState(\"\");\n  const [cityError, setCityError] = useState(\"\");\n\n  const [country, setCountry] = useState(\"\");\n  const [countryError, setCountryError] = useState(\"\");\n  //\n  const [coordinator, setCoordinator] = useState(\"\");\n  const [coordinatorError, setCoordinatorError] = useState(\"\");\n\n  const [caseDate, setCaseDate] = useState(\"\");\n  const [caseDateError, setCaseDateError] = useState(\"\");\n\n  const [caseType, setCaseType] = useState(\"\");\n  const [caseTypeError, setCaseTypeError] = useState(\"\");\n\n  const [caseDescription, setCaseDescription] = useState(\"\");\n  const [caseDescriptionError, setCaseDescriptionError] = useState(\"\");\n  //\n  const [coordinatStatus, setCoordinatStatus] = useState(\"\");\n  const [coordinatStatusError, setCoordinatStatusError] = useState(\"\");\n\n  const [appointmentDate, setAppointmentDate] = useState(\"\");\n  const [appointmentDateError, setAppointmentDateError] = useState(\"\");\n\n  const [serviceLocation, setServiceLocation] = useState(\"\");\n  const [serviceLocationError, setServiceLocationError] = useState(\"\");\n  //\n  const [providerName, setProviderName] = useState(\"\");\n  const [providerNameError, setProviderNameError] = useState(\"\");\n\n  const [providerPhone, setProviderPhone] = useState(\"\");\n  const [providerPhoneError, setProviderPhoneError] = useState(\"\");\n\n  const [providerEmail, setProviderEmail] = useState(\"\");\n  const [providerEmailError, setProviderEmailError] = useState(\"\");\n\n  const [providerAddress, setProviderAddress] = useState(\"\");\n  const [providerAddressError, setProviderAddressError] = useState(\"\");\n  //\n  const [invoiceNumber, setInvoiceNumber] = useState(\"\");\n  const [invoiceNumberError, setInvoiceNumberError] = useState(\"\");\n\n  const [dateIssued, setDateIssued] = useState(\"\");\n  const [dateIssuedError, setDateIssuedError] = useState(\"\");\n\n  const [amount, setAmount] = useState(0);\n  const [amountError, setAmountError] = useState(\"\");\n  //\n  const [insuranceCompany, setInsuranceCompany] = useState(\"\");\n  const [insuranceCompanyError, setInsuranceCompanyError] = useState(\"\");\n\n  const [insuranceNumber, setInsuranceNumber] = useState(\"\");\n  const [insuranceNumberError, setInsuranceNumberError] = useState(\"\");\n\n  const [policyNumber, setPolicyNumber] = useState(\"\");\n  const [policyNumberError, setPolicyNumberError] = useState(\"\");\n\n  const [initialStatus, setInitialStatus] = useState(\"\");\n  const [initialStatusError, setInitialStatusError] = useState(\"\");\n\n  // fils\n  // initialMedicalReports\n  const [filesInitialMedicalReports, setFilesInitialMedicalReports] = useState(\n    []\n  );\n  const {\n    getRootProps: getRootPropsInitialMedical,\n    getInputProps: getInputPropsInitialMedical,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesInitialMedicalReports((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesInitialMedicalReports.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Upload Invoice\n  const [filesUploadInvoice, setFilesUploadInvoice] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadInvoice,\n    getInputProps: getInputPropsUploadInvoice,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadInvoice((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadInvoice.forEach((file) => URL.revokeObjectURL(file.preview));\n  }, []);\n  // Upload Authorization Documents\n  const [\n    filesUploadAuthorizationDocuments,\n    setFilesUploadAuthorizationDocuments,\n  ] = useState([]);\n  const {\n    getRootProps: getRootPropsUploadAuthorizationDocuments,\n    getInputProps: getInputPropsUploadAuthorizationDocuments,\n  } = useDropzone({\n    accept: {\n      \"*\": [],\n    },\n    onDrop: (acceptedFiles) => {\n      setFilesUploadAuthorizationDocuments((prevFiles) => [\n        ...prevFiles,\n        ...acceptedFiles.map((file) =>\n          Object.assign(file, {\n            preview: URL.createObjectURL(file),\n          })\n        ),\n      ]);\n    },\n  });\n\n  useEffect(() => {\n    return () =>\n      filesUploadAuthorizationDocuments.forEach((file) =>\n        URL.revokeObjectURL(file.preview)\n      );\n  }, []);\n\n  // Configure react-dropzone\n\n  //\n\n  const [stepSelect, setStepSelect] = useState(0);\n\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo } = userLogin;\n\n  const listProviders = useSelector((state) => state.providerList);\n  const { providers, loadingProviders, errorProviders } = listProviders;\n\n  const createCase = useSelector((state) => state.createNewCase);\n  const { loadingCaseAdd, successCaseAdd, errorCaseAdd } = createCase;\n\n  const listInsurances = useSelector((state) => state.insuranceList);\n  const { insurances, loadingInsurances, errorInsurances } = listInsurances;\n\n  const listCoordinators = useSelector((state) => state.coordinatorsList);\n  const { coordinators, loadingCoordinators, errorCoordinators } =\n    listCoordinators;\n\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      setStepSelect(0);\n      dispatch(getListCoordinators(\"0\"));\n      dispatch(providersList(\"0\"));\n      dispatch(getInsuranesList(\"0\"));\n      //   dispatch(clientList(\"0\"));\n    }\n  }, [navigate, userInfo, dispatch]);\n\n  useEffect(() => {\n    if (successCaseAdd) {\n      setStepSelect(5);\n    }\n  }, [successCaseAdd]);\n\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Create New Case</div>\n        </div>\n        {/*  */}\n        <div className=\"py-5 px-4 flex justify-between\">\n          <h4 className=\" uppercase font-semibold text-black dark:text-white\">\n            New Case\n          </h4>\n        </div>\n        {/*  */}\n        <div className=\"rounded-sm border border-stroke bg-white px-5 pt-6 pb-2.5 shadow-default dark:border-strokedark dark:bg-boxdark sm:px-7.5 xl:pb-1\">\n          <div className=\"flex md:flex-row flex-col\">\n            <div className=\"md:w-1/3 w-full flex md:flex-col flex-wrap px-3  relative\">\n              <div className=\"w-[1px] h-full absolute top-0 bg-[#0388A6] bg-opacity-60 left-[27.5px] md:block hidden\"></div>\n              {STEPSLIST?.map((step, index) => (\n                <div\n                  // onClick={() => setStepSelect(step.index)}\n                  className=\"flex flex-row mb-3 md:min-h-20 cursor-pointer md:items-start items-center\"\n                >\n                  {stepSelect < step.index ? (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <img\n                        src={addreactionface}\n                        className=\"size-5\"\n                        onError={(e) => {\n                          e.target.onerror = null;\n                          e.target.src = \"/assets/placeholder.png\";\n                        }}\n                      />\n                    </div>\n                  ) : stepSelect === step.index ? (\n                    <div className=\"size-8 bg-white z-10  border-[11px] rounded-full\"></div>\n                  ) : (\n                    <div className=\"size-8 bg-[#E2EFF2] z-10  rounded-full text-center items-center flex justify-center text-[#0388A6]\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-5\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                    </div>\n                  )}\n\n                  <div className=\"text-black flex-1 px-2\">\n                    <div className=\"font-medium text-sm\">{step.title}</div>\n                    {stepSelect === step.index ? (\n                      <div className=\"text-xs font-light md:block hidden\">\n                        {step.description}\n                      </div>\n                    ) : null}\n                  </div>\n                </div>\n              ))}\n            </div>\n            <div className=\"md:w-2/3 w-full  px-3 py-4  bg-[#F8F9FF]\">\n              {/* step 1 - General Information */}\n              {stepSelect === 0 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    General Information\n                  </div>\n                  {/* Patient Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Patient Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          First Name <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              firstNameError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"First Name\"\n                            value={firstName}\n                            onChange={(v) => setFirstName(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {firstNameError ? firstNameError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Last Name\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Last Name\"\n                            value={lastName}\n                            onChange={(v) => setLastName(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Email\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              emailError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"email\"\n                            placeholder=\"Email Address\"\n                            value={email}\n                            onChange={(v) => setEmail(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {emailError ? emailError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          phone <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={`outline-none border ${\n                              phoneError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"Phone no\"\n                            value={phone}\n                            onChange={(v) => setPhone(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {phoneError ? phoneError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Country <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={country}\n                            onChange={(option) => {\n                              setCountry(option);\n                            }}\n                            options={COUNTRIES.map((country) => ({\n                              value: country.title,\n                              label: (\n                                <div\n                                  className={`${\n                                    country.title === \"\" ? \"py-2\" : \"\"\n                                  } flex flex-row items-center`}\n                                >\n                                  <span className=\"mr-2\">{country.icon}</span>\n                                  <span>{country.title}</span>\n                                </div>\n                              ),\n                            }))}\n                            className=\"text-sm\"\n                            placeholder=\"Select a country...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: countryError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n\n                          <div className=\" text-[8px] text-danger\">\n                            {countryError ? countryError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"w-full md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">City</div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              cityError ? \"border-danger\" : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"City\"\n                            value={city}\n                            onChange={(v) => setCity(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {cityError ? cityError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">CIA</div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceCompanyError ? insuranceCompanyError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          CIA ID\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              insuranceNumberError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                            type=\"text\"\n                            placeholder=\"CIA ID\"\n                            value={insuranceNumber}\n                            onChange={(v) => setInsuranceNumber(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {insuranceNumberError ? insuranceNumberError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                  </div>\n                  {/* Case Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Case Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Assigned Coordinator{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <Select\n                            value={coordinator}\n                            onChange={(option) => {\n                              setCoordinator(option);\n                            }}\n                            className=\"text-sm\"\n                            options={coordinators?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Coordinator...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: coordinatorError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatorError ? coordinatorError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pl-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs mb-1\">\n                          Case Creation Date{\" \"}\n                          <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <input\n                            className={` outline-none border ${\n                              caseDateError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                            type=\"date\"\n                            placeholder=\"Case Creation Date\"\n                            value={caseDate}\n                            onChange={(v) => setCaseDate(v.target.value)}\n                          />\n                          <div className=\" text-[8px] text-danger\">\n                            {caseDateError ? caseDateError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Type <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={caseType}\n                            onChange={(v) => setCaseType(v.target.value)}\n                            className={` outline-none border ${\n                              caseTypeError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            } px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Type</option>\n                            <option value={\"Medical\"}>Medical</option>\n                            <option value={\"Technical\"}>Technical</option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {caseTypeError ? caseTypeError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Description\n                        </div>\n                        <div>\n                          <textarea\n                            value={caseDescription}\n                            rows={5}\n                            onChange={(v) => setCaseDescription(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          ></textarea>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Save & Continue - step 1 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setFirstNameError(\"\");\n                        setLastNameError(\"\");\n                        setBirthDateError(\"\");\n                        setPhoneError(\"\");\n                        setEmailError(\"\");\n                        setAddressError(\"\");\n                        setCaseTypeError(\"\");\n                        setCaseDateError(\"\");\n                        setCoordinatorError(\"\");\n                        setCityError(\"\");\n                        setCountryError(\"\");\n\n                        if (firstName === \"\") {\n                          setFirstNameError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (phone === \"\") {\n                          setPhoneError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (country === \"\" || country.value === \"\") {\n                          setCountryError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (coordinator === \"\" || coordinator.value === \"\") {\n                          setCoordinatorError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseDate === \"\") {\n                          setCaseDateError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (caseType === \"\") {\n                          setCaseTypeError(\"This field is required.\");\n                          check = false;\n                        }\n                        if (check) {\n                          setStepSelect(1);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 2 */}\n              {stepSelect === 1 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Coordination Details\n                  </div>\n                  {/* Initial Coordination Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Coordination Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Status <strong className=\"text-danger\">*</strong>\n                        </div>\n                        <div>\n                          <select\n                            value={coordinatStatus}\n                            onChange={(v) => setCoordinatStatus(v.target.value)}\n                            className={`outline-none border ${\n                              coordinatStatusError\n                                ? \"border-danger\"\n                                : \"border-[#F1F3FF]\"\n                            }  px-3 py-2 w-full rounded text-sm`}\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"pending-coordination\"}>\n                              Pending Coordination\n                            </option>\n                            <option value={\"coordinated-missing-m-r\"}>\n                              Coordinated, Missing M.R.\n                            </option>\n                            <option value={\"coordinated-missing-invoice\"}>\n                              Coordinated, Missing Invoice\n                            </option>\n                            <option\n                              value={\"waiting-for-insurance-authorization\"}\n                            >\n                              Waiting for Insurance Authorization\n                            </option>\n                            <option value={\"coordinated-patient-not-seen-yet\"}>\n                              Coordinated, Patient not seen yet\n                            </option>\n                            <option value={\"fully-coordinated\"}>\n                              Fully Coordinated\n                            </option>\n                          </select>\n                          <div className=\" text-[8px] text-danger\">\n                            {coordinatStatusError ? coordinatStatusError : \"\"}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Appointment Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Appointment Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Appointment Date\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Appointment Date\"\n                            value={appointmentDate}\n                            onChange={(v) => setAppointmentDate(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Service Location\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\" Service Location\"\n                            value={serviceLocation}\n                            onChange={(v) => setServiceLocation(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Provider Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Provider Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\" w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Provider Name\n                        </div>\n                        <div>\n                          <Select\n                            value={providerName}\n                            onChange={(option) => {\n                              setProviderName(option);\n                            }}\n                            className=\"text-sm\"\n                            options={providers?.map((item) => ({\n                              value: item.id,\n                              label: item.full_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            placeholder=\"Select Provider...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: providerNameError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Save & Continue - step 2 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(0)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => {\n                        var check = true;\n                        setCoordinatStatusError(\"\");\n\n                        if (coordinatStatus === \"\") {\n                          setCoordinatStatusError(\"This field is required.\");\n                          check = false;\n                        }\n\n                        if (check) {\n                          setStepSelect(2);\n                        } else {\n                          toast.error(\n                            \"Some fields are empty or invalid. please try again\"\n                          );\n                        }\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 3 */}\n              {stepSelect === 2 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Medical Reports\n                  </div>\n                  {/* Initial Medical Reports: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Initial Medical Reports:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsInitialMedical({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsInitialMedical()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesInitialMedicalReports?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesInitialMedicalReports((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 3 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(1)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 4 */}\n              {stepSelect === 3 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Invoices\n                  </div>\n                  {/* Invoice Information: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Invoice Information:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Invoice Number (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Invoice Number (Optional)\"\n                            value={invoiceNumber}\n                            onChange={(v) => setInvoiceNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Date Issued (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"date\"\n                            placeholder=\"Date Issued (Optional)\"\n                            value={dateIssued}\n                            onChange={(v) => setDateIssued(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                    {/*  */}\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Amount (Optional)\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"number\"\n                            placeholder=\"Amount (Optional)\"\n                            value={amount}\n                            onChange={(v) => setAmount(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Invoice\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadInvoice({ className: \"dropzone\" })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadInvoice()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadInvoice?.map((file, index) => (\n                          <div\n                            className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                            key={file.name}\n                          >\n                            <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                class=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {file.name}\n                              </div>\n                              <div>\n                                {(file.size / (1024 * 1024)).toFixed(2)} mb\n                              </div>\n                            </div>\n                            <button\n                              onClick={() => {\n                                setFilesUploadInvoice((prevFiles) =>\n                                  prevFiles.filter(\n                                    (_, indexToRemove) =>\n                                      index !== indexToRemove\n                                  )\n                                );\n                              }}\n                              className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                            >\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                fill=\"none\"\n                                viewBox=\"0 0 24 24\"\n                                stroke-width=\"1.5\"\n                                stroke=\"currentColor\"\n                                class=\"size-5\"\n                              >\n                                <path\n                                  stroke-linecap=\"round\"\n                                  stroke-linejoin=\"round\"\n                                  d=\"M6 18 18 6M6 6l12 12\"\n                                />\n                              </svg>\n                            </button>\n                          </div>\n                        ))}\n                      </div>\n                    </aside>\n                  </div>\n\n                  {/* Save & Continue - step 4 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(2)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      onClick={() => setStepSelect(4)}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      Save & Continue\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 5 */}\n              {stepSelect === 4 ? (\n                <div className=\"\">\n                  <div className=\"text-[#0388A6] font-semibold text-xl\">\n                    Insurance Authorization\n                  </div>\n                  {/* Insurance Details: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Insurance Details:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Insurance Company Name\n                        </div>\n                        <div>\n                          <Select\n                            value={insuranceCompany}\n                            onChange={(option) => {\n                              setInsuranceCompany(option);\n                            }}\n                            options={insurances?.map((assurance) => ({\n                              value: assurance.id,\n                              label: assurance.assurance_name || \"\",\n                            }))}\n                            filterOption={(option, inputValue) =>\n                              option.label\n                                .toLowerCase()\n                                .includes(inputValue.toLowerCase())\n                            }\n                            className=\"text-sm\"\n                            placeholder=\"Select Insurance...\"\n                            isSearchable\n                            styles={{\n                              control: (base, state) => ({\n                                ...base,\n                                background: \"#fff\",\n                                border: insuranceCompanyError\n                                  ? \"1px solid #d34053\"\n                                  : \"1px solid #F1F3FF\",\n                                boxShadow: state.isFocused ? \"none\" : \"none\",\n                                \"&:hover\": {\n                                  border: \"1px solid #F1F3FF\",\n                                },\n                              }),\n                              option: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                              singleValue: (base) => ({\n                                ...base,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                              }),\n                            }}\n                          />\n                        </div>\n                      </div>\n                      {/*  */}\n                      <div className=\"md:w-1/2 w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Policy Number\n                        </div>\n                        <div>\n                          <input\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                            type=\"text\"\n                            placeholder=\"Policy Number\"\n                            value={policyNumber}\n                            onChange={(v) => setPolicyNumber(v.target.value)}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Authorization Status: */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Authorization Status:\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"flex md:flex-row flex-col  \">\n                      <div className=\"w-full  md:pr-1 my-1\">\n                        <div className=\"text-[#B4B4B4] text-xs  mb-1\">\n                          Initial Status\n                        </div>\n                        <div>\n                          <select\n                            value={initialStatus}\n                            onChange={(v) => setInitialStatus(v.target.value)}\n                            className=\" outline-none border border-[#F1F3FF] px-3 py-2 w-full rounded text-sm\"\n                          >\n                            <option value={\"\"}>Select Status</option>\n                            <option value={\"Pending\"}>Pending</option>\n                            <option value={\"Approved\"}>Approved</option>\n                            <option value={\"Denied\"}>Denied</option>\n                          </select>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/* Upload Authorization Documents */}\n                  <div className=\"text-xs font-medium mt-5 mb-2 text-black\">\n                    Upload Authorization Documents\n                  </div>\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div\n                      {...getRootPropsUploadAuthorizationDocuments({\n                        className: \"dropzone\",\n                      })}\n                      // style={dropzoneStyle}\n                      className=\"bg-[#F5F6FF] w-full min-h-50 flex flex-col items-center justify-center  cursor-pointer\"\n                    >\n                      <input {...getInputPropsUploadAuthorizationDocuments()} />\n                      <div className=\"my-2\">\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          fill=\"none\"\n                          viewBox=\"0 0 24 24\"\n                          stroke-width=\"1.5\"\n                          stroke=\"currentColor\"\n                          className=\"size-8 p-2 bg-[#0388A6] rounded-full text-white\"\n                        >\n                          <path\n                            stroke-linecap=\"round\"\n                            stroke-linejoin=\"round\"\n                            d=\"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n                          />\n                        </svg>\n                      </div>\n                      <div className=\"my-2\">\n                        Drag & Drop Image File or BROWSE\n                      </div>\n                    </div>\n                    <aside style={thumbsContainer}>\n                      <div className=\"w-full flex flex-col \">\n                        {filesUploadAuthorizationDocuments?.map(\n                          (file, index) => (\n                            <div\n                              className=\"bg-[#F3F5F7] rounded-xl px-5 py-5 w-full my-2 flex flex-row items-center\"\n                              key={file.name}\n                            >\n                              <div className=\" rounded-full  bg-white text-[#81838E] text-center p-2 shadow-1 \">\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  viewBox=\"0 0 24 24\"\n                                  fill=\"currentColor\"\n                                  class=\"size-4\"\n                                >\n                                  <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                  <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                                </svg>\n                              </div>\n                              <div className=\"flex-1 px-5 text-[#303030] text-sm\">\n                                <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                  {file.name}\n                                </div>\n                                <div>\n                                  {(file.size / (1024 * 1024)).toFixed(2)} mb\n                                </div>\n                              </div>\n                              <button\n                                onClick={() => {\n                                  setFilesUploadAuthorizationDocuments(\n                                    (prevFiles) =>\n                                      prevFiles.filter(\n                                        (_, indexToRemove) =>\n                                          index !== indexToRemove\n                                      )\n                                  );\n                                }}\n                                className=\"rounded-full p-1 bg-[#E6E7E8] font-bold text-[#9D9D9D]\"\n                              >\n                                <svg\n                                  xmlns=\"http://www.w3.org/2000/svg\"\n                                  fill=\"none\"\n                                  viewBox=\"0 0 24 24\"\n                                  stroke-width=\"1.5\"\n                                  stroke=\"currentColor\"\n                                  class=\"size-5\"\n                                >\n                                  <path\n                                    stroke-linecap=\"round\"\n                                    stroke-linejoin=\"round\"\n                                    d=\"M6 18 18 6M6 6l12 12\"\n                                  />\n                                </svg>\n                              </button>\n                            </div>\n                          )\n                        )}\n                      </div>\n                    </aside>\n                  </div>\n                  {/* Save & Continue - step 5 */}\n                  <div className=\"flex flex-row items-center justify-end my-3\">\n                    <button\n                      onClick={() => setStepSelect(3)}\n                      className=\"bg-white text-[#0388A6] text-sm px-5 py-3 rounded-full mr-3\"\n                    >\n                      Back\n                    </button>\n                    <button\n                      disabled={loadingCaseAdd}\n                      onClick={async () => {\n                        await dispatch(\n                          addNewCase({\n                            first_name: firstName,\n                            last_name: lastName,\n                            full_name: firstName + \" \" + lastName,\n                            birth_day: birthDate,\n                            patient_phone: phone,\n                            patient_email: email,\n                            patient_address: address,\n                            patient_city: city,\n                            patient_country: country.value,\n                            //\n                            coordinator: coordinator.value,\n                            case_date: caseDate,\n                            case_type: caseType,\n                            case_description: caseDescription,\n                            //\n                            status_coordination: coordinatStatus,\n                            appointment_date: appointmentDate,\n                            service_location: serviceLocation,\n                            provider: providerName.value,\n                            //\n                            invoice_number: invoiceNumber,\n                            date_issued: dateIssued,\n                            invoice_amount: amount,\n                            assurance: insuranceCompany.value,\n                            assurance_number: insuranceNumber,\n                            policy_number: policyNumber,\n                            assurance_status: initialStatus,\n                            // files\n                            initial_medical_reports: filesInitialMedicalReports,\n                            upload_invoice: filesUploadInvoice,\n                            upload_authorization_documents:\n                              filesUploadAuthorizationDocuments,\n                          })\n                        );\n                      }}\n                      className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                    >\n                      {loadingCaseAdd ? \"Loading..\" : \"Submit\"}\n                    </button>\n                  </div>\n                </div>\n              ) : null}\n              {/* step 6 */}\n              {stepSelect === 5 ? (\n                <div className=\"\">\n                  <div className=\"my-2 bg-white py-4 px-2 rounded-md\">\n                    <div className=\"min-h-30 flex flex-col items-center justify-center\">\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                        stroke-width=\"1.5\"\n                        stroke=\"currentColor\"\n                        className=\"size-13 p-3 rounded-full text-[#0388A6] bg-[#0388A6] bg-opacity-10\"\n                      >\n                        <path\n                          stroke-linecap=\"round\"\n                          stroke-linejoin=\"round\"\n                          d=\"m4.5 12.75 6 6 9-13.5\"\n                        />\n                      </svg>\n                      <div className=\"my-5 font-semibold text-2xl text-black\">\n                        Case Created Successfully!\n                      </div>\n                      <div className=\"text-base text-center md:w-2/3 mx-auto w-full px-3\">\n                        Your case has been successfully created and saved. You\n                        can now view the case details or create another case.\n                      </div>\n                      <div className=\"flex flex-row items-center justify-end my-3\">\n                        {/* <button\n                          onClick={() => {\n                            setStepSelect(4);\n                          }}\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </button> */}\n                        <a\n                          href=\"/dashboard\"\n                          className=\"text-white bg-[#0388A6] text-sm px-5 py-3 rounded-full\"\n                        >\n                          Go to Dahboard\n                        </a>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        </div>\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default AddCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,eAAe,KAAM,oCAAoC,CAChE,OAASC,KAAK,KAAQ,gBAAgB,CACtC,OAASC,aAAa,KAAQ,qCAAqC,CACnE,OAASC,UAAU,KAAQ,iCAAiC,CAE5D,MAAO,CAAAC,MAAM,KAAM,cAAc,CAEjC,OAASC,WAAW,KAAQ,gBAAgB,CAC5C,OAASC,gBAAgB,KAAQ,sCAAsC,CACvE,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,SAAS,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,SAAS,CAAG,CAChB,CACEC,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,qBAAqB,CAC5BC,WAAW,CACT,sEACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,sBAAsB,CAC7BC,WAAW,CACT,yFACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,iBAAiB,CACxBC,WAAW,CAAE,yDACf,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,UAAU,CACjBC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,yBAAyB,CAChCC,WAAW,CACT,6GACJ,CAAC,CACD,CACEF,KAAK,CAAE,CAAC,CACRC,KAAK,CAAE,QAAQ,CACfC,WAAW,CAAE,8CACf,CAAC,CACF,CAED,KAAM,CAAAC,eAAe,CAAG,CACtBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,KAAK,CACpBC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,EACb,CAAC,CAED,QAAS,CAAAC,aAAaA,CAAA,CAAG,CACvB,KAAM,CAAAC,QAAQ,CAAGzB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA0B,QAAQ,CAAG3B,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4B,QAAQ,CAAG9B,WAAW,CAAC,CAAC,CAE9B;AACA,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACkC,cAAc,CAAEC,iBAAiB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACoC,QAAQ,CAAEC,WAAW,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACsC,aAAa,CAAEC,gBAAgB,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACwC,KAAK,CAAEC,QAAQ,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC0C,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAAC4C,SAAS,CAAEC,YAAY,CAAC,CAAG7C,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAAC8C,cAAc,CAAEC,iBAAiB,CAAC,CAAG/C,QAAQ,CAAC,EAAE,CAAC,CAExD,KAAM,CAACgD,KAAK,CAAEC,QAAQ,CAAC,CAAGjD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkD,UAAU,CAAEC,aAAa,CAAC,CAAGnD,QAAQ,CAAC,EAAE,CAAC,CAEhD,KAAM,CAACoD,OAAO,CAAEC,UAAU,CAAC,CAAGrD,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACsD,YAAY,CAAEC,eAAe,CAAC,CAAGvD,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACwD,IAAI,CAAEC,OAAO,CAAC,CAAGzD,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAAC0D,SAAS,CAAEC,YAAY,CAAC,CAAG3D,QAAQ,CAAC,EAAE,CAAC,CAE9C,KAAM,CAAC4D,OAAO,CAAEC,UAAU,CAAC,CAAG7D,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC8D,YAAY,CAAEC,eAAe,CAAC,CAAG/D,QAAQ,CAAC,EAAE,CAAC,CACpD;AACA,KAAM,CAACgE,WAAW,CAAEC,cAAc,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnE,QAAQ,CAAC,EAAE,CAAC,CAE5D,KAAM,CAACoE,QAAQ,CAAEC,WAAW,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACsE,aAAa,CAAEC,gBAAgB,CAAC,CAAGvE,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAACwE,QAAQ,CAAEC,WAAW,CAAC,CAAGzE,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAAC0E,aAAa,CAAEC,gBAAgB,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAEtD,KAAM,CAAC4E,eAAe,CAAEC,kBAAkB,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8E,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/E,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACkF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACoF,eAAe,CAAEC,kBAAkB,CAAC,CAAGrF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAACsF,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGvF,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACwF,eAAe,CAAEC,kBAAkB,CAAC,CAAGzF,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0F,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3F,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC4F,YAAY,CAAEC,eAAe,CAAC,CAAG7F,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC8F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACgG,aAAa,CAAEC,gBAAgB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACoG,aAAa,CAAEC,gBAAgB,CAAC,CAAGrG,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvG,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACwG,eAAe,CAAEC,kBAAkB,CAAC,CAAGzG,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC0G,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CACpE;AACA,KAAM,CAAC4G,aAAa,CAAEC,gBAAgB,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC8G,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG/G,QAAQ,CAAC,EAAE,CAAC,CAEhE,KAAM,CAACgH,UAAU,CAAEC,aAAa,CAAC,CAAGjH,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkH,eAAe,CAAEC,kBAAkB,CAAC,CAAGnH,QAAQ,CAAC,EAAE,CAAC,CAE1D,KAAM,CAACoH,MAAM,CAAEC,SAAS,CAAC,CAAGrH,QAAQ,CAAC,CAAC,CAAC,CACvC,KAAM,CAACsH,WAAW,CAAEC,cAAc,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CAClD;AACA,KAAM,CAACwH,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC0H,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG3H,QAAQ,CAAC,EAAE,CAAC,CAEtE,KAAM,CAAC4H,eAAe,CAAEC,kBAAkB,CAAC,CAAG7H,QAAQ,CAAC,EAAE,CAAC,CAC1D,KAAM,CAAC8H,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/H,QAAQ,CAAC,EAAE,CAAC,CAEpE,KAAM,CAACgI,YAAY,CAAEC,eAAe,CAAC,CAAGjI,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnI,QAAQ,CAAC,EAAE,CAAC,CAE9D,KAAM,CAACoI,aAAa,CAAEC,gBAAgB,CAAC,CAAGrI,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACsI,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGvI,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA;AACA,KAAM,CAACwI,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGzI,QAAQ,CAC1E,EACF,CAAC,CACD,KAAM,CACJ0I,YAAY,CAAEC,0BAA0B,CACxCC,aAAa,CAAEC,2BACjB,CAAC,CAAGlI,WAAW,CAAC,CACdmI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBP,6BAA6B,CAAEQ,SAAS,EAAK,CAC3C,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFpJ,SAAS,CAAC,IAAM,CACd,MAAO,IACLyI,0BAA0B,CAACiB,OAAO,CAAEN,IAAI,EACtCI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG5J,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CACJ0I,YAAY,CAAEmB,yBAAyB,CACvCjB,aAAa,CAAEkB,0BACjB,CAAC,CAAGnJ,WAAW,CAAC,CACdmI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBY,qBAAqB,CAAEX,SAAS,EAAK,CACnC,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFpJ,SAAS,CAAC,IAAM,CACd,MAAO,IACL4J,kBAAkB,CAACF,OAAO,CAAEN,IAAI,EAAKI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAAC,CAAC,CAC3E,CAAC,CAAE,EAAE,CAAC,CACN;AACA,KAAM,CACJS,iCAAiC,CACjCC,oCAAoC,CACrC,CAAGhK,QAAQ,CAAC,EAAE,CAAC,CAChB,KAAM,CACJ0I,YAAY,CAAEuB,wCAAwC,CACtDrB,aAAa,CAAEsB,yCACjB,CAAC,CAAGvJ,WAAW,CAAC,CACdmI,MAAM,CAAE,CACN,GAAG,CAAE,EACP,CAAC,CACDC,MAAM,CAAGC,aAAa,EAAK,CACzBgB,oCAAoC,CAAEf,SAAS,EAAK,CAClD,GAAGA,SAAS,CACZ,GAAGD,aAAa,CAACE,GAAG,CAAEC,IAAI,EACxBC,MAAM,CAACC,MAAM,CAACF,IAAI,CAAE,CAClBG,OAAO,CAAEC,GAAG,CAACC,eAAe,CAACL,IAAI,CACnC,CAAC,CACH,CAAC,CACF,CAAC,CACJ,CACF,CAAC,CAAC,CAEFpJ,SAAS,CAAC,IAAM,CACd,MAAO,IACLgK,iCAAiC,CAACN,OAAO,CAAEN,IAAI,EAC7CI,GAAG,CAACG,eAAe,CAACP,IAAI,CAACG,OAAO,CAClC,CAAC,CACL,CAAC,CAAE,EAAE,CAAC,CAEN;AAEA;AAEA,KAAM,CAACa,UAAU,CAAEC,aAAa,CAAC,CAAGpK,QAAQ,CAAC,CAAC,CAAC,CAE/C,KAAM,CAAAqK,SAAS,CAAGnK,WAAW,CAAEoK,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAS,CAAC,CAAGF,SAAS,CAE9B,KAAM,CAAAG,aAAa,CAAGtK,WAAW,CAAEoK,KAAK,EAAKA,KAAK,CAACG,YAAY,CAAC,CAChE,KAAM,CAAEC,SAAS,CAAEC,gBAAgB,CAAEC,cAAe,CAAC,CAAGJ,aAAa,CAErE,KAAM,CAAAK,UAAU,CAAG3K,WAAW,CAAEoK,KAAK,EAAKA,KAAK,CAACQ,aAAa,CAAC,CAC9D,KAAM,CAAEC,cAAc,CAAEC,cAAc,CAAEC,YAAa,CAAC,CAAGJ,UAAU,CAEnE,KAAM,CAAAK,cAAc,CAAGhL,WAAW,CAAEoK,KAAK,EAAKA,KAAK,CAACa,aAAa,CAAC,CAClE,KAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,eAAgB,CAAC,CAAGJ,cAAc,CAEzE,KAAM,CAAAK,gBAAgB,CAAGrL,WAAW,CAAEoK,KAAK,EAAKA,KAAK,CAACkB,gBAAgB,CAAC,CACvE,KAAM,CAAEC,YAAY,CAAEC,mBAAmB,CAAEC,iBAAkB,CAAC,CAC5DJ,gBAAgB,CAElB,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpB7L,SAAS,CAAC,IAAM,CACd,GAAI,CAACwK,QAAQ,CAAE,CACb1I,QAAQ,CAAC+J,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLxB,aAAa,CAAC,CAAC,CAAC,CAChBrI,QAAQ,CAAClB,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAClCkB,QAAQ,CAACvB,aAAa,CAAC,GAAG,CAAC,CAAC,CAC5BuB,QAAQ,CAACnB,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAC/B;AACF,CACF,CAAC,CAAE,CAACiB,QAAQ,CAAE0I,QAAQ,CAAExI,QAAQ,CAAC,CAAC,CAElChC,SAAS,CAAC,IAAM,CACd,GAAIiL,cAAc,CAAE,CAClBZ,aAAa,CAAC,CAAC,CAAC,CAClB,CACF,CAAC,CAAE,CAACY,cAAc,CAAC,CAAC,CAEpB,mBACEhK,IAAA,CAACX,aAAa,EAAAwL,QAAA,cACZ3K,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf3K,KAAA,QAAK4K,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD7K,IAAA,MAAG+K,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB3K,KAAA,QAAK4K,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7K,IAAA,SACEoL,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNtL,IAAA,SAAM8K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ7K,IAAA,SAAA6K,QAAA,cACE7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB7K,IAAA,SACEoL,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPtL,IAAA,QAAK8K,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,EACpC,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,gCAAgC,CAAAD,QAAA,cAC7C7K,IAAA,OAAI8K,SAAS,CAAC,qDAAqD,CAAAD,QAAA,CAAC,UAEpE,CAAI,CAAC,CACF,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,mIAAmI,CAAAD,QAAA,cAChJ3K,KAAA,QAAK4K,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC3K,KAAA,QAAK4K,SAAS,CAAC,2DAA2D,CAAAD,QAAA,eACxE7K,IAAA,QAAK8K,SAAS,CAAC,wFAAwF,CAAM,CAAC,CAC7G3K,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAE+H,GAAG,CAAC,CAACqD,IAAI,CAAEnL,KAAK,gBAC1BF,KAAA,QACE;AACA4K,SAAS,CAAC,2EAA2E,CAAAD,QAAA,EAEpF1B,UAAU,CAAGoC,IAAI,CAACnL,KAAK,cACtBJ,IAAA,QAAK8K,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH7K,IAAA,QACEwL,GAAG,CAAElM,eAAgB,CACrBwL,SAAS,CAAC,QAAQ,CAClBW,OAAO,CAAGC,CAAC,EAAK,CACdA,CAAC,CAACC,MAAM,CAACC,OAAO,CAAG,IAAI,CACvBF,CAAC,CAACC,MAAM,CAACH,GAAG,CAAG,yBAAyB,CAC1C,CAAE,CACH,CAAC,CACC,CAAC,CACJrC,UAAU,GAAKoC,IAAI,CAACnL,KAAK,cAC3BJ,IAAA,QAAK8K,SAAS,CAAC,kDAAkD,CAAM,CAAC,cAExE9K,IAAA,QAAK8K,SAAS,CAAC,oGAAoG,CAAAD,QAAA,cACjH7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,QAAQ,CAAAD,QAAA,cAElB7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CACN,cAEDpL,KAAA,QAAK4K,SAAS,CAAC,wBAAwB,CAAAD,QAAA,eACrC7K,IAAA,QAAK8K,SAAS,CAAC,qBAAqB,CAAAD,QAAA,CAAEU,IAAI,CAAClL,KAAK,CAAM,CAAC,CACtD8I,UAAU,GAAKoC,IAAI,CAACnL,KAAK,cACxBJ,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAChDU,IAAI,CAACjL,WAAW,CACd,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CACN,CAAC,EACC,CAAC,cACNJ,KAAA,QAAK4K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,EAEtD1B,UAAU,GAAK,CAAC,cACfjJ,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7K,IAAA,QAAK8K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,qBAEtD,CAAK,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,kBAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,aACjC,cAAA7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAClD,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,yBAAAe,MAAA,CACP3K,cAAc,CACV,eAAe,CACf,kBAAkB,qCACY,CACpC4K,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,YAAY,CACxBC,KAAK,CAAEhL,SAAU,CACjBiL,QAAQ,CAAGC,CAAC,EAAKjL,YAAY,CAACiL,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC/C,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3J,cAAc,CAAGA,cAAc,CAAG,EAAE,CAClC,CAAC,EACH,CAAC,EACH,CAAC,cAENhB,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,CAAC,WAE7C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,WAAW,CACvBC,KAAK,CAAE5K,QAAS,CAChB6K,QAAQ,CAAGC,CAAC,EAAK7K,WAAW,CAAC6K,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC9C,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAEN9L,KAAA,QAAK4K,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,OAE9C,CAAK,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,yBAAAe,MAAA,CACPnK,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpCoK,IAAI,CAAC,OAAO,CACZC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAExK,KAAM,CACbyK,QAAQ,CAAGC,CAAC,EAAKzK,QAAQ,CAACyK,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC3C,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnJ,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,cAENxB,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,QACrC,cAAA7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC7C,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,wBAAAe,MAAA,CACP3J,UAAU,CAAG,eAAe,CAAG,kBAAkB,qCACf,CACpC4J,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,UAAU,CACtBC,KAAK,CAAEhK,KAAM,CACbiK,QAAQ,CAAGC,CAAC,EAAKjK,QAAQ,CAACiK,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC3C,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3I,UAAU,CAAGA,UAAU,CAAG,EAAE,CAC1B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNhC,KAAA,QAAK4K,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC3K,KAAA,QAAK4K,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,UACpC,cAAA7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC/C,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,CAACN,MAAM,EACLsM,KAAK,CAAEpJ,OAAQ,CACfqJ,QAAQ,CAAGE,MAAM,EAAK,CACpBtJ,UAAU,CAACsJ,MAAM,CAAC,CACpB,CAAE,CACFC,OAAO,CAAEtM,SAAS,CAACoI,GAAG,CAAEtF,OAAO,GAAM,CACnCoJ,KAAK,CAAEpJ,OAAO,CAACvC,KAAK,CACpBgM,KAAK,cACHnM,KAAA,QACE4K,SAAS,IAAAe,MAAA,CACPjJ,OAAO,CAACvC,KAAK,GAAK,EAAE,CAAG,MAAM,CAAG,EAAE,+BACN,CAAAwK,QAAA,eAE9B7K,IAAA,SAAM8K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAEjI,OAAO,CAAC0J,IAAI,CAAO,CAAC,cAC5CtM,IAAA,SAAA6K,QAAA,CAAOjI,OAAO,CAACvC,KAAK,CAAO,CAAC,EACzB,CAET,CAAC,CAAC,CAAE,CACJyK,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEpD,KAAK,IAAM,CACzB,GAAGoD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE9J,YAAY,CAChB,mBAAmB,CACnB,mBAAmB,CACvB+J,SAAS,CAAEvD,KAAK,CAACwD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cAEF/M,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/H,YAAY,CAAGA,YAAY,CAAG,EAAE,CAC9B,CAAC,EACH,CAAC,EACH,CAAC,cACN5C,KAAA,QAAK4K,SAAS,CAAC,qBAAqB,CAAAD,QAAA,eAClC7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,MAAI,CAAK,CAAC,cACxD3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,yBAAAe,MAAA,CACPnJ,SAAS,CAAG,eAAe,CAAG,kBAAkB,sCACb,CACrCoJ,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,MAAM,CAClBC,KAAK,CAAExJ,IAAK,CACZyJ,QAAQ,CAAGC,CAAC,EAAKzJ,OAAO,CAACyJ,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC1C,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnI,SAAS,CAAGA,SAAS,CAAG,EAAE,CACxB,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENxC,KAAA,QAAK4K,SAAS,CAAC,4BAA4B,CAAAD,QAAA,eACzC3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,KAAG,CAAK,CAAC,cACvD3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,CAACN,MAAM,EACLsM,KAAK,CAAExF,gBAAiB,CACxByF,QAAQ,CAAGE,MAAM,EAAK,CACpB1F,mBAAmB,CAAC0F,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEhC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAElC,GAAG,CAAE+E,SAAS,GAAM,CACvCjB,KAAK,CAAEiB,SAAS,CAACC,EAAE,CACnBb,KAAK,CAAEY,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACjB,MAAM,CAAEkB,UAAU,GAC/BlB,MAAM,CAACE,KAAK,CACTiB,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDxC,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEpD,KAAK,IAAM,CACzB,GAAGoD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElG,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBmG,SAAS,CAAEvD,KAAK,CAACwD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF/M,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnE,qBAAqB,CAAGA,qBAAqB,CAAG,EAAE,CAChD,CAAC,EACH,CAAC,EACH,CAAC,cACNxG,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,QAE9C,CAAK,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,yBAAAe,MAAA,CACP/E,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CACrCgF,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,QAAQ,CACpBC,KAAK,CAAEpF,eAAgB,CACvBqF,QAAQ,CAAGC,CAAC,EAAKrF,kBAAkB,CAACqF,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACrD,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC/D,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EAEH,CAAC,cAEN9G,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,eAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,sBACxB,CAAC,GAAG,cACxB7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,CAACN,MAAM,EACLsM,KAAK,CAAEhJ,WAAY,CACnBiJ,QAAQ,CAAGE,MAAM,EAAK,CACpBlJ,cAAc,CAACkJ,MAAM,CAAC,CACxB,CAAE,CACFrB,SAAS,CAAC,SAAS,CACnBsB,OAAO,CAAE3B,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEvC,GAAG,CAAEsF,IAAI,GAAM,CACpCxB,KAAK,CAAEwB,IAAI,CAACN,EAAE,CACdb,KAAK,CAAEmB,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJL,YAAY,CAAEA,CAACjB,MAAM,CAAEkB,UAAU,GAC/BlB,MAAM,CAACE,KAAK,CACTiB,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvB,WAAW,CAAC,uBAAuB,CACnCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEpD,KAAK,IAAM,CACzB,GAAGoD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE1J,gBAAgB,CACpB,mBAAmB,CACnB,mBAAmB,CACvB2J,SAAS,CAAEvD,KAAK,CAACwD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,cACF/M,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3H,gBAAgB,CAAGA,gBAAgB,CAAG,EAAE,CACtC,CAAC,EACH,CAAC,EACH,CAAC,cAENhD,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,EAAC,oBACzB,CAAC,GAAG,cACtB7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EACvC,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE7K,IAAA,UACE8K,SAAS,yBAAAe,MAAA,CACPvI,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CACpCwI,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,oBAAoB,CAChCC,KAAK,CAAE5I,QAAS,CAChB6I,QAAQ,CAAGC,CAAC,EAAK7I,WAAW,CAAC6I,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC9C,CAAC,cACFhM,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCvH,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENtD,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,OACvC,cAAA7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC5C,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE3K,KAAA,WACE8L,KAAK,CAAExI,QAAS,CAChByI,QAAQ,CAAGC,CAAC,EAAKzI,WAAW,CAACyI,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC7ClB,SAAS,yBAAAe,MAAA,CACPnI,aAAa,CACT,eAAe,CACf,kBAAkB,qCACY,CAAAmH,QAAA,eAEpC7K,IAAA,WAAQgM,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,aAAW,CAAQ,CAAC,cACvC7K,IAAA,WAAQgM,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C7K,IAAA,WAAQgM,KAAK,CAAE,WAAY,CAAAnB,QAAA,CAAC,WAAS,CAAQ,CAAC,EACxC,CAAC,cACT7K,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrCnH,aAAa,CAAGA,aAAa,CAAG,EAAE,CAChC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAGN1D,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,aAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,aACEgM,KAAK,CAAEpI,eAAgB,CACvB8J,IAAI,CAAE,CAAE,CACRzB,QAAQ,CAAGC,CAAC,EAAKrI,kBAAkB,CAACqI,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACpDlB,SAAS,CAAC,wEAAwE,CACzE,CAAC,CACT,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN9K,IAAA,QAAK8K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAC1D7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBzM,iBAAiB,CAAC,EAAE,CAAC,CACrBI,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,iBAAiB,CAAC,EAAE,CAAC,CACrBI,aAAa,CAAC,EAAE,CAAC,CACjBR,aAAa,CAAC,EAAE,CAAC,CACjBY,eAAe,CAAC,EAAE,CAAC,CACnBoB,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,gBAAgB,CAAC,EAAE,CAAC,CACpBJ,mBAAmB,CAAC,EAAE,CAAC,CACvBR,YAAY,CAAC,EAAE,CAAC,CAChBI,eAAe,CAAC,EAAE,CAAC,CAEnB,GAAI/B,SAAS,GAAK,EAAE,CAAE,CACpBG,iBAAiB,CAAC,yBAAyB,CAAC,CAC5CyM,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5L,KAAK,GAAK,EAAE,CAAE,CAChBG,aAAa,CAAC,yBAAyB,CAAC,CACxCyL,KAAK,CAAG,KAAK,CACf,CAEA,GAAIhL,OAAO,GAAK,EAAE,EAAIA,OAAO,CAACoJ,KAAK,GAAK,EAAE,CAAE,CAC1CjJ,eAAe,CAAC,yBAAyB,CAAC,CAC1C6K,KAAK,CAAG,KAAK,CACf,CAEA,GAAI5K,WAAW,GAAK,EAAE,EAAIA,WAAW,CAACgJ,KAAK,GAAK,EAAE,CAAE,CAClD7I,mBAAmB,CAAC,yBAAyB,CAAC,CAC9CyK,KAAK,CAAG,KAAK,CACf,CAEA,GAAIxK,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CqK,KAAK,CAAG,KAAK,CACf,CAEA,GAAIpK,QAAQ,GAAK,EAAE,CAAE,CACnBG,gBAAgB,CAAC,yBAAyB,CAAC,CAC3CiK,KAAK,CAAG,KAAK,CACf,CACA,GAAIA,KAAK,CAAE,CACTxE,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL7J,KAAK,CAACsO,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF/C,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfjJ,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7K,IAAA,QAAK8K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,sBAEtD,CAAK,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,8BAE1D,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD7K,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC3K,KAAA,QAAK4K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,EAAC,SACrC,cAAA7K,IAAA,WAAQ8K,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,GAAC,CAAQ,CAAC,EAC9C,CAAC,cACN3K,KAAA,QAAA2K,QAAA,eACE3K,KAAA,WACE8L,KAAK,CAAEhI,eAAgB,CACvBiI,QAAQ,CAAGC,CAAC,EAAKjI,kBAAkB,CAACiI,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACpDlB,SAAS,wBAAAe,MAAA,CACP3H,oBAAoB,CAChB,eAAe,CACf,kBAAkB,sCACa,CAAA2G,QAAA,eAErC7K,IAAA,WAAQgM,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC7K,IAAA,WAAQgM,KAAK,CAAE,sBAAuB,CAAAnB,QAAA,CAAC,sBAEvC,CAAQ,CAAC,cACT7K,IAAA,WAAQgM,KAAK,CAAE,yBAA0B,CAAAnB,QAAA,CAAC,2BAE1C,CAAQ,CAAC,cACT7K,IAAA,WAAQgM,KAAK,CAAE,6BAA8B,CAAAnB,QAAA,CAAC,8BAE9C,CAAQ,CAAC,cACT7K,IAAA,WACEgM,KAAK,CAAE,qCAAsC,CAAAnB,QAAA,CAC9C,qCAED,CAAQ,CAAC,cACT7K,IAAA,WAAQgM,KAAK,CAAE,kCAAmC,CAAAnB,QAAA,CAAC,mCAEnD,CAAQ,CAAC,cACT7K,IAAA,WAAQgM,KAAK,CAAE,mBAAoB,CAAAnB,QAAA,CAAC,mBAEpC,CAAQ,CAAC,EACH,CAAC,cACT7K,IAAA,QAAK8K,SAAS,CAAC,yBAAyB,CAAAD,QAAA,CACrC3G,oBAAoB,CAAGA,oBAAoB,CAAG,EAAE,CAC9C,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAENlE,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,kBAAkB,CAC9BC,KAAK,CAAE5H,eAAgB,CACvB6H,QAAQ,CAAGC,CAAC,EAAK7H,kBAAkB,CAAC6H,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,cAEN9L,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,kBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAExH,eAAgB,CACvByH,QAAQ,CAAGC,CAAC,EAAKzH,kBAAkB,CAACyH,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACrD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENhM,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD7K,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,eACpC7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,CAACN,MAAM,EACLsM,KAAK,CAAEpH,YAAa,CACpBqH,QAAQ,CAAGE,MAAM,EAAK,CACpBtH,eAAe,CAACsH,MAAM,CAAC,CACzB,CAAE,CACFrB,SAAS,CAAC,SAAS,CACnBsB,OAAO,CAAE1C,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAExB,GAAG,CAAEsF,IAAI,GAAM,CACjCxB,KAAK,CAAEwB,IAAI,CAACN,EAAE,CACdb,KAAK,CAAEmB,IAAI,CAACC,SAAS,EAAI,EAC3B,CAAC,CAAC,CAAE,CACJL,YAAY,CAAEA,CAACjB,MAAM,CAAEkB,UAAU,GAC/BlB,MAAM,CAACE,KAAK,CACTiB,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDvB,WAAW,CAAC,oBAAoB,CAChCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEpD,KAAK,IAAM,CACzB,GAAGoD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE9H,iBAAiB,CACrB,mBAAmB,CACnB,mBAAmB,CACvB+H,SAAS,CAAEvD,KAAK,CAACwD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN7M,KAAA,QAAK4K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAAAC,KAAK,CAAG,IAAI,CAChBzJ,uBAAuB,CAAC,EAAE,CAAC,CAE3B,GAAIH,eAAe,GAAK,EAAE,CAAE,CAC1BG,uBAAuB,CAAC,yBAAyB,CAAC,CAClDyJ,KAAK,CAAG,KAAK,CACf,CAEA,GAAIA,KAAK,CAAE,CACTxE,aAAa,CAAC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL7J,KAAK,CAACsO,KAAK,CACT,oDACF,CAAC,CACH,CACF,CAAE,CACF/C,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfjJ,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7K,IAAA,QAAK8K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,iBAEtD,CAAK,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,0BAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,WACMyH,0BAA0B,CAAC,CAAEmD,SAAS,CAAE,UAAW,CAAC,CAAC,CACzD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG7K,IAAA,aAAW6H,2BAA2B,CAAC,CAAC,CAAG,CAAC,cAC5C7H,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNtL,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN7K,IAAA,UAAO8N,KAAK,CAAEvN,eAAgB,CAAAsK,QAAA,cAC5B7K,IAAA,QAAK8K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnCrD,0BAA0B,SAA1BA,0BAA0B,iBAA1BA,0BAA0B,CAAEU,GAAG,CAAC,CAACC,IAAI,CAAE/H,KAAK,gBAC3CF,KAAA,QACE4K,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF7K,IAAA,QAAK8K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E3K,KAAA,QACE8K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB8C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,eAEd7K,IAAA,SAAMsL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOtL,IAAA,SAAMsL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNpL,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7K,IAAA,QAAK8K,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAAC6F,IAAI,CACP,CAAC,cACN9N,KAAA,QAAA2K,QAAA,EACG,CAAC1C,IAAI,CAAC8F,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNlO,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAM,CACblG,6BAA6B,CAAEQ,SAAS,EACtCA,SAAS,CAACkG,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfjO,KAAK,GAAKiO,aACd,CACF,CAAC,CACH,CAAE,CACFvD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB4C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,cAEd7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJnD,IAAI,CAAC6F,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAEN9N,KAAA,QAAK4K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfjJ,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7K,IAAA,QAAK8K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,UAEtD,CAAK,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,sBAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,2BAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,2BAA2B,CACvCC,KAAK,CAAEpG,aAAc,CACrBqG,QAAQ,CAAGC,CAAC,EAAKrG,gBAAgB,CAACqG,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CACnD,CAAC,CACC,CAAC,EACH,CAAC,cAEN9L,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,wBAAwB,CACpCC,KAAK,CAAEhG,UAAW,CAClBiG,QAAQ,CAAGC,CAAC,EAAKjG,aAAa,CAACiG,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAChD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAENhM,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,mBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,QAAQ,CACbC,WAAW,CAAC,mBAAmB,CAC/BC,KAAK,CAAE5F,MAAO,CACd6F,QAAQ,CAAGC,CAAC,EAAK7F,SAAS,CAAC6F,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAC5C,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cACNhM,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gBAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,WACM2I,yBAAyB,CAAC,CAAEiC,SAAS,CAAE,UAAW,CAAC,CAAC,CACxD;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG7K,IAAA,aAAW8I,0BAA0B,CAAC,CAAC,CAAG,CAAC,cAC3C9I,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNtL,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN7K,IAAA,UAAO8N,KAAK,CAAEvN,eAAgB,CAAAsK,QAAA,cAC5B7K,IAAA,QAAK8K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnClC,kBAAkB,SAAlBA,kBAAkB,iBAAlBA,kBAAkB,CAAET,GAAG,CAAC,CAACC,IAAI,CAAE/H,KAAK,gBACnCF,KAAA,QACE4K,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF7K,IAAA,QAAK8K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E3K,KAAA,QACE8K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB8C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,eAEd7K,IAAA,SAAMsL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOtL,IAAA,SAAMsL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNpL,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7K,IAAA,QAAK8K,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAAC6F,IAAI,CACP,CAAC,cACN9N,KAAA,QAAA2K,QAAA,EACG,CAAC1C,IAAI,CAAC8F,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNlO,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAM,CACb/E,qBAAqB,CAAEX,SAAS,EAC9BA,SAAS,CAACkG,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfjO,KAAK,GAAKiO,aACd,CACF,CAAC,CACH,CAAE,CACFvD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB4C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,cAEd7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA9CJnD,IAAI,CAAC6F,IA+CP,CACN,CAAC,CACC,CAAC,CACD,CAAC,EACL,CAAC,cAGN9N,KAAA,QAAK4K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,iBAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEP1B,UAAU,GAAK,CAAC,cACfjJ,KAAA,QAAK4K,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7K,IAAA,QAAK8K,SAAS,CAAC,sCAAsC,CAAAD,QAAA,CAAC,yBAEtD,CAAK,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,oBAE1D,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3K,KAAA,QAAK4K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,eAC1C3K,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,wBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,CAACN,MAAM,EACLsM,KAAK,CAAExF,gBAAiB,CACxByF,QAAQ,CAAGE,MAAM,EAAK,CACpB1F,mBAAmB,CAAC0F,MAAM,CAAC,CAC7B,CAAE,CACFC,OAAO,CAAEhC,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAElC,GAAG,CAAE+E,SAAS,GAAM,CACvCjB,KAAK,CAAEiB,SAAS,CAACC,EAAE,CACnBb,KAAK,CAAEY,SAAS,CAACE,cAAc,EAAI,EACrC,CAAC,CAAC,CAAE,CACJC,YAAY,CAAEA,CAACjB,MAAM,CAAEkB,UAAU,GAC/BlB,MAAM,CAACE,KAAK,CACTiB,WAAW,CAAC,CAAC,CACbC,QAAQ,CAACF,UAAU,CAACC,WAAW,CAAC,CAAC,CACrC,CACDxC,SAAS,CAAC,SAAS,CACnBiB,WAAW,CAAC,qBAAqB,CACjCQ,YAAY,MACZC,MAAM,CAAE,CACNC,OAAO,CAAEA,CAACC,IAAI,CAAEpD,KAAK,IAAM,CACzB,GAAGoD,IAAI,CACPC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAElG,qBAAqB,CACzB,mBAAmB,CACnB,mBAAmB,CACvBmG,SAAS,CAAEvD,KAAK,CAACwD,SAAS,CAAG,MAAM,CAAG,MAAM,CAC5C,SAAS,CAAE,CACTF,MAAM,CAAE,mBACV,CACF,CAAC,CAAC,CACFT,MAAM,CAAGO,IAAI,GAAM,CACjB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CAAC,CACFC,WAAW,CAAGN,IAAI,GAAM,CACtB,GAAGA,IAAI,CACPlM,OAAO,CAAE,MAAM,CACfuM,UAAU,CAAE,QACd,CAAC,CACH,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,cAEN7M,KAAA,QAAK4K,SAAS,CAAC,+BAA+B,CAAAD,QAAA,eAC5C7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,eAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE7K,IAAA,UACE8K,SAAS,CAAC,wEAAwE,CAClFgB,IAAI,CAAC,MAAM,CACXC,WAAW,CAAC,eAAe,CAC3BC,KAAK,CAAEhF,YAAa,CACpBiF,QAAQ,CAAGC,CAAC,EAAKjF,eAAe,CAACiF,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAENhM,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,uBAE1D,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD7K,IAAA,QAAK8K,SAAS,CAAC,6BAA6B,CAAAD,QAAA,cAC1C3K,KAAA,QAAK4K,SAAS,CAAC,sBAAsB,CAAAD,QAAA,eACnC7K,IAAA,QAAK8K,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,gBAE9C,CAAK,CAAC,cACN7K,IAAA,QAAA6K,QAAA,cACE3K,KAAA,WACE8L,KAAK,CAAE5E,aAAc,CACrB6E,QAAQ,CAAGC,CAAC,EAAK7E,gBAAgB,CAAC6E,CAAC,CAACP,MAAM,CAACK,KAAK,CAAE,CAClDlB,SAAS,CAAC,wEAAwE,CAAAD,QAAA,eAElF7K,IAAA,WAAQgM,KAAK,CAAE,EAAG,CAAAnB,QAAA,CAAC,eAAa,CAAQ,CAAC,cACzC7K,IAAA,WAAQgM,KAAK,CAAE,SAAU,CAAAnB,QAAA,CAAC,SAAO,CAAQ,CAAC,cAC1C7K,IAAA,WAAQgM,KAAK,CAAE,UAAW,CAAAnB,QAAA,CAAC,UAAQ,CAAQ,CAAC,cAC5C7K,IAAA,WAAQgM,KAAK,CAAE,QAAS,CAAAnB,QAAA,CAAC,QAAM,CAAQ,CAAC,EAClC,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,cAEN7K,IAAA,QAAK8K,SAAS,CAAC,0CAA0C,CAAAD,QAAA,CAAC,gCAE1D,CAAK,CAAC,cACN3K,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD3K,KAAA,WACM+I,wCAAwC,CAAC,CAC3C6B,SAAS,CAAE,UACb,CAAC,CAAC,CACF;AACAA,SAAS,CAAC,wFAAwF,CAAAD,QAAA,eAElG7K,IAAA,aAAWkJ,yCAAyC,CAAC,CAAC,CAAG,CAAC,cAC1DlJ,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,cACnB7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,iDAAiD,CAAAD,QAAA,cAE3D7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,4GAA4G,CAC/G,CAAC,CACC,CAAC,CACH,CAAC,cACNtL,IAAA,QAAK8K,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,kCAEtB,CAAK,CAAC,EACH,CAAC,cACN7K,IAAA,UAAO8N,KAAK,CAAEvN,eAAgB,CAAAsK,QAAA,cAC5B7K,IAAA,QAAK8K,SAAS,CAAC,uBAAuB,CAAAD,QAAA,CACnC9B,iCAAiC,SAAjCA,iCAAiC,iBAAjCA,iCAAiC,CAAEb,GAAG,CACrC,CAACC,IAAI,CAAE/H,KAAK,gBACVF,KAAA,QACE4K,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eAGpF7K,IAAA,QAAK8K,SAAS,CAAC,kEAAkE,CAAAD,QAAA,cAC/E3K,KAAA,QACE8K,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnB8C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,eAEd7K,IAAA,SAAMsL,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOtL,IAAA,SAAMsL,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNpL,KAAA,QAAK4K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,eACjD7K,IAAA,QAAK8K,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5F1C,IAAI,CAAC6F,IAAI,CACP,CAAC,cACN9N,KAAA,QAAA2K,QAAA,EACG,CAAC1C,IAAI,CAAC8F,IAAI,EAAI,IAAI,CAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAC1C,EAAK,CAAC,EACH,CAAC,cACNlO,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAM,CACb3E,oCAAoC,CACjCf,SAAS,EACRA,SAAS,CAACkG,MAAM,CACd,CAACC,CAAC,CAAEC,aAAa,GACfjO,KAAK,GAAKiO,aACd,CACJ,CAAC,CACH,CAAE,CACFvD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,cAElE7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrB4C,KAAK,CAAC,QAAQ,CAAAlD,QAAA,cAEd7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,sBAAsB,CACzB,CAAC,CACC,CAAC,CACA,CAAC,GA/CJnD,IAAI,CAAC6F,IAgDP,CAET,CAAC,CACE,CAAC,CACD,CAAC,EACL,CAAC,cAEN9N,KAAA,QAAK4K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,eAC1D7K,IAAA,WACE2N,OAAO,CAAEA,CAAA,GAAMvE,aAAa,CAAC,CAAC,CAAE,CAChC0B,SAAS,CAAC,6DAA6D,CAAAD,QAAA,CACxE,MAED,CAAQ,CAAC,cACT7K,IAAA,WACEsO,QAAQ,CAAEvE,cAAe,CACzB4D,OAAO,CAAE,KAAAA,CAAA,GAAY,CACnB,KAAM,CAAA5M,QAAQ,CACZtB,UAAU,CAAC,CACT8O,UAAU,CAAEvN,SAAS,CACrBwN,SAAS,CAAEpN,QAAQ,CACnBqM,SAAS,CAAEzM,SAAS,CAAG,GAAG,CAAGI,QAAQ,CACrCqN,SAAS,CAAE7M,SAAS,CACpB8M,aAAa,CAAE1M,KAAK,CACpB2M,aAAa,CAAEnN,KAAK,CACpBoN,eAAe,CAAExM,OAAO,CACxByM,YAAY,CAAErM,IAAI,CAClBsM,eAAe,CAAElM,OAAO,CAACoJ,KAAK,CAC9B;AACAhJ,WAAW,CAAEA,WAAW,CAACgJ,KAAK,CAC9B+C,SAAS,CAAE3L,QAAQ,CACnB4L,SAAS,CAAExL,QAAQ,CACnByL,gBAAgB,CAAErL,eAAe,CACjC;AACAsL,mBAAmB,CAAElL,eAAe,CACpCmL,gBAAgB,CAAE/K,eAAe,CACjCgL,gBAAgB,CAAE5K,eAAe,CACjC6K,QAAQ,CAAEzK,YAAY,CAACoH,KAAK,CAC5B;AACAsD,cAAc,CAAE1J,aAAa,CAC7B2J,WAAW,CAAEvJ,UAAU,CACvBwJ,cAAc,CAAEpJ,MAAM,CACtB6G,SAAS,CAAEzG,gBAAgB,CAACwF,KAAK,CACjCyD,gBAAgB,CAAE7I,eAAe,CACjC8I,aAAa,CAAE1I,YAAY,CAC3B2I,gBAAgB,CAAEvI,aAAa,CAC/B;AACAwI,uBAAuB,CAAEpI,0BAA0B,CACnDqI,cAAc,CAAElH,kBAAkB,CAClCmH,8BAA8B,CAC5B/G,iCACJ,CAAC,CACH,CAAC,CACH,CAAE,CACF+B,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CAEjEd,cAAc,CAAG,WAAW,CAAG,QAAQ,CAClC,CAAC,EACN,CAAC,EACH,CAAC,CACJ,IAAI,CAEPZ,UAAU,GAAK,CAAC,cACfnJ,IAAA,QAAK8K,SAAS,CAAC,EAAE,CAAAD,QAAA,cACf7K,IAAA,QAAK8K,SAAS,CAAC,oCAAoC,CAAAD,QAAA,cACjD3K,KAAA,QAAK4K,SAAS,CAAC,oDAAoD,CAAAD,QAAA,eACjE7K,IAAA,QACEgL,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,oEAAoE,CAAAD,QAAA,cAE9E7K,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBsL,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,cACNtL,IAAA,QAAK8K,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,4BAExD,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,oDAAoD,CAAAD,QAAA,CAAC,8GAGpE,CAAK,CAAC,cACN7K,IAAA,QAAK8K,SAAS,CAAC,6CAA6C,CAAAD,QAAA,cAS1D7K,IAAA,MACE+K,IAAI,CAAC,YAAY,CACjBD,SAAS,CAAC,wDAAwD,CAAAD,QAAA,CACnE,gBAED,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAjK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}