{"ast": null, "code": "import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nconst composeSignals = (signals, timeout) => {\n  let controller = new AbortController();\n  let aborted;\n  const onabort = function (cancel) {\n    if (!aborted) {\n      aborted = true;\n      unsubscribe();\n      const err = cancel instanceof Error ? cancel : this.reason;\n      controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n    }\n  };\n  let timer = timeout && setTimeout(() => {\n    onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));\n  }, timeout);\n  const unsubscribe = () => {\n    if (signals) {\n      timer && clearTimeout(timer);\n      timer = null;\n      signals.forEach(signal => {\n        signal && (signal.removeEventListener ? signal.removeEventListener('abort', onabort) : signal.unsubscribe(onabort));\n      });\n      signals = null;\n    }\n  };\n  signals.forEach(signal => signal && signal.addEventListener && signal.addEventListener('abort', onabort));\n  const {\n    signal\n  } = controller;\n  signal.unsubscribe = unsubscribe;\n  return [signal, () => {\n    timer && clearTimeout(timer);\n    timer = null;\n  }];\n};\nexport default composeSignals;", "map": {"version": 3, "names": ["CanceledError", "AxiosError", "composeSignals", "signals", "timeout", "controller", "AbortController", "aborted", "<PERSON>ab<PERSON>", "cancel", "unsubscribe", "err", "Error", "reason", "abort", "message", "timer", "setTimeout", "ETIMEDOUT", "clearTimeout", "for<PERSON>ach", "signal", "removeEventListener", "addEventListener"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/axios/lib/helpers/composeSignals.js"], "sourcesContent": ["import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\n\nconst composeSignals = (signals, timeout) => {\n  let controller = new AbortController();\n\n  let aborted;\n\n  const onabort = function (cancel) {\n    if (!aborted) {\n      aborted = true;\n      unsubscribe();\n      const err = cancel instanceof Error ? cancel : this.reason;\n      controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n    }\n  }\n\n  let timer = timeout && setTimeout(() => {\n    onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT))\n  }, timeout)\n\n  const unsubscribe = () => {\n    if (signals) {\n      timer && clearTimeout(timer);\n      timer = null;\n      signals.forEach(signal => {\n        signal &&\n        (signal.removeEventListener ? signal.removeEventListener('abort', onabort) : signal.unsubscribe(onabort));\n      });\n      signals = null;\n    }\n  }\n\n  signals.forEach((signal) => signal && signal.addEventListener && signal.addEventListener('abort', onabort));\n\n  const {signal} = controller;\n\n  signal.unsubscribe = unsubscribe;\n\n  return [signal, () => {\n    timer && clearTimeout(timer);\n    timer = null;\n  }];\n}\n\nexport default composeSignals;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAE9C,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EAC3C,IAAIC,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EAEtC,IAAIC,OAAO;EAEX,MAAMC,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAE;IAChC,IAAI,CAACF,OAAO,EAAE;MACZA,OAAO,GAAG,IAAI;MACdG,WAAW,CAAC,CAAC;MACb,MAAMC,GAAG,GAAGF,MAAM,YAAYG,KAAK,GAAGH,MAAM,GAAG,IAAI,CAACI,MAAM;MAC1DR,UAAU,CAACS,KAAK,CAACH,GAAG,YAAYV,UAAU,GAAGU,GAAG,GAAG,IAAIX,aAAa,CAACW,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACI,OAAO,GAAGJ,GAAG,CAAC,CAAC;IACjH;EACF,CAAC;EAED,IAAIK,KAAK,GAAGZ,OAAO,IAAIa,UAAU,CAAC,MAAM;IACtCT,OAAO,CAAC,IAAIP,UAAU,CAAE,WAAUG,OAAQ,iBAAgB,EAAEH,UAAU,CAACiB,SAAS,CAAC,CAAC;EACpF,CAAC,EAAEd,OAAO,CAAC;EAEX,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIP,OAAO,EAAE;MACXa,KAAK,IAAIG,YAAY,CAACH,KAAK,CAAC;MAC5BA,KAAK,GAAG,IAAI;MACZb,OAAO,CAACiB,OAAO,CAACC,MAAM,IAAI;QACxBA,MAAM,KACLA,MAAM,CAACC,mBAAmB,GAAGD,MAAM,CAACC,mBAAmB,CAAC,OAAO,EAAEd,OAAO,CAAC,GAAGa,MAAM,CAACX,WAAW,CAACF,OAAO,CAAC,CAAC;MAC3G,CAAC,CAAC;MACFL,OAAO,GAAG,IAAI;IAChB;EACF,CAAC;EAEDA,OAAO,CAACiB,OAAO,CAAEC,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAACE,gBAAgB,IAAIF,MAAM,CAACE,gBAAgB,CAAC,OAAO,EAAEf,OAAO,CAAC,CAAC;EAE3G,MAAM;IAACa;EAAM,CAAC,GAAGhB,UAAU;EAE3BgB,MAAM,CAACX,WAAW,GAAGA,WAAW;EAEhC,OAAO,CAACW,MAAM,EAAE,MAAM;IACpBL,KAAK,IAAIG,YAAY,CAACH,KAAK,CAAC;IAC5BA,KAAK,GAAG,IAAI;EACd,CAAC,CAAC;AACJ,CAAC;AAED,eAAed,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}