{"ast": null, "code": "export const CLIENT_LIST_REQUEST = \"CLIENT_LIST_REQUEST\";\nexport const CLIENT_LIST_SUCCESS = \"CLIENT_LIST_SUCCESS\";\nexport const CLIENT_LIST_FAIL = \"CLIENT_LIST_FAIL\";\nexport const CLIENT_ADD_REQUEST = \"CLIENT_ADD_REQUEST\";\nexport const CLIENT_ADD_SUCCESS = \"CLIENT_ADD_SUCCESS\";\nexport const CLIENT_ADD_FAIL = \"CLIENT_ADD_FAIL\";\nexport const CLIENT_DETAIL_REQUEST = \"CLIENT_DETAIL_REQUEST\";\nexport const CLIENT_DETAIL_SUCCESS = \"CLIENT_DETAIL_SUCCESS\";\nexport const CLIENT_DETAIL_FAIL = \"CLIENT_DETAIL_FAIL\";\nexport const CLIENT_UPDATE_REQUEST = \"CLIENT_UPDATE_REQUEST\";\nexport const CLIENT_UPDATE_SUCCESS = \"CLIENT_UPDATE_SUCCESS\";\nexport const CLIENT_UPDATE_FAIL = \"CLIENT_UPDATE_FAIL\";\nexport const CLIENT_DELETE_REQUEST = \"CLIENT_DELETE_REQUEST\";\nexport const CLIENT_DELETE_SUCCESS = \"CLIENT_DELETE_SUCCESS\";\nexport const CLIENT_DELETE_FAIL = \"CLIENT_DELETE_FAIL\";", "map": {"version": 3, "names": ["CLIENT_LIST_REQUEST", "CLIENT_LIST_SUCCESS", "CLIENT_LIST_FAIL", "CLIENT_ADD_REQUEST", "CLIENT_ADD_SUCCESS", "CLIENT_ADD_FAIL", "CLIENT_DETAIL_REQUEST", "CLIENT_DETAIL_SUCCESS", "CLIENT_DETAIL_FAIL", "CLIENT_UPDATE_REQUEST", "CLIENT_UPDATE_SUCCESS", "CLIENT_UPDATE_FAIL", "CLIENT_DELETE_REQUEST", "CLIENT_DELETE_SUCCESS", "CLIENT_DELETE_FAIL"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/constants/clientConstants.js"], "sourcesContent": ["export const CLIENT_LIST_REQUEST = \"CLIENT_LIST_REQUEST\";\nexport const CLIENT_LIST_SUCCESS = \"CLIENT_LIST_SUCCESS\";\nexport const CLIENT_LIST_FAIL = \"CLIENT_LIST_FAIL\";\n\nexport const CLIENT_ADD_REQUEST = \"CLIENT_ADD_REQUEST\";\nexport const CLIENT_ADD_SUCCESS = \"CLIENT_ADD_SUCCESS\";\nexport const CLIENT_ADD_FAIL = \"CLIENT_ADD_FAIL\";\n\nexport const CLIENT_DETAIL_REQUEST = \"CLIENT_DETAIL_REQUEST\";\nexport const CLIENT_DETAIL_SUCCESS = \"CLIENT_DETAIL_SUCCESS\";\nexport const CLIENT_DETAIL_FAIL = \"CLIENT_DETAIL_FAIL\";\n\nexport const CLIENT_UPDATE_REQUEST = \"CLIENT_UPDATE_REQUEST\";\nexport const CLIENT_UPDATE_SUCCESS = \"CLIENT_UPDATE_SUCCESS\";\nexport const CLIENT_UPDATE_FAIL = \"CLIENT_UPDATE_FAIL\";\n\nexport const CLIENT_DELETE_REQUEST = \"CLIENT_DELETE_REQUEST\";\nexport const CLIENT_DELETE_SUCCESS = \"CLIENT_DELETE_SUCCESS\";\nexport const CLIENT_DELETE_FAIL = \"CLIENT_DELETE_FAIL\";\n"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG,qBAAqB;AACxD,OAAO,MAAMC,mBAAmB,GAAG,qBAAqB;AACxD,OAAO,MAAMC,gBAAgB,GAAG,kBAAkB;AAElD,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AACtD,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AACtD,OAAO,MAAMC,eAAe,GAAG,iBAAiB;AAEhD,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AAEtD,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB;AAEtD,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,qBAAqB,GAAG,uBAAuB;AAC5D,OAAO,MAAMC,kBAAkB,GAAG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}