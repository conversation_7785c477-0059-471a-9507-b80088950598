{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/SendResetPasswordScreen.js\";\nimport React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.jpeg\";\nimport iconMessageSend from \"../../images/icon/icon-message-send.png\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SendResetPasswordScreen() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \",\n    children: [/*#__PURE__*/_jsxDEV(ToastContainer, {\n      position: \"top-right\",\n      autoClose: 3000,\n      hideProgressBar: false,\n      newestOnTop: false,\n      closeOnClick: true,\n      rtl: false,\n      pauseOnFocusLoss: true,\n      draggable: true,\n      pauseOnHover: true,\n      theme: \"light\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n      href: \"/\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: logoProjet,\n        className: \"size-24 m-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-black text-center  text-2xl font-semibold\",\n        children: \"The request has been sent\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: iconMessageSend,\n        className: \"size-24 my-5 mx-auto \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-[#929396] text-center my-2 text-sm\",\n        children: \"Password reset link has been sent to your email\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center my-2 text-sm\",\n        children: \"Please follow the instructions in the email to rest your password\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-[#878787] text-center text-sm my-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Copyright \\xA9 2024 Atlas Assistance | \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-semibold\",\n        children: \" Privacy Policy\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n}\n_c = SendResetPasswordScreen;\nexport default SendResetPasswordScreen;\nvar _c;\n$RefreshReg$(_c, \"SendResetPasswordScreen\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "logoProjet", "iconMessageSend", "ToastContainer", "jsxDEV", "_jsxDEV", "SendResetPasswordScreen", "className", "children", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover", "theme", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "src", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/auth/SendResetPasswordScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport logoProjet from \"../../images/logo-project.jpeg\";\nimport iconMessageSend from \"../../images/icon/icon-message-send.png\";\nimport { ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nfunction SendResetPasswordScreen() {\n  return (\n    <div className=\"min-h-screen w-full bg-[#0388A6] bg-opacity-10 flex flex-col items-center justify-center px-3 \">\n      <ToastContainer\n        position=\"top-right\"\n        autoClose={3000}\n        hideProgressBar={false}\n        newestOnTop={false}\n        closeOnClick\n        rtl={false}\n        pauseOnFocusLoss\n        draggable\n        pauseOnHover\n        theme=\"light\"\n      />\n      <a href=\"/\">\n        <img src={logoProjet} className=\"size-24 m-1\" />\n      </a>\n      <div className=\"my-5  bg-white shadow-4 rounded-md px-3 py-8 md:w-1/2 w-full flex flex-col text-center\">\n        <div className=\"text-black text-center  text-2xl font-semibold\">\n          The request has been sent\n        </div>\n        <img src={iconMessageSend} className=\"size-24 my-5 mx-auto \" />\n        <div className=\"text-[#929396] text-center my-2 text-sm\">\n          Password reset link has been sent to your email\n        </div>\n        <div className=\"text-center my-2 text-sm\">\n          Please follow the instructions in the email to rest your password\n        </div>\n      </div>\n      <div className=\"text-[#878787] text-center text-sm my-3\">\n        <span>Copyright © 2024 Atlas Assistance | </span>\n        <span className=\"font-semibold\"> Privacy Policy</span>\n      </div>\n    </div>\n  );\n}\n\nexport default SendResetPasswordScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,uBAAuBA,CAAA,EAAG;EACjC,oBACED,OAAA;IAAKE,SAAS,EAAC,gGAAgG;IAAAC,QAAA,gBAC7GH,OAAA,CAACF,cAAc;MACbM,QAAQ,EAAC,WAAW;MACpBC,SAAS,EAAE,IAAK;MAChBC,eAAe,EAAE,KAAM;MACvBC,WAAW,EAAE,KAAM;MACnBC,YAAY;MACZC,GAAG,EAAE,KAAM;MACXC,gBAAgB;MAChBC,SAAS;MACTC,YAAY;MACZC,KAAK,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACFjB,OAAA;MAAGkB,IAAI,EAAC,GAAG;MAAAf,QAAA,eACTH,OAAA;QAAKmB,GAAG,EAAEvB,UAAW;QAACM,SAAS,EAAC;MAAa;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eACJjB,OAAA;MAAKE,SAAS,EAAC,wFAAwF;MAAAC,QAAA,gBACrGH,OAAA;QAAKE,SAAS,EAAC,gDAAgD;QAAAC,QAAA,EAAC;MAEhE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjB,OAAA;QAAKmB,GAAG,EAAEtB,eAAgB;QAACK,SAAS,EAAC;MAAuB;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/DjB,OAAA;QAAKE,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAEzD;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNjB,OAAA;QAAKE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,EAAC;MAE1C;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNjB,OAAA;MAAKE,SAAS,EAAC,yCAAyC;MAAAC,QAAA,gBACtDH,OAAA;QAAAG,QAAA,EAAM;MAAoC;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjDjB,OAAA;QAAME,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAe;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACG,EAAA,GApCQnB,uBAAuB;AAsChC,eAAeA,uBAAuB;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}