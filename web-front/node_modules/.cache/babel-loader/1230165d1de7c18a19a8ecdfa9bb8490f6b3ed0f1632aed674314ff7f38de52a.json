{"ast": null, "code": "'use strict';\n\n/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  const threshold = 1000 / freq;\n  let timer = null;\n  return function throttled() {\n    const force = this === true;\n    const now = Date.now();\n    if (force || now - timestamp > threshold) {\n      if (timer) {\n        clearTimeout(timer);\n        timer = null;\n      }\n      timestamp = now;\n      return fn.apply(null, arguments);\n    }\n    if (!timer) {\n      timer = setTimeout(() => {\n        timer = null;\n        timestamp = Date.now();\n        return fn.apply(null, arguments);\n      }, threshold - (now - timestamp));\n    }\n  };\n}\nexport default throttle;", "map": {"version": 3, "names": ["throttle", "fn", "freq", "timestamp", "threshold", "timer", "throttled", "force", "now", "Date", "clearTimeout", "apply", "arguments", "setTimeout"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/node_modules/axios/lib/helpers/throttle.js"], "sourcesContent": ["'use strict';\n\n/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */\nfunction throttle(fn, freq) {\n  let timestamp = 0;\n  const threshold = 1000 / freq;\n  let timer = null;\n  return function throttled() {\n    const force = this === true;\n\n    const now = Date.now();\n    if (force || now - timestamp > threshold) {\n      if (timer) {\n        clearTimeout(timer);\n        timer = null;\n      }\n      timestamp = now;\n      return fn.apply(null, arguments);\n    }\n    if (!timer) {\n      timer = setTimeout(() => {\n        timer = null;\n        timestamp = Date.now();\n        return fn.apply(null, arguments);\n      }, threshold - (now - timestamp));\n    }\n  };\n}\n\nexport default throttle;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,EAAE,EAAEC,IAAI,EAAE;EAC1B,IAAIC,SAAS,GAAG,CAAC;EACjB,MAAMC,SAAS,GAAG,IAAI,GAAGF,IAAI;EAC7B,IAAIG,KAAK,GAAG,IAAI;EAChB,OAAO,SAASC,SAASA,CAAA,EAAG;IAC1B,MAAMC,KAAK,GAAG,IAAI,KAAK,IAAI;IAE3B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,IAAID,KAAK,IAAIC,GAAG,GAAGL,SAAS,GAAGC,SAAS,EAAE;MACxC,IAAIC,KAAK,EAAE;QACTK,YAAY,CAACL,KAAK,CAAC;QACnBA,KAAK,GAAG,IAAI;MACd;MACAF,SAAS,GAAGK,GAAG;MACf,OAAOP,EAAE,CAACU,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IAClC;IACA,IAAI,CAACP,KAAK,EAAE;MACVA,KAAK,GAAGQ,UAAU,CAAC,MAAM;QACvBR,KAAK,GAAG,IAAI;QACZF,SAAS,GAAGM,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,OAAOP,EAAE,CAACU,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;MAClC,CAAC,EAAER,SAAS,IAAII,GAAG,GAAGL,SAAS,CAAC,CAAC;IACnC;EACF,CAAC;AACH;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}