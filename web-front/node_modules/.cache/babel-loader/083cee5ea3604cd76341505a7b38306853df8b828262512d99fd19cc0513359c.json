{"ast": null, "code": "/**\n * This should only ever be modified on the client otherwise it'll\n * persist through server requests. If we need instanced states we\n * could lazy-init via root.\n */\nconst globalProjectionState = {\n  /**\n   * Global flag as to whether the tree has animated since the last time\n   * we resized the window\n   */\n  hasAnimatedSinceResize: true,\n  /**\n   * We set this to true once, on the first update. Any nodes added to the tree beyond that\n   * update will be given a `data-projection-id` attribute.\n   */\n  hasEverUpdated: false\n};\nexport { globalProjectionState };", "map": {"version": 3, "names": ["globalProjectionState", "hasAnimatedSinceResize", "hasEverUpdated"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/node_modules/framer-motion/dist/es/projection/node/state.mjs"], "sourcesContent": ["/**\n * This should only ever be modified on the client otherwise it'll\n * persist through server requests. If we need instanced states we\n * could lazy-init via root.\n */\nconst globalProjectionState = {\n    /**\n     * Global flag as to whether the tree has animated since the last time\n     * we resized the window\n     */\n    hasAnimatedSinceResize: true,\n    /**\n     * We set this to true once, on the first update. Any nodes added to the tree beyond that\n     * update will be given a `data-projection-id` attribute.\n     */\n    hasEverUpdated: false,\n};\n\nexport { globalProjectionState };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,MAAMA,qBAAqB,GAAG;EAC1B;AACJ;AACA;AACA;EACIC,sBAAsB,EAAE,IAAI;EAC5B;AACJ;AACA;AACA;EACIC,cAAc,EAAE;AACpB,CAAC;AAED,SAASF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}