{"ast": null, "code": "import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\nimport { createNewUserReducer, deleteUserReducer, getProfileUserReducer, updateProfileUserReducer, userLoginReducer, usersListReducer } from \"./reducers/userReducers\";\nimport { clientListReducer, createNewClientReducer, deleteClientReducer, detailClientReducer, updateClientReducer } from \"./reducers/clientReducers\";\nimport { caseListReducer, createNewCaseReducer, deleteCaseReducer, detailCaseReducer, updateCaseReducer } from \"./reducers/caseReducers\";\nimport { addNewProviderReducer, detailProviderReducer, providerListReducer } from \"./reducers/providerReducers\";\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer\n  //\n});\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\") ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\")) : null;\nconst initialState = {\n  userLogin: {\n    userInfo: userInfoFromStorage\n  }\n};\nconst middleware = [thunk];\nconst store = createStore(reducer, initialState, composeWithDevTools(applyMiddleware(...middleware)));\nexport default store;", "map": {"version": 3, "names": ["createStore", "combineReducers", "applyMiddleware", "thunk", "composeWithDevTools", "createNewUserReducer", "deleteUserReducer", "getProfileUserReducer", "updateProfileUserReducer", "userLoginReducer", "usersListReducer", "clientListReducer", "createNewClientReducer", "deleteClientReducer", "detailClientReducer", "updateClientReducer", "caseListReducer", "createNewCaseReducer", "deleteCaseReducer", "detailCaseReducer", "updateCaseReducer", "addNewProviderReducer", "detailProviderReducer", "providerListReducer", "reducer", "userLogin", "caseList", "detailCase", "createNewCase", "deleteCase", "updateCase", "providerList", "detail<PERSON>rovider", "addNewProvider", "clientList", "createNewClient", "detailClient", "updateClient", "deleteClient", "usersList", "createNewUser", "getProfileUser", "updateProfileUser", "deleteUser", "userInfoFromStorage", "localStorage", "getItem", "JSON", "parse", "initialState", "userInfo", "middleware", "store"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/redux/store.js"], "sourcesContent": ["import { createStore, combineReducers, applyMiddleware } from \"redux\";\nimport thunk from \"redux-thunk\";\nimport { composeWithDevTools } from \"redux-devtools-extension\";\n\nimport {\n  createNewUserReducer,\n  deleteUserReducer,\n  getProfileUserReducer,\n  updateProfileUserReducer,\n  userLoginReducer,\n  usersListReducer,\n} from \"./reducers/userReducers\";\nimport {\n  clientListReducer,\n  createNewClientReducer,\n  deleteClientReducer,\n  detailClientReducer,\n  updateClientReducer,\n} from \"./reducers/clientReducers\";\n\nimport {\n  caseListReducer,\n  createNewCaseReducer,\n  deleteCaseReducer,\n  detailCaseReducer,\n  updateCaseReducer,\n} from \"./reducers/caseReducers\";\nimport {\n  addNewProviderReducer,\n  detailProviderReducer,\n  providerListReducer,\n} from \"./reducers/providerReducers\";\n\nconst reducer = combineReducers({\n  userLogin: userLoginReducer,\n\n  // cases\n  caseList: caseListReducer,\n  detailCase: detailCaseReducer,\n  createNewCase: createNewCaseReducer,\n  deleteCase: deleteCaseReducer,\n  updateCase: updateCaseReducer,\n  // providers\n  providerList: providerListReducer,\n  detailProvider: detailProviderReducer,\n  addNewProvider: addNewProviderReducer,\n  //\n  clientList: clientListReducer,\n  createNewClient: createNewClientReducer,\n  detailClient: detailClientReducer,\n  updateClient: updateClientReducer,\n  deleteClient: deleteClientReducer,\n  //\n\n  //\n  usersList: usersListReducer,\n  createNewUser: createNewUserReducer,\n  getProfileUser: getProfileUserReducer,\n  updateProfileUser: updateProfileUserReducer,\n  deleteUser: deleteUserReducer,\n  //\n});\n\nconst userInfoFromStorage = localStorage.getItem(\"userInfoUnimedCare\")\n  ? JSON.parse(localStorage.getItem(\"userInfoUnimedCare\"))\n  : null;\n\nconst initialState = {\n  userLogin: { userInfo: userInfoFromStorage },\n};\n\nconst middleware = [thunk];\n\nconst store = createStore(\n  reducer,\n  initialState,\n  composeWithDevTools(applyMiddleware(...middleware))\n);\n\nexport default store;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,eAAe,EAAEC,eAAe,QAAQ,OAAO;AACrE,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,mBAAmB,QAAQ,0BAA0B;AAE9D,SACEC,oBAAoB,EACpBC,iBAAiB,EACjBC,qBAAqB,EACrBC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,QACX,yBAAyB;AAChC,SACEC,iBAAiB,EACjBC,sBAAsB,EACtBC,mBAAmB,EACnBC,mBAAmB,EACnBC,mBAAmB,QACd,2BAA2B;AAElC,SACEC,eAAe,EACfC,oBAAoB,EACpBC,iBAAiB,EACjBC,iBAAiB,EACjBC,iBAAiB,QACZ,yBAAyB;AAChC,SACEC,qBAAqB,EACrBC,qBAAqB,EACrBC,mBAAmB,QACd,6BAA6B;AAEpC,MAAMC,OAAO,GAAGvB,eAAe,CAAC;EAC9BwB,SAAS,EAAEhB,gBAAgB;EAE3B;EACAiB,QAAQ,EAAEV,eAAe;EACzBW,UAAU,EAAER,iBAAiB;EAC7BS,aAAa,EAAEX,oBAAoB;EACnCY,UAAU,EAAEX,iBAAiB;EAC7BY,UAAU,EAAEV,iBAAiB;EAC7B;EACAW,YAAY,EAAER,mBAAmB;EACjCS,cAAc,EAAEV,qBAAqB;EACrCW,cAAc,EAAEZ,qBAAqB;EACrC;EACAa,UAAU,EAAEvB,iBAAiB;EAC7BwB,eAAe,EAAEvB,sBAAsB;EACvCwB,YAAY,EAAEtB,mBAAmB;EACjCuB,YAAY,EAAEtB,mBAAmB;EACjCuB,YAAY,EAAEzB,mBAAmB;EACjC;;EAEA;EACA0B,SAAS,EAAE7B,gBAAgB;EAC3B8B,aAAa,EAAEnC,oBAAoB;EACnCoC,cAAc,EAAElC,qBAAqB;EACrCmC,iBAAiB,EAAElC,wBAAwB;EAC3CmC,UAAU,EAAErC;EACZ;AACF,CAAC,CAAC;AAEF,MAAMsC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,GAClEC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GACtD,IAAI;AAER,MAAMG,YAAY,GAAG;EACnBxB,SAAS,EAAE;IAAEyB,QAAQ,EAAEN;EAAoB;AAC7C,CAAC;AAED,MAAMO,UAAU,GAAG,CAAChD,KAAK,CAAC;AAE1B,MAAMiD,KAAK,GAAGpD,WAAW,CACvBwB,OAAO,EACPyB,YAAY,EACZ7C,mBAAmB,CAACF,eAAe,CAAC,GAAGiD,UAAU,CAAC,CACpD,CAAC;AAED,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}