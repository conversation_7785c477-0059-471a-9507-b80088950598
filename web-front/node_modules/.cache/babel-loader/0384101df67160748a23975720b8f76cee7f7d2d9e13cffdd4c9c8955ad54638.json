{"ast": null, "code": "import axios from\"../../axios\";import{EMPLOYE_LIST_REQUEST,EMPLOYE_LIST_SUCCESS,EMPLOYE_LIST_FAIL,//\nEMPLOYE_ADD_REQUEST,EMPLOYE_ADD_SUCCESS,EMPLOYE_ADD_FAIL,//\nEMPLOYE_DETAIL_REQUEST,EMPLOYE_DETAIL_SUCCESS,EMPLOYE_DETAIL_FAIL,//\nEMPLOYE_UPDATE_REQUEST,EMPLOYE_UPDATE_SUCCESS,EMPLOYE_UPDATE_FAIL//\n}from\"../constants/employeConstants\";// update employe\nexport const updateEmploye=(id,employe)=>async(dispatch,getState)=>{try{dispatch({type:EMPLOYE_UPDATE_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"multipart/form-data\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.put(\"/employes/update/\".concat(id,\"/\"),employe,config);dispatch({type:EMPLOYE_UPDATE_SUCCESS,payload:data});}catch(error){dispatch({type:EMPLOYE_UPDATE_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// detail employe\nexport const detailEmploye=id=>async(dispatch,getState)=>{try{dispatch({type:EMPLOYE_DETAIL_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.get(\"/employes/detail/\".concat(id,\"/\"),config);dispatch({type:EMPLOYE_DETAIL_SUCCESS,payload:data});}catch(error){dispatch({type:EMPLOYE_DETAIL_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// add new employer\nexport const addNewEmploye=employe=>async(dispatch,getState)=>{try{dispatch({type:EMPLOYE_ADD_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"multipart/form-data\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.post(\"/employes/add/\",employe,config);dispatch({type:EMPLOYE_ADD_SUCCESS,payload:data});}catch(error){dispatch({type:EMPLOYE_ADD_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};// get list employes\nexport const getEmployesList=(status,page)=>async(dispatch,getState)=>{try{dispatch({type:EMPLOYE_LIST_REQUEST});var{userLogin:{userInfo}}=getState();const config={headers:{\"Content-Type\":\"application/json\",Authorization:\"Bearer \".concat(userInfo.access)}};const{data}=await axios.get(\"/employes/?status=\".concat(status,\"&page=\").concat(page),config);dispatch({type:EMPLOYE_LIST_SUCCESS,payload:data});}catch(error){dispatch({type:EMPLOYE_LIST_FAIL,payload:error.response&&error.response.data.detail?error.response.data.detail:error.detail});}};", "map": {"version": 3, "names": ["axios", "EMPLOYE_LIST_REQUEST", "EMPLOYE_LIST_SUCCESS", "EMPLOYE_LIST_FAIL", "EMPLOYE_ADD_REQUEST", "EMPLOYE_ADD_SUCCESS", "EMPLOYE_ADD_FAIL", "EMPLOYE_DETAIL_REQUEST", "EMPLOYE_DETAIL_SUCCESS", "EMPLOYE_DETAIL_FAIL", "EMPLOYE_UPDATE_REQUEST", "EMPLOYE_UPDATE_SUCCESS", "EMPLOYE_UPDATE_FAIL", "updateEmploye", "id", "employe", "dispatch", "getState", "type", "userLogin", "userInfo", "config", "headers", "Authorization", "concat", "access", "data", "put", "payload", "error", "response", "detail", "detailEmploye", "get", "addNewEmploye", "post", "getEmployesList", "status", "page"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/Project Location/web-location/src/redux/actions/employeActions.js"], "sourcesContent": ["import axios from \"../../axios\";\nimport {\n  EMPLOYE_LIST_REQUEST,\n  EMPLOYE_LIST_SUCCESS,\n  EMPLOYE_LIST_FAIL,\n  //\n  EMPLOYE_ADD_REQUEST,\n  EMPLOYE_ADD_SUCCESS,\n  EMPLOYE_ADD_FAIL,\n  //\n  EMPLOYE_DETAIL_REQUEST,\n  EMPLOYE_DETAIL_SUCCESS,\n  EMPLOYE_DETAIL_FAIL,\n  //\n  EMPLOYE_UPDATE_REQUEST,\n  EMPLOYE_UPDATE_SUCCESS,\n  EMPLOYE_UPDATE_FAIL,\n\n  //\n} from \"../constants/employeConstants\";\n\n// update employe\nexport const updateEmploye = (id, employe) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: EMPLOYE_UPDATE_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.put(\n      `/employes/update/${id}/`,\n      employe,\n      config\n    );\n\n    dispatch({\n      type: EMPLOYE_UPDATE_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: EMPLOYE_UPDATE_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// detail employe\nexport const detailEmploye = (id) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: EMPLOYE_DETAIL_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(`/employes/detail/${id}/`, config);\n\n    dispatch({\n      type: EMPLOYE_DETAIL_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: EMPLOYE_DETAIL_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// add new employer\nexport const addNewEmploye = (employe) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: EMPLOYE_ADD_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.post(`/employes/add/`, employe, config);\n\n    dispatch({\n      type: EMPLOYE_ADD_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: EMPLOYE_ADD_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n\n// get list employes\nexport const getEmployesList = (status, page) => async (dispatch, getState) => {\n  try {\n    dispatch({\n      type: EMPLOYE_LIST_REQUEST,\n    });\n    var {\n      userLogin: { userInfo },\n    } = getState();\n    const config = {\n      headers: {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${userInfo.access}`,\n      },\n    };\n    const { data } = await axios.get(\n      `/employes/?status=${status}&page=${page}`,\n      config\n    );\n\n    dispatch({\n      type: EMPLOYE_LIST_SUCCESS,\n      payload: data,\n    });\n  } catch (error) {\n    dispatch({\n      type: EMPLOYE_LIST_FAIL,\n      payload:\n        error.response && error.response.data.detail\n          ? error.response.data.detail\n          : error.detail,\n    });\n  }\n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,aAAa,CAC/B,OACEC,oBAAoB,CACpBC,oBAAoB,CACpBC,iBAAiB,CACjB;AACAC,mBAAmB,CACnBC,mBAAmB,CACnBC,gBAAgB,CAChB;AACAC,sBAAsB,CACtBC,sBAAsB,CACtBC,mBAAmB,CACnB;AACAC,sBAAsB,CACtBC,sBAAsB,CACtBC,mBAEA;AAAA,KACK,+BAA+B,CAEtC;AACA,MAAO,MAAM,CAAAC,aAAa,CAAGA,CAACC,EAAE,CAAEC,OAAO,GAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CAC1E,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAER,sBACR,CAAC,CAAC,CACF,GAAI,CACFS,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA1B,KAAK,CAAC2B,GAAG,qBAAAH,MAAA,CACVV,EAAE,MACtBC,OAAO,CACPM,MACF,CAAC,CAEDL,QAAQ,CAAC,CACPE,IAAI,CAAEP,sBAAsB,CAC5BiB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACdb,QAAQ,CAAC,CACPE,IAAI,CAAEN,mBAAmB,CACzBgB,OAAO,CACLC,KAAK,CAACC,QAAQ,EAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CAC1BF,KAAK,CAACE,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAIlB,EAAE,EAAK,MAAOE,QAAQ,CAAEC,QAAQ,GAAK,CACjE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEX,sBACR,CAAC,CAAC,CACF,GAAI,CACFY,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA1B,KAAK,CAACiC,GAAG,qBAAAT,MAAA,CAAqBV,EAAE,MAAKO,MAAM,CAAC,CAEnEL,QAAQ,CAAC,CACPE,IAAI,CAAEV,sBAAsB,CAC5BoB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACdb,QAAQ,CAAC,CACPE,IAAI,CAAET,mBAAmB,CACzBmB,OAAO,CACLC,KAAK,CAACC,QAAQ,EAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CAC1BF,KAAK,CAACE,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAG,aAAa,CAAInB,OAAO,EAAK,MAAOC,QAAQ,CAAEC,QAAQ,GAAK,CACtE,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEd,mBACR,CAAC,CAAC,CACF,GAAI,CACFe,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,qBAAqB,CACrCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA1B,KAAK,CAACmC,IAAI,kBAAmBpB,OAAO,CAAEM,MAAM,CAAC,CAEpEL,QAAQ,CAAC,CACPE,IAAI,CAAEb,mBAAmB,CACzBuB,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACdb,QAAQ,CAAC,CACPE,IAAI,CAAEZ,gBAAgB,CACtBsB,OAAO,CACLC,KAAK,CAACC,QAAQ,EAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CAC1BF,KAAK,CAACE,MACd,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAK,eAAe,CAAGA,CAACC,MAAM,CAAEC,IAAI,GAAK,MAAOtB,QAAQ,CAAEC,QAAQ,GAAK,CAC7E,GAAI,CACFD,QAAQ,CAAC,CACPE,IAAI,CAAEjB,oBACR,CAAC,CAAC,CACF,GAAI,CACFkB,SAAS,CAAE,CAAEC,QAAS,CACxB,CAAC,CAAGH,QAAQ,CAAC,CAAC,CACd,KAAM,CAAAI,MAAM,CAAG,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAAkB,CAClCC,aAAa,WAAAC,MAAA,CAAYJ,QAAQ,CAACK,MAAM,CAC1C,CACF,CAAC,CACD,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAA1B,KAAK,CAACiC,GAAG,sBAAAT,MAAA,CACTa,MAAM,WAAAb,MAAA,CAASc,IAAI,EACxCjB,MACF,CAAC,CAEDL,QAAQ,CAAC,CACPE,IAAI,CAAEhB,oBAAoB,CAC1B0B,OAAO,CAAEF,IACX,CAAC,CAAC,CACJ,CAAE,MAAOG,KAAK,CAAE,CACdb,QAAQ,CAAC,CACPE,IAAI,CAAEf,iBAAiB,CACvByB,OAAO,CACLC,KAAK,CAACC,QAAQ,EAAID,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CACxCF,KAAK,CAACC,QAAQ,CAACJ,IAAI,CAACK,MAAM,CAC1BF,KAAK,CAACE,MACd,CAAC,CAAC,CACJ,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}