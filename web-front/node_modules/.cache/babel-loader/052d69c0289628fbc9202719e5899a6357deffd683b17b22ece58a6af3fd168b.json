{"ast": null, "code": "import React,{useEffect,useState}from\"react\";import{useDispatch,useSelector}from\"react-redux\";import{useLocation,useNavigate,useParams}from\"react-router-dom\";import{detailCase}from\"../../redux/actions/caseActions\";import DefaultLayout from\"../../layouts/DefaultLayout\";import Loader from\"../../components/Loader\";import Alert from\"../../components/Alert\";import{baseURLFile}from\"../../constants\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function DetailCaseScreen(){var _caseInfo$patient$ful,_caseInfo$patient,_caseInfo$patient$ful2,_caseInfo$patient2,_caseInfo$patient$bir,_caseInfo$patient3,_caseInfo$patient$pat,_caseInfo$patient4,_caseInfo$patient$pat2,_caseInfo$patient5,_caseInfo$patient$pat3,_caseInfo$patient6,_caseInfo$coordinator,_caseInfo$case_descri,_caseInfo$status_coor,_caseInfo$service_loc,_caseInfo$provider$fu,_caseInfo$provider,_caseInfo$provider$ph,_caseInfo$provider2,_caseInfo$provider$em,_caseInfo$provider3,_caseInfo$provider$ad,_caseInfo$provider4,_caseInfo$medical_rep,_caseInfo$invoice_num,_caseInfo$upload_invo,_caseInfo$assurance_s,_caseInfo$assurance$a,_caseInfo$assurance,_caseInfo$policy_numb,_caseInfo$upload_auth;const navigate=useNavigate();const location=useLocation();const dispatch=useDispatch();let{id}=useParams();const[selectPage,setSelectPage]=useState(\"General Information\");//\nconst userLogin=useSelector(state=>state.userLogin);const{userInfo,loading,error}=userLogin;const caseDetail=useSelector(state=>state.detailCase);const{loadingCaseInfo,errorCaseInfo,successCaseInfo,caseInfo}=caseDetail;//\nconst redirect=\"/\";useEffect(()=>{if(!userInfo){navigate(redirect);}else{dispatch(detailCase(id));}},[navigate,userInfo,dispatch,id]);const formatDate=dateString=>{if(dateString&&dateString!==\"\"){const date=new Date(dateString);return date.toLocaleDateString(\"en-US\",{year:\"numeric\",month:\"long\",day:\"numeric\"});}else{return dateString;}};const caseStatus=casestatus=>{switch(casestatus){case\"pending-coordination\":return\"Pending Coordination\";case\"coordinated-missing-m-r\":return\"Coordinated, Missing M.R.\";case\"coordinated-missing-invoice\":return\"Coordinated, Missing Invoice\";case\"waiting-for-insurance-authorization\":return\"Waiting for Insurance Authorization\";case\"coordinated-patient-not-seen-yet\":return\"Coordinated, Patient not seen yet\";case\"fully-coordinate\":return\"Fully Coordinated\";default:return casestatus;}};//\nreturn/*#__PURE__*/_jsx(DefaultLayout,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row text-sm items-center my-1\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"/dashboard\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row  items-center hover:text-black \",children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"})}),/*#__PURE__*/_jsx(\"span\",{className:\"mx-1\",children:\"Dashboard\"})]})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"a\",{href:\"/cases-list\",children:/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Cases List\"})}),/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",className:\"w-4 h-4\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",d:\"m8.25 4.5 7.5 7.5-7.5 7.5\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:\"Case Page\"})]}),loadingCaseInfo?/*#__PURE__*/_jsx(Loader,{}):errorCaseInfo?/*#__PURE__*/_jsx(Alert,{type:\"error\",message:errorCaseInfo}):caseInfo?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\" text-[#32475C] text-md font-medium opacity-85\",children:[\"#\",caseInfo.id]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center my-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center mr-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-60 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-60 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Full Name:\"}),\" \",(_caseInfo$patient$ful=(_caseInfo$patient=caseInfo.patient)===null||_caseInfo$patient===void 0?void 0:_caseInfo$patient.full_name)!==null&&_caseInfo$patient$ful!==void 0?_caseInfo$patient$ful:\"---\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-row items-center ml-1\",children:[/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",fill:\"none\",viewBox:\"0 0 24 24\",\"stroke-width\":\"1.5\",stroke:\"currentColor\",class:\"size-4 mx-1 text-[#303030]  opacity-60 \",children:/*#__PURE__*/_jsx(\"path\",{\"stroke-linecap\":\"round\",\"stroke-linejoin\":\"round\",d:\"m4.5 12.75 6 6 9-13.5\"})})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mx-1 text-[#303030] text-sm opacity-60 \",children:[/*#__PURE__*/_jsx(\"span\",{className:\"font-semibold\",children:\"Status:\"}),\" \",caseStatus(caseInfo.status_coordination)]}),/*#__PURE__*/_jsx(\"div\",{className:\"\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-2 bg-white shadow-1 px-3 py-4 rounded\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\",children:[\"General Information\",\"Coordination Details\",\"Medical Reports\",\"Invoices\",\"Insurance Authorization\"].map((select,index)=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>setSelectPage(select),className:\"px-4 py-1 md:my-0 my-1  text-sm \".concat(selectPage===select?\"rounded-full bg-[#0388A6] text-white font-medium \":\"font-normal text-[#838383]\"),children:select}))}),selectPage===\"General Information\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Patient Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$ful2=(_caseInfo$patient2=caseInfo.patient)===null||_caseInfo$patient2===void 0?void 0:_caseInfo$patient2.full_name)!==null&&_caseInfo$patient$ful2!==void 0?_caseInfo$patient$ful2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date of Birth:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$bir=(_caseInfo$patient3=caseInfo.patient)===null||_caseInfo$patient3===void 0?void 0:_caseInfo$patient3.birth_day)!==null&&_caseInfo$patient$bir!==void 0?_caseInfo$patient$bir:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat=(_caseInfo$patient4=caseInfo.patient)===null||_caseInfo$patient4===void 0?void 0:_caseInfo$patient4.patient_phone)!==null&&_caseInfo$patient$pat!==void 0?_caseInfo$patient$pat:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat2=(_caseInfo$patient5=caseInfo.patient)===null||_caseInfo$patient5===void 0?void 0:_caseInfo$patient5.patient_email)!==null&&_caseInfo$patient$pat2!==void 0?_caseInfo$patient$pat2:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Address:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$patient$pat3=(_caseInfo$patient6=caseInfo.patient)===null||_caseInfo$patient6===void 0?void 0:_caseInfo$patient6.patient_address)!==null&&_caseInfo$patient$pat3!==void 0?_caseInfo$patient$pat3:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Case Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Case Creation Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.case_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Assigned Coordinator:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$coordinator=caseInfo.coordinator)!==null&&_caseInfo$coordinator!==void 0?_caseInfo$coordinator:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Description:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$case_descri=caseInfo.case_description)!==null&&_caseInfo$case_descri!==void 0?_caseInfo$case_descri:\"---\"})]})]})]}):null,selectPage===\"Coordination Details\"?/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Coordination Status\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Current Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$status_coor=caseInfo.status_coordination)!==null&&_caseInfo$status_coor!==void 0?_caseInfo$status_coor:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Last Updated Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.updated_at)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Appointment Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Scheduled Appointment Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.appointment_date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Service Location:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$service_loc=caseInfo.service_location)!==null&&_caseInfo$service_loc!==void 0?_caseInfo$service_loc:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Provider Information\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Provider Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$fu=(_caseInfo$provider=caseInfo.provider)===null||_caseInfo$provider===void 0?void 0:_caseInfo$provider.full_name)!==null&&_caseInfo$provider$fu!==void 0?_caseInfo$provider$fu:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Phone:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ph=(_caseInfo$provider2=caseInfo.provider)===null||_caseInfo$provider2===void 0?void 0:_caseInfo$provider2.phone)!==null&&_caseInfo$provider$ph!==void 0?_caseInfo$provider$ph:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Email:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$em=(_caseInfo$provider3=caseInfo.provider)===null||_caseInfo$provider3===void 0?void 0:_caseInfo$provider3.email)!==null&&_caseInfo$provider$em!==void 0?_caseInfo$provider$em:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Address:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$provider$ad=(_caseInfo$provider4=caseInfo.provider)===null||_caseInfo$provider4===void 0?void 0:_caseInfo$provider4.address)!==null&&_caseInfo$provider$ad!==void 0?_caseInfo$provider$ad:\"---\"})]})]})]})]}):null,selectPage===\"Medical Reports\"?/*#__PURE__*/_jsx(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$medical_rep=caseInfo.medical_reports)===null||_caseInfo$medical_rep===void 0?void 0:_caseInfo$medical_rep.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})}):null,selectPage===\"Invoices\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Invoice Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$invoice_num=caseInfo.invoice_number)!==null&&_caseInfo$invoice_num!==void 0?_caseInfo$invoice_num:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Date Issued:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:formatDate(caseInfo.date_issued)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Amount:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 mx-1\",children:[\"$\",parseFloat(caseInfo.invoice_amount).toFixed(2)]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Due Date:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Invoice Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:\"??\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_invo=caseInfo.upload_invoices)===null||_caseInfo$upload_invo===void 0?void 0:_caseInfo$upload_invo.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null,selectPage===\"Insurance Authorization\"?/*#__PURE__*/_jsxs(\"div\",{className:\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex md:flex-row flex-col\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Insurance Details\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Authorization Status:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance_s=caseInfo.assurance_status)!==null&&_caseInfo$assurance_s!==void 0?_caseInfo$assurance_s:\"---\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Insurance Company Name:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$assurance$a=(_caseInfo$assurance=caseInfo.assurance)===null||_caseInfo$assurance===void 0?void 0:_caseInfo$assurance.assurance_name)!==null&&_caseInfo$assurance$a!==void 0?_caseInfo$assurance$a:\"---\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:w-1/2 w-full  px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\" \"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"font-semibold\",children:\"Policy Number:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 mx-1\",children:(_caseInfo$policy_numb=caseInfo.policy_number)!==null&&_caseInfo$policy_numb!==void 0?_caseInfo$policy_numb:\"---\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full px-2 y-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"my-2 text-xs text-[#303030] opacity-60\",children:\"Uploaded Documents\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap\",children:(_caseInfo$upload_auth=caseInfo.upload_authorization)===null||_caseInfo$upload_auth===void 0?void 0:_caseInfo$upload_auth.map((item,index)=>/*#__PURE__*/_jsx(\"a\",{href:baseURLFile+item.file,target:\"_blank\",rel:\"noopener noreferrer\",className:\"md:w-1/3 w-full px-2 py-2 flex \",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\",children:/*#__PURE__*/_jsxs(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"0 0 24 24\",fill:\"currentColor\",className:\"size-4\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\"})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \",children:[/*#__PURE__*/_jsx(\"div\",{className:\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\",children:item.file_name}),/*#__PURE__*/_jsxs(\"div\",{children:[item.file_size,\" mb\"]})]})]})}))})]})]}):null]})]}):null]})});}export default DetailCaseScreen;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useLocation", "useNavigate", "useParams", "detailCase", "DefaultLayout", "Loader", "<PERSON><PERSON>", "baseURLFile", "jsx", "_jsx", "jsxs", "_jsxs", "DetailCaseScreen", "_caseInfo$patient$ful", "_caseInfo$patient", "_caseInfo$patient$ful2", "_caseInfo$patient2", "_caseInfo$patient$bir", "_caseInfo$patient3", "_caseInfo$patient$pat", "_caseInfo$patient4", "_caseInfo$patient$pat2", "_caseInfo$patient5", "_caseInfo$patient$pat3", "_caseInfo$patient6", "_caseInfo$coordinator", "_caseInfo$case_descri", "_caseInfo$status_coor", "_caseInfo$service_loc", "_caseInfo$provider$fu", "_caseInfo$provider", "_caseInfo$provider$ph", "_caseInfo$provider2", "_caseInfo$provider$em", "_caseInfo$provider3", "_caseInfo$provider$ad", "_caseInfo$provider4", "_caseInfo$medical_rep", "_caseInfo$invoice_num", "_caseInfo$upload_invo", "_caseInfo$assurance_s", "_caseInfo$assurance$a", "_caseInfo$assurance", "_caseInfo$policy_numb", "_caseInfo$upload_auth", "navigate", "location", "dispatch", "id", "selectPage", "setSelectPage", "userLogin", "state", "userInfo", "loading", "error", "caseDetail", "loadingCaseInfo", "errorCaseInfo", "successCaseInfo", "caseInfo", "redirect", "formatDate", "dateString", "date", "Date", "toLocaleDateString", "year", "month", "day", "caseStatus", "casestatus", "children", "className", "href", "xmlns", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "d", "type", "message", "class", "patient", "full_name", "status_coordination", "map", "select", "index", "onClick", "concat", "birth_day", "patient_phone", "patient_email", "patient_address", "case_date", "coordinator", "case_description", "updated_at", "appointment_date", "service_location", "provider", "phone", "email", "address", "medical_reports", "item", "file", "target", "rel", "file_name", "file_size", "invoice_number", "date_issued", "parseFloat", "invoice_amount", "toFixed", "upload_invoices", "assurance_status", "assurance", "assurance_name", "policy_number", "upload_authorization"], "sources": ["/Users/<USER>/Desktop/Freelances/<PERSON><PERSON><PERSON>/UNIMEDCARE/web-front/src/screens/cases/DetailCaseScreen.js"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useLocation, useNavigate, useParams } from \"react-router-dom\";\nimport { detailCase } from \"../../redux/actions/caseActions\";\nimport DefaultLayout from \"../../layouts/DefaultLayout\";\nimport Loader from \"../../components/Loader\";\nimport Alert from \"../../components/Alert\";\nimport { baseURLFile } from \"../../constants\";\n\nfunction DetailCaseScreen() {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const dispatch = useDispatch();\n  let { id } = useParams();\n\n  const [selectPage, setSelectPage] = useState(\"General Information\");\n\n  //\n  const userLogin = useSelector((state) => state.userLogin);\n  const { userInfo, loading, error } = userLogin;\n\n  const caseDetail = useSelector((state) => state.detailCase);\n  const { loadingCaseInfo, errorCaseInfo, successCaseInfo, caseInfo } =\n    caseDetail;\n  //\n  const redirect = \"/\";\n  useEffect(() => {\n    if (!userInfo) {\n      navigate(redirect);\n    } else {\n      dispatch(detailCase(id));\n    }\n  }, [navigate, userInfo, dispatch, id]);\n\n  const formatDate = (dateString) => {\n    if (dateString && dateString !== \"\") {\n      const date = new Date(dateString);\n      return date.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n      });\n    } else {\n      return dateString;\n    }\n  };\n\n  const caseStatus = (casestatus) => {\n    switch (casestatus) {\n      case \"pending-coordination\":\n        return \"Pending Coordination\";\n      case \"coordinated-missing-m-r\":\n        return \"Coordinated, Missing M.R.\";\n      case \"coordinated-missing-invoice\":\n        return \"Coordinated, Missing Invoice\";\n      case \"waiting-for-insurance-authorization\":\n        return \"Waiting for Insurance Authorization\";\n      case \"coordinated-patient-not-seen-yet\":\n        return \"Coordinated, Patient not seen yet\";\n      case \"fully-coordinate\":\n        return \"Fully Coordinated\";\n      default:\n        return casestatus;\n    }\n  };\n\n  //\n  return (\n    <DefaultLayout>\n      <div className=\"\">\n        <div className=\"flex flex-row text-sm items-center my-1\">\n          {/* home */}\n          <a href=\"/dashboard\">\n            <div className=\"flex flex-row  items-center hover:text-black \">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke-width=\"1.5\"\n                stroke=\"currentColor\"\n                className=\"w-4 h-4\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  d=\"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25\"\n                />\n              </svg>\n              <span className=\"mx-1\">Dashboard</span>\n            </div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <a href=\"/cases-list\">\n            <div className=\"\">Cases List</div>\n          </a>\n          <span>\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke-width=\"1.5\"\n              stroke=\"currentColor\"\n              className=\"w-4 h-4\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                d=\"m8.25 4.5 7.5 7.5-7.5 7.5\"\n              />\n            </svg>\n          </span>\n          <div className=\"\">Case Page</div>\n        </div>\n        {/*  */}\n\n        {loadingCaseInfo ? (\n          <Loader />\n        ) : errorCaseInfo ? (\n          <Alert type={\"error\"} message={errorCaseInfo} />\n        ) : caseInfo ? (\n          <div>\n            {/* info top */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\" text-[#32475C] text-md font-medium opacity-85\">\n                #{caseInfo.id}\n              </div>\n              <div className=\"flex flex-row items-center my-2\">\n                <div className=\"flex flex-row items-center mr-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Full Name:</span>{\" \"}\n                    {caseInfo.patient?.full_name ?? \"---\"}\n                  </div>\n                </div>\n                <div className=\"flex flex-row items-center ml-1\">\n                  <div>\n                    <svg\n                      xmlns=\"http://www.w3.org/2000/svg\"\n                      fill=\"none\"\n                      viewBox=\"0 0 24 24\"\n                      stroke-width=\"1.5\"\n                      stroke=\"currentColor\"\n                      class=\"size-4 mx-1 text-[#303030]  opacity-60 \"\n                    >\n                      <path\n                        stroke-linecap=\"round\"\n                        stroke-linejoin=\"round\"\n                        d=\"m4.5 12.75 6 6 9-13.5\"\n                      />\n                    </svg>\n                  </div>\n                  <div className=\"mx-1 text-[#303030] text-sm opacity-60 \">\n                    <span className=\"font-semibold\">Status:</span>{\" \"}\n                    {caseStatus(caseInfo.status_coordination)}\n                  </div>\n                  <div className=\"\"></div>\n                </div>\n              </div>\n            </div>\n            {/* info others */}\n            <div className=\"my-2 bg-white shadow-1 px-3 py-4 rounded\">\n              <div className=\"my-3 mx-2 bg-white shadow-2 py-3 flex flex-wrap md:rounded-full rounded md:justify-between px-2\">\n                {[\n                  \"General Information\",\n                  \"Coordination Details\",\n                  \"Medical Reports\",\n                  \"Invoices\",\n                  \"Insurance Authorization\",\n                ].map((select, index) => (\n                  <button\n                    onClick={() => setSelectPage(select)}\n                    className={`px-4 py-1 md:my-0 my-1  text-sm ${\n                      selectPage === select\n                        ? \"rounded-full bg-[#0388A6] text-white font-medium \"\n                        : \"font-normal text-[#838383]\"\n                    }`}\n                  >\n                    {select}\n                  </button>\n                ))}\n              </div>\n              {/* General Information */}\n              {selectPage === \"General Information\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"md:w-1/2 w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Patient Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Name:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.full_name ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Date of Birth:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.birth_day ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Phone:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_phone ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Email:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_email ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Address:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.patient?.patient_address ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Case Details\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Case Creation Date:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {formatDate(caseInfo.case_date)}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Assigned Coordinator:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.coordinator ?? \"---\"}\n                      </div>\n                    </div>\n                    <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                      <div className=\"font-semibold\">Description:</div>\n                      <div className=\"flex-1 mx-1\">\n                        {caseInfo.case_description ?? \"---\"}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* Coordination Details */}\n              {selectPage === \"Coordination Details\" ? (\n                <div>\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Coordination Status\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Current Status:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.status_coordination ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Last Updated Date:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.updated_at)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Appointment Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Scheduled Appointment Date:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.appointment_date)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Service Location:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.service_location ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  {/*  */}\n                  <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Provider Information\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Provider Name:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.full_name ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Phone:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.phone ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Email:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.email ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Address:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.provider?.address ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Medical Reports\" */}\n              {selectPage === \"Medical Reports\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 flex md:flex-row flex-col rounded \">\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.medical_reports?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Invoices\" */}\n              {selectPage === \"Invoices\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3 rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Invoice Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.invoice_number ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Date Issued:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {formatDate(caseInfo.date_issued)}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Amount:</div>\n                        <div className=\"flex-1 mx-1\">\n                          ${parseFloat(caseInfo.invoice_amount).toFixed(2)}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Due Date:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Invoice Status:</div>\n                        <div className=\"flex-1 mx-1\">??</div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_invoices?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n              {/* \"Insurance Authorization\" */}\n              {selectPage === \"Insurance Authorization\" ? (\n                <div className=\"my-3 mx-2 bg-white shadow-2 py-3 px-3  rounded \">\n                  <div className=\"flex md:flex-row flex-col\">\n                    <div className=\"md:w-1/2 w-full px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        Insurance Details\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Authorization Status:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance_status ?? \"---\"}\n                        </div>\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">\n                          Insurance Company Name:\n                        </div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.assurance?.assurance_name ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"md:w-1/2 w-full  px-2 y-2\">\n                      <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                        {\" \"}\n                      </div>\n                      <div className=\"text-[#303030] text-sm opacity-60 my-3 flex flex-row\">\n                        <div className=\"font-semibold\">Policy Number:</div>\n                        <div className=\"flex-1 mx-1\">\n                          {caseInfo.policy_number ?? \"---\"}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"w-full px-2 y-2\">\n                    <div className=\"my-2 text-xs text-[#303030] opacity-60\">\n                      Uploaded Documents\n                    </div>\n                    <div className=\"flex flex-wrap\">\n                      {caseInfo.upload_authorization?.map((item, index) => (\n                        <a\n                          href={baseURLFile + item.file}\n                          target=\"_blank\"\n                          rel=\"noopener noreferrer\"\n                          className=\"md:w-1/3 w-full px-2 py-2 flex \"\n                        >\n                          <div className=\"bg-[#F3F5F7] rounded-xl px-5 py-3 w-full flex flex-row items-center\">\n                            <div className=\"rounded-full bg-white text-[#81838E] text-center p-2 shadow-1\">\n                              <svg\n                                xmlns=\"http://www.w3.org/2000/svg\"\n                                viewBox=\"0 0 24 24\"\n                                fill=\"currentColor\"\n                                className=\"size-4\"\n                              >\n                                <path d=\"M5.625 1.5c-1.036 0-1.875.84-1.875 1.875v17.25c0 1.035.84 1.875 1.875 1.875h12.75c1.035 0 1.875-.84 1.875-1.875V12.75A3.75 3.75 0 0 0 16.5 9h-1.875a1.875 1.875 0 0 1-1.875-1.875V5.25A3.75 3.75 0 0 0 9 1.5H5.625Z\" />\n                                <path d=\"M12.971 1.816A5.23 5.23 0 0 1 14.25 5.25v1.875c0 .207.168.375.375.375H16.5a5.23 5.23 0 0 1 3.434 1.279 9.768 9.768 0 0 0-6.963-6.963Z\" />\n                              </svg>\n                            </div>\n                            <div className=\"flex-1 px-2 text-[#303030] text-sm overflow-hidden \">\n                              <div className=\"whitespace-nowrap overflow-hidden text-ellipsis max-w-full flex-shrink text-xs\">\n                                {item.file_name}\n                              </div>\n                              <div>{item.file_size} mb</div>\n                            </div>\n                          </div>\n                        </a>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ) : null}\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </DefaultLayout>\n  );\n}\n\nexport default DetailCaseScreen;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,WAAW,CAAEC,WAAW,CAAEC,SAAS,KAAQ,kBAAkB,CACtE,OAASC,UAAU,KAAQ,iCAAiC,CAC5D,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,KAAK,KAAM,wBAAwB,CAC1C,OAASC,WAAW,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9C,QAAS,CAAAC,gBAAgBA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,iBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,mBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC1B,KAAM,CAAAC,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,QAAQ,CAAGjD,WAAW,CAAC,CAAC,CAC9B,GAAI,CAAEkD,EAAG,CAAC,CAAG9C,SAAS,CAAC,CAAC,CAExB,KAAM,CAAC+C,UAAU,CAAEC,aAAa,CAAC,CAAGrD,QAAQ,CAAC,qBAAqB,CAAC,CAEnE;AACA,KAAM,CAAAsD,SAAS,CAAGpD,WAAW,CAAEqD,KAAK,EAAKA,KAAK,CAACD,SAAS,CAAC,CACzD,KAAM,CAAEE,QAAQ,CAAEC,OAAO,CAAEC,KAAM,CAAC,CAAGJ,SAAS,CAE9C,KAAM,CAAAK,UAAU,CAAGzD,WAAW,CAAEqD,KAAK,EAAKA,KAAK,CAACjD,UAAU,CAAC,CAC3D,KAAM,CAAEsD,eAAe,CAAEC,aAAa,CAAEC,eAAe,CAAEC,QAAS,CAAC,CACjEJ,UAAU,CACZ;AACA,KAAM,CAAAK,QAAQ,CAAG,GAAG,CACpBjE,SAAS,CAAC,IAAM,CACd,GAAI,CAACyD,QAAQ,CAAE,CACbR,QAAQ,CAACgB,QAAQ,CAAC,CACpB,CAAC,IAAM,CACLd,QAAQ,CAAC5C,UAAU,CAAC6C,EAAE,CAAC,CAAC,CAC1B,CACF,CAAC,CAAE,CAACH,QAAQ,CAAEQ,QAAQ,CAAEN,QAAQ,CAAEC,EAAE,CAAC,CAAC,CAEtC,KAAM,CAAAc,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAIA,UAAU,EAAIA,UAAU,GAAK,EAAE,CAAE,CACnC,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACF,UAAU,CAAC,CACjC,MAAO,CAAAC,IAAI,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACtCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SACP,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAN,UAAU,CACnB,CACF,CAAC,CAED,KAAM,CAAAO,UAAU,CAAIC,UAAU,EAAK,CACjC,OAAQA,UAAU,EAChB,IAAK,sBAAsB,CACzB,MAAO,sBAAsB,CAC/B,IAAK,yBAAyB,CAC5B,MAAO,2BAA2B,CACpC,IAAK,6BAA6B,CAChC,MAAO,8BAA8B,CACvC,IAAK,qCAAqC,CACxC,MAAO,qCAAqC,CAC9C,IAAK,kCAAkC,CACrC,MAAO,mCAAmC,CAC5C,IAAK,kBAAkB,CACrB,MAAO,mBAAmB,CAC5B,QACE,MAAO,CAAAA,UAAU,CACrB,CACF,CAAC,CAED;AACA,mBACE9D,IAAA,CAACL,aAAa,EAAAoE,QAAA,cACZ7D,KAAA,QAAK8D,SAAS,CAAC,EAAE,CAAAD,QAAA,eACf7D,KAAA,QAAK8D,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eAEtD/D,IAAA,MAAGiE,IAAI,CAAC,YAAY,CAAAF,QAAA,cAClB7D,KAAA,QAAK8D,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5D/D,IAAA,QACEkE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/D,IAAA,SACEsE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,4OAA4O,CAC/O,CAAC,CACC,CAAC,cACNxE,IAAA,SAAMgE,SAAS,CAAC,MAAM,CAAAD,QAAA,CAAC,WAAS,CAAM,CAAC,EACpC,CAAC,CACL,CAAC,cACJ/D,IAAA,SAAA+D,QAAA,cACE/D,IAAA,QACEkE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/D,IAAA,SACEsE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxE,IAAA,MAAGiE,IAAI,CAAC,aAAa,CAAAF,QAAA,cACnB/D,IAAA,QAAKgE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,YAAU,CAAK,CAAC,CACjC,CAAC,cACJ/D,IAAA,SAAA+D,QAAA,cACE/D,IAAA,QACEkE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBL,SAAS,CAAC,SAAS,CAAAD,QAAA,cAEnB/D,IAAA,SACEsE,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,CAAC,CAAC,2BAA2B,CAC9B,CAAC,CACC,CAAC,CACF,CAAC,cACPxE,IAAA,QAAKgE,SAAS,CAAC,EAAE,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,EAC9B,CAAC,CAGLf,eAAe,cACdhD,IAAA,CAACJ,MAAM,GAAE,CAAC,CACRqD,aAAa,cACfjD,IAAA,CAACH,KAAK,EAAC4E,IAAI,CAAE,OAAQ,CAACC,OAAO,CAAEzB,aAAc,CAAE,CAAC,CAC9CE,QAAQ,cACVjD,KAAA,QAAA6D,QAAA,eAEE7D,KAAA,QAAK8D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD7D,KAAA,QAAK8D,SAAS,CAAC,gDAAgD,CAAAD,QAAA,EAAC,GAC7D,CAACZ,QAAQ,CAACZ,EAAE,EACV,CAAC,cACNrC,KAAA,QAAK8D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C7D,KAAA,QAAK8D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C/D,IAAA,QAAA+D,QAAA,cACE/D,IAAA,QACEkE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/D,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwE,CAAC,CAAC,yJAAyJ,CAC5J,CAAC,CACC,CAAC,CACH,CAAC,cACNtE,KAAA,QAAK8D,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/D,IAAA,SAAMgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,CAAC,GAAG,EAAA3D,qBAAA,EAAAC,iBAAA,CACpD8C,QAAQ,CAACyB,OAAO,UAAAvE,iBAAA,iBAAhBA,iBAAA,CAAkBwE,SAAS,UAAAzE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,EAClC,CAAC,EACH,CAAC,cACNF,KAAA,QAAK8D,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC9C/D,IAAA,QAAA+D,QAAA,cACE/D,IAAA,QACEkE,KAAK,CAAC,4BAA4B,CAClCC,IAAI,CAAC,MAAM,CACXC,OAAO,CAAC,WAAW,CACnB,eAAa,KAAK,CAClBC,MAAM,CAAC,cAAc,CACrBM,KAAK,CAAC,yCAAyC,CAAAZ,QAAA,cAE/C/D,IAAA,SACE,iBAAe,OAAO,CACtB,kBAAgB,OAAO,CACvBwE,CAAC,CAAC,uBAAuB,CAC1B,CAAC,CACC,CAAC,CACH,CAAC,cACNtE,KAAA,QAAK8D,SAAS,CAAC,yCAAyC,CAAAD,QAAA,eACtD/D,IAAA,SAAMgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAM,CAAC,CAAC,GAAG,CACjDF,UAAU,CAACV,QAAQ,CAAC2B,mBAAmB,CAAC,EACtC,CAAC,cACN9E,IAAA,QAAKgE,SAAS,CAAC,EAAE,CAAM,CAAC,EACrB,CAAC,EACH,CAAC,EACH,CAAC,cAEN9D,KAAA,QAAK8D,SAAS,CAAC,0CAA0C,CAAAD,QAAA,eACvD/D,IAAA,QAAKgE,SAAS,CAAC,iGAAiG,CAAAD,QAAA,CAC7G,CACC,qBAAqB,CACrB,sBAAsB,CACtB,iBAAiB,CACjB,UAAU,CACV,yBAAyB,CAC1B,CAACgB,GAAG,CAAC,CAACC,MAAM,CAAEC,KAAK,gBAClBjF,IAAA,WACEkF,OAAO,CAAEA,CAAA,GAAMzC,aAAa,CAACuC,MAAM,CAAE,CACrChB,SAAS,oCAAAmB,MAAA,CACP3C,UAAU,GAAKwC,MAAM,CACjB,mDAAmD,CACnD,4BAA4B,CAC/B,CAAAjB,QAAA,CAEFiB,MAAM,CACD,CACT,CAAC,CACC,CAAC,CAELxC,UAAU,GAAK,qBAAqB,cACnCtC,KAAA,QAAK8D,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7D,KAAA,QAAK8D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,OAAK,CAAK,CAAC,cAC1C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzD,sBAAA,EAAAC,kBAAA,CACzB4C,QAAQ,CAACyB,OAAO,UAAArE,kBAAA,iBAAhBA,kBAAA,CAAkBsE,SAAS,UAAAvE,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNJ,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvD,qBAAA,EAAAC,kBAAA,CACzB0C,QAAQ,CAACyB,OAAO,UAAAnE,kBAAA,iBAAhBA,kBAAA,CAAkB2E,SAAS,UAAA5E,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAClC,CAAC,EACH,CAAC,cACNN,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArD,qBAAA,EAAAC,kBAAA,CACzBwC,QAAQ,CAACyB,OAAO,UAAAjE,kBAAA,iBAAhBA,kBAAA,CAAkB0E,aAAa,UAAA3E,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNR,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAnD,sBAAA,EAAAC,kBAAA,CACzBsC,QAAQ,CAACyB,OAAO,UAAA/D,kBAAA,iBAAhBA,kBAAA,CAAkByE,aAAa,UAAA1E,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACtC,CAAC,EACH,CAAC,cACNV,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAjD,sBAAA,EAAAC,kBAAA,CACzBoC,QAAQ,CAACyB,OAAO,UAAA7D,kBAAA,iBAAhBA,kBAAA,CAAkBwE,eAAe,UAAAzE,sBAAA,UAAAA,sBAAA,CAAI,KAAK,CACxC,CAAC,EACH,CAAC,EACH,CAAC,cACNZ,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,cAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,qBAAmB,CAAK,CAAC,cACxD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACF,QAAQ,CAACqC,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cACNtF,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAAqB,CAAK,CAAC,cAC1D/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/C,qBAAA,CACzBmC,QAAQ,CAACsC,WAAW,UAAAzE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC3B,CAAC,EACH,CAAC,cACNd,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA9C,qBAAA,CACzBkC,QAAQ,CAACuC,gBAAgB,UAAAzE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPuB,UAAU,GAAK,sBAAsB,cACpCtC,KAAA,QAAA6D,QAAA,eACE7D,KAAA,QAAK8D,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7D,KAAA,QAAK8D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA7C,qBAAA,CACzBiC,QAAQ,CAAC2B,mBAAmB,UAAA5D,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNhB,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,oBAAkB,CAAK,CAAC,cACvD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACF,QAAQ,CAACwC,UAAU,CAAC,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,cACNzF,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,qBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,6BAE/B,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACF,QAAQ,CAACyC,gBAAgB,CAAC,CACnC,CAAC,EACH,CAAC,cACN1F,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,mBAAiB,CAAK,CAAC,cACtD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA5C,qBAAA,CACzBgC,QAAQ,CAAC0C,gBAAgB,UAAA1E,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENjB,KAAA,QAAK8D,SAAS,CAAC,0EAA0E,CAAAD,QAAA,eACvF7D,KAAA,QAAK8D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,sBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA3C,qBAAA,EAAAC,kBAAA,CACzB8B,QAAQ,CAAC2C,QAAQ,UAAAzE,kBAAA,iBAAjBA,kBAAA,CAAmBwD,SAAS,UAAAzD,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACnC,CAAC,EACH,CAAC,cACNlB,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAzC,qBAAA,EAAAC,mBAAA,CACzB4B,QAAQ,CAAC2C,QAAQ,UAAAvE,mBAAA,iBAAjBA,mBAAA,CAAmBwE,KAAK,UAAAzE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,EACH,CAAC,cACNpB,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,QAAM,CAAK,CAAC,cAC3C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAvC,qBAAA,EAAAC,mBAAA,CACzB0B,QAAQ,CAAC2C,QAAQ,UAAArE,mBAAA,iBAAjBA,mBAAA,CAAmBuE,KAAK,UAAAxE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC/B,CAAC,EACH,CAAC,cACNtB,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,UAAQ,CAAK,CAAC,cAC7C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAArC,qBAAA,EAAAC,mBAAA,CACzBwB,QAAQ,CAAC2C,QAAQ,UAAAnE,mBAAA,iBAAjBA,mBAAA,CAAmBsE,OAAO,UAAAvE,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACjC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPc,UAAU,GAAK,iBAAiB,cAC/BxC,IAAA,QAAKgE,SAAS,CAAC,0EAA0E,CAAAD,QAAA,cACvF7D,KAAA,QAAK8D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAnC,qBAAA,CAC5BuB,QAAQ,CAAC+C,eAAe,UAAAtE,qBAAA,iBAAxBA,qBAAA,CAA0BmD,GAAG,CAAC,CAACoB,IAAI,CAAElB,KAAK,gBACzCjF,IAAA,MACEiE,IAAI,CAAEnE,WAAW,CAAGqG,IAAI,CAACC,IAAK,CAC9BC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBtC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7D,KAAA,QAAK8D,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/D,IAAA,QAAKgE,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7D,KAAA,QACEgE,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/D,IAAA,SAAMwE,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxE,IAAA,SAAMwE,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtE,KAAA,QAAK8D,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/D,IAAA,QAAKgE,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FoC,IAAI,CAACI,SAAS,CACZ,CAAC,cACNrG,KAAA,QAAA6D,QAAA,EAAMoC,IAAI,CAACK,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CACJ,IAAI,CAEPhE,UAAU,GAAK,UAAU,cACxBtC,KAAA,QAAK8D,SAAS,CAAC,gDAAgD,CAAAD,QAAA,eAC7D7D,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7D,KAAA,QAAK8D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,iBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAlC,qBAAA,CACzBsB,QAAQ,CAACsD,cAAc,UAAA5E,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC9B,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,cAAY,CAAK,CAAC,cACjD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CACzBV,UAAU,CAACF,QAAQ,CAACuD,WAAW,CAAC,CAC9B,CAAC,EACH,CAAC,cACNxG,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,SAAO,CAAK,CAAC,cAC5C7D,KAAA,QAAK8D,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAC,GAC1B,CAAC4C,UAAU,CAACxD,QAAQ,CAACyD,cAAc,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,EAC7C,CAAC,EACH,CAAC,EACH,CAAC,cACN3G,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,WAAS,CAAK,CAAC,cAC9C/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAK,CAAC,cACpD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,CAAC,IAAE,CAAK,CAAC,EAClC,CAAC,EACH,CAAC,EACH,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAAjC,qBAAA,CAC5BqB,QAAQ,CAAC2D,eAAe,UAAAhF,qBAAA,iBAAxBA,qBAAA,CAA0BiD,GAAG,CAAC,CAACoB,IAAI,CAAElB,KAAK,gBACzCjF,IAAA,MACEiE,IAAI,CAAEnE,WAAW,CAAGqG,IAAI,CAACC,IAAK,CAC9BC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBtC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7D,KAAA,QAAK8D,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/D,IAAA,QAAKgE,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7D,KAAA,QACEgE,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/D,IAAA,SAAMwE,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxE,IAAA,SAAMwE,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtE,KAAA,QAAK8D,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/D,IAAA,QAAKgE,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FoC,IAAI,CAACI,SAAS,CACZ,CAAC,cACNrG,KAAA,QAAA6D,QAAA,EAAMoC,IAAI,CAACK,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,CAEPhE,UAAU,GAAK,yBAAyB,cACvCtC,KAAA,QAAK8D,SAAS,CAAC,iDAAiD,CAAAD,QAAA,eAC9D7D,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC7D,KAAA,QAAK8D,SAAS,CAAC,0BAA0B,CAAAD,QAAA,eACvC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,mBAExD,CAAK,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,uBAE/B,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAAhC,qBAAA,CACzBoB,QAAQ,CAAC4D,gBAAgB,UAAAhF,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAChC,CAAC,EACH,CAAC,cACN7B,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,yBAE/B,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA/B,qBAAA,EAAAC,mBAAA,CACzBkB,QAAQ,CAAC6D,SAAS,UAAA/E,mBAAA,iBAAlBA,mBAAA,CAAoBgF,cAAc,UAAAjF,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CACzC,CAAC,EACH,CAAC,EACH,CAAC,cACN9B,KAAA,QAAK8D,SAAS,CAAC,2BAA2B,CAAAD,QAAA,eACxC/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CACpD,GAAG,CACD,CAAC,cACN7D,KAAA,QAAK8D,SAAS,CAAC,sDAAsD,CAAAD,QAAA,eACnE/D,IAAA,QAAKgE,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,gBAAc,CAAK,CAAC,cACnD/D,IAAA,QAAKgE,SAAS,CAAC,aAAa,CAAAD,QAAA,EAAA7B,qBAAA,CACzBiB,QAAQ,CAAC+D,aAAa,UAAAhF,qBAAA,UAAAA,qBAAA,CAAI,KAAK,CAC7B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cACNhC,KAAA,QAAK8D,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9B/D,IAAA,QAAKgE,SAAS,CAAC,wCAAwC,CAAAD,QAAA,CAAC,oBAExD,CAAK,CAAC,cACN/D,IAAA,QAAKgE,SAAS,CAAC,gBAAgB,CAAAD,QAAA,EAAA5B,qBAAA,CAC5BgB,QAAQ,CAACgE,oBAAoB,UAAAhF,qBAAA,iBAA7BA,qBAAA,CAA+B4C,GAAG,CAAC,CAACoB,IAAI,CAAElB,KAAK,gBAC9CjF,IAAA,MACEiE,IAAI,CAAEnE,WAAW,CAAGqG,IAAI,CAACC,IAAK,CAC9BC,MAAM,CAAC,QAAQ,CACfC,GAAG,CAAC,qBAAqB,CACzBtC,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cAE3C7D,KAAA,QAAK8D,SAAS,CAAC,qEAAqE,CAAAD,QAAA,eAClF/D,IAAA,QAAKgE,SAAS,CAAC,+DAA+D,CAAAD,QAAA,cAC5E7D,KAAA,QACEgE,KAAK,CAAC,4BAA4B,CAClCE,OAAO,CAAC,WAAW,CACnBD,IAAI,CAAC,cAAc,CACnBH,SAAS,CAAC,QAAQ,CAAAD,QAAA,eAElB/D,IAAA,SAAMwE,CAAC,CAAC,qNAAqN,CAAE,CAAC,cAChOxE,IAAA,SAAMwE,CAAC,CAAC,uIAAuI,CAAE,CAAC,EAC/I,CAAC,CACH,CAAC,cACNtE,KAAA,QAAK8D,SAAS,CAAC,qDAAqD,CAAAD,QAAA,eAClE/D,IAAA,QAAKgE,SAAS,CAAC,gFAAgF,CAAAD,QAAA,CAC5FoC,IAAI,CAACI,SAAS,CACZ,CAAC,cACNrG,KAAA,QAAA6D,QAAA,EAAMoC,IAAI,CAACK,SAAS,CAAC,KAAG,EAAK,CAAC,EAC3B,CAAC,EACH,CAAC,CACL,CACJ,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,EACH,CAAC,CACJ,IAAI,EACL,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAArG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}