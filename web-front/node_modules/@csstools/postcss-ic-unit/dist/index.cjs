"use strict";var e=require("@csstools/postcss-progressive-custom-properties"),s=require("postcss-value-parser");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var r=t(e),o=t(s);const u=e=>({postcssPlugin:"postcss-ic-unit",Declaration(s){if(!s.value.toLowerCase().includes("ic"))return;if(function(e){let s=e.parent;for(;s;)if("atrule"===s.type){if("supports"===s.name.toLowerCase()&&/\(font-size: \d+ic\)/.test(s.params.toLowerCase()))return!0;s=s.parent}else s=s.parent;return!1}(s))return;const t=o.default(s.value);t.walk((e=>{if(!e.type||"word"!==e.type)return;const s=o.default.unit(e.value);s&&"ic"===s.unit.toLowerCase()&&(e.value=`${s.number}em`)}));const r=String(t);r!==s.value&&(s.cloneBefore({value:r}),e.preserve||s.remove())}});u.postcss=!0;const n=e=>{const s=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return s.enableProgressiveCustomProperties&&s.preserve?{postcssPlugin:"postcss-ic-unit",plugins:[r.default(),u(s)]}:u(s)};n.postcss=!0,module.exports=n;
