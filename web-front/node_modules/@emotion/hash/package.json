{"name": "@emotion/hash", "version": "0.9.1", "description": "A MurmurHash2 implementation", "main": "dist/emotion-hash.cjs.js", "module": "dist/emotion-hash.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/hash", "files": ["src", "dist", "types/*.d.ts"], "scripts": {"test:typescript": "dtslint types"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^4.5.5"}, "exports": {".": {"module": "./dist/emotion-hash.esm.js", "import": "./dist/emotion-hash.cjs.mjs", "default": "./dist/emotion-hash.cjs.js"}, "./package.json": "./package.json"}}