{"name": "@emotion/memoize", "version": "0.8.1", "description": "emotion's memoize utility", "main": "dist/emotion-memoize.cjs.js", "module": "dist/emotion-memoize.esm.js", "types": "types/index.d.ts", "license": "MIT", "repository": "https://github.com/emotion-js/emotion/tree/main/packages/memoize", "scripts": {"test:typescript": "dtslint types"}, "publishConfig": {"access": "public"}, "devDependencies": {"@definitelytyped/dtslint": "0.0.112", "typescript": "^4.5.5"}, "files": ["src", "dist", "types/*.d.ts"], "exports": {".": {"module": "./dist/emotion-memoize.esm.js", "import": "./dist/emotion-memoize.cjs.mjs", "default": "./dist/emotion-memoize.cjs.js"}, "./package.json": "./package.json"}}