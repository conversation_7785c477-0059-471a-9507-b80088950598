!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).emotionHoistNonReactStatics=t()}(this,(function(){"use strict";function e(e,t,r){return e(r={path:t,exports:{},require:function(e,t){return function(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}(null==t&&r.path)}},r.exports),r.exports}var t=e((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&Symbol.for,o=r?Symbol.for("react.element"):60103,n=r?Symbol.for("react.portal"):60106,f=r?Symbol.for("react.fragment"):60107,c=r?Symbol.for("react.strict_mode"):60108,i=r?Symbol.for("react.profiler"):60114,a=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,u=r?Symbol.for("react.async_mode"):60111,p=r?Symbol.for("react.concurrent_mode"):60111,y=r?Symbol.for("react.forward_ref"):60112,l=r?Symbol.for("react.suspense"):60113,m=r?Symbol.for("react.suspense_list"):60120,d=r?Symbol.for("react.memo"):60115,b=r?Symbol.for("react.lazy"):60116,$=r?Symbol.for("react.fundamental"):60117,S=r?Symbol.for("react.responder"):60118,g=r?Symbol.for("react.scope"):60119;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case o:switch(e=e.type){case u:case p:case f:case i:case c:case l:return e;default:switch(e=e&&e.$$typeof){case s:case y:case b:case d:case a:return e;default:return t}}case n:return t}}}function P(e){return v(e)===p}t.typeOf=v,t.AsyncMode=u,t.ConcurrentMode=p,t.ContextConsumer=s,t.ContextProvider=a,t.Element=o,t.ForwardRef=y,t.Fragment=f,t.Lazy=b,t.Memo=d,t.Portal=n,t.Profiler=i,t.StrictMode=c,t.Suspense=l,t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===f||e===p||e===i||e===c||e===l||e===m||"object"==typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===d||e.$$typeof===a||e.$$typeof===s||e.$$typeof===y||e.$$typeof===$||e.$$typeof===S||e.$$typeof===g)},t.isAsyncMode=function(e){return P(e)||v(e)===u},t.isConcurrentMode=P,t.isContextConsumer=function(e){return v(e)===s},t.isContextProvider=function(e){return v(e)===a},t.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===o},t.isForwardRef=function(e){return v(e)===y},t.isFragment=function(e){return v(e)===f},t.isLazy=function(e){return v(e)===b},t.isMemo=function(e){return v(e)===d},t.isPortal=function(e){return v(e)===n},t.isProfiler=function(e){return v(e)===i},t.isStrictMode=function(e){return v(e)===c},t.isSuspense=function(e){return v(e)===l}}));e((function(e,t){}));var r=e((function(e){e.exports=t})),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},n={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},f={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},c={};function i(e){return r.isMemo(e)?f:c[e.$$typeof]||o}c[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0};var a=Object.defineProperty,s=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,y=Object.getPrototypeOf,l=Object.prototype;var m=function e(t,r,o){if("string"!=typeof r){if(l){var f=y(r);f&&f!==l&&e(t,f,o)}var c=s(r);u&&(c=c.concat(u(r)));for(var m=i(t),d=i(r),b=0;b<c.length;++b){var $=c[b];if(!(n[$]||o&&o[$]||d&&d[$]||m&&m[$])){var S=p(r,$);try{a(t,$,S)}catch(e){}}}}return t};return function(e,t){return m(e,t)}}));
//# sourceMappingURL=emotion-react-_isolated-hnrs.umd.min.js.map
